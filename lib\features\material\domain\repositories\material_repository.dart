import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material.dart';
import '../entities/create_material_request.dart';

abstract class MaterialRepository {
  /// 创建材料
  Future<Either<Failure, Material>> createMaterial(CreateMaterialRequest request);
  
  /// 获取用户材料列表
  Future<Either<Failure, List<Material>>> getUserMaterials(String userId);

  /// 获取公开材料列表（游客模式使用）
  Future<Either<Failure, List<Material>>> getPublicMaterials();
  
  /// 根据分类获取材料
  Future<Either<Failure, List<Material>>> getMaterialsByCategory(
    String userId, 
    String category,
  );
  
  /// 获取材料详情
  Future<Either<Failure, Material>> getMaterialById(String materialId);
  
  /// 更新材料
  Future<Either<Failure, Material>> updateMaterial(
    String materialId, 
    Map<String, dynamic> updates,
  );
  
  /// 删除材料
  Future<Either<Failure, void>> deleteMaterial(String materialId);
  
  /// 搜索材料
  Future<Either<Failure, List<Material>>> searchMaterials(
    String userId,
    String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  });
  
  /// 增加使用次数
  Future<Either<Failure, void>> incrementUsageCount(String materialId);
  
  /// 获取热门材料
  Future<Either<Failure, List<Material>>> getPopularMaterials(
    String userId, {
    int limit = 10,
  });
  
  /// 获取最近使用的材料
  Future<Either<Failure, List<Material>>> getRecentlyUsedMaterials(
    String userId, {
    int limit = 10,
  });
}