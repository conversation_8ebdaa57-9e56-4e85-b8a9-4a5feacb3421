import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/errors/failures.dart';
import '../../data/repositories/material_favorite_repository_impl.dart';
import '../../domain/entities/material_favorite.dart';
import '../../domain/repositories/material_favorite_repository.dart';

part 'material_favorite_provider.g.dart';

/// 材料收藏Repository Provider
@riverpod
MaterialFavoriteRepository materialFavoriteRepository(MaterialFavoriteRepositoryRef ref) {
  return MaterialFavoriteRepositoryImpl(
    supabaseClient: Supabase.instance.client,
  );
}

/// 材料收藏控制器Provider
@riverpod
class MaterialFavoriteController extends _$MaterialFavoriteController {
  @override
  AsyncValue<void> build() {
    return const AsyncData(null);
  }

  /// 切换收藏状态
  Future<Either<Failure, bool>> toggleFavorite({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialFavoriteRepositoryProvider).toggleFavorite(
        materialId: materialId,
        tags: tags,
        notes: notes,
        category: category,
        priority: priority,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight()) {
        ref.invalidate(userFavoritesProvider);
        ref.invalidate(materialFavoriteStatusProvider(materialId));
        ref.invalidate(favoriteStatsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '切换收藏状态失败: $e'));
    }
  }

  /// 添加到收藏
  Future<Either<Failure, MaterialFavorite>> addToFavorites({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialFavoriteRepositoryProvider).addToFavorites(
        materialId: materialId,
        tags: tags,
        notes: notes,
        category: category,
        priority: priority,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight()) {
        ref.invalidate(userFavoritesProvider);
        ref.invalidate(materialFavoriteStatusProvider(materialId));
        ref.invalidate(favoriteStatsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '添加收藏失败: $e'));
    }
  }

  /// 从收藏中移除
  Future<Either<Failure, void>> removeFromFavorites(String materialId) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialFavoriteRepositoryProvider).removeFromFavorites(materialId);

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight()) {
        ref.invalidate(userFavoritesProvider);
        ref.invalidate(materialFavoriteStatusProvider(materialId));
        ref.invalidate(favoriteStatsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '移除收藏失败: $e'));
    }
  }

  /// 更新收藏信息
  Future<Either<Failure, MaterialFavorite>> updateFavorite(
    String favoriteId,
    Map<String, dynamic> updates,
  ) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialFavoriteRepositoryProvider).updateFavorite(
        favoriteId,
        updates,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight()) {
        ref.invalidate(userFavoritesProvider);
        ref.invalidate(favoriteStatsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '更新收藏失败: $e'));
    }
  }
}

/// 用户收藏列表Provider
@riverpod
Future<List<MaterialFavorite>> userFavorites(UserFavoritesRef ref) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).getUserFavorites();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 材料收藏状态Provider
@riverpod
Future<bool> materialFavoriteStatus(MaterialFavoriteStatusRef ref, String materialId) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).isFavorited(materialId);
  
  return result.fold(
    (failure) => false, // 出错时默认为未收藏
    (isFavorited) => isFavorited,
  );
}

/// 收藏统计Provider
@riverpod
Future<FavoriteStats> favoriteStats(FavoriteStatsRef ref) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).getFavoriteStats();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
}

/// 按分类获取收藏Provider
@riverpod
Future<List<MaterialFavorite>> favoritesByCategory(
  FavoritesByCategoryRef ref, 
  String category,
) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider)
      .getFavoritesByCategory(category);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 按标签获取收藏Provider
@riverpod
Future<List<MaterialFavorite>> favoritesByTag(
  FavoritesByTagRef ref, 
  String tag,
) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider)
      .getFavoritesByTag(tag);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 按优先级获取收藏Provider
@riverpod
Future<List<MaterialFavorite>> favoritesByPriority(
  FavoritesByPriorityRef ref, 
  int priority,
) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider)
      .getFavoritesByPriority(priority);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 置顶收藏Provider
@riverpod
Future<List<MaterialFavorite>> pinnedFavorites(PinnedFavoritesRef ref) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).getPinnedFavorites();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 最近收藏Provider
@riverpod
Future<List<MaterialFavorite>> recentFavorites(RecentFavoritesRef ref) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).getRecentFavorites();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorites) => favorites,
  );
}

/// 收藏详情Provider
@riverpod
Future<MaterialFavorite> favoriteDetail(FavoriteDetailRef ref, String favoriteId) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider).getFavoriteById(favoriteId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (favorite) => favorite,
  );
}

/// 通过材料ID获取收藏Provider
@riverpod
Future<MaterialFavorite?> favoriteByMaterialId(
  FavoriteByMaterialIdRef ref, 
  String materialId,
) async {
  final result = await ref.watch(materialFavoriteRepositoryProvider)
      .getFavoriteByMaterialId(materialId);
  
  return result.fold(
    (failure) => null, // 出错时返回null
    (favorite) => favorite,
  );
}
