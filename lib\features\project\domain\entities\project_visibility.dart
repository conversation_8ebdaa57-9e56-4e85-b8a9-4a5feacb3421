enum ProjectVisibility {
  private,
  public,
  unlisted,
}

extension ProjectVisibilityX on ProjectVisibility {
  String get displayName {
    switch (this) {
      case ProjectVisibility.private:
        return 'Private';
      case ProjectVisibility.public:
        return 'Public';
      case ProjectVisibility.unlisted:
        return 'Unlisted';
    }
  }

  String get code {
    switch (this) {
      case ProjectVisibility.private:
        return 'private';
      case ProjectVisibility.public:
        return 'public';
      case ProjectVisibility.unlisted:
        return 'unlisted';
    }
  }
}