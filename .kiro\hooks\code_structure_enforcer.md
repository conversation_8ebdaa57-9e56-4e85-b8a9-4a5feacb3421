# Code Structure Enforcer Hook

## Hook Configuration
```yaml
name: "Code Structure Enforcer"
description: "强制执行VanHub项目的代码结构规范"
trigger: "on_file_create"
file_patterns: ["lib/features/**/*.dart"]
auto_execute: true
```

## Structure Rules

### Feature Module Structure
每个功能模块必须包含：
```
lib/features/[feature_name]/
├── domain/
│   ├── entities/          # freezed实体
│   ├── repositories/      # Repository接口
│   └── usecases/         # 业务用例
├── data/
│   ├── models/           # DTO模型
│   ├── datasources/      # 数据源实现
│   └── repositories/     # Repository实现
└── presentation/
    ├── pages/            # 页面
    ├── widgets/          # 组件
    └── providers/        # Riverpod Notifiers
```

### File Naming Conventions
- 实体文件：`[entity_name].dart`
- Repository接口：`[feature_name]_repository.dart`
- Repository实现：`[feature_name]_repository_impl.dart`
- Notifier：`[feature_name]_provider.dart`
- 页面：`[page_name]_page.dart`

### Required Imports
- Domain层：只能导入dart:core和freezed
- Data层：可以导入supabase、http等
- Presentation层：可以导入flutter、riverpod

## 执行动作
1. 检查新创建的文件是否符合结构规范
2. 自动创建缺失的文件夹结构
3. 生成模板代码