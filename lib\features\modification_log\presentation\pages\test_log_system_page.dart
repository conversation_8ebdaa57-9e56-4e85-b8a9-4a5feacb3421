import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';
import '../providers/log_provider.dart';
import '../providers/timeline_provider.dart';
import 'log_editor_page.dart';
import 'log_list_page.dart';
import 'timeline_page.dart';

/// 测试改装日志系统页面
class TestLogSystemPage extends ConsumerStatefulWidget {
  const TestLogSystemPage({Key? key}) : super(key: key);

  @override
  ConsumerState<TestLogSystemPage> createState() => _TestLogSystemPageState();
}

class _TestLogSystemPageState extends ConsumerState<TestLogSystemPage> {
  // 测试项目ID
  final String _projectId = 'test-project-123';
  // 测试系统ID
  final String _systemId = 'test-system-456';
  // 测试日志ID
  String? _testLogId;
  
  @override
  void initState() {
    super.initState();
    // 初始化依赖
    _initDependencies();
    // 创建测试数据
    _createTestData();
  }
  
  /// 初始化依赖
  Future<void> _initDependencies() async {
    try {
      // TODO: 实现改装日志系统依赖初始化
      // await initModificationLogDependencies();
      debugPrint('改装日志系统依赖初始化成功');
    } catch (e) {
      debugPrint('改装日志系统依赖初始化失败: $e');
    }
  }
  
  /// 创建测试数据
  Future<void> _createTestData() async {
    // 创建测试日志
    final testLog = LogEntry(
      id: const Uuid().v4(),
      projectId: _projectId,
      systemId: _systemId,
      title: '测试日志 - 安装太阳能板',
      content: '''
<h2>安装太阳能板步骤</h2>
<p>今天完成了太阳能板的安装，过程如下：</p>
<ol>
  <li>准备工具和材料</li>
  <li>测量屋顶尺寸</li>
  <li>安装支架</li>
  <li>固定太阳能板</li>
  <li>连接电线</li>
  <li>测试系统</li>
</ol>
<p>安装过程中遇到了一些挑战，特别是在固定支架时需要确保完全水平。</p>
<h3>使用的材料</h3>
<ul>
  <li>200W太阳能板 x 2</li>
  <li>铝合金支架</li>
  <li>防水接线盒</li>
  <li>10AWG太阳能专用线缆</li>
</ul>
<p>总体来说，安装过程顺利，系统工作正常。</p>
''',
      logDate: DateTime.now().subtract(const Duration(days: 2)),
      authorId: 'test-user-789',
      authorName: '测试用户',
      createdAt: DateTime.now().subtract(const Duration(days: 2, hours: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2, hours: 1)),
      status: LogStatus.published,
      difficulty: DifficultyLevel.intermediate,
      timeSpentMinutes: 180,
      totalCost: 2800.0,
    );
    
    // 保存测试日志
    final result = await ref.read(logProvider.notifier).createLogEntry(testLog);
    
    result.fold(
      (failure) => debugPrint('创建测试日志失败: ${failure.message}'),
      (createdLog) {
        debugPrint('创建测试日志成功: ${createdLog.id}');
        setState(() {
          _testLogId = createdLog.id;
        });
      },
    );
    
    // 加载时间轴
    await ref.read(timelineProvider.notifier).getProjectTimeline(_projectId);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('改装日志系统测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '改装日志系统功能测试',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildFeatureCard(
              title: '日志列表',
              description: '查看项目的所有日志条目',
              icon: Icons.list_alt,
              onTap: () => _navigateToLogList(),
            ),
            _buildFeatureCard(
              title: '创建日志',
              description: '创建新的日志条目',
              icon: Icons.add_circle,
              onTap: () => _navigateToLogEditor(),
            ),
            if (_testLogId != null)
              _buildFeatureCard(
                title: '日志详情',
                description: '查看日志详情',
                icon: Icons.article,
                onTap: () => _navigateToLogDetail(_testLogId!),
              ),
            _buildFeatureCard(
              title: '项目时间轴',
              description: '查看项目时间轴',
              icon: Icons.timeline,
              onTap: () => _navigateToTimeline(),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建功能卡片
  Widget _buildFeatureCard({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 导航到日志列表页面
  void _navigateToLogList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LogListPage(
          projectId: _projectId,
          title: '项目日志',
        ),
      ),
    );
  }
  
  /// 导航到日志编辑页面
  void _navigateToLogEditor() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LogEditorPage(
          projectId: _projectId,
          systemId: _systemId,
        ),
      ),
    );
  }
  
  /// 导航到日志详情页面
  void _navigateToLogDetail(String logId) {
    // TODO: 实现LogDetailPage后取消注释
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => LogDetailPage(logId: logId),
    //   ),
    // );
    debugPrint('导航到日志详情页面: $logId');
  }
  
  /// 导航到时间轴页面
  void _navigateToTimeline() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimelinePage(projectId: _projectId),
      ),
    );
  }
}