import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'timeline.freezed.dart';
part 'timeline.g.dart';

/// 时间轴实体
/// 代表项目的时间轴视图
@freezed
class Timeline with _$Timeline {
  const factory Timeline({
    /// 关联的项目ID
    required String projectId,
    
    /// 时间轴项目列表
    required List<TimelineItem> items,
    
    /// 开始日期
    required DateTime startDate,
    
    /// 结束日期
    required DateTime endDate,
  }) = _Timeline;

  factory Timeline.fromJson(Map<String, dynamic> json) =>
      _$TimelineFromJson(json);
}

/// 时间轴项目
/// 可以是日志条目或里程碑
@freezed
class TimelineItem with _$TimelineItem {
  /// 日志条目类型的时间轴项目
  const factory TimelineItem.logEntry({
    required String id,
    required String title,
    required DateTime date,
    required String systemId,
    String? systemName,
    String? description,
    String? iconName,
    String? colorHex,
  }) = LogEntryItem;
  
  /// 里程碑类型的时间轴项目
  const factory TimelineItem.milestone({
    required String id,
    required String title,
    required DateTime date,
    String? systemId,
    String? systemName,
    String? description,
    required MilestoneStatus status,
    String? iconName,
    String? colorHex,
  }) = MilestoneItem;
  
  factory TimelineItem.fromJson(Map<String, dynamic> json) =>
      _$TimelineItemFromJson(json);
}

/// TimelineItem扩展方法
extension TimelineItemX on TimelineItem {
  /// 获取项目日期
  DateTime get itemDate => map(
    logEntry: (item) => item.date,
    milestone: (item) => item.date,
  );
  
  /// 获取项目标题
  String get itemTitle => map(
    logEntry: (item) => item.title,
    milestone: (item) => item.title,
  );
  
  /// 获取项目ID
  String get itemId => map(
    logEntry: (item) => item.id,
    milestone: (item) => item.id,
  );
  
  /// 获取项目描述
  String? get itemDescription => map(
    logEntry: (item) => item.description,
    milestone: (item) => item.description,
  );
  
  /// 获取系统ID
  String? get itemSystemId => map(
    logEntry: (item) => item.systemId,
    milestone: (item) => item.systemId,
  );
  
  /// 获取系统名称
  String? get itemSystemName => map(
    logEntry: (item) => item.systemName,
    milestone: (item) => item.systemName,
  );
  
  /// 是否为日志条目
  bool get isLogEntry => this is LogEntryItem;
  
  /// 是否为里程碑
  bool get isMilestone => this is MilestoneItem;
  
  /// 获取图标名称
  String? get iconName => map(
    logEntry: (item) => item.iconName,
    milestone: (item) => item.iconName,
  );
  
  /// 获取颜色十六进制值
  String? get colorHex => map(
    logEntry: (item) => item.colorHex,
    milestone: (item) => item.colorHex,
  );
  
  /// 获取里程碑状态（如果是里程碑）
  MilestoneStatus? get milestoneStatus => map(
    logEntry: (_) => null,
    milestone: (item) => item.status,
  );
  
  /// 获取日期显示文本
  String get dateDisplayText {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final itemDate = DateTime(this.itemDate.year, this.itemDate.month, this.itemDate.day);
    
    if (itemDate == today) {
      return '今天';
    } else if (itemDate == today.add(const Duration(days: 1))) {
      return '明天';
    } else if (itemDate == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else {
      return '${this.itemDate.year}-${this.itemDate.month.toString().padLeft(2, '0')}-${this.itemDate.day.toString().padLeft(2, '0')}';
    }
  }
}