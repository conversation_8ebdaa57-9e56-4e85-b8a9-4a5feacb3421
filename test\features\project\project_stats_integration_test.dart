import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mockito/mockito.dart';

import 'package:vanhub/features/project/domain/entities/project.dart';
import 'package:vanhub/features/project/domain/services/project_stats_service.dart';
import 'package:vanhub/features/project/data/services/project_stats_service_impl.dart';
import 'package:vanhub/features/bom/domain/entities/bom_item.dart';
import 'package:vanhub/core/errors/failures.dart';

import '../../mocks/project_repository_mock.dart';
import '../../mocks/bom_repository_mock.dart';
void main() {
  group('ProjectStatsService Integration Tests', () {
    late ProjectStatsService projectStatsService;
    late MockProjectRepository mockProjectRepository;
    late MockBomRepository mockBomRepository;

    setUp(() {
      mockProjectRepository = MockProjectRepository();
      mockBomRepository = MockBomRepository();
      projectStatsService = ProjectStatsServiceImpl(
        mockProjectRepository,
        mockBomRepository,
      );
    });

    group('项目统计功能完整性测试', () {
      const testProjectId = 'test-project-123';
      
      final testProject = Project(
        id: testProjectId,
        title: '测试改装项目',
        description: '这是一个测试项目',
        authorId: 'user-123',
        status: ProjectStatus.inProgress,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        budget: 50000.0,
        tags: ['电路系统', '水路系统'],
      );

      final testBomItems = [
        BomItem(
          id: 'bom-1',
          projectId: testProjectId,
          userId: 'user-123',
          materialName: '锂电池组',
          description: '12V 200Ah 磷酸铁锂电池',
          status: BomItemStatus.installed,
          quantity: 2,
          unitPrice: 3000.0,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
          category: '电路系统',
          actualPrice: 2800.0,
          plannedDate: DateTime.now().subtract(const Duration(days: 5)),
          usedDate: DateTime.now().subtract(const Duration(days: 1)),
        ),
        BomItem(
          id: 'bom-2',
          projectId: testProjectId,
          userId: 'user-123',
          materialName: '逆变器',
          description: '3000W 纯正弦波逆变器',
          status: BomItemStatus.received,
          quantity: 1,
          unitPrice: 1500.0,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
          category: '电路系统',
          plannedDate: DateTime.now().add(const Duration(days: 3)),
        ),
        BomItem(
          id: 'bom-3',
          projectId: testProjectId,
          userId: 'user-123',
          materialName: '水泵',
          description: '12V 静音水泵',
          status: BomItemStatus.pending,
          quantity: 1,
          unitPrice: 800.0,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          updatedAt: DateTime.now().subtract(const Duration(days: 3)),
          category: '水路系统',
          plannedDate: DateTime.now().add(const Duration(days: 10)),
        ),
      ];

      setUp(() {
        when(mockProjectRepository.getProjectById(testProjectId))
            .thenAnswer((_) async => Right(testProject));
        when(mockBomRepository.getProjectBomItems(testProjectId))
            .thenAnswer((_) async => Right(testBomItems));
      });

      test('应该正确计算项目统计数据', () async {
        // Act
        final result = await projectStatsService.getProjectStats(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final stats = result.getOrElse((l) => throw Exception());
        
        expect(stats.projectId, testProjectId);
        expect(stats.totalBomItems, 3);
        expect(stats.completedBomItems, 1);
        expect(stats.progressPercentage, closeTo(33.33, 0.1));
        expect(stats.actualCost, 7100.0); // (2800*2) + 1500 + 800
        expect(stats.totalBudget, 50000.0);
        expect(stats.budgetUtilization, closeTo(14.2, 0.1));
      });

      test('应该正确计算项目进度', () async {
        // Act
        final result = await projectStatsService.calculateProjectProgress(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final progress = result.getOrElse((l) => throw Exception());
        expect(progress, closeTo(33.33, 0.1));
      });

      test('应该正确计算预算分析', () async {
        // Act
        final result = await projectStatsService.calculateBudgetAnalysis(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final analysis = result.getOrElse((l) => throw Exception());
        
        expect(analysis.totalBudget, 50000.0);
        expect(analysis.actualCost, 7100.0);
        expect(analysis.remainingBudget, 42900.0);
        expect(analysis.isOverBudget, false);
        expect(analysis.categoryBreakdown['电路系统'], 7100.0);
        expect(analysis.categoryBreakdown['水路系统'], 800.0);
      });

      test('应该正确计算效率指标', () async {
        // Act
        final result = await projectStatsService.getEfficiencyMetrics(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final metrics = result.getOrElse((l) => throw Exception());
        
        expect(metrics.costPerItem, closeTo(2366.67, 0.1)); // 7100/3
        expect(metrics.daysActive, 31); // 30天 + 1
        expect(metrics.completionVelocity, closeTo(0.032, 0.01)); // 1/31
        expect(metrics.budgetEfficiency, greaterThan(0));
      });

      test('应该正确进行项目比较', () async {
        // Act
        final result = await projectStatsService.compareWithAverage(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final comparison = result.getOrElse((l) => throw Exception());
        
        expect(comparison.costVsAverage, greaterThan(0));
        expect(comparison.timeVsAverage, greaterThan(0));
        expect(comparison.efficiencyVsAverage, greaterThan(0));
        expect(comparison.performanceLevel, isA<ProjectPerformanceLevel>());
        expect(comparison.strengths, isNotEmpty);
        expect(comparison.improvements, isNotEmpty);
      });

      test('应该正确评估项目风险', () async {
        // Act
        final result = await projectStatsService.assessProjectRisk(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final assessment = result.getOrElse((l) => throw Exception());
        
        expect(assessment.overallRisk, isA<ProjectRiskLevel>());
        expect(assessment.riskScore, greaterThanOrEqualTo(0));
        expect(assessment.riskScore, lessThanOrEqualTo(100));
        expect(assessment.risks, isA<List<ProjectRisk>>());
        expect(assessment.recommendations, isNotEmpty);
      });

      test('应该正确生成项目报告', () async {
        // Act
        final result = await projectStatsService.generateProjectReport(
          testProjectId, 
          ProjectReportType.comprehensive,
        );

        // Assert
        expect(result.isRight(), true);
        final report = result.getOrElse((l) => throw Exception());
        
        expect(report.projectId, testProjectId);
        expect(report.type, ProjectReportType.comprehensive);
        expect(report.data, isNotEmpty);
        expect(report.summary, isNotEmpty);
        expect(report.keyInsights, isNotEmpty);
        expect(report.recommendations, isNotEmpty);
      });
    });

    group('错误处理测试', () {
      const testProjectId = 'invalid-project';

      test('当项目不存在时应该返回失败', () async {
        // Arrange
        when(mockProjectRepository.getProjectById(testProjectId))
            .thenAnswer((_) async => Left(NotFoundFailure(message: '项目不存在')));

        // Act
        final result = await projectStatsService.getProjectStats(testProjectId);

        // Assert
        expect(result.isLeft(), true);
      });

      test('当BOM数据获取失败时应该返回失败', () async {
        // Arrange
        final testProject = Project(
          id: testProjectId,
          title: '测试项目',
          description: '测试描述',
          authorId: 'user-123',
          status: ProjectStatus.inProgress,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockProjectRepository.getProjectById(testProjectId))
            .thenAnswer((_) async => Right(testProject));
        when(mockBomRepository.getProjectBomItems(testProjectId))
            .thenAnswer((_) async => Left(ServerFailure(message: 'BOM数据获取失败')));

        // Act
        final result = await projectStatsService.getProjectStats(testProjectId);

        // Assert
        expect(result.isLeft(), true);
      });
    });

    group('边界条件测试', () {
      const testProjectId = 'empty-project';

      test('当项目没有BOM项目时应该返回零值统计', () async {
        // Arrange
        final testProject = Project(
          id: testProjectId,
          title: '空项目',
          description: '没有BOM项目的项目',
          authorId: 'user-123',
          status: ProjectStatus.inProgress,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          budget: 10000.0,
        );

        when(mockProjectRepository.getProjectById(testProjectId))
            .thenAnswer((_) async => Right(testProject));
        when(mockBomRepository.getProjectBomItems(testProjectId))
            .thenAnswer((_) async => Right([]));

        // Act
        final result = await projectStatsService.getProjectStats(testProjectId);

        // Assert
        expect(result.isRight(), true);
        final stats = result.getOrElse((l) => throw Exception());
        
        expect(stats.totalBomItems, 0);
        expect(stats.completedBomItems, 0);
        expect(stats.progressPercentage, 0.0);
        expect(stats.actualCost, 0.0);
      });
    });
  });
}
