import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../repositories/log_repository.dart';

/// 更新日志条目用例
class UpdateLogEntryUseCase implements UseCase<LogEntry, UpdateLogEntryParams> {
  final LogRepository repository;

  UpdateLogEntryUseCase(this.repository);

  @override
  Future<Either<Failure, LogEntry>> call(UpdateLogEntryParams params) async {
    return await repository.updateLogEntry(params.logEntry);
  }
}

/// 更新日志条目参数
class UpdateLogEntryParams {
  final LogEntry logEntry;

  UpdateLogEntryParams({required this.logEntry});
}