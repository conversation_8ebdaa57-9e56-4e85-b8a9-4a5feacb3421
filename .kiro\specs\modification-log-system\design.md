# Design Document: 改装日志系统 (Modification Log System)

## Overview

改装日志系统是VanHub平台的核心功能模块，旨在为用户提供一个详细记录和展示房车改装过程的工具。该系统允许用户按时间顺序记录改装步骤、上传照片、添加技术说明，并与BOM物料清单关联，形成完整的改装知识库。

本设计文档详细描述了改装日志系统的架构设计、数据模型、核心组件、用户界面和交互流程，以及与现有系统的集成方案。

## Architecture

改装日志系统将遵循VanHub平台的Clean Architecture架构原则，分为Domain、Data和Presentation三层：

### Domain Layer

Domain层包含业务实体、仓库接口和用例，是系统的核心层，不依赖于任何外部框架。

- **Entities**: 定义核心业务实体如ModificationLog、LogEntry、LogMedia等
- **Repositories**: 定义数据访问接口如LogRepository
- **UseCases**: 实现业务逻辑如CreateLogEntryUseCase、UpdateLogEntryUseCase等
- **Services**: 提供跨实体的业务服务如LogAnalyticsService、TimelineService等

### Data Layer

Data层负责实现Domain层定义的仓库接口，处理数据持久化和外部API交互。

- **Models**: 定义数据传输对象如LogEntryModel、LogMediaModel等
- **DataSources**: 实现数据源如LogRemoteDataSource、LogLocalDataSource等
- **Repositories**: 实现Domain层定义的仓库接口如LogRepositoryImpl

### Presentation Layer

Presentation层负责用户界面和交互，使用Riverpod进行状态管理。

- **Pages**: 实现页面如LogListPage、LogDetailPage、TimelinePage等
- **Widgets**: 实现可复用组件如LogEntryCard、MediaGallery、TimelineView等
- **Providers**: 使用Riverpod管理状态如logEntryProvider、timelineProvider等

## Components and Interfaces

### Core Components

1. **LogEntryManager**
   - 负责日志条目的CRUD操作
   - 提供富文本编辑和格式化功能
   - 管理日志的版本历史和协作编辑

2. **MediaManager**
   - 处理多媒体内容的上传、存储和优化
   - 支持图片、视频、PDF和CAD文件的处理
   - 提供媒体内容的预览和交互功能

3. **BOMIntegrationService**
   - 实现日志条目与BOM物料的关联
   - 计算每个日志步骤的物料成本
   - 跟踪物料使用情况和状态变更

4. **TimelineService**
   - 生成项目时间轴视图
   - 管理项目里程碑和计划
   - 提供进度可视化和比较功能

5. **CollaborationService**
   - 处理多用户协作编辑
   - 管理评论和通知
   - 解决编辑冲突和版本合并

6. **SharingService**
   - 管理日志的可见性和访问控制
   - 生成分享链接和社交媒体集成
   - 处理社区互动如点赞、收藏等

7. **TemplateEngine**
   - 管理日志模板的创建和应用
   - 提供预设模板库和自定义模板功能
   - 处理模板的版本控制和分享

8. **ExportService**
   - 将日志导出为多种格式
   - 生成打印友好的文档
   - 处理批量导出和数据备份

9. **OfflineSyncManager**
   - 管理离线数据缓存和同步
   - 处理冲突解决和数据合并
   - 优化移动设备上的性能和体验

### Interfaces

```dart
/// 日志仓库接口
abstract class LogRepository {
  /// 获取项目的所有日志条目
  Future<Either<Failure, List<LogEntry>>> getProjectLogs(String projectId);
  
  /// 获取特定系统的日志条目
  Future<Either<Failure, List<LogEntry>>> getSystemLogs(String systemId);
  
  /// 获取单个日志条目详情
  Future<Either<Failure, LogEntry>> getLogEntry(String logId);
  
  /// 创建新的日志条目
  Future<Either<Failure, LogEntry>> createLogEntry(LogEntry log);
  
  /// 更新日志条目
  Future<Either<Failure, LogEntry>> updateLogEntry(LogEntry log);
  
  /// 删除日志条目
  Future<Either<Failure, void>> deleteLogEntry(String logId);
  
  /// 搜索日志条目
  Future<Either<Failure, List<LogEntry>>> searchLogs(LogSearchCriteria criteria);
}

/// 媒体管理接口
abstract class MediaRepository {
  /// 上传媒体文件
  Future<Either<Failure, LogMedia>> uploadMedia(File file, MediaType type);
  
  /// 获取日志关联的所有媒体
  Future<Either<Failure, List<LogMedia>>> getLogMedia(String logId);
  
  /// 删除媒体
  Future<Either<Failure, void>> deleteMedia(String mediaId);
  
  /// 更新媒体元数据
  Future<Either<Failure, LogMedia>> updateMediaMetadata(String mediaId, MediaMetadata metadata);
}

/// 时间轴服务接口
abstract class TimelineService {
  /// 获取项目时间轴
  Future<Either<Failure, Timeline>> getProjectTimeline(String projectId);
  
  /// 添加里程碑
  Future<Either<Failure, Milestone>> addMilestone(Milestone milestone);
  
  /// 更新里程碑
  Future<Either<Failure, Milestone>> updateMilestone(Milestone milestone);
  
  /// 删除里程碑
  Future<Either<Failure, void>> deleteMilestone(String milestoneId);
  
  /// 获取特定时间范围的时间轴
  Future<Either<Failure, Timeline>> getTimelineRange(String projectId, DateTime start, DateTime end);
}
```

## Data Models

### Core Entities

```dart
/// 日志条目实体
@freezed
class LogEntry with _$LogEntry {
  const factory LogEntry({
    required String id,
    required String projectId,
    required String systemId,
    required String title,
    required String content,
    required DateTime logDate,
    required String authorId,
    String? authorName,
    required DateTime createdAt,
    required DateTime updatedAt,
    required LogStatus status,
    required DifficultyLevel difficulty,
    required int timeSpentMinutes,
    List<String>? mediaIds,
    List<String>? relatedBomItemIds,
    double? totalCost,
    Map<String, dynamic>? metadata,
  }) = _LogEntry;
}

/// 日志状态枚举
enum LogStatus {
  draft,
  published,
  archived
}

/// 难度级别枚举
enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
  expert
}

/// 媒体实体
@freezed
class LogMedia with _$LogMedia {
  const factory LogMedia({
    required String id,
    required String logId,
    required MediaType type,
    required String url,
    required String filename,
    String? caption,
    required int sortOrder,
    required DateTime uploadedAt,
    required String uploadedBy,
    Map<String, dynamic>? metadata,
  }) = _LogMedia;
}

/// 媒体类型枚举
enum MediaType {
  image,
  video,
  document,
  cad,
  panorama
}

/// 里程碑实体
@freezed
class Milestone with _$Milestone {
  const factory Milestone({
    required String id,
    required String projectId,
    required String title,
    String? description,
    required DateTime date,
    required MilestoneStatus status,
    String? systemId,
    List<String>? relatedLogIds,
  }) = _Milestone;
}

/// 里程碑状态枚举
enum MilestoneStatus {
  planned,
  inProgress,
  completed,
  delayed
}

/// 时间轴实体
@freezed
class Timeline with _$Timeline {
  const factory Timeline({
    required String projectId,
    required List<TimelineItem> items,
    required DateTime startDate,
    required DateTime endDate,
  }) = _Timeline;
}

/// 时间轴项目
@freezed
class TimelineItem with _$TimelineItem {
  const factory TimelineItem.logEntry({
    required LogEntry log,
  }) = LogEntryItem;
  
  const factory TimelineItem.milestone({
    required Milestone milestone,
  }) = MilestoneItem;
}

/// 日志模板实体
@freezed
class LogTemplate with _$LogTemplate {
  const factory LogTemplate({
    required String id,
    required String name,
    required String description,
    required String content,
    required String creatorId,
    required bool isPublic,
    required TemplateType type,
    required DateTime createdAt,
    required DateTime updatedAt,
    int? usageCount,
    double? rating,
  }) = _LogTemplate;
}

/// 模板类型枚举
enum TemplateType {
  installation,
  troubleshooting,
  upgrade,
  maintenance,
  custom
}
```

### Data Transfer Objects

```dart
/// 日志条目数据模型
class LogEntryModel {
  final String id;
  final String project_id;
  final String system_id;
  final String title;
  final String content;
  final String log_date;
  final String author_id;
  final String? author_name;
  final String created_at;
  final String updated_at;
  final String status;
  final String difficulty;
  final int time_spent_minutes;
  final List<String>? media_ids;
  final List<String>? related_bom_item_ids;
  final double? total_cost;
  final Map<String, dynamic>? metadata;
  
  // 构造函数和toEntity方法
}

/// 媒体数据模型
class LogMediaModel {
  final String id;
  final String log_id;
  final String type;
  final String url;
  final String filename;
  final String? caption;
  final int sort_order;
  final String uploaded_at;
  final String uploaded_by;
  final Map<String, dynamic>? metadata;
  
  // 构造函数和toEntity方法
}
```

## Error Handling

改装日志系统将使用Either类型进行错误处理，定义特定的Failure类型：

```dart
/// 日志相关错误
class LogFailure extends Failure {
  final LogErrorType errorType;
  
  const LogFailure({
    required this.errorType,
    required String message,
  }) : super(message: message);
}

/// 日志错误类型
enum LogErrorType {
  invalidContent,
  mediaUploadFailed,
  permissionDenied,
  versionConflict,
  notFound,
  serverError,
  offlineError
}
```

## Testing Strategy

改装日志系统的测试策略包括：

1. **单元测试**
   - 测试所有UseCase的业务逻辑
   - 测试Repository实现的数据处理
   - 测试各种边缘情况和错误处理

2. **集成测试**
   - 测试日志系统与BOM系统的集成
   - 测试多媒体处理和存储
   - 测试离线同步和冲突解决

3. **UI测试**
   - 测试日志编辑器的功能
   - 测试时间轴视图的交互
   - 测试媒体预览和管理

4. **性能测试**
   - 测试大量日志和媒体的加载性能
   - 测试移动设备上的响应性
   - 测试离线模式下的性能

## User Interface Design

### Key Screens

1. **日志列表页面**
   - 显示项目或系统的所有日志条目
   - 提供筛选、排序和搜索功能
   - 支持列表视图和卡片视图切换

2. **日志详情页面**
   - 显示日志标题、内容、元数据
   - 提供媒体画廊和预览
   - 显示关联的BOM物料和成本
   - 包含评论区和协作信息

3. **日志编辑页面**
   - 提供富文本编辑器
   - 支持媒体上传和管理
   - 允许关联BOM物料
   - 提供模板选择和应用

4. **时间轴页面**
   - 显示项目的完整时间轴
   - 支持缩放和筛选
   - 允许添加和编辑里程碑
   - 提供进度可视化

5. **模板管理页面**
   - 显示预设和自定义模板
   - 支持创建和编辑模板
   - 提供模板预览和应用
   - 显示社区热门模板

### UI Components

1. **LogEntryCard**
   - 显示日志条目的摘要信息
   - 包含标题、日期、作者、难度级别
   - 显示缩略图和状态标识

2. **MediaGallery**
   - 显示日志关联的所有媒体
   - 支持缩略图和全屏预览
   - 提供媒体类型筛选和排序

3. **RichTextEditor**
   - 提供格式化文本编辑功能
   - 支持插入媒体和链接
   - 提供Markdown和HTML切换

4. **TimelineView**
   - 显示时间轴和事件点
   - 支持交互式导航和缩放
   - 使用颜色编码区分不同类型的事件

5. **BOMSelector**
   - 允许从项目BOM中选择物料
   - 显示物料详情和成本
   - 支持搜索和筛选

## Integration with Existing Systems

改装日志系统将与VanHub平台的其他系统集成：

1. **与BOM系统集成**
   - 日志条目可以关联BOM物料
   - 更新物料状态时同步更新相关日志
   - 计算每个日志步骤的物料成本

2. **与项目管理系统集成**
   - 日志条目与项目和改装系统关联
   - 基于日志更新项目进度和状态
   - 在项目详情页面显示最新日志

3. **与用户系统集成**
   - 记录日志作者和协作者信息
   - 管理日志的访问权限和可见性
   - 处理用户通知和互动

4. **与社区系统集成**
   - 支持日志的社区分享和发现
   - 处理点赞、评论和收藏功能
   - 实现模板的社区分享和评分

## Security Considerations

1. **访问控制**
   - 实现基于角色的权限系统
   - 确保用户只能访问授权的日志
   - 支持公开、私有和受限访问模式

2. **数据保护**
   - 加密敏感信息和个人数据
   - 实现安全的媒体存储和访问
   - 防止未授权的数据导出和分享

3. **版本控制**
   - 保留日志的编辑历史
   - 防止未授权的内容修改
   - 支持内容回滚和恢复

## Performance Optimization

1. **媒体优化**
   - 自动压缩和调整图片大小
   - 使用渐进式加载和缩略图
   - 实现媒体内容的CDN分发

2. **离线支持**
   - 实现智能缓存策略
   - 优先同步关键数据
   - 减少网络请求和数据传输

3. **渲染优化**
   - 使用虚拟滚动加载长列表
   - 延迟加载非关键内容
   - 优化移动设备上的渲染性能

## Deployment and Rollout Strategy

1. **阶段性发布**
   - 第一阶段：基础日志功能和BOM关联
   - 第二阶段：多媒体支持和时间轴
   - 第三阶段：协作、模板和社区功能
   - 第四阶段：离线支持和移动优化

2. **用户测试**
   - 在每个阶段进行内部测试
   - 邀请部分用户进行beta测试
   - 收集反馈并迭代改进

3. **培训和文档**
   - 提供详细的用户指南
   - 创建功能演示视频
   - 更新API文档和开发指南

## Future Enhancements

1. **AI辅助功能**
   - 自动生成日志摘要
   - 智能识别图片内容
   - 提供写作建议和模板推荐

2. **增强现实集成**
   - 支持AR标记和注释
   - 提供3D模型查看
   - 实现虚拟导览功能

3. **高级分析**
   - 提供项目对比和基准测试
   - 生成详细的成本和时间分析
   - 识别优化机会和最佳实践

## Diagrams

### System Architecture Diagram

```mermaid
graph TD
    User[User] --> UI[UI Layer]
    UI --> Providers[Riverpod Providers]
    Providers --> UseCases[Use Cases]
    UseCases --> Repositories[Repositories]
    Repositories --> DataSources[Data Sources]
    DataSources --> API[Remote API]
    DataSources --> LocalDB[Local Database]
    
    subgraph Presentation Layer
        UI
        Providers
    end
    
    subgraph Domain Layer
        UseCases
        DomainEntities[Domain Entities]
        UseCases --> DomainEntities
    end
    
    subgraph Data Layer
        Repositories
        DataSources
        DataModels[Data Models]
        Repositories --> DataModels
    end
    
    subgraph External Systems
        API
        LocalDB
        BOMSystem[BOM System]
        ProjectSystem[Project System]
    end
    
    Repositories -.-> BOMSystem
    Repositories -.-> ProjectSystem
```

### Data Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant Provider as Riverpod Provider
    participant UseCase as Use Case
    participant Repo as Repository
    participant API as Remote API
    participant DB as Local DB
    
    User->>UI: Create log entry
    UI->>Provider: Call createLogEntry
    Provider->>UseCase: Execute CreateLogEntryUseCase
    UseCase->>Repo: Save log entry
    
    alt Online Mode
        Repo->>API: Send data to server
        API-->>Repo: Return result
    else Offline Mode
        Repo->>DB: Save to local database
        DB-->>Repo: Confirm save
    end
    
    Repo-->>UseCase: Return Either<Failure, LogEntry>
    UseCase-->>Provider: Update state
    Provider-->>UI: Reflect changes
    UI-->>User: Show success/error
```

### Entity Relationship Diagram

```mermaid
erDiagram
    PROJECT ||--o{ SYSTEM : contains
    SYSTEM ||--o{ LOG_ENTRY : contains
    LOG_ENTRY ||--o{ LOG_MEDIA : has
    LOG_ENTRY ||--o{ BOM_ITEM : uses
    LOG_ENTRY }|--|| USER : created_by
    LOG_ENTRY }|--o{ COMMENT : has
    PROJECT }|--|| USER : owned_by
    TIMELINE ||--o{ TIMELINE_ITEM : contains
    TIMELINE_ITEM ||--o{ LOG_ENTRY : references
    TIMELINE_ITEM ||--o{ MILESTONE : references
    LOG_TEMPLATE }|--|| USER : created_by
    LOG_ENTRY }o--o{ LOG_TEMPLATE : uses
    
    PROJECT {
        string id PK
        string title
        string description
        string author_id FK
        boolean is_public
        datetime created_at
        datetime updated_at
    }
    
    SYSTEM {
        string id PK
        string project_id FK
        string name
        string description
        double budget
        double actual_cost
        int completion_percentage
    }
    
    LOG_ENTRY {
        string id PK
        string project_id FK
        string system_id FK
        string title
        string content
        datetime log_date
        string author_id FK
        datetime created_at
        datetime updated_at
        enum status
        enum difficulty
        int time_spent_minutes
        double total_cost
    }
    
    LOG_MEDIA {
        string id PK
        string log_id FK
        enum type
        string url
        string filename
        string caption
        int sort_order
        datetime uploaded_at
        string uploaded_by FK
    }
    
    BOM_ITEM {
        string id PK
        string project_id FK
        string name
        string specification
        double unit_price
        int quantity
        double total_price
        enum status
    }
    
    COMMENT {
        string id PK
        string log_id FK
        string content
        string author_id FK
        datetime created_at
        string parent_id FK
    }
    
    TIMELINE {
        string project_id FK
        datetime start_date
        datetime end_date
    }
    
    TIMELINE_ITEM {
        string id PK
        string timeline_id FK
        enum type
        string reference_id
        datetime date
    }
    
    MILESTONE {
        string id PK
        string project_id FK
        string title
        string description
        datetime date
        enum status
        string system_id FK
    }
    
    LOG_TEMPLATE {
        string id PK
        string name
        string description
        string content
        string creator_id FK
        boolean is_public
        enum type
        datetime created_at
        datetime updated_at
        int usage_count
        double rating
    }
    
    USER {
        string id PK
        string name
        string email
        string avatar_url
    }
```