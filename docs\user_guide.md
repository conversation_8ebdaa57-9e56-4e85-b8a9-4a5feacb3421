# VanHub改装宝 - 用户使用指南

## 🚀 快速开始

### 访问应用
1. 打开浏览器访问：`http://localhost:8080`
2. 点击"测试模式（跳过登录）"按钮进入主页
3. 开始使用各项功能

### 主页概览
主页包含以下主要区域：
- **欢迎信息**：显示平台介绍
- **快速操作**：新建项目、材料库、BOM清单
- **主要功能**：四个核心功能模块
- **连接状态**：Supabase数据库连接状态
- **项目状态**：开发进度展示

## 📋 功能使用指南

### 1. 项目管理 📁

#### 查看项目列表
- 点击主页"项目管理"按钮
- 查看所有项目的基本信息：
  - 项目名称
  - 项目状态（规划中、进行中、已完成、已取消）
  - 项目描述
  - 预算金额
  - 创建时间

#### 创建新项目
1. 在项目管理页面点击右下角"+"按钮
2. 填写项目信息：
   - 项目名称（必填）
   - 项目描述
   - 预算金额
   - 项目状态
3. 点击"保存"创建项目

#### 编辑项目
1. 在项目列表中点击"编辑"按钮
2. 修改项目信息
3. 点击"保存"更新项目

#### 删除项目
1. 在项目列表中点击"删除"按钮
2. 确认删除操作
3. 项目将被永久删除

### 2. 材料库 📦

#### 浏览材料
- 点击主页"材料库"按钮
- 查看材料分类标签（20+个分类）：
  - 电子设备、电器设备
  - 水路系统、燃气系统
  - 家具定制、厨房设备
  - 安全设备、工具配件
  - 舒适配件、储物收纳等

#### 搜索材料
1. 在搜索框中输入材料名称或描述
2. 系统将实时过滤匹配的材料
3. 支持模糊搜索和关键词匹配

#### 分类筛选
1. 点击分类标签进行筛选
2. 点击"全部"显示所有材料
3. 支持多分类组合筛选

#### 添加材料
1. 点击右下角"+"按钮
2. 填写材料信息：
   - 材料名称（必填）
   - 材料描述
   - 分类选择
   - 单价
   - 供应商
   - 型号规格
3. 点击"保存"添加材料

### 3. BOM清单 📊

#### 查看BOM清单
- 点击主页"BOM清单"按钮
- 选择要查看的项目
- 查看该项目的所有BOM项目

#### 添加BOM项目
1. 选择项目后点击"+"按钮
2. 选择材料（从材料库中选择）
3. 填写数量和单价
4. 系统自动计算小计金额
5. 点击"保存"添加到BOM

#### 管理BOM状态
BOM项目支持以下状态：
- **待采购**：刚添加的项目
- **已下单**：已向供应商下单
- **已到货**：材料已到达现场
- **已安装**：材料已安装完成

#### 成本统计
- 查看单个BOM项目成本
- 查看项目总成本
- 支持按状态统计成本

### 4. 数据分析 📈

#### 概览统计
查看关键指标：
- **项目总数**：当前系统中的项目数量
- **总预算**：所有项目的预算总和
- **材料种类**：材料库中的材料数量
- **BOM项目**：所有BOM项目的数量

#### 项目状态分布
- 以圆形图表显示项目状态分布
- 包含规划中、进行中、已完成、已取消四种状态
- 直观了解项目进展情况

#### 月度趋势分析
- 柱状图显示每月项目创建趋势
- 帮助了解项目活跃度
- 支持年度和季度视图

#### 最近活动
- 时间线显示最近的系统活动
- 包含项目创建、材料添加、BOM更新等
- 帮助跟踪系统使用情况

## 🔧 系统功能

### 连接状态监控
- 实时显示Supabase数据库连接状态
- 显示连接URL和响应时间
- 连接异常时提供重试功能

### 性能监控
- 应用启动时间监控
- 数据库操作性能监控
- 定期性能报告（5分钟间隔）
- 性能问题自动提醒

### 错误处理
- 友好的错误提示信息
- 详细的错误原因说明
- 重试机制和解决建议
- 错误日志记录

### 加载状态管理
- 统一的加载指示器
- 操作进度提示
- 成功/失败状态反馈
- 防止重复操作

## 🎨 界面特性

### 响应式设计
- 自适应不同屏幕尺寸
- 移动端友好界面
- 触摸操作优化

### 主题风格
- Material 3设计语言
- 深橙色主题色调
- 统一的视觉风格
- 现代化界面设计

### 交互体验
- 流畅的页面切换动画
- 直观的操作反馈
- 快捷键支持
- 无障碍访问支持

## 🚨 注意事项

### 数据库配置
当前版本需要在Supabase中手动创建以下表：
- `materials` 表（材料信息）
- 完善 `material_categories` 表结构
- 建立表之间的外键关系

### 测试模式
- 测试模式跳过用户认证
- 适用于功能演示和测试
- 生产环境需要配置真实认证

### 浏览器兼容性
- 推荐使用Chrome、Firefox、Safari最新版本
- 需要启用JavaScript
- 建议屏幕分辨率1280x720以上

## 📞 技术支持

### 常见问题
1. **页面加载缓慢**：检查网络连接，刷新页面重试
2. **数据加载失败**：查看错误提示，点击重试按钮
3. **功能无响应**：刷新页面或重启应用

### 联系方式
- 开发者：Augment Agent
- 项目地址：D:\AIAPP\VanHub
- 技术栈：Flutter + Supabase + Riverpod

---

**版本：** v1.0.0  
**最后更新：** 2025-07-16  
**适用平台：** Web浏览器
