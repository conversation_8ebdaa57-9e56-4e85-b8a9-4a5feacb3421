import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';
import '../providers/log_provider.dart';
import '../widgets/media_upload_widget.dart';
import '../widgets/milestone_settings_widget.dart';

/// 日志编辑页面
class LogEditorPage extends ConsumerStatefulWidget {
  final String projectId;
  final String systemId;
  final LogEntry? logEntry;

  const LogEditorPage({
    Key? key,
    required this.projectId,
    required this.systemId,
    this.logEntry,
  }) : super(key: key);

  @override
  ConsumerState<LogEditorPage> createState() => _LogEditorPageState();
}

class _LogEditorPageState extends ConsumerState<LogEditorPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _htmlEditorController = HtmlEditorController();
  final _timeSpentController = TextEditingController();
  
  DifficultyLevel _selectedDifficulty = DifficultyLevel.medium;
  LogStatus _selectedStatus = LogStatus.completed;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  // 媒体文件相关状态
  List<LogMedia> _mediaList = [];
  bool _isUploadingMedia = false;

  // 自动保存相关
  Timer? _autoSaveTimer;
  Timer? _contentChangeTimer;
  bool _isAutoSaving = false;
  DateTime? _lastAutoSaveTime;
  String _autoSaveStatus = '';

  // 里程碑相关状态
  bool _isMilestone = false;
  String? _milestoneIconName;
  String? _milestoneColorHex;
  MilestonePriority? _milestonePriority;
  bool _hasUnsavedChanges = false;

  // 草稿相关
  String get _draftKey => 'draft_log_${widget.projectId}_${widget.systemId}_${widget.logEntry?.id ?? 'new'}';

  // 表单验证
  String? _titleError;
  String? _contentError;

  // 编辑器模式
  bool _useSimpleEditor = false;
  final _simpleContentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _initializeAutoSave();
    _loadDraftIfExists();
  }

  /// 初始化表单
  void _initializeForm() {
    if (widget.logEntry != null) {
      _titleController.text = widget.logEntry!.title;
      _timeSpentController.text = widget.logEntry!.timeSpentMinutes.toString();
      _selectedDifficulty = widget.logEntry!.difficulty;
      _selectedStatus = widget.logEntry!.status;
      _selectedDate = widget.logEntry!.logDate;
      
      // 延迟设置HTML内容，确保编辑器已初始化
      Future.delayed(const Duration(milliseconds: 500), () {
        _htmlEditorController.setText(widget.logEntry!.content);
      });

      // 加载现有的媒体文件
      _loadExistingMedia();

      // 初始化里程碑状态
      _isMilestone = widget.logEntry!.isMilestone;
      _milestoneIconName = widget.logEntry!.milestoneIconName;
      _milestoneColorHex = widget.logEntry!.milestoneColorHex;
      _milestonePriority = widget.logEntry!.milestonePriority;
    }
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _contentChangeTimer?.cancel();
    _titleController.dispose();
    _timeSpentController.dispose();
    _simpleContentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: <LogicalKeySet, Intent>{
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS): const SaveIntent(),
      },
      child: Actions(
        actions: <Type, Action<Intent>>{
          SaveIntent: CallbackAction<SaveIntent>(
            onInvoke: (SaveIntent intent) {
              if (!_isLoading) {
                _saveLog();
              }
              return null;
            },
          ),
        },
        child: Focus(
          autofocus: true,
          child: WillPopScope(
            onWillPop: _onWillPop,
            child: Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.logEntry == null ? '创建日志' : '编辑日志'),
            Row(
              children: [
                Text(
                  'Ctrl+S 快速保存',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.normal,
                  ),
                ),
                if (_autoSaveStatus.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Icon(
                    _isAutoSaving ? Icons.sync : Icons.check_circle,
                    size: 12,
                    color: _isAutoSaving ? Colors.orange : Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _autoSaveStatus,
                    style: TextStyle(
                      fontSize: 10,
                      color: _isAutoSaving ? Colors.orange : Colors.green,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          // 增强的保存按钮
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _saveLog,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(
                      Icons.save,
                      size: 18,
                      color: Colors.white,
                    ),
              label: Text(
                _isLoading ? '保存中...' : '保存',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                elevation: 2,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _buildForm(),
      // 添加底部保存按钮作为备选
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.cancel_outlined),
                label: const Text('取消'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Colors.grey),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _saveLog,
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.save),
                label: Text(_isLoading ? '保存中...' : '保存日志'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建表单
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTitleField(),
                  const SizedBox(height: 16),
                  _buildDatePicker(),
                  const SizedBox(height: 16),
                  _buildDifficultySelector(),
                  const SizedBox(height: 16),
                  _buildTimeSpentField(),
                  const SizedBox(height: 16),
                  _buildStatusSelector(),
                  const SizedBox(height: 24),
                  _buildContentEditor(),
                  const SizedBox(height: 24),
                  _buildMediaUploadSection(),
                  const SizedBox(height: 24),
                  _buildMilestoneSettings(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建标题输入框
  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _titleController,
          maxLength: 100,
          decoration: InputDecoration(
            labelText: '标题',
            hintText: '输入日志标题（1-100字符）',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.title),
            errorText: _titleError,
            suffixIcon: _titleController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _titleController.clear();
                      _onContentChanged();
                    },
                  )
                : null,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入标题';
            }
            if (value.length > 100) {
              return '标题不能超过100个字符';
            }
            return null;
          },
          onChanged: (value) {
            setState(() {
              if (value.isEmpty) {
                _titleError = '请输入标题';
              } else if (value.length > 100) {
                _titleError = '标题不能超过100个字符';
              } else {
                _titleError = null;
              }
            });
            _onContentChanged();
          },
        ),
        if (_titleController.text.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 12),
            child: Text(
              '${_titleController.text.length}/100 字符',
              style: TextStyle(
                fontSize: 12,
                color: _titleController.text.length > 100
                    ? Colors.red
                    : Colors.grey,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建日期选择器
  Widget _buildDatePicker() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: '日期',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
        ),
      ),
    );
  }

  /// 构建难度选择器
  Widget _buildDifficultySelector() {
    return InputDecorator(
      decoration: const InputDecoration(
        labelText: '难度级别',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.trending_up),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<DifficultyLevel>(
          value: _selectedDifficulty,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedDifficulty = value;
              });
            }
          },
          items: [
            DifficultyLevel.easy,
            DifficultyLevel.medium,
            DifficultyLevel.hard,
            DifficultyLevel.expert,
          ].map((difficulty) {
            return DropdownMenuItem<DifficultyLevel>(
              value: difficulty,
              child: Text(_getDifficultyText(difficulty)),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建耗时输入框
  Widget _buildTimeSpentField() {
    return TextFormField(
      controller: _timeSpentController,
      decoration: const InputDecoration(
        labelText: '耗时（分钟）',
        hintText: '输入耗时',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.access_time),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入耗时';
        }
        if (int.tryParse(value) == null) {
          return '请输入有效的数字';
        }
        return null;
      },
    );
  }

  /// 构建状态选择器
  Widget _buildStatusSelector() {
    return InputDecorator(
      decoration: const InputDecoration(
        labelText: '状态',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.flag),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<LogStatus>(
          value: _selectedStatus,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedStatus = value;
              });
            }
          },
          items: [
            LogStatus.completed,
            LogStatus.inProgress,
            LogStatus.onHold,
            LogStatus.cancelled,
          ].map((status) {
            return DropdownMenuItem<LogStatus>(
              value: status,
              child: Text(_getStatusText(status)),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建内容编辑器
  Widget _buildContentEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '日志内容 *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                Text(
                  _useSimpleEditor ? '简单编辑器' : '富文本编辑器',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(width: 8),
                Switch(
                  value: _useSimpleEditor,
                  onChanged: (value) {
                    setState(() {
                      _useSimpleEditor = value;
                    });
                    _onContentChanged();
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        _useSimpleEditor ? _buildSimpleEditor() : _buildHtmlEditor(),
      ],
    );
  }

  /// 构建HTML编辑器
  Widget _buildHtmlEditor() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      height: 400,
      child: HtmlEditor(
        controller: _htmlEditorController,
        htmlEditorOptions: HtmlEditorOptions(
          hint: '输入日志内容...',
          initialText: '',
          shouldEnsureVisible: true,
          adjustHeightForKeyboard: true,
          characterLimit: 10000,
          autoAdjustHeight: false,
          spellCheck: true,
        ),
        htmlToolbarOptions: HtmlToolbarOptions(
          toolbarPosition: ToolbarPosition.aboveEditor,
          toolbarType: ToolbarType.nativeScrollable,
          defaultToolbarButtons: [
            const StyleButtons(style: false),
            const FontSettingButtons(
              fontName: false,
              fontSize: true,
            ),
            const FontButtons(
              bold: true,
              italic: true,
              underline: true,
              clearAll: false,
              strikethrough: false,
              superscript: false,
              subscript: false,
            ),
            const ColorButtons(
              foregroundColor: true,
              highlightColor: false,
            ),
            const ListButtons(
              ul: true,
              ol: true,
              listStyles: false,
            ),
            const ParagraphButtons(
              textDirection: false,
              lineHeight: false,
              caseConverter: false,
            ),
            const InsertButtons(
              link: true,
              picture: false, // 禁用图片插入，避免权限问题
              video: false,
              audio: false,
              table: true,
              hr: true,
              otherFile: false,
            ),
          ],
        ),
        otherOptions: OtherOptions(
          height: 400,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        callbacks: Callbacks(
          onInit: () {
            debugPrint('HTML编辑器初始化完成');
          },
          onChangeContent: (String? changed) {
            _onContentChanged();
          },
          onFocus: () {
            debugPrint('HTML编辑器获得焦点');
          },
          onBlur: () {
            debugPrint('HTML编辑器失去焦点');
          },
        ),
      ),
    );
  }

  /// 构建简单文本编辑器
  Widget _buildSimpleEditor() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      height: 400,
      child: TextFormField(
        controller: _simpleContentController,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
        decoration: const InputDecoration(
          hintText: '输入日志内容...\n\n支持纯文本格式，简单易用，避免复杂的富文本编辑器问题。',
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.all(12),
        ),
        style: const TextStyle(
          fontSize: 14,
          height: 1.5,
        ),
        onChanged: (value) {
          _onContentChanged();
        },
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return '请输入日志内容';
          }
          return null;
        },
      ),
    );
  }

  /// 构建里程碑设置
  Widget _buildMilestoneSettings() {
    return MilestoneSettingsWidget(
      isMilestone: _isMilestone,
      iconName: _milestoneIconName,
      colorHex: _milestoneColorHex,
      priority: _milestonePriority,
      onMilestoneChanged: (value) {
        setState(() {
          _isMilestone = value;
        });
        _onContentChanged();
      },
      onIconChanged: (value) {
        setState(() {
          _milestoneIconName = value;
        });
        _onContentChanged();
      },
      onColorChanged: (value) {
        setState(() {
          _milestoneColorHex = value;
        });
        _onContentChanged();
      },
      onPriorityChanged: (value) {
        setState(() {
          _milestonePriority = value;
        });
        _onContentChanged();
      },
    );
  }

  /// 构建媒体上传区域
  Widget _buildMediaUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.perm_media, size: 20),
            const SizedBox(width: 8),
            const Text(
              '媒体文件',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (_isUploadingMedia)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: 12),
        MediaUploadWidget(
          initialMedia: _mediaList,
          onMediaChanged: _onMediaChanged,
          isEditable: !_isLoading,
          maxMediaCount: 10,
          allowedTypes: const [MediaType.image, MediaType.video],
        ),
        if (_mediaList.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '提示：媒体文件将在保存日志时上传到服务器',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// 媒体文件变化回调
  void _onMediaChanged(List<LogMedia> mediaList) {
    setState(() {
      _mediaList = mediaList;
    });
  }

  /// 加载现有的媒体文件
  Future<void> _loadExistingMedia() async {
    if (widget.logEntry == null || widget.logEntry!.mediaIds.isEmpty) {
      return;
    }

    setState(() {
      _isUploadingMedia = true;
    });

    try {
      // TODO: 实现从mediaIds加载LogMedia对象的逻辑
      // 这里需要调用MediaRepository来获取媒体文件详情
      // 暂时创建占位符媒体对象
      final List<LogMedia> loadedMedia = [];
      for (int i = 0; i < widget.logEntry!.mediaIds.length; i++) {
        final mediaId = widget.logEntry!.mediaIds[i];
        loadedMedia.add(LogMedia(
          id: mediaId,
          logId: widget.logEntry!.id,
          type: MediaType.image, // 默认类型，实际应从数据库获取
          url: '', // 实际应从数据库获取
          filename: '媒体文件 ${i + 1}', // 实际应从数据库获取
          caption: '',
          sortOrder: i,
          uploadedAt: DateTime.now(),
          uploadedBy: widget.logEntry!.authorId,
        ));
      }

      setState(() {
        _mediaList = loadedMedia;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载媒体文件失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isUploadingMedia = false;
      });
    }
  }

  /// 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// 保存日志
  Future<void> _saveLog() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 安全获取内容（支持两种编辑器）
      String content = '';
      try {
        if (_useSimpleEditor) {
          content = _simpleContentController.text.trim();
        } else {
          content = await _htmlEditorController.getText();
          // 清理HTML内容，移除空标签
          content = content.replaceAll(RegExp(r'<p><br></p>|<p></p>|<br>'), '').trim();
        }
      } catch (e) {
        debugPrint('获取编辑器内容失败: $e');
        _showErrorSnackBar('获取编辑器内容失败，请切换到简单编辑器重试');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      if (content.isEmpty || content == '<p></p>' || content == '<br>') {
        _showErrorSnackBar('请输入日志内容');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 安全解析时间
      int timeSpent = 0;
      try {
        timeSpent = int.parse(_timeSpentController.text.isEmpty ? '0' : _timeSpentController.text);
      } catch (e) {
        _showErrorSnackBar('工时格式错误，请输入有效数字');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      if (widget.logEntry == null) {
        // 获取当前用户ID，支持游客模式
        final currentUserId = ref.read(currentUserIdProvider);
        if (currentUserId == null) {
          _showErrorSnackBar('用户未登录，请先登录或使用游客模式');
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // 处理媒体文件上传
        List<String> mediaIds = [];
        if (_mediaList.isNotEmpty) {
          setState(() {
            _isUploadingMedia = true;
          });

          try {
            mediaIds = await _uploadMediaFiles();
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('媒体文件上传失败: $e')),
            );
            setState(() {
              _isUploadingMedia = false;
              _isLoading = false;
            });
            return;
          }

          setState(() {
            _isUploadingMedia = false;
          });
        }

        // 创建新日志
        final newLog = LogEntry(
          id: const Uuid().v4(),
          projectId: widget.projectId,
          systemId: widget.systemId,
          title: _titleController.text,
          content: content,
          logDate: _selectedDate,
          authorId: currentUserId, // 使用真实的用户ID
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          status: _selectedStatus,
          difficulty: _selectedDifficulty,
          timeSpentMinutes: timeSpent,
          mediaIds: mediaIds, // 包含上传的媒体文件ID
          isMilestone: _isMilestone,
          milestoneIconName: _milestoneIconName,
          milestoneColorHex: _milestoneColorHex,
          milestonePriority: _milestonePriority,
        );

        final result = await ref.read(logProvider.notifier).createLogEntry(newLog);

        result.fold(
          (failure) {
            _showErrorSnackBar('创建失败: ${failure.message}');
          },
          (_) {
            _clearDraft(); // 清除草稿
            _showSuccessSnackBar('日志创建成功', showViewAction: true);
            Navigator.pop(context, true);
          },
        );
      } else {
        // 更新现有日志
        final updatedLog = widget.logEntry!.copyWith(
          title: _titleController.text,
          content: content,
          logDate: _selectedDate,
          updatedAt: DateTime.now(),
          status: _selectedStatus,
          difficulty: _selectedDifficulty,
          timeSpentMinutes: timeSpent,
          isMilestone: _isMilestone,
          milestoneIconName: _milestoneIconName,
          milestoneColorHex: _milestoneColorHex,
          milestonePriority: _milestonePriority,
        );

        final result = await ref.read(logProvider.notifier).updateLogEntry(updatedLog);

        result.fold(
          (failure) {
            _showErrorSnackBar('更新失败: ${failure.message}');
          },
          (_) {
            _clearDraft(); // 清除草稿
            _showSuccessSnackBar('日志更新成功', showViewAction: true);
            Navigator.pop(context, true);
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 获取难度级别文本
  String _getDifficultyText(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return '简单';
      case DifficultyLevel.medium:
        return '中等';
      case DifficultyLevel.hard:
        return '困难';
      case DifficultyLevel.expert:
        return '专家';
      case DifficultyLevel.beginner:
        return '初级';
      case DifficultyLevel.intermediate:
        return '中级';
      case DifficultyLevel.advanced:
        return '高级';
    }
  }

  /// 获取状态文本
  String _getStatusText(LogStatus status) {
    switch (status) {
      case LogStatus.draft:
        return '草稿';
      case LogStatus.inProgress:
        return '进行中';
      case LogStatus.completed:
        return '已完成';
      case LogStatus.onHold:
        return '暂停';
      case LogStatus.cancelled:
        return '已取消';
      case LogStatus.published:
        return '已发布';
      case LogStatus.archived:
        return '已归档';
    }
  }

  /// 上传媒体文件
  Future<List<String>> _uploadMediaFiles() async {
    final List<String> uploadedMediaIds = [];

    for (final media in _mediaList) {
      // 如果媒体文件已经有ID（编辑模式下的现有文件），直接使用
      if (media.id.isNotEmpty && media.url.startsWith('http')) {
        uploadedMediaIds.add(media.id);
        continue;
      }

      // 对于新的本地文件，需要上传到服务器
      if (!media.url.startsWith('http')) {
        try {
          // TODO: 实现实际的文件上传逻辑
          // 这里需要调用MediaRepository的uploadMedia方法
          // 暂时生成一个模拟的媒体ID
          final uploadedMediaId = 'media_${DateTime.now().millisecondsSinceEpoch}_${uploadedMediaIds.length}';
          uploadedMediaIds.add(uploadedMediaId);

          // 在实际实现中，这里应该：
          // 1. 将文件上传到Supabase Storage
          // 2. 在数据库中创建LogMedia记录
          // 3. 返回创建的媒体ID

        } catch (e) {
          throw Exception('上传媒体文件失败: ${media.filename} - $e');
        }
      }
    }

    return uploadedMediaIds;
  }

  /// 初始化自动保存
  void _initializeAutoSave() {
    // 设置定时自动保存（每30秒）
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_hasUnsavedChanges && !_isLoading && !_isAutoSaving) {
        _autoSave();
      }
    });

    // 监听标题变化
    _titleController.addListener(_onContentChanged);

    // 监听简单编辑器内容变化
    _simpleContentController.addListener(_onContentChanged);
  }

  /// 内容变化时的处理
  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
        _autoSaveStatus = '';
      });
    }

    // 重置内容变化定时器
    _contentChangeTimer?.cancel();
    _contentChangeTimer = Timer(const Duration(seconds: 2), () {
      if (_hasUnsavedChanges && !_isLoading && !_isAutoSaving) {
        _autoSave();
      }
    });
  }

  /// 自动保存
  Future<void> _autoSave() async {
    if (_isAutoSaving || _isLoading) return;

    try {
      setState(() {
        _isAutoSaving = true;
        _autoSaveStatus = '自动保存中...';
      });

      await _saveDraft();

      setState(() {
        _isAutoSaving = false;
        _autoSaveStatus = '已自动保存';
        _lastAutoSaveTime = DateTime.now();
        _hasUnsavedChanges = false;
      });

      // 3秒后清除状态显示
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _autoSaveStatus = '';
          });
        }
      });

    } catch (e) {
      setState(() {
        _isAutoSaving = false;
        _autoSaveStatus = '自动保存失败';
      });

      // 5秒后清除错误状态
      Timer(const Duration(seconds: 5), () {
        if (mounted) {
          setState(() {
            _autoSaveStatus = '';
          });
        }
      });
    }
  }

  /// 保存草稿到本地
  Future<void> _saveDraft() async {
    final prefs = await SharedPreferences.getInstance();

    // 获取内容（支持两种编辑器）
    String content = '';
    try {
      if (_useSimpleEditor) {
        content = _simpleContentController.text;
      } else {
        content = await _htmlEditorController.getText();
      }
    } catch (e) {
      debugPrint('保存草稿时获取内容失败: $e');
      content = _simpleContentController.text; // 降级到简单编辑器内容
    }

    final draftData = {
      'title': _titleController.text,
      'content': content,
      'timeSpent': _timeSpentController.text,
      'difficulty': _selectedDifficulty.index,
      'status': _selectedStatus.index,
      'date': _selectedDate.toIso8601String(),
      'timestamp': DateTime.now().toIso8601String(),
      'useSimpleEditor': _useSimpleEditor,
    };

    await prefs.setString(_draftKey, jsonEncode(draftData));
  }

  /// 加载草稿
  Future<void> _loadDraftIfExists() async {
    // 只有在创建新日志时才加载草稿
    if (widget.logEntry != null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final draftJson = prefs.getString(_draftKey);

      if (draftJson != null) {
        final draftData = jsonDecode(draftJson) as Map<String, dynamic>;
        final draftTime = DateTime.parse(draftData['timestamp']);

        // 只加载24小时内的草稿
        if (DateTime.now().difference(draftTime).inHours < 24) {
          _showDraftRecoveryDialog(draftData);
        } else {
          // 清除过期草稿
          await prefs.remove(_draftKey);
        }
      }
    } catch (e) {
      debugPrint('加载草稿失败: $e');
    }
  }

  /// 显示草稿恢复对话框
  void _showDraftRecoveryDialog(Map<String, dynamic> draftData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('发现未保存的草稿'),
        content: Text(
          '发现一个未保存的草稿，创建时间：${DateTime.parse(draftData['timestamp']).toString().substring(0, 16)}\n\n是否要恢复这个草稿？',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearDraft();
            },
            child: const Text('删除草稿'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _restoreDraft(draftData);
            },
            child: const Text('恢复草稿'),
          ),
        ],
      ),
    );
  }

  /// 恢复草稿
  void _restoreDraft(Map<String, dynamic> draftData) {
    setState(() {
      _titleController.text = draftData['title'] ?? '';
      _timeSpentController.text = draftData['timeSpent'] ?? '';
      _selectedDifficulty = DifficultyLevel.values[draftData['difficulty'] ?? 1];
      _selectedStatus = LogStatus.values[draftData['status'] ?? 0];
      _selectedDate = DateTime.parse(draftData['date'] ?? DateTime.now().toIso8601String());
      _useSimpleEditor = draftData['useSimpleEditor'] ?? false;
      _hasUnsavedChanges = true;
    });

    final content = draftData['content'] ?? '';

    if (_useSimpleEditor) {
      _simpleContentController.text = content;
    } else {
      // 延迟设置HTML内容
      Future.delayed(const Duration(milliseconds: 500), () {
        _htmlEditorController.setText(content);
      });
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('草稿已恢复 (${_useSimpleEditor ? '简单编辑器' : '富文本编辑器'})'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message, {bool showViewAction = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        action: showViewAction
            ? SnackBarAction(
                label: '查看',
                textColor: Colors.white,
                onPressed: () {
                  // TODO: 导航到日志详情页面
                },
              )
            : null,
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: '重试',
          textColor: Colors.white,
          onPressed: () {
            _saveLog();
          },
        ),
      ),
    );
  }

  /// 处理返回按钮
  Future<bool> _onWillPop() async {
    if (!_hasUnsavedChanges) {
      return true;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('未保存的更改'),
        content: const Text('您有未保存的更改，确定要离开吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context, true);
              _clearDraft(); // 清除草稿
            },
            child: const Text('放弃更改'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context, false);
              _saveLog(); // 保存后离开
            },
            child: const Text('保存并离开'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 清除草稿
  Future<void> _clearDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_draftKey);
      setState(() {
        _hasUnsavedChanges = false;
      });
    } catch (e) {
      debugPrint('清除草稿失败: $e');
    }
  }
}

/// 保存意图类，用于快捷键
class SaveIntent extends Intent {
  const SaveIntent();
}