import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/chart_data.dart';
import '../../domain/services/export_service.dart';

/// Export service implementation - Clean Architecture compliant
class ExportServiceImpl implements ExportService {
  const ExportServiceImpl();

  @override
  Future<Either<Failure, String>> exportToPdf({
    required VisualizationData data,
    required String fileName,
    String? customTitle,
    bool includeCharts = true,
    bool includeRawData = false,
  }) async {
    try {
      final pdf = pw.Document();
      
      // Add title page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Header(
                  level: 0,
                  child: pw.Text(
                    customTitle ?? 'VanHub Data Visualization Report',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  'Generated on: ${DateTime.now().toString()}',
                  style: const pw.TextStyle(fontSize: 12),
                ),
                pw.SizedBox(height: 40),
                
                // Summary section
                _buildPdfSummarySection(data),
                
                if (includeCharts) ...[
                  pw.SizedBox(height: 30),
                  _buildPdfChartsSection(data),
                ],
                
                if (includeRawData) ...[
                  pw.SizedBox(height: 30),
                  _buildPdfRawDataSection(data),
                ],
              ],
            );
          },
        ),
      );

      // Save PDF
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.pdf');
      await file.writeAsBytes(await pdf.save());
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to export PDF: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> exportToExcel({
    required VisualizationData data,
    required String fileName,
    bool includeCharts = true,
    bool includeRawData = true,
  }) async {
    try {
      final excel = Excel.createExcel();
      
      // Remove default sheet
      excel.delete('Sheet1');
      
      // Create summary sheet
      final summarySheet = excel['Summary'];
      _buildExcelSummarySheet(summarySheet, data);
      
      // Create data sheets
      if (includeRawData) {
        _buildExcelDataSheets(excel, data);
      }
      
      // Create charts sheet (metadata only, as Excel charts require different approach)
      if (includeCharts) {
        final chartsSheet = excel['Charts_Info'];
        _buildExcelChartsSheet(chartsSheet, data);
      }

      // Save Excel file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.xlsx');
      final bytes = excel.save();
      if (bytes != null) {
        await file.writeAsBytes(bytes);
      }
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to export Excel: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> exportChartAsImage({
    required String chartType,
    required dynamic chartData,
    required String fileName,
    String format = 'png',
    int width = 800,
    int height = 600,
  }) async {
    try {
      // This would typically use a chart rendering library
      // For now, we'll create a placeholder implementation
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.$format');
      
      // Create a simple placeholder image (in real implementation, use chart rendering)
      final imageData = _createPlaceholderImage(width, height, chartType);
      await file.writeAsBytes(imageData);
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to export chart image: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> generateCustomReport({
    required VisualizationData data,
    required List<String> selectedCharts,
    required String fileName,
    String format = 'pdf',
    Map<String, dynamic>? customSettings,
  }) async {
    try {
      switch (format.toLowerCase()) {
        case 'pdf':
          return await _generateCustomPdfReport(data, selectedCharts, fileName, customSettings);
        case 'html':
          return await _generateCustomHtmlReport(data, selectedCharts, fileName, customSettings);
        default:
          return Left(ValidationFailure(message: 'Unsupported format: $format'));
      }
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to generate custom report: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> exportForSharing({
    required VisualizationData data,
    required String fileName,
    bool includeImages = true,
    String compressionLevel = 'medium',
  }) async {
    try {
      // Create a JSON export for sharing
      final exportData = {
        'metadata': {
          'exportedAt': DateTime.now().toIso8601String(),
          'version': '1.0',
          'compressionLevel': compressionLevel,
          'includeImages': includeImages,
        },
        'data': _serializeVisualizationData(data),
      };

      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.json');
      await file.writeAsString(_compressData(exportData, compressionLevel));
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to export for sharing: $e'));
    }
  }

  @override
  List<ExportFormat> getAvailableFormats() {
    return [
      const ExportFormat(
        id: 'pdf',
        name: 'PDF Document',
        extension: 'pdf',
        mimeType: 'application/pdf',
        supportsCharts: true,
        supportsRawData: true,
        features: ['charts', 'tables', 'formatting'],
      ),
      const ExportFormat(
        id: 'excel',
        name: 'Excel Spreadsheet',
        extension: 'xlsx',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        supportsCharts: false,
        supportsRawData: true,
        features: ['tables', 'formulas', 'multiple_sheets'],
      ),
      const ExportFormat(
        id: 'png',
        name: 'PNG Image',
        extension: 'png',
        mimeType: 'image/png',
        supportsCharts: true,
        supportsRawData: false,
        features: ['high_quality', 'transparency'],
      ),
      const ExportFormat(
        id: 'html',
        name: 'HTML Report',
        extension: 'html',
        mimeType: 'text/html',
        supportsCharts: true,
        supportsRawData: true,
        features: ['interactive', 'responsive', 'web_compatible'],
      ),
    ];
  }

  @override
  Either<Failure, bool> validateExportParameters({
    required String fileName,
    required String format,
    required VisualizationData data,
  }) {
    try {
      // Validate file name
      if (fileName.isEmpty || fileName.contains(RegExp(r'[<>:"/\\|?*]'))) {
        return Left(ValidationFailure(message: 'Invalid file name'));
      }

      // Validate format
      final supportedFormats = getAvailableFormats().map((f) => f.id).toList();
      if (!supportedFormats.contains(format.toLowerCase())) {
        return Left(ValidationFailure(message: 'Unsupported format: $format'));
      }

      // Validate data - check if data is CompleteVisualizationData
      if (data is CompleteVisualizationData) {
        final completeData = data as CompleteVisualizationData;
        if (completeData.costTrend.isEmpty && completeData.materialCategories.isEmpty && 
            completeData.projectProgress.isEmpty && completeData.bomStatus.isEmpty) {
          return Left(ValidationFailure(message: 'No data available for export'));
        }
      }

      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure(message: 'Validation failed: $e'));
    }
  }

  // Private helper methods

  pw.Widget _buildPdfSummarySection(VisualizationData data) {
    if (data is CompleteVisualizationData) {
      final completeData = data as CompleteVisualizationData;
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Header(level: 1, child: pw.Text('Summary')),
          pw.SizedBox(height: 10),
          pw.Text('Cost Trend Data Points: ${completeData.costTrend.length}'),
          pw.Text('Material Categories: ${completeData.materialCategories.length}'),
          pw.Text('Projects: ${completeData.projectProgress.length}'),
          pw.Text('BOM Status Items: ${completeData.bomStatus.length}'),
        ],
      );
    }
    return pw.Text('No data available');
  }

  pw.Widget _buildPdfChartsSection(VisualizationData data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Header(level: 1, child: pw.Text('Charts')),
        pw.SizedBox(height: 10),
        pw.Text('Chart visualizations would be rendered here in a full implementation.'),
        pw.Text('This requires integration with chart rendering libraries.'),
      ],
    );
  }

  pw.Widget _buildPdfRawDataSection(VisualizationData data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Header(level: 1, child: pw.Text('Raw Data')),
        pw.SizedBox(height: 10),

        // 根据数据类型显示不同的表格
        if (data is CompleteVisualizationData) ...[
          // Cost trend table
          if (data.costTrend.isNotEmpty) ...[
            pw.Text('Cost Trend Data:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 5),
            pw.Table.fromTextArray(
              headers: ['Date', 'Value'],
              data: data.costTrend.take(10).map((item) => [
                item.timestamp.toString().split(' ')[0],
                '\$${item.value.toStringAsFixed(2)}',
              ]).toList(),
            ),
            pw.SizedBox(height: 20),
          ],

          // Material categories table
          if (data.materialCategories.isNotEmpty) ...[
            pw.Text('Material Categories:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 5),
            pw.Table.fromTextArray(
              headers: ['Category', 'Value'],
              data: data.materialCategories.take(10).map((item) => [
                item.label,
                '\$${item.value.toStringAsFixed(2)}',
              ]).toList(),
            ),
            pw.SizedBox(height: 20),
          ],
        ] else if (data is CostTrendData) ...[
          // Cost trend specific data
          if (data.dataPoints.isNotEmpty) ...[
            pw.Text('Cost Trend Data:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 5),
            pw.Table.fromTextArray(
              headers: ['Date', 'Value'],
              data: data.dataPoints.take(10).map((item) => [
                item.timestamp.toString().split(' ')[0],
                '\$${item.value.toStringAsFixed(2)}',
              ]).toList(),
            ),
            pw.SizedBox(height: 20),
          ],
        ] else if (data is MaterialCategoryData) ...[
          // Material category specific data
          if (data.categories.isNotEmpty) ...[
            pw.Text('Material Categories:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 5),
            pw.Table.fromTextArray(
              headers: ['Category', 'Value'],
              data: data.categories.take(10).map((item) => [
                item.label,
                '\$${item.value.toStringAsFixed(2)}',
              ]).toList(),
            ),
            pw.SizedBox(height: 20),
          ],
        ],

        // 基础数据信息
        pw.Text('Data Info:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 5),
        pw.Table.fromTextArray(
          headers: ['Property', 'Value'],
          data: [
            ['ID', data.id],
            ['Title', data.title],
            ['Created At', data.createdAt.toString().split(' ')[0]],
          ],
        ),
      ],
    );
  }

  void _buildExcelSummarySheet(Sheet sheet, VisualizationData data) {
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('VanHub Data Export Summary');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Generated: ${DateTime.now()}');
    
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Data Summary:');
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Cost Trend Points: ${data.costTrend.length}');
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Material Categories: ${data.materialCategories.length}');
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Projects: ${data.projectProgress.length}');
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('BOM Status Items: ${data.bomStatus.length}');
  }

  void _buildExcelDataSheets(Excel excel, VisualizationData data) {
    // Cost trend sheet
    if (data.costTrend.isNotEmpty) {
      final costSheet = excel['Cost_Trend'];
      costSheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Date');
      costSheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Cost');
      costSheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Cumulative Cost');
      
      for (int i = 0; i < data.costTrend.length; i++) {
        final item = data.costTrend[i];
        costSheet.cell(CellIndex.indexByString('A${i + 2}')).value = TextCellValue(item.date.toString());
        costSheet.cell(CellIndex.indexByString('B${i + 2}')).value = DoubleCellValue(item.cost);
        costSheet.cell(CellIndex.indexByString('C${i + 2}')).value = DoubleCellValue(item.cumulativeCost);
      }
    }

    // Material categories sheet
    if (data.materialCategories.isNotEmpty) {
      final materialSheet = excel['Material_Categories'];
      materialSheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Category');
      materialSheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Count');
      materialSheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Cost');
      
      for (int i = 0; i < data.materialCategories.length; i++) {
        final item = data.materialCategories[i];
        materialSheet.cell(CellIndex.indexByString('A${i + 2}')).value = TextCellValue(item.category);
        materialSheet.cell(CellIndex.indexByString('B${i + 2}')).value = IntCellValue(item.count);
        materialSheet.cell(CellIndex.indexByString('C${i + 2}')).value = DoubleCellValue(item.cost);
      }
    }
  }

  void _buildExcelChartsSheet(Sheet sheet, VisualizationData data) {
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Chart Information');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Note: Chart visualizations require specialized Excel chart creation');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Available Charts:');
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('- Cost Trend Chart');
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('- Material Category Chart');
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('- Project Progress Chart');
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('- BOM Status Chart');
  }

  Future<Either<Failure, String>> _generateCustomPdfReport(
    VisualizationData data,
    List<String> selectedCharts,
    String fileName,
    Map<String, dynamic>? customSettings,
  ) async {
    // Implementation for custom PDF report
    return await exportToPdf(
      data: data,
      fileName: fileName,
      customTitle: customSettings?['title'] ?? 'Custom Report',
      includeCharts: selectedCharts.isNotEmpty,
      includeRawData: customSettings?['includeRawData'] ?? false,
    );
  }

  Future<Either<Failure, String>> _generateCustomHtmlReport(
    VisualizationData data,
    List<String> selectedCharts,
    String fileName,
    Map<String, dynamic>? customSettings,
  ) async {
    try {
      final htmlContent = _generateHtmlContent(data, selectedCharts, customSettings);
      
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.html');
      await file.writeAsString(htmlContent);
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: 'Failed to generate HTML report: $e'));
    }
  }

  String _generateHtmlContent(
    VisualizationData data,
    List<String> selectedCharts,
    Map<String, dynamic>? customSettings,
  ) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>${customSettings?['title'] ?? 'VanHub Data Report'}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .chart-placeholder { 
            border: 2px dashed #ccc; 
            padding: 50px; 
            text-align: center; 
            margin: 10px 0; 
        }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${customSettings?['title'] ?? 'VanHub Data Visualization Report'}</h1>
        <p>Generated on: ${DateTime.now()}</p>
    </div>
    
    <div class="section">
        <h2>Data Summary</h2>
        <ul>
            <li>Cost Trend Data Points: ${data.costTrend.length}</li>
            <li>Material Categories: ${data.materialCategories.length}</li>
            <li>Projects: ${data.projectProgress.length}</li>
            <li>BOM Status Items: ${data.bomStatus.length}</li>
        </ul>
    </div>
    
    ${selectedCharts.contains('cost_trend') ? '<div class="section"><h2>Cost Trend Chart</h2><div class="chart-placeholder">Cost Trend Chart would be rendered here</div></div>' : ''}
    ${selectedCharts.contains('material_categories') ? '<div class="section"><h2>Material Categories Chart</h2><div class="chart-placeholder">Material Categories Chart would be rendered here</div></div>' : ''}
    
</body>
</html>
    ''';
  }

  Uint8List _createPlaceholderImage(int width, int height, String chartType) {
    // Create a simple placeholder image
    // In a real implementation, this would use a proper image generation library
    final bytes = Uint8List(width * height * 4); // RGBA
    for (int i = 0; i < bytes.length; i += 4) {
      bytes[i] = 200;     // R
      bytes[i + 1] = 200; // G
      bytes[i + 2] = 200; // B
      bytes[i + 3] = 255; // A
    }
    return bytes;
  }

  Map<String, dynamic> _serializeVisualizationData(VisualizationData data) {
    final Map<String, dynamic> result = {
      'id': data.id,
      'title': data.title,
      'createdAt': data.createdAt.toIso8601String(),
      'metadata': data.metadata,
    };

    // 根据数据类型添加特定属性
    if (data is CompleteVisualizationData) {
      result.addAll({
        'costTrend': data.costTrend.map((item) => {
          'timestamp': item.timestamp.toIso8601String(),
          'value': item.value,
          'label': item.label,
        }).toList(),
        'materialCategories': data.materialCategories.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
        'projectProgress': data.projectProgress.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
        'bomStatus': data.bomStatus.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
      });
    } else if (data is CostTrendData) {
      result.addAll({
        'dataPoints': data.dataPoints.map((item) => {
          'timestamp': item.timestamp.toIso8601String(),
          'value': item.value,
          'label': item.label,
        }).toList(),
        'totalCost': data.totalCost,
        'averageMonthlyCost': data.averageMonthlyCost,
      });
    } else if (data is MaterialCategoryData) {
      result.addAll({
        'categories': data.categories.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
        'totalCost': data.totalCost,
      });
    } else if (data is ProjectProgressData) {
      result.addAll({
        'completionPercentage': data.completionPercentage,
        'totalTasks': data.totalTasks,
        'completedTasks': data.completedTasks,
        'milestones': data.milestones.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
      });
    } else if (data is BomStatusData) {
      result.addAll({
        'statusCounts': data.statusCounts,
        'totalItems': data.totalItems,
      });
    } else if (data is BudgetAnalysisData) {
      result.addAll({
        'totalBudget': data.totalBudget,
        'actualCost': data.actualCost,
        'remainingBudget': data.remainingBudget,
        'categoryBreakdown': data.categoryBreakdown.map((item) => {
          'label': item.label,
          'value': item.value,
          'category': item.category,
        }).toList(),
      });
    }

    return result;
  }

  String _compressData(Map<String, dynamic> data, String compressionLevel) {
    // Simple JSON serialization (in real implementation, could use compression)
    return data.toString();
  }
}
