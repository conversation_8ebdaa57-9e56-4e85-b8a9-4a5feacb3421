本文档是“VanHub改装宝”项目的唯一真相来源 (Single Source of Truth)，旨在指导一个基于Flutter和Supabase的全新项目的完整开发生命周期。
第一部分：项目愿景与战略
1.1. 项目的“黄金圈” (The Golden Circle)
WHY (为什么)： 我们相信，每一次改装的经验都是一笔宝贵的财富，不应随着时间流逝或信息的碎片化而消失。我们致力于对抗改装过程中的不确定性、资源浪费和知识流失，帮助每一个DIY爱好者用更少的试错、更低的成本、更多创造的乐趣，去实现自己的“轮上之家”梦想。
HOW (怎么做)： 我们借鉴全球最成功的开源软件协作模式，将每一次“房车改装”视为一个严肃、开放、可管理的**“开源项目”**。通过结构化记录、数据驱动、知识复用和智能联动，来系统性地沉淀和传承改装知识。
WHAT (做什么)： 我们将构建一个名为“VanHub改装宝”的跨平台应用（Flutter for iOS, Android, Web），它是一个集项目管理、知识分享、社区互动于一体的垂直领域专业平台。
1.2. 核心用户画像 (Target Audience)
核心用户： DIY爱好者，他们充满热情，渴望创造，但被信息混乱和成本不可控所困扰。
潜在用户： 准备入门的新手，他们需要清晰、可信赖的指引。
游客 (Guest): 对房车改装感兴趣的浏览者，他们是社区未来的潜在贡献者。
延伸用户： 专业的改装厂、配件商，他们需要高效、透明的方式来展示其专业能力。
第二部分：技术架构与规范
2.1. 核心应用架构：三层分离架构 (Clean Architecture)
本项目严格遵循三层分离架构，确保代码的高度解耦、可测试性和长期可维护性。
目录结构规范:
Generated code
lib/
├── features/                    # 功能模块 (Feature-first)
│   ├── [module_name]/         # 例如: auth, project, bom
│   │   ├── di/                 # (可选) 模块内部的依赖注入配置
│   │   ├── domain/             # 领域层 (Pure Dart)
│   │   │   ├── entities/       # 业务实体 (使用freezed创建)
│   │   │   ├── repositories/   # Repository抽象接口
│   │   │   └── usecases/       # 业务用例
│   │   ├── data/               # 数据层
│   │   │   ├── models/         # 数据传输模型 (DTOs)
│   │   │   ├── repositories/   # Repository实现
│   │   │   └── datasources/    # 数据源 (远程API)
│   │   └── presentation/       # 表现层 (Flutter)
│   │       ├── pages/          # 完整页面/屏幕
│   │       ├── widgets/        # 可复用的小组件
│   │       └── providers/      # 状态管理 (Riverpod Notifiers)
├── core/                        # 核心层 (全局共享,与业务无关)
│   ├── api/                     # 网络客户端封装
│   ├── di/                      # 全局依赖注入配置
│   ├── errors/                  # 自定义错误和失败处理
│   ├── navigation/              # 路由配置 (GoRouter)
│   └── theme/                   # 应用主题
└── main.dart```

#### **2.2. 技术栈配置 (Final Tech Stack)**

```yaml
dependencies:
  # 核心框架
  flutter:
    sdk: flutter
  flutter_localizations: # 国际化支持
    sdk: flutter

  # 状态管理 & 依赖注入
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # 数据类与不可变性
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  equatable: ^2.0.7 # 可选,用于简单的实体

  # 后端服务
  supabase_flutter: ^2.8.0

  # 数据可视化
  fl_chart: ^0.69.0

  # 路由管理
  go_router: ^14.6.2

  # UI & UX
  material_design_icons_flutter: ^7.0.7296
  intl: ^0.20.1 # 国际化工具

  # 实用工具
  fpdart: ^1.1.0 # 用于错误处理的Either类型
  http: ^1.2.2 # 用于调用第三方API
  crypto: ^3.0.6 # 数据加密

  # 文件处理
  file_picker: ^8.1.4
  csv: ^6.0.0
  flutter_dotenv: ^5.2.1

  # UI增强
  shimmer: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成器
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.2
  freezed: ^2.5.7
  json_serializable: ^6.8.0

  # 代码规范
  flutter_lints: ^5.0.0

  # 测试辅助
  mockito: ^5.4.4
Use code with caution.
第三部分：数据库设计与数据安全
3.1. 数据库结构 (保留并沿用)
我们将继续使用您现有的Supabase项目及其PostgreSQL数据库。核心表结构如下，并为未来功能预留了扩展字段。
用户系统: users, auth.users
项目管理: projects, project_stats, project_nodes, project_versions
BOM系统: bom_items, bom_templates, bom_template_items, bom_version_history
时间轴系统: timeline_events
材料库系统: material_library, material_reviews, material_categories, material_brands
改装日志: commits, media
社交功能: project_forks, project_likes, project_follows, comments
协作系统: project_collaborators, collaboration_sessions, collaboration_operations
3.2. 数据安全与隐私增强
行级安全 (RLS) 策略:
核心原则： 所有写入/修改操作必须严格验证所有权 (auth.uid() = user_id)。
读取策略 (SELECT): 必须允许 任何用户（包括未登录的游客） 读取 is_public = true 的项目及其关联数据。这是实现游客模式的基础。
敏感数据加密:
实现： 在Data层的Model中，对price, description, notes等敏感字段，在存入数据库前使用crypto库进行对称加密，读取后解密。
理由： 防止数据库被攻破后核心财务和隐私信息直接泄露。
合规性支持 (GDPR):
实现： 在用户资料管理模块中，提供“导出我的所有数据”和“删除我的账户”功能，通过Supabase Edge Function安全地处理数据请求。
理由： 为应用的国际化运营扫清法律障碍。
第四部分：功能模块实施细则
4.1. 🔐 用户认证与游客模式 (Auth & Guest Mode Feature)
游客模式: 应用启动后，用户可直接浏览公开内容。在需要认证的操作（如点赞、评论、创建项目）处，通过优雅的弹窗或提示，引导用户登录或注册。
状态管理: 全局认证状态由Riverpod管理，能优雅处理User对象为null的“游客”状态。
导航: go_router的redirect逻辑根据认证状态决定页面访问权限。
4.2. 逻辑联动与自动化：减少手动输入 (核心增强)
核心思想： 将不同模块的数据智能地连接起来，让用户一次输入，多处复用。
【材料库 (Material Library)】与【BOM】联动:
从BOM到库: 在BOM中添加新物料时，智能提示并一键保存到个人材料库。
从库到BOM: 添加BOM物料时，优先从个人材料库选择，自动填充所有已知信息，用户只需输入数量。
【改装日志 (Commit)】与【BOM/时间轴】联动:
一体化编辑界面: 在改装日志的编辑页面，提供专门的区域来关联“本次使用的物料”和“关联的任务/里程碑”。
自动同步: 保存日志时，系统将自动：
将被关联的物料信息创建为新的bom_items条目。
将被关联的timeline_events任务状态更新为“已完成”，并累加工时。
【项目 (Project)】与【时间轴】联动:
模板化生成: 基于项目模板，自动为新项目生成一系列典型的初始时间轴任务，为新手提供清晰的路线图。
自动化进度: 项目的总体进度条，由数据库视图或函数根据已完成任务的工时占比自动计算，无需用户手动更新。
4.3. 其余功能模块简述
项目管理系统 (Project Feature): 实现项目的完整CRUD。项目详情页使用TabBarView实现四个选项卡（概览、BOM、时间轴、管理）。
BOM物料管理系统 (BOM Feature): 实现11个专业分类、物料生命周期（计划/采购/使用）、模板系统和数据导出。
时间轴功能 (Timeline Feature): 支持里程碑、任务、笔记、检查点四种事件类型。
改装日志系统 (Commit Feature): 实现类似Git Commit的记录流程，支持Markdown和媒体附件。
第五部分：开发、测试与部署
5.1. 开发优先级 (Staged Rollout Plan)
第一阶段 (核心与开放):
用户认证系统 (包含游客模式)。
项目管理基础 (CRUD)。
项目发现系统 (游客可浏览公开项目)。
实现“材料库 → BOM”的联动添加。
基础UI框架。
目标： 打造一个开放的、内容可被浏览的社区雏形，并验证核心的自动化逻辑。
第二阶段 (深度联动与效率提升):
改装日志系统。
时间轴功能。
实现“改装日志 → BOM & 时间轴”的深度联动。
实现“BOM → 材料库”的智能保存。
目标： 将平台的核心竞争力——“智能化的项目记录体验”——打磨完善。
第三阶段 (高级功能与社区):
数据可视化仪表盘 (fl_chart)。
项目复刻功能。
评价、评论、点赞、关注等社交功能。
目标： 增强数据洞察力，并构建社区互动闭环。
第四阶段 (扩展与优化):
协作系统。
AI辅助功能。
性能优化和用户体验打磨。
目标： 从工具平台向协同平台演进，并注入智能化。
5.2. 测试策略 (Testing Strategy)
单元测试 (Unit Tests): 必须覆盖所有Domain层的Use Cases和Data层的Repositories。
组件测试 (Widget Tests): 覆盖核心的、可复用的UI组件。
集成测试 (Integration Tests): 覆盖核心业务流程，如“游客浏览 -> 登录 -> 创建项目 -> 通过日志添加BOM物料 -> 查看成本统计”。
5.3. CI/CD (持续集成/部署)
工具： GitHub Actions。
工作流配置：
On Pull Request: 自动运行flutter analyze和flutter test。
On Push to develop: 自动构建测试版App并上传至TestFlight/Google Play内部测试。
On Push to main: 手动触发，构建正式版App并上传至应用商店。
5.4. 可访问性 (Accessibility - a11y)
实施： 在开发所有自定义Widget时，同步使用Semantics widget包裹并提供有意义的label，确保UI的包容性。
这份文档为您和您的团队提供了一个坚实、全面、且具备前瞻性的起点。祝您的“VanHub改装宝”项目构建顺利，打造出一款真正能帮助改装爱好者的卓越产品！