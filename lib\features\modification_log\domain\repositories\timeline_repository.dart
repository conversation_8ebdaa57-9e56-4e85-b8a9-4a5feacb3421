import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/milestone.dart';
import '../entities/timeline.dart';
import '../entities/enums.dart';

/// 时间轴仓库接口
abstract class TimelineRepository {
  /// 获取项目时间轴
  Future<Either<Failure, Timeline>> getProjectTimeline(String projectId);
  
  /// 获取特定时间范围的时间轴
  Future<Either<Failure, Timeline>> getTimelineRange(String projectId, DateTime start, DateTime end);
  
  /// 添加里程碑
  Future<Either<Failure, Milestone>> addMilestone(Milestone milestone);
  
  /// 更新里程碑
  Future<Either<Failure, Milestone>> updateMilestone(Milestone milestone);
  
  /// 删除里程碑
  Future<Either<Failure, void>> deleteMilestone(String milestoneId);
  
  /// 获取项目的所有里程碑
  Future<Either<Failure, List<Milestone>>> getProjectMilestones(String projectId);
  
  /// 获取系统的所有里程碑
  Future<Either<Failure, List<Milestone>>> getSystemMilestones(String systemId);
  
  /// 获取单个里程碑详情
  Future<Either<Failure, Milestone>> getMilestone(String milestoneId);
  
  /// 更新里程碑状态
  Future<Either<Failure, Milestone>> updateMilestoneStatus(String milestoneId, MilestoneStatus status);
  
  /// 关联日志到里程碑
  Future<Either<Failure, Milestone>> linkLogToMilestone(String milestoneId, String logId);
  
  /// 从里程碑解除日志关联
  Future<Either<Failure, Milestone>> unlinkLogFromMilestone(String milestoneId, String logId);
}