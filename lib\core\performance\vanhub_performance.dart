import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// 性能指标类型
enum PerformanceMetricType {
  frameRate,
  memoryUsage,
  networkRequest,
  renderTime,
  loadTime,
}

/// 性能指标
class PerformanceMetric {
  final PerformanceMetricType type;
  final String name;
  final dynamic value;
  final DateTime timestamp;

  PerformanceMetric({
    required this.type,
    required this.name,
    required this.value,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// 性能问题
class PerformanceIssue {
  final PerformanceMetricType type;
  final String description;
  final String recommendation;
  final int severity; // 1-5，5为最严重
  final DateTime timestamp;

  PerformanceIssue({
    required this.type,
    required this.description,
    required this.recommendation,
    required this.severity,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// VanHub性能监控系统
class VanHubPerformance {
  static final VanHubPerformance _instance = VanHubPerformance._internal();
  factory VanHubPerformance() => _instance;
  VanHubPerformance._internal();

  final List<PerformanceMetric> _metrics = [];
  final List<PerformanceIssue> _issues = [];
  bool _isMonitoring = false;
  Ticker? _ticker;
  int _frameCount = 0;
  DateTime? _lastFrameTime;
  final Queue<double> _frameRates = Queue<double>();
  final int _maxFrameRateHistory = 60;

  /// 启动性能监控
  void startMonitoring() {
    if (_isMonitoring) return;
    _isMonitoring = true;
    
    // 启动帧率监控
    _ticker = Ticker((elapsed) {
      _frameCount++;
      final now = DateTime.now();
      
      if (_lastFrameTime != null) {
        final frameTime = now.difference(_lastFrameTime!);
        final fps = 1000 / frameTime.inMilliseconds;
        
        _frameRates.add(fps);
        if (_frameRates.length > _maxFrameRateHistory) {
          _frameRates.removeFirst();
        }
        
        // 每秒记录一次帧率
        if (_frameCount % 60 == 0) {
          _metrics.add(PerformanceMetric(
            type: PerformanceMetricType.frameRate,
            name: '帧率',
            value: getAverageFrameRate(),
          ));
          
          // 检测帧率问题
          if (getAverageFrameRate() < 55) {
            _issues.add(PerformanceIssue(
              type: PerformanceMetricType.frameRate,
              description: '帧率低于55FPS (${getAverageFrameRate().toStringAsFixed(1)}FPS)',
              recommendation: '检查复杂动画、大型列表渲染或后台计算任务',
              severity: getAverageFrameRate() < 30 ? 5 : 3,
            ));
          }
        }
      }
      
      _lastFrameTime = now;
    });
    
    _ticker!.start();
  }

  /// 停止性能监控
  void stopMonitoring() {
    if (!_isMonitoring) return;
    _isMonitoring = false;
    
    _ticker?.stop();
    _ticker?.dispose();
    _ticker = null;
  }

  /// 获取平均帧率
  double getAverageFrameRate() {
    if (_frameRates.isEmpty) return 60.0;
    return _frameRates.reduce((a, b) => a + b) / _frameRates.length;
  }

  /// 记录渲染时间
  void recordRenderTime(String componentName, Duration duration) {
    _metrics.add(PerformanceMetric(
      type: PerformanceMetricType.renderTime,
      name: '渲染时间: $componentName',
      value: duration.inMilliseconds,
    ));
    
    // 检测渲染时间问题
    if (duration.inMilliseconds > 16) {
      _issues.add(PerformanceIssue(
        type: PerformanceMetricType.renderTime,
        description: '$componentName 渲染时间超过16ms (${duration.inMilliseconds}ms)',
        recommendation: '优化组件渲染逻辑，减少不必要的重建',
        severity: duration.inMilliseconds > 100 ? 5 : 
                 duration.inMilliseconds > 50 ? 4 : 3,
      ));
    }
  }

  /// 记录加载时间
  void recordLoadTime(String resourceName, Duration duration) {
    _metrics.add(PerformanceMetric(
      type: PerformanceMetricType.loadTime,
      name: '加载时间: $resourceName',
      value: duration.inMilliseconds,
    ));
    
    // 检测加载时间问题
    if (duration.inMilliseconds > 1000) {
      _issues.add(PerformanceIssue(
        type: PerformanceMetricType.loadTime,
        description: '$resourceName 加载时间超过1秒 (${duration.inMilliseconds}ms)',
        recommendation: '考虑使用缓存、压缩资源或延迟加载',
        severity: duration.inMilliseconds > 5000 ? 5 : 
                 duration.inMilliseconds > 3000 ? 4 : 3,
      ));
    }
  }

  /// 获取性能指标
  List<PerformanceMetric> getMetrics() {
    return List.unmodifiable(_metrics);
  }

  /// 获取性能问题
  List<PerformanceIssue> detectIssues() {
    return List.unmodifiable(_issues);
  }

  /// 清除性能数据
  void clearData() {
    _metrics.clear();
    _issues.clear();
    _frameRates.clear();
  }
}

/// 内存泄漏检测器
class VanHubMemoryLeakDetector {
  static final Map<String, WeakReference<Object>> _trackedObjects = {};
  static bool _isRunning = false;
  
  /// 开始检测
  static void start() {
    _isRunning = true;
  }
  
  /// 停止检测
  static void stop() {
    _isRunning = false;
    _trackedObjects.clear();
  }
  
  /// 跟踪对象
  static void track(String id, Object object) {
    if (!_isRunning) return;
    _trackedObjects[id] = WeakReference<Object>(object);
  }
  
  /// 取消跟踪对象
  static void untrack(String id) {
    if (!_isRunning) return;
    _trackedObjects.remove(id);
  }
  
  /// 获取跟踪对象数量
  static int getTrackedObjectCount() {
    return _trackedObjects.length;
  }
  
  /// 检测泄漏
  static List<String> detectLeaks() {
    if (!_isRunning) return [];
    
    final leaks = <String>[];
    _trackedObjects.forEach((id, weakRef) {
      if (weakRef.target == null) {
        leaks.add(id);
      }
    });
    
    // 清理已泄漏的对象
    for (final id in leaks) {
      _trackedObjects.remove(id);
    }
    
    return leaks;
  }
}

/// 网络请求监控
class VanHubNetworkMonitor {
  static final List<_NetworkRequest> _requests = [];
  static int _requestCounter = 0;
  
  /// 开始请求
  static String startRequest(String url, String method) {
    final requestId = 'req_${_requestCounter++}';
    _requests.add(_NetworkRequest(
      id: requestId,
      url: url,
      method: method,
      startTime: DateTime.now(),
    ));
    return requestId;
  }
  
  /// 结束请求
  static void endRequest(
    String requestId, {
    required int statusCode,
    int? responseSize,
    String? errorMessage,
  }) {
    final index = _requests.indexWhere((req) => req.id == requestId);
    if (index >= 0) {
      final request = _requests[index];
      _requests[index] = _NetworkRequest(
        id: request.id,
        url: request.url,
        method: request.method,
        startTime: request.startTime,
        endTime: DateTime.now(),
        statusCode: statusCode,
        responseSize: responseSize,
        errorMessage: errorMessage,
      );
    }
  }
  
  /// 获取请求列表
  static List<_NetworkRequest> getRequests() {
    return List.unmodifiable(_requests);
  }
  
  /// 清除请求记录
  static void clearRequests() {
    _requests.clear();
  }
}

/// 网络请求信息
class _NetworkRequest {
  final String id;
  final String url;
  final String method;
  final DateTime startTime;
  final DateTime? endTime;
  final int? statusCode;
  final int? responseSize;
  final String? errorMessage;
  
  _NetworkRequest({
    required this.id,
    required this.url,
    required this.method,
    required this.startTime,
    this.endTime,
    this.statusCode,
    this.responseSize,
    this.errorMessage,
  });
  
  Duration? get duration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }
  
  bool get isSuccess {
    return statusCode != null && statusCode! >= 200 && statusCode! < 300;
  }
  
  bool get isError {
    return statusCode == null || statusCode! >= 400 || errorMessage != null;
  }
  
  bool get isCompleted {
    return endTime != null;
  }
}

/// 图片优化工具
class VanHubImageOptimizer {
  /// 计算最佳图片尺寸
  static Size calculateOptimalSize(Size originalSize, Size targetSize) {
    final aspectRatio = originalSize.width / originalSize.height;
    
    if (targetSize.width / targetSize.height > aspectRatio) {
      // 目标区域更宽，以高度为基准
      return Size(targetSize.height * aspectRatio, targetSize.height);
    } else {
      // 目标区域更高，以宽度为基准
      return Size(targetSize.width, targetSize.width / aspectRatio);
    }
  }
  
  /// 获取最佳缓存大小
  static int calculateCacheSize() {
    // 根据设备内存情况动态计算
    // 这里简化处理，实际应用中可以根据设备信息调整
    return 100 * 1024 * 1024; // 100MB
  }
}

/// 性能监控组件
class VanHubPerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;
  final bool monitorFrameRate;
  final bool monitorMemory;
  final bool monitorNetwork;

  const VanHubPerformanceMonitor({
    super.key,
    required this.child,
    this.showOverlay = false,
    this.monitorFrameRate = true,
    this.monitorMemory = false,
    this.monitorNetwork = false,
  });

  @override
  State<VanHubPerformanceMonitor> createState() => _VanHubPerformanceMonitorState();
}

class _VanHubPerformanceMonitorState extends State<VanHubPerformanceMonitor> {
  final VanHubPerformance _performance = VanHubPerformance();
  double _fps = 60.0;
  Timer? _updateTimer;

  @override
  void initState() {
    super.initState();
    
    if (widget.monitorFrameRate) {
      _performance.startMonitoring();
      
      _updateTimer = Timer.periodic(const Duration(seconds: 1), (_) {
        if (mounted) {
          setState(() {
            _fps = _performance.getAverageFrameRate();
          });
        }
      });
    }
    
    if (widget.monitorMemory) {
      VanHubMemoryLeakDetector.start();
    }
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _performance.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showOverlay) {
      return widget.child;
    }
    
    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 0,
          right: 0,
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.all(8),
              color: Colors.black.withOpacity(0.7),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (widget.monitorFrameRate)
                    Text(
                      'FPS: ${_fps.toStringAsFixed(1)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  if (widget.monitorMemory)
                    Text(
                      '跟踪对象: ${VanHubMemoryLeakDetector.getTrackedObjectCount()}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  if (widget.monitorNetwork)
                    Text(
                      '网络请求: ${VanHubNetworkMonitor.getRequests().length}',
                      style: const TextStyle(color: Colors.white),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}