import 'package:freezed_annotation/freezed_annotation.dart';
import 'project_visibility.dart';
import 'fork_settings.dart';

part 'project.freezed.dart';
part 'project.g.dart';

enum ProjectStatus {
  planning,    // 规划中
  inProgress,  // 进行中
  completed,   // 已完成
  paused,      // 已暂停
}

extension ProjectStatusX on ProjectStatus {
  String get displayName {
    switch (this) {
      case ProjectStatus.planning:
        return '规划中';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.paused:
        return '已暂停';
    }
  }

  String get code {
    switch (this) {
      case ProjectStatus.planning:
        return 'planning';
      case ProjectStatus.inProgress:
        return 'in_progress';
      case ProjectStatus.completed:
        return 'completed';
      case ProjectStatus.paused:
        return 'paused';
    }
  }
}

@freezed
class Project with _$Project {
  const factory Project({
    required String id,
    required String authorId,
    required String title,
    required String description,
    required ProjectStatus status,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(ProjectVisibility.private) ProjectVisibility visibility,
    @Default(0.0) double budget,
    @Default(0.0) double spentAmount,
    @Default(0) int progress,
    @Default(0) int likesCount,
    @Default(0) int forksCount,
    @Default(0) int viewsCount,
    String? authorName,
    String? authorAvatarUrl,
    List<String>? tags,
    String? imageUrl,
    String? coverImageUrl,
    String? vehicleBrand,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleInfo,
    DateTime? startDate,
    DateTime? endDate,
    // 复刻相关字段
    String? sourceProjectId,
    String? sourceProjectTitle,
    String? sourceAuthorId,
    String? sourceAuthorName,
    @Default(ForkSettings()) ForkSettings forkSettings,
    // 兼容性字段
    @Deprecated('Use authorId instead') String? userId,
    @Deprecated('Use visibility instead') bool? isPublic,
    @Deprecated('Use vehicleBrand and vehicleModel instead') String? vehicleType,
  }) = _Project;

  factory Project.fromJson(Map<String, dynamic> json) => _$ProjectFromJson(json);
}

extension ProjectX on Project {
  /// 是否为复刻项目
  bool get isFork => sourceProjectId != null;
  
  /// 是否公开
  bool get isPublicProject => visibility == ProjectVisibility.public;
  
  /// 是否允许复刻
  bool get allowsFork => forkSettings.allowFork;
  
  /// 预算使用率
  double get budgetUtilization => budget > 0 ? (spentAmount / budget) * 100 : 0.0;
  
  /// 是否超预算
  bool get isOverBudget => spentAmount > budget;
  
  /// 项目完整车型信息
  String get fullVehicleInfo {
    final parts = <String>[];
    if (vehicleBrand?.isNotEmpty == true) parts.add(vehicleBrand!);
    if (vehicleModel?.isNotEmpty == true) parts.add(vehicleModel!);
    if (vehicleYear?.isNotEmpty == true) parts.add(vehicleYear!);
    return parts.join(' ');
  }
  
  /// 获取实际作者ID（兼容旧字段）
  String get actualAuthorId => authorId.isNotEmpty ? authorId : (userId ?? '');
  
  /// 获取实际可见性（兼容旧字段）
  ProjectVisibility get actualVisibility {
    if (isPublic == true) return ProjectVisibility.public;
    return visibility;
  }
}