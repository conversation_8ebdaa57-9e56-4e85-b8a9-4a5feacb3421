import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../entities/material_review.dart';
import '../repositories/material_review_repository.dart';

class CreateMaterialReviewUseCase {
  final MaterialReviewRepository repository;

  CreateMaterialReviewUseCase(this.repository);

  Future<Either<Failure, MaterialReview>> call(CreateMaterialReviewParams params) async {
    return await repository.createReview(params.review);
  }
}

class CreateMaterialReviewParams {
  final MaterialReview review;

  CreateMaterialReviewParams({required this.review});
}