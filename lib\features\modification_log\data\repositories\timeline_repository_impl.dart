import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/milestone.dart';
import '../../domain/entities/timeline.dart';
import '../../domain/entities/enums.dart';
import '../../domain/repositories/timeline_repository.dart';
import '../datasources/timeline_remote_datasource.dart';
import '../models/milestone_model.dart';
import '../models/timeline_model.dart';

/// 时间轴仓库实现
class TimelineRepositoryImpl implements TimelineRepository {
  final TimelineRemoteDataSource remoteDataSource;

  TimelineRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, Timeline>> getProjectTimeline(String projectId) async {
    try {
      final timelineModel = await remoteDataSource.getProjectTimeline(projectId);
      return Right(timelineModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '获取项目时间轴失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Timeline>> getTimelineRange(String projectId, DateTime start, DateTime end) async {
    try {
      final timelineModel = await remoteDataSource.getTimelineRange(projectId, start, end);
      return Right(timelineModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '获取时间范围内的时间轴失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> addMilestone(Milestone milestone) async {
    try {
      final milestoneModel = MilestoneModel.fromEntity(milestone);
      final createdMilestoneModel = await remoteDataSource.addMilestone(milestoneModel);
      return Right(createdMilestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '添加里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> updateMilestone(Milestone milestone) async {
    try {
      final milestoneModel = MilestoneModel.fromEntity(milestone);
      final updatedMilestoneModel = await remoteDataSource.updateMilestone(milestoneModel);
      return Right(updatedMilestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '更新里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMilestone(String milestoneId) async {
    try {
      await remoteDataSource.deleteMilestone(milestoneId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: '删除里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Milestone>>> getProjectMilestones(String projectId) async {
    try {
      final milestoneModels = await remoteDataSource.getProjectMilestones(projectId);
      final milestones = milestoneModels.map((model) => model.toEntity()).toList();
      return Right(milestones);
    } catch (e) {
      return Left(ServerFailure(message: '获取项目里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Milestone>>> getSystemMilestones(String systemId) async {
    try {
      final milestoneModels = await remoteDataSource.getSystemMilestones(systemId);
      final milestones = milestoneModels.map((model) => model.toEntity()).toList();
      return Right(milestones);
    } catch (e) {
      return Left(ServerFailure(message: '获取系统里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> getMilestone(String milestoneId) async {
    try {
      final milestoneModel = await remoteDataSource.getMilestone(milestoneId);
      return Right(milestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '获取里程碑详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> updateMilestoneStatus(String milestoneId, MilestoneStatus status) async {
    try {
      final statusString = MilestoneModel.milestoneStatusToString(status);
      final updatedMilestoneModel = await remoteDataSource.updateMilestoneStatus(milestoneId, statusString);
      return Right(updatedMilestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '更新里程碑状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> linkLogToMilestone(String milestoneId, String logId) async {
    try {
      final updatedMilestoneModel = await remoteDataSource.linkLogToMilestone(milestoneId, logId);
      return Right(updatedMilestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '关联日志到里程碑失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Milestone>> unlinkLogFromMilestone(String milestoneId, String logId) async {
    try {
      final updatedMilestoneModel = await remoteDataSource.unlinkLogFromMilestone(milestoneId, logId);
      return Right(updatedMilestoneModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '从里程碑解除日志关联失败: $e'));
    }
  }
}