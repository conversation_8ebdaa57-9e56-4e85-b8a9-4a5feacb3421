import 'package:flutter/material.dart';
import 'gesture_detector_widget.dart';
import 'responsive_layout_widget.dart';

/// 移动端优化按钮类型
enum MobileButtonType {
  primary,    // 主要按钮
  secondary,  // 次要按钮
  outline,    // 轮廓按钮
  text,       // 文本按钮
  icon,       // 图标按钮
  fab,        // 浮动操作按钮
}

/// 移动端优化按钮尺寸
enum MobileButtonSize {
  small,      // 小尺寸
  medium,     // 中等尺寸
  large,      // 大尺寸
  extraLarge, // 超大尺寸
}

/// 移动端优化按钮组件
class MobileButton extends StatefulWidget {
  final String? text;
  final IconData? icon;
  final Widget? child;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final MobileButtonType type;
  final MobileButtonSize size;
  final Color? color;
  final Color? textColor;
  final bool enabled;
  final bool loading;
  final bool fullWidth;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final double? elevation;
  final bool enableHapticFeedback;
  final HapticFeedbackType hapticFeedbackType;
  final Duration animationDuration;

  const MobileButton({
    super.key,
    this.text,
    this.icon,
    this.child,
    this.onPressed,
    this.onLongPress,
    this.type = MobileButtonType.primary,
    this.size = MobileButtonSize.medium,
    this.color,
    this.textColor,
    this.enabled = true,
    this.loading = false,
    this.fullWidth = false,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.lightImpact,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<MobileButton> createState() => _MobileButtonState();
}

class _MobileButtonState extends State<MobileButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: widget.enabled ? _opacityAnimation.value : 0.6,
                child: _buildButton(context, info),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildButton(BuildContext context, ResponsiveInfo info) {
    final buttonConfig = _getButtonConfig(info);
    
    Widget buttonChild = _buildButtonChild(context, buttonConfig);
    
    if (widget.fullWidth) {
      buttonChild = SizedBox(
        width: double.infinity,
        child: buttonChild,
      );
    }

    return EnhancedGestureDetector(
      enableHapticFeedback: widget.enableHapticFeedback,
      hapticFeedbackType: widget.hapticFeedbackType,
      enableScaleAnimation: false, // 我们自己处理动画
      onTap: widget.enabled && !widget.loading ? _handleTap : null,
      onLongPress: widget.enabled && !widget.loading ? widget.onLongPress : null,
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: Container(
          padding: buttonConfig.padding,
          decoration: _buildDecoration(context, buttonConfig),
          child: buttonChild,
        ),
      ),
    );
  }

  Widget _buildButtonChild(BuildContext context, _ButtonConfig config) {
    if (widget.loading) {
      return SizedBox(
        width: config.iconSize,
        height: config.iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.textColor ?? config.textColor,
          ),
        ),
      );
    }

    if (widget.child != null) {
      return widget.child!;
    }

    List<Widget> children = [];

    if (widget.icon != null) {
      children.add(
        Icon(
          widget.icon,
          size: config.iconSize,
          color: widget.textColor ?? config.textColor,
        ),
      );
    }

    if (widget.text != null) {
      if (children.isNotEmpty) {
        children.add(SizedBox(width: config.spacing));
      }
      
      children.add(
        Text(
          widget.text!,
          style: TextStyle(
            fontSize: config.fontSize,
            fontWeight: config.fontWeight,
            color: widget.textColor ?? config.textColor,
          ),
        ),
      );
    }

    if (children.isEmpty) {
      return const SizedBox();
    }

    if (children.length == 1) {
      return children.first;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  Decoration _buildDecoration(BuildContext context, _ButtonConfig config) {
    switch (widget.type) {
      case MobileButtonType.primary:
        return BoxDecoration(
          color: widget.enabled 
              ? (widget.color ?? config.backgroundColor)
              : (widget.color ?? config.backgroundColor).withValues(alpha: 0.6),
          borderRadius: widget.borderRadius ?? config.borderRadius,
          boxShadow: widget.elevation != null
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: widget.elevation!,
                    offset: Offset(0, widget.elevation! / 2),
                  ),
                ]
              : config.boxShadow,
        );
      
      case MobileButtonType.secondary:
        return BoxDecoration(
          color: widget.enabled 
              ? (widget.color ?? config.secondaryBackgroundColor)
              : (widget.color ?? config.secondaryBackgroundColor).withValues(alpha: 0.6),
          borderRadius: widget.borderRadius ?? config.borderRadius,
          boxShadow: config.boxShadow,
        );
      
      case MobileButtonType.outline:
        return BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: widget.enabled 
                ? (widget.color ?? config.borderColor)
                : (widget.color ?? config.borderColor).withValues(alpha: 0.6),
            width: config.borderWidth,
          ),
          borderRadius: widget.borderRadius ?? config.borderRadius,
        );
      
      case MobileButtonType.text:
      case MobileButtonType.icon:
        return const BoxDecoration(
          color: Colors.transparent,
        );
      
      case MobileButtonType.fab:
        return BoxDecoration(
          color: widget.enabled 
              ? (widget.color ?? config.backgroundColor)
              : (widget.color ?? config.backgroundColor).withValues(alpha: 0.6),
          borderRadius: BorderRadius.circular(config.fabRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );
    }
  }

  _ButtonConfig _getButtonConfig(ResponsiveInfo info) {
    final theme = Theme.of(context);
    final primaryColor = widget.color ?? Theme.of(context).primaryColor;
    
    // 根据设备类型和按钮尺寸调整配置
    double baseFontSize;
    double baseIconSize;
    EdgeInsets basePadding;
    
    switch (widget.size) {
      case MobileButtonSize.small:
        baseFontSize = info.isMobile ? 12 : 14;
        baseIconSize = info.isMobile ? 16 : 18;
        basePadding = EdgeInsets.symmetric(
          horizontal: info.isMobile ? 12 : 16,
          vertical: info.isMobile ? 8 : 10,
        );
        break;
      
      case MobileButtonSize.medium:
        baseFontSize = info.isMobile ? 14 : 16;
        baseIconSize = info.isMobile ? 20 : 22;
        basePadding = EdgeInsets.symmetric(
          horizontal: info.isMobile ? 16 : 20,
          vertical: info.isMobile ? 12 : 14,
        );
        break;
      
      case MobileButtonSize.large:
        baseFontSize = info.isMobile ? 16 : 18;
        baseIconSize = info.isMobile ? 24 : 26;
        basePadding = EdgeInsets.symmetric(
          horizontal: info.isMobile ? 20 : 24,
          vertical: info.isMobile ? 16 : 18,
        );
        break;
      
      case MobileButtonSize.extraLarge:
        baseFontSize = info.isMobile ? 18 : 20;
        baseIconSize = info.isMobile ? 28 : 30;
        basePadding = EdgeInsets.symmetric(
          horizontal: info.isMobile ? 24 : 28,
          vertical: info.isMobile ? 20 : 22,
        );
        break;
    }

    return _ButtonConfig(
      fontSize: baseFontSize,
      iconSize: baseIconSize,
      padding: widget.padding ?? basePadding,
      backgroundColor: primaryColor,
      secondaryBackgroundColor: theme.colorScheme.secondary,
      textColor: Colors.white,
      borderColor: primaryColor,
      borderWidth: 1.5,
      borderRadius: BorderRadius.circular(info.isMobile ? 12 : 8),
      fontWeight: FontWeight.w600,
      spacing: 8,
      fabRadius: info.isMobile ? 28 : 24,
      boxShadow: [
        BoxShadow(
          color: primaryColor.withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enabled && !widget.loading) {
      setState(() {
        _isPressed = true;
      });
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (_isPressed) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  void _handleTap() {
    widget.onPressed?.call();
  }
}

/// 按钮配置类
class _ButtonConfig {
  final double fontSize;
  final double iconSize;
  final EdgeInsets padding;
  final Color backgroundColor;
  final Color secondaryBackgroundColor;
  final Color textColor;
  final Color borderColor;
  final double borderWidth;
  final BorderRadius borderRadius;
  final FontWeight fontWeight;
  final double spacing;
  final double fabRadius;
  final List<BoxShadow> boxShadow;

  const _ButtonConfig({
    required this.fontSize,
    required this.iconSize,
    required this.padding,
    required this.backgroundColor,
    required this.secondaryBackgroundColor,
    required this.textColor,
    required this.borderColor,
    required this.borderWidth,
    required this.borderRadius,
    required this.fontWeight,
    required this.spacing,
    required this.fabRadius,
    required this.boxShadow,
  });
}
