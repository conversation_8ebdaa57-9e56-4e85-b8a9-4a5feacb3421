import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'log_editor_page.dart';

/// 测试改装日志编辑器页面
class TestLogEditorPage extends ConsumerWidget {
  const TestLogEditorPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试改装日志编辑器'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.edit_note,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 24),
              const Text(
                '改装日志编辑器测试',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                '点击下面的按钮测试改装日志编辑器的保存功能',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: 300,
                child: ElevatedButton.icon(
                  onPressed: () => _openLogEditor(context),
                  icon: const Icon(Icons.add_circle),
                  label: const Text('创建新日志'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: 300,
                child: OutlinedButton.icon(
                  onPressed: () => _openLogEditorWithData(context),
                  icon: const Icon(Icons.edit),
                  label: const Text('编辑示例日志'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '保存功能说明',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• 顶部应用栏有绿色的"保存"按钮\n'
                      '• 底部有"保存日志"按钮\n'
                      '• 支持 Ctrl+S 快捷键保存\n'
                      '• 保存时会显示加载状态',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openLogEditor(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LogEditorPage(
          projectId: 'test-project-123',
          systemId: 'test-system-456',
        ),
      ),
    );
  }

  void _openLogEditorWithData(BuildContext context) {
    // 这里可以传入一个示例的LogEntry来测试编辑功能
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LogEditorPage(
          projectId: 'test-project-123',
          systemId: 'test-system-456',
          // logEntry: sampleLogEntry, // 可以添加示例数据
        ),
      ),
    );
  }
}
