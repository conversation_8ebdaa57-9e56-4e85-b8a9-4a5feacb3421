import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/components/atoms/vanhub_avatar.dart';

void main() {
  group('VanHubAvatar', () {
    testWidgets('应该显示默认头像', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(),
          ),
        ),
      );

      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
    });

    testWidgets('应该显示网络图片头像', (WidgetTester tester) async {
      const imageUrl = 'https://example.com/avatar.jpg';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              imageUrl: imageUrl,
            ),
          ),
        ),
      );

      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    testWidgets('应该显示首字母头像', (WidgetTester tester) async {
      const name = '张三';
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              name: name,
            ),
          ),
        ),
      );

      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.text('张'), findsOneWidget);
    });

    testWidgets('应该显示在线状态指示器', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              showOnlineStatus: true,
              isOnline: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircleAvatar), findsNWidgets(2)); // 头像 + 状态指示器
    });

    testWidgets('应该响应点击事件', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VanHubAvatar));
      expect(tapped, isTrue);
    });

    testWidgets('应该支持不同尺寸', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                VanHubAvatar(size: VanHubAvatarSize.xs),
                VanHubAvatar(size: VanHubAvatarSize.sm),
                VanHubAvatar(size: VanHubAvatarSize.md),
                VanHubAvatar(size: VanHubAvatarSize.lg),
                VanHubAvatar(size: VanHubAvatarSize.xl),
                VanHubAvatar(size: VanHubAvatarSize.xxl),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(VanHubAvatar), findsNWidgets(6));
    });

    testWidgets('应该支持自定义边框', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              borderWidth: 2.0,
              borderColor: Colors.red,
            ),
          ),
        ),
      );

      expect(find.byType(VanHubAvatar), findsOneWidget);
    });

    testWidgets('应该支持占位符', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubAvatar(
              placeholder: Icon(Icons.camera_alt),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });
  });
}