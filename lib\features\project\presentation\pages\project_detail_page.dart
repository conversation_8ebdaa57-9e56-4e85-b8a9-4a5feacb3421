import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/project_stats.dart';
import '../providers/project_provider.dart';
import '../providers/project_stats_provider.dart';
import '../../../bom/presentation/pages/bom_management_page.dart';
import '../../../bom/presentation/widgets/create_bom_item_dialog_widget.dart';
import '../../../bom/presentation/widgets/add_material_to_bom_dialog_widget.dart';
import '../../../material/presentation/pages/material_library_page_unified.dart';
import '../widgets/project_dashboard_widget.dart';
import '../../../modification_log/presentation/pages/log_list_page.dart';
import '../../../modification_log/presentation/pages/log_editor_page.dart';

/// VanHub项目详情页面 - 完整的项目管理界面
class ProjectDetailPage extends ConsumerStatefulWidget {
  final String projectId;

  const ProjectDetailPage({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<ProjectDetailPage> createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends ConsumerState<ProjectDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final projectAsync = ref.watch(projectDetailProvider(widget.projectId));

    return projectAsync.when(
      data: (project) {
        final isOwner = currentUserId == project.userId;
        
        return Scaffold(
          body: NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                // 项目头部信息
                SliverAppBar(
                  expandedHeight: 280,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.indigo,
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      project.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.indigo,
                            Colors.indigo.shade700,
                          ],
                        ),
                      ),
                      child: Stack(
                        children: [
                          // 背景图片或图案
                          if (project.coverImageUrl != null)
                            Positioned.fill(
                              child: Image.network(
                                project.coverImageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.indigo.shade700,
                                    child: Icon(
                                      Icons.rv_hookup,
                                      size: 100,
                                      color: Colors.white.withValues(alpha: 0.3),
                                    ),
                                  );
                                },
                              ),
                            )
                          else
                            Positioned(
                              right: -30,
                              top: 20,
                              child: Icon(
                                Icons.rv_hookup,
                                size: 120,
                                color: Colors.white.withValues(alpha: 0.1),
                              ),
                            ),
                          
                          // 渐变遮罩
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          
                          // 项目信息
                          Positioned(
                            left: 20,
                            bottom: 80,
                            right: 20,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 项目状态和可见性
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(project.status),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        project.status.displayName,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: project.isPublicProject
                                            ? Colors.green.withValues(alpha: 0.8)
                                            : Colors.orange.withValues(alpha: 0.8),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            project.isPublicProject
                                                ? Icons.public
                                                : Icons.lock,
                                            size: 12,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            project.isPublicProject ? '公开' : '私有',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                
                                const SizedBox(height: 8),
                                
                                // 项目描述
                                Text(
                                  project.description,
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 14,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                
                                const SizedBox(height: 8),
                                
                                // 车辆信息
                                if (project.vehicleInfo != null) ...[
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.directions_car,
                                        color: Colors.white70,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        project.vehicleInfo!,
                                        style: const TextStyle(
                                          color: Colors.white70,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    if (isOwner) ...[
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.white),
                        onPressed: _editProject,
                      ),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'share',
                            child: Row(
                              children: [
                                Icon(Icons.share),
                                SizedBox(width: 8),
                                Text('分享项目'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'visibility',
                            child: Row(
                              children: [
                                Icon(project.isPublicProject ? Icons.lock : Icons.public),
                                const SizedBox(width: 8),
                                Text(project.isPublicProject ? '设为私有' : '设为公开'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red),
                                SizedBox(width: 8),
                                Text('删除项目', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                        onSelected: (value) => _handleMenuAction(value, project),
                      ),
                    ] else ...[
                      IconButton(
                        icon: const Icon(Icons.favorite_border, color: Colors.white),
                        onPressed: _toggleFavorite,
                      ),
                      IconButton(
                        icon: const Icon(Icons.share, color: Colors.white),
                        onPressed: () => _shareProject(project),
                      ),
                    ],
                  ],
                ),
                
                // Tab栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverTabBarDelegate(
                    TabBar(
                      controller: _tabController,
                      labelColor: Colors.indigo,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: Colors.indigo,
                      indicatorWeight: 3,
                      tabs: const [
                        Tab(
                          icon: Icon(Icons.dashboard),
                          text: '概览',
                        ),
                        Tab(
                          icon: Icon(Icons.list_alt),
                          text: 'BOM',
                        ),
                        Tab(
                          icon: Icon(Icons.inventory),
                          text: '材料库',
                        ),
                        Tab(
                          icon: Icon(Icons.timeline),
                          text: '改装日志',
                        ),
                      ],
                    ),
                  ),
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                // 概览Tab - 使用新的仪表盘组件
                ProjectDashboardWidget(projectId: widget.projectId),

                // BOM Tab
                BomManagementPage(projectId: widget.projectId),

                // 材料库Tab
                MaterialLibraryPageUnified(
                  userId: currentUserId,
                  isGuestMode: currentUserId == null,
                ),

                // 改装日志Tab
                LogListPage(
                  projectId: widget.projectId,
                  title: '改装日志',
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Scaffold(
        body: LoadingWidget(message: '加载项目详情...'),
      ),
      error: (error, stack) => Scaffold(
        body: ErrorDisplayWidget(
          message: '加载项目失败: ${error.toString()}',
          onRetry: () {
            ref.invalidate(projectDetailProvider(widget.projectId));
          },
        ),
      ),
    );
  }

  Widget _buildOverviewTab(project) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目统计卡片 - 连接真实数据
          _buildProjectStatsSection(project.id),
          
          const SizedBox(height: 24),
          
          // 项目详细信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '项目信息',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildInfoRow('项目名称', project.title),
                  _buildInfoRow('项目描述', project.description),
                  if (project.vehicleInfo != null)
                    _buildInfoRow('车辆信息', project.vehicleInfo!),
                  _buildInfoRow('创建时间', _formatDate(project.createdAt)),
                  _buildInfoRow('最后更新', _formatDate(project.updatedAt)),
                  
                  if (project.tags != null && project.tags!.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    const Text(
                      '标签',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: project.tags!.map<Widget>((tag) {
                        return Chip(
                          label: Text(
                            tag,
                            style: const TextStyle(fontSize: 12),
                          ),
                          backgroundColor: Colors.grey[200],
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 快速操作
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '快速操作',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showAddBomItemDialog(),
                          icon: const Icon(Icons.add_shopping_cart),
                          label: const Text('添加BOM项'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.indigo,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showCreateLogDialog(),
                          icon: const Icon(Icons.add_circle),
                          label: const Text('记录日志'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建项目统计数据区域
  Widget _buildProjectStatsSection(String projectId) {
    final projectStatsAsync = ref.watch(projectStatsSummaryProvider(projectId));

    return projectStatsAsync.when(
      data: (stats) => Column(
        children: [
          // 第一行：进度和预算
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '项目进度',
                  stats.formattedProgress,
                  Icons.trending_up,
                  _getProgressColor(stats.progressPercentage),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '总预算',
                  stats.formattedTotalBudget,
                  Icons.account_balance_wallet,
                  _getBudgetColor(stats.budgetHealthStatus),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 第二行：BOM项目和已完成
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'BOM项目',
                  '${stats.totalBomItems}',
                  Icons.list_alt,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '已完成',
                  '${stats.completedBomItems}',
                  Icons.check_circle,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 第三行：实际成本和预算状态
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '实际成本',
                  stats.formattedActualCost,
                  Icons.attach_money,
                  stats.isOverBudget ? Colors.red : Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '预算状态',
                  stats.budgetStatusDescription,
                  stats.isOverBudget ? Icons.warning : Icons.check,
                  stats.isOverBudget ? Colors.red : Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
      loading: () => const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('加载项目统计数据...'),
              ],
            ),
          ),
        ),
      ),
      error: (error, stack) => Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                '加载统计数据失败',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: const TextStyle(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(projectStatsSummaryProvider(projectId));
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 根据进度获取颜色
  Color _getProgressColor(double progress) {
    if (progress >= 80) return Colors.green;
    if (progress >= 50) return Colors.orange;
    if (progress >= 25) return Colors.blue;
    return Colors.grey;
  }

  /// 根据预算健康状态获取颜色
  Color _getBudgetColor(BudgetHealthStatus status) {
    switch (status) {
      case BudgetHealthStatus.healthy:
        return Colors.green;
      case BudgetHealthStatus.warning:
        return Colors.orange;
      case BudgetHealthStatus.critical:
        return Colors.red;
      case BudgetHealthStatus.overBudget:
        return Colors.purple;
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.planning:
        return Colors.blue;
      case ProjectStatus.inProgress:
        return Colors.orange;
      case ProjectStatus.completed:
        return Colors.green;
      case ProjectStatus.paused:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'planning':
        return '规划中';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'paused':
        return '暂停';
      default:
        return '未知';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _editProject() {
    // TODO: 实现编辑项目功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('编辑项目功能开发中...'),
        backgroundColor: Colors.indigo,
      ),
    );
  }

  void _handleMenuAction(String action, project) {
    switch (action) {
      case 'share':
        _shareProject(project);
        break;
      case 'visibility':
        _toggleVisibility(project);
        break;
      case 'delete':
        _deleteProject(project);
        break;
    }
  }

  void _shareProject(project) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _toggleVisibility(project) {
    // TODO: 实现可见性切换
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('可见性切换功能开发中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _deleteProject(project) {
    // TODO: 实现删除项目
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除项目'),
        content: const Text('确定要删除这个项目吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('删除功能开发中...'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite() {
    // TODO: 实现收藏功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('收藏功能开发中...'),
        backgroundColor: Colors.pink,
      ),
    );
  }

  /// 显示添加BOM项对话框 - 提供两种添加方式
  void _showAddBomItemDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '添加BOM项',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // 从材料库添加
            ListTile(
              leading: const Icon(Icons.inventory, color: Colors.blue),
              title: const Text('从材料库添加'),
              subtitle: const Text('从已有材料库中选择物料'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (context) => AddMaterialToBomDialogWidget(
                    projectId: widget.projectId,
                  ),
                );
              },
            ),

            const Divider(),

            // 手动创建
            ListTile(
              leading: const Icon(Icons.add_circle, color: Colors.green),
              title: const Text('手动创建'),
              subtitle: const Text('手动输入物料信息'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (context) => CreateBomItemDialogWidget(
                    projectId: widget.projectId,
                  ),
                );
              },
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 显示创建改装日志对话框
  void _showCreateLogDialog() async {
    // 导航到日志编辑页面
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => LogEditorPage(
          projectId: widget.projectId,
          systemId: 'default_system', // 可以后续改为用户选择
        ),
      ),
    );

    // 如果日志创建成功，刷新项目数据并切换到改装日志Tab
    if (result == true) {
      // 刷新项目统计数据
      ref.invalidate(projectStatsSummaryProvider(widget.projectId));

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('改装日志创建成功！'),
          backgroundColor: Colors.green,
        ),
      );

      // 切换到改装日志Tab查看新创建的日志
      _tabController.animateTo(3);
    }
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}