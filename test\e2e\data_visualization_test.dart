import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:vanhub/main.dart' as app;
import 'package:vanhub/features/visualization/presentation/pages/data_visualization_page.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Data Visualization E2E Tests', () {
    testWidgets('should load and display visualization data correctly', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to data visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Verify page loads
      expect(find.byType(DataVisualizationPage), findsOneWidget);
      expect(find.text('Overall Data Analysis'), findsOneWidget);

      // Wait for data to load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify tabs are present
      expect(find.text('Cost Analysis'), findsOneWidget);
      expect(find.text('Material Distribution'), findsOneWidget);
      expect(find.text('Project Progress'), findsOneWidget);
      expect(find.text('BOM Status'), findsOneWidget);

      // Verify summary cards are displayed
      expect(find.text('Total Cost'), findsOneWidget);
      expect(find.text('Material Count'), findsOneWidget);
      expect(find.text('Completion Rate'), findsOneWidget);
    });

    testWidgets('should handle time range selection correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Tap time range selector
      await tester.tap(find.byIcon(Icons.date_range));
      await tester.pumpAndSettle();

      // Select different time range
      await tester.tap(find.text('Last 7 days'));
      await tester.pumpAndSettle();

      // Wait for data refresh
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify data refreshed (loading indicator should appear and disappear)
      expect(find.byType(CircularProgressIndicator), findsNothing);
    });

    testWidgets('should handle refresh functionality correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for initial load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Tap refresh button
      await tester.tap(find.byIcon(Icons.refresh));
      await tester.pumpAndSettle();

      // Verify refresh animation
      expect(find.byIcon(Icons.refresh), findsOneWidget);

      // Wait for refresh to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify last updated time is shown
      expect(find.textContaining('Last updated:'), findsOneWidget);
    });

    testWidgets('should display export options correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Tap export button
      await tester.tap(find.byIcon(Icons.download));
      await tester.pumpAndSettle();

      // Verify export options are shown
      expect(find.text('Export Data'), findsOneWidget);
      expect(find.text('Export as PDF'), findsOneWidget);
      expect(find.text('Export as Excel'), findsOneWidget);
      expect(find.text('Export as Image'), findsOneWidget);

      // Test PDF export
      await tester.tap(find.text('Export as PDF'));
      await tester.pumpAndSettle();

      // Verify snackbar message
      expect(find.textContaining('PDF export'), findsOneWidget);
    });

    testWidgets('should navigate between tabs correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Test Material Distribution tab
      await tester.tap(find.text('Material Distribution'));
      await tester.pumpAndSettle();
      expect(find.text('Material Category Distribution'), findsOneWidget);

      // Test Project Progress tab
      await tester.tap(find.text('Project Progress'));
      await tester.pumpAndSettle();
      expect(find.text('Project Progress Overview'), findsOneWidget);

      // Test BOM Status tab
      await tester.tap(find.text('BOM Status'));
      await tester.pumpAndSettle();
      expect(find.text('BOM Status Distribution'), findsOneWidget);

      // Return to Cost Analysis tab
      await tester.tap(find.text('Cost Analysis'));
      await tester.pumpAndSettle();
      expect(find.text('Cost Trend Analysis'), findsOneWidget);
    });

    testWidgets('should handle chart interactions correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Test chart info button
      await tester.tap(find.byIcon(Icons.info_outline).first);
      await tester.pumpAndSettle();

      // Verify info dialog
      expect(find.textContaining('Cost Trend Analysis Info'), findsOneWidget);
      expect(find.textContaining('This chart shows'), findsOneWidget);

      // Close dialog
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();

      // Test full screen chart
      await tester.tap(find.byIcon(Icons.zoom_in).first);
      await tester.pumpAndSettle();

      // Verify full screen view
      expect(find.text('Cost Trend'), findsOneWidget);
      expect(find.byIcon(Icons.download), findsOneWidget);

      // Go back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
    });

    testWidgets('should handle pull-to-refresh correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for initial load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Perform pull-to-refresh gesture
      await tester.fling(
        find.byType(TabBarView),
        const Offset(0, 300),
        1000,
      );
      await tester.pumpAndSettle();

      // Wait for refresh to complete
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify data is refreshed
      expect(find.textContaining('Last updated:'), findsOneWidget);
    });

    testWidgets('should handle error states correctly', (tester) async {
      // This test would require mocking network failures
      // For now, we'll test the error UI structure
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // In case of error, verify error UI elements exist
      // (This would be triggered by network issues in real scenarios)
      if (find.byIcon(Icons.error_outline).evaluate().isNotEmpty) {
        expect(find.text('Data Loading Failed'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.text('Go Back'), findsOneWidget);
      }
    });

    testWidgets('should display material statistics correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Switch to Material Distribution tab
      await tester.tap(find.text('Material Distribution'));
      await tester.pumpAndSettle();

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify material statistics section
      expect(find.text('Material Statistics'), findsOneWidget);

      // Verify list items are displayed
      expect(find.byType(ListTile), findsWidgets);
      expect(find.byType(CircleAvatar), findsWidgets);
    });

    testWidgets('should handle project-specific visualization correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // First, create or select a project
      // (This would depend on your app's navigation structure)
      
      // Navigate to project-specific visualization
      // This test assumes there's a way to access project-specific data
      
      // For now, we'll test the general structure
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Verify the page can handle project-specific data
      expect(find.byType(DataVisualizationPage), findsOneWidget);
    });

    testWidgets('should maintain state during tab switches', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Change time range
      await tester.tap(find.byIcon(Icons.date_range));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Last 7 days'));
      await tester.pumpAndSettle();

      // Switch tabs
      await tester.tap(find.text('Material Distribution'));
      await tester.pumpAndSettle();

      // Switch back to Cost Analysis
      await tester.tap(find.text('Cost Analysis'));
      await tester.pumpAndSettle();

      // Verify time range selection is maintained
      // (This would be verified by checking if the data reflects the 7-day range)
    });
  });

  group('Data Visualization Performance Tests', () {
    testWidgets('should load large datasets efficiently', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      
      // Measure load time
      final stopwatch = Stopwatch()..start();
      await tester.pumpAndSettle();
      stopwatch.stop();

      // Verify reasonable load time (less than 5 seconds)
      expect(stopwatch.elapsedMilliseconds, lessThan(5000));

      // Verify all charts are rendered
      expect(find.byType(DataVisualizationPage), findsOneWidget);
    });

    testWidgets('should handle rapid tab switching smoothly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Wait for initial load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Rapidly switch between tabs
      for (int i = 0; i < 5; i++) {
        await tester.tap(find.text('Material Distribution'));
        await tester.pump();
        await tester.tap(find.text('Project Progress'));
        await tester.pump();
        await tester.tap(find.text('BOM Status'));
        await tester.pump();
        await tester.tap(find.text('Cost Analysis'));
        await tester.pump();
      }

      await tester.pumpAndSettle();

      // Verify app remains stable
      expect(find.byType(DataVisualizationPage), findsOneWidget);
    });
  });

  group('Data Visualization Accessibility Tests', () {
    testWidgets('should have proper accessibility labels', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Verify semantic labels exist
      expect(find.bySemanticsLabel('Refresh Data'), findsOneWidget);
      expect(find.bySemanticsLabel('Export Data'), findsOneWidget);
      expect(find.bySemanticsLabel('Select Time Range'), findsOneWidget);
    });

    testWidgets('should support screen reader navigation', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to visualization page
      await tester.tap(find.byIcon(Icons.analytics));
      await tester.pumpAndSettle();

      // Verify semantic structure
      expect(find.byType(Semantics), findsWidgets);
    });
  });
}