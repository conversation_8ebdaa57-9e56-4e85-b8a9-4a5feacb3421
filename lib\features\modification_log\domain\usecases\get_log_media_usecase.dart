import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_media.dart';
import '../repositories/media_repository.dart';

/// 获取日志媒体用例
class GetLogMediaUseCase implements UseCase<List<LogMedia>, GetLogMediaParams> {
  final MediaRepository repository;

  GetLogMediaUseCase(this.repository);

  @override
  Future<Either<Failure, List<LogMedia>>> call(GetLogMediaParams params) async {
    return await repository.getLogMedia(params.logId);
  }
}

/// 获取日志媒体参数
class GetLogMediaParams {
  final String logId;

  GetLogMediaParams({required this.logId});
}