/// VanHub Dashboard Component 2.0
/// 
/// 个性化智能仪表盘组件
/// 
/// 特性：
/// - 智能卡片布局算法
/// - 拖拽重排功能
/// - 个性化推荐引擎
/// - 实时数据动画
/// - 响应式瀑布流布局

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import '../utils/responsive_utils.dart';
import 'vanhub_card_v2.dart';

/// 仪表盘小部件类型
enum DashboardWidgetType {
  projectOverview,    // 项目概览
  recentActivity,     // 最近活动
  costAnalysis,       // 成本分析
  progressChart,      // 进度图表
  quickActions,       // 快速操作
  recommendations,    // 智能推荐
  statistics,         // 统计数据
  timeline,           // 时间轴
}

/// 仪表盘小部件尺寸
enum DashboardWidgetSize {
  small,    // 1x1
  medium,   // 2x1
  large,    // 2x2
  wide,     // 3x1
  tall,     // 1x2
}

/// 仪表盘小部件数据模型
class DashboardWidget {
  final String id;
  final String title;
  final DashboardWidgetType type;
  final DashboardWidgetSize size;
  final Widget content;
  final IconData? icon;
  final Color? color;
  final bool isDraggable;
  final bool isResizable;
  final VoidCallback? onTap;
  final VoidCallback? onRefresh;

  const DashboardWidget({
    required this.id,
    required this.title,
    required this.type,
    required this.size,
    required this.content,
    this.icon,
    this.color,
    this.isDraggable = true,
    this.isResizable = false,
    this.onTap,
    this.onRefresh,
  });

  /// 获取网格尺寸
  StaggeredGridTile getGridTile(Widget child) {
    switch (size) {
      case DashboardWidgetSize.small:
        return StaggeredGridTile.count(
          crossAxisCellCount: 1,
          mainAxisCellCount: 1,
          child: child,
        );
      case DashboardWidgetSize.medium:
        return StaggeredGridTile.count(
          crossAxisCellCount: 2,
          mainAxisCellCount: 1,
          child: child,
        );
      case DashboardWidgetSize.large:
        return StaggeredGridTile.count(
          crossAxisCellCount: 2,
          mainAxisCellCount: 2,
          child: child,
        );
      case DashboardWidgetSize.wide:
        return StaggeredGridTile.count(
          crossAxisCellCount: 3,
          mainAxisCellCount: 1,
          child: child,
        );
      case DashboardWidgetSize.tall:
        return StaggeredGridTile.count(
          crossAxisCellCount: 1,
          mainAxisCellCount: 2,
          child: child,
        );
    }
  }
}

/// VanHub仪表盘组件
class VanHubDashboard extends StatefulWidget {
  final List<DashboardWidget> widgets;
  final bool enableDragAndDrop;
  final bool enablePersonalization;
  final Function(List<DashboardWidget>)? onLayoutChanged;
  final VoidCallback? onAddWidget;
  final Function(String)? onRemoveWidget;
  
  // 响应式配置
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  
  // 动画配置
  final Duration reorderAnimationDuration;
  final Duration dataUpdateAnimationDuration;

  const VanHubDashboard({
    Key? key,
    required this.widgets,
    this.enableDragAndDrop = true,
    this.enablePersonalization = true,
    this.onLayoutChanged,
    this.onAddWidget,
    this.onRemoveWidget,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.reorderAnimationDuration = VanHubAnimationDurations.normal,
    this.dataUpdateAnimationDuration = VanHubAnimationDurations.fast,
  }) : super(key: key);

  @override
  State<VanHubDashboard> createState() => _VanHubDashboardState();
}

class _VanHubDashboardState extends State<VanHubDashboard>
    with TickerProviderStateMixin {
  late List<DashboardWidget> _widgets;
  late AnimationController _layoutController;
  late AnimationController _dataController;

  @override
  void initState() {
    super.initState();
    _widgets = List.from(widget.widgets);
    
    _layoutController = AnimationController(
      duration: widget.reorderAnimationDuration,
      vsync: this,
    );
    
    _dataController = AnimationController(
      duration: widget.dataUpdateAnimationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _layoutController.dispose();
    _dataController.dispose();
    super.dispose();
  }

  /// 获取响应式列数
  int _getColumnCount() {
    return VanHubResponsiveUtils.getSimpleValue<int>(
      context,
      mobile: widget.mobileColumns,
      tablet: widget.tabletColumns,
      desktop: widget.desktopColumns,
    );
  }

  /// 处理小部件重排
  void _onReorder(int oldIndex, int newIndex) {
    if (!widget.enableDragAndDrop) return;
    
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _widgets.removeAt(oldIndex);
      _widgets.insert(newIndex, item);
    });
    
    _layoutController.forward().then((_) {
      _layoutController.reset();
    });
    
    widget.onLayoutChanged?.call(_widgets);
  }

  /// 构建小部件卡片
  Widget _buildWidgetCard(DashboardWidget widget, int index) {
    return VanHubCardV2.interactive(
      key: ValueKey(widget.id),
      size: VanHubCardSize.sm,
      enable3DEffect: VanHubResponsiveUtils.isDesktop(context),
      onTap: widget.onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              if (widget.icon != null) ...[
                Icon(
                  widget.icon,
                  size: 20,
                  color: widget.color ?? VanHubBrandColors.primary,
                ),
                SizedBox(width: VanHubResponsiveSpacing.sm),
              ],
              Expanded(
                child: Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: VanHubSemanticColors.getTextColor(context),
                  ),
                ),
              ),
              if (widget.onRefresh != null)
                IconButton(
                  icon: const Icon(Icons.refresh, size: 18),
                  onPressed: widget.onRefresh,
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
              if (this.widget.enablePersonalization)
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    size: 18,
                    color: VanHubSemanticColors.getTextColor(context, secondary: true),
                  ),
                  onSelected: (value) {
                    if (value == 'remove') {
                      this.widget.onRemoveWidget?.call(widget.id);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'remove',
                      child: Row(
                        children: [
                          Icon(Icons.delete_outline, size: 16),
                          SizedBox(width: 8),
                          Text('移除'),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          // 内容区域
          Expanded(
            child: widget.content,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final columnCount = _getColumnCount();
    
    return AnimatedBuilder(
      animation: _layoutController,
      builder: (context, child) {
        return Column(
          children: [
            // 仪表盘标题和操作
            if (widget.enablePersonalization) ...[
              Row(
                children: [
                  Text(
                    '个性化仪表盘',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: VanHubSemanticColors.getTextColor(context),
                    ),
                  ),
                  const Spacer(),
                  if (widget.onAddWidget != null)
                    IconButton(
                      icon: const Icon(Icons.add_circle_outline),
                      onPressed: widget.onAddWidget,
                      color: VanHubBrandColors.primary,
                      tooltip: '添加小部件',
                    ),
                ],
              ),
              SizedBox(height: VanHubResponsiveSpacing.lg),
            ],
            
            // 仪表盘网格
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxHeight.isInfinite || constraints.maxWidth.isInfinite) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return StaggeredGrid.count(
                    crossAxisCount: columnCount.clamp(1, 6),
                    mainAxisSpacing: VanHubResponsiveSpacing.md,
                    crossAxisSpacing: VanHubResponsiveSpacing.md,
                    children: _widgets.asMap().entries.map((entry) {
                      final index = entry.key;
                      final widget = entry.value;

                      return widget.getGridTile(
                        _buildWidgetCard(widget, index),
                      );
                    }).toList(),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}

/// 预定义仪表盘小部件
class DashboardWidgets {
  /// 项目概览小部件
  static DashboardWidget projectOverview({
    required int totalProjects,
    required int activeProjects,
    required int completedProjects,
  }) {
    return DashboardWidget(
      id: 'project_overview',
      title: '项目概览',
      type: DashboardWidgetType.projectOverview,
      size: DashboardWidgetSize.medium,
      icon: Icons.folder_outlined,
      color: VanHubBrandColors.primary,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildStatItem('总计', totalProjects.toString(), VanHubSemanticColors.info),
              const Spacer(),
              _buildStatItem('进行中', activeProjects.toString(), VanHubSemanticColors.warning),
              const Spacer(),
              _buildStatItem('已完成', completedProjects.toString(), VanHubSemanticColors.success),
            ],
          ),
        ],
      ),
    );
  }

  /// 快速操作小部件
  static DashboardWidget quickActions({
    required List<QuickAction> actions,
  }) {
    return DashboardWidget(
      id: 'quick_actions',
      title: '快速操作',
      type: DashboardWidgetType.quickActions,
      size: DashboardWidgetSize.wide,
      icon: Icons.flash_on_outlined,
      color: VanHubBrandColors.accent,
      content: Wrap(
        spacing: VanHubResponsiveSpacing.sm,
        runSpacing: VanHubResponsiveSpacing.sm,
        children: actions.map((action) => _buildQuickActionButton(action)).toList(),
      ),
    );
  }

  /// 构建统计项
  static Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: VanHubSemanticColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建快速操作按钮
  static Widget _buildQuickActionButton(QuickAction action) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: action.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: action.color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(action.icon, size: 16, color: action.color),
          const SizedBox(width: 4),
          Text(
            action.label,
            style: TextStyle(
              fontSize: 12,
              color: action.color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// 快速操作数据模型
class QuickAction {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const QuickAction({
    required this.label,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
