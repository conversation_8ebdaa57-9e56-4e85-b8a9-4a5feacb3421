import 'package:flutter/material.dart';
import '../../../foundation/colors/colors.dart';
import '../../../foundation/spacing/spacing.dart';
import '../../../foundation/typography/typography.dart';

/// 加载状态类型枚举
enum VanHubLoadingType {
  /// 圆形加载指示器
  circular,
  /// 线性加载指示器
  linear,
  /// 骨架屏加载
  skeleton,
  /// 脉冲加载
  pulse,
  /// 自定义加载
  custom,
}

/// VanHub加载状态组件
/// 提供统一的加载状态显示，支持多种加载样式
class VanHubLoadingState extends StatelessWidget {
  /// 加载类型
  final VanHubLoadingType type;
  
  /// 加载文本
  final String? message;
  
  /// 是否显示文本
  final bool showMessage;
  
  /// 自定义加载组件
  final Widget? customLoader;
  
  /// 组件大小
  final double? size;
  
  /// 主色调
  final Color? color;
  
  /// 背景色
  final Color? backgroundColor;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 是否居中显示
  final bool centered;

  const VanHubLoadingState({
    super.key,
    this.type = VanHubLoadingType.circular,
    this.message,
    this.showMessage = true,
    this.customLoader,
    this.size,
    this.color,
    this.backgroundColor,
    this.padding,
    this.centered = true,
  });

  /// 创建圆形加载状态
  const VanHubLoadingState.circular({
    super.key,
    this.message,
    this.showMessage = true,
    this.size,
    this.color,
    this.backgroundColor,
    this.padding,
    this.centered = true,
  }) : type = VanHubLoadingType.circular,
       customLoader = null;

  /// 创建线性加载状态
  const VanHubLoadingState.linear({
    super.key,
    this.message,
    this.showMessage = true,
    this.color,
    this.backgroundColor,
    this.padding,
    this.centered = true,
  }) : type = VanHubLoadingType.linear,
       size = null,
       customLoader = null;

  /// 创建骨架屏加载状态
  const VanHubLoadingState.skeleton({
    super.key,
    this.message,
    this.showMessage = false,
    this.size,
    this.backgroundColor,
    this.padding,
    this.centered = true,
  }) : type = VanHubLoadingType.skeleton,
       color = null,
       customLoader = null;

  /// 创建脉冲加载状态
  const VanHubLoadingState.pulse({
    super.key,
    this.message,
    this.showMessage = true,
    this.size,
    this.color,
    this.backgroundColor,
    this.padding,
    this.centered = true,
  }) : type = VanHubLoadingType.pulse,
       customLoader = null;

  /// 创建自定义加载状态
  const VanHubLoadingState.custom({
    super.key,
    required this.customLoader,
    this.message,
    this.showMessage = true,
    this.padding,
    this.centered = true,
  }) : type = VanHubLoadingType.custom,
       size = null,
       color = null,
       backgroundColor = null;

  @override
  Widget build(BuildContext context) {
    final content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLoader(context),
        if (showMessage && message != null) ...[
          VanHubSpacing.vSm,
          Text(
            message!,
            style: VanHubTypography.bodyMedium.copyWith(
              color: color ?? VanHubColors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    final paddedContent = padding != null
        ? Padding(padding: padding!, child: content)
        : content;

    if (centered) {
      return Center(child: paddedContent);
    }

    return paddedContent;
  }

  Widget _buildLoader(BuildContext context) {
    final loaderColor = color ?? VanHubColors.primary;
    final loaderSize = size ?? 24.0;

    switch (type) {
      case VanHubLoadingType.circular:
        return SizedBox(
          width: loaderSize,
          height: loaderSize,
          child: CircularProgressIndicator(
            color: loaderColor,
            backgroundColor: backgroundColor,
            strokeWidth: 2.0,
          ),
        );

      case VanHubLoadingType.linear:
        return SizedBox(
          width: 200,
          child: LinearProgressIndicator(
            color: loaderColor,
            backgroundColor: backgroundColor ?? VanHubColors.surfaceVariant,
          ),
        );

      case VanHubLoadingType.skeleton:
        return _buildSkeletonLoader(context);

      case VanHubLoadingType.pulse:
        return _buildPulseLoader(context);

      case VanHubLoadingType.custom:
        return customLoader ?? const SizedBox.shrink();
    }
  }

  Widget _buildSkeletonLoader(BuildContext context) {
    return Container(
      width: size ?? 200,
      height: 20,
      decoration: BoxDecoration(
        color: backgroundColor ?? VanHubColors.surfaceVariant,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  Widget _buildPulseLoader(BuildContext context) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1.0),
      duration: const Duration(milliseconds: 800),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Container(
            width: size ?? 24,
            height: size ?? 24,
            decoration: BoxDecoration(
              color: color ?? VanHubColors.primary,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }
}

/// VanHub加载状态工厂类
class VanHubLoadingStateFactory {
  VanHubLoadingStateFactory._();

  /// 创建默认加载状态
  static Widget defaultLoading({String? message}) {
    return VanHubLoadingState.circular(
      message: message ?? '加载中...',
    );
  }

  /// 创建页面加载状态
  static Widget pageLoading({String? message}) {
    return VanHubLoadingState.circular(
      message: message ?? '页面加载中...',
      size: 32,
      padding: VanHubSpacing.allXl,
    );
  }

  /// 创建按钮加载状态
  static Widget buttonLoading({Color? color}) {
    return VanHubLoadingState.circular(
      showMessage: false,
      size: 16,
      color: color ?? VanHubColors.onPrimary,
      centered: false,
    );
  }

  /// 创建列表加载状态
  static Widget listLoading() {
    return VanHubLoadingState.skeleton(
      size: double.infinity,
      padding: VanHubSpacing.allMd,
    );
  }
}
