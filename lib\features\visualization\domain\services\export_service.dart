import 'package:fpdart/fpdart.dart';
import '../../../../core/error/failures.dart';
import '../entities/chart_data.dart';

/// Export service interface for data visualization - Clean Architecture compliant
abstract class ExportService {
  /// Export chart data to PDF format
  Future<Either<Failure, String>> exportToPdf({
    required VisualizationData data,
    required String fileName,
    String? customTitle,
    bool includeCharts = true,
    bool includeRawData = false,
  });

  /// Export chart data to Excel format
  Future<Either<Failure, String>> exportToExcel({
    required VisualizationData data,
    required String fileName,
    bool includeCharts = true,
    bool includeRawData = true,
  });

  /// Export individual chart as image
  Future<Either<Failure, String>> exportChartAsImage({
    required String chartType,
    required dynamic chartData,
    required String fileName,
    String format = 'png', // png, jpg, svg
    int width = 800,
    int height = 600,
  });

  /// Generate custom report with multiple charts
  Future<Either<Failure, String>> generateCustomReport({
    required VisualizationData data,
    required List<String> selectedCharts,
    required String fileName,
    String format = 'pdf', // pdf, html, docx
    Map<String, dynamic>? customSettings,
  });

  /// Export data for sharing (compressed format)
  Future<Either<Failure, String>> exportForSharing({
    required VisualizationData data,
    required String fileName,
    bool includeImages = true,
    String compressionLevel = 'medium', // low, medium, high
  });

  /// Get available export formats
  List<ExportFormat> getAvailableFormats();

  /// Validate export parameters
  Either<Failure, bool> validateExportParameters({
    required String fileName,
    required String format,
    required VisualizationData data,
  });
}

/// Export format configuration
class ExportFormat {
  final String id;
  final String name;
  final String extension;
  final String mimeType;
  final bool supportsCharts;
  final bool supportsRawData;
  final List<String> features;

  const ExportFormat({
    required this.id,
    required this.name,
    required this.extension,
    required this.mimeType,
    required this.supportsCharts,
    required this.supportsRawData,
    required this.features,
  });
}

/// Export configuration options
class ExportConfig {
  final String fileName;
  final String format;
  final bool includeCharts;
  final bool includeRawData;
  final bool includeMetadata;
  final String? customTitle;
  final String? customDescription;
  final Map<String, dynamic>? customSettings;

  const ExportConfig({
    required this.fileName,
    required this.format,
    this.includeCharts = true,
    this.includeRawData = false,
    this.includeMetadata = true,
    this.customTitle,
    this.customDescription,
    this.customSettings,
  });
}

/// Export result information
class ExportResult {
  final String filePath;
  final String fileName;
  final String format;
  final int fileSize;
  final DateTime exportedAt;
  final Map<String, dynamic>? metadata;

  const ExportResult({
    required this.filePath,
    required this.fileName,
    required this.format,
    required this.fileSize,
    required this.exportedAt,
    this.metadata,
  });
}
