import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/material_review.dart';
import '../providers/material_review_provider.dart';
import '../widgets/material_review_card_widget.dart';
import '../widgets/material_review_summary_widget.dart';
import '../widgets/write_review_dialog_widget.dart';

/// 材料评价列表页面
/// 显示特定材料的所有评价，支持筛选和排序
class MaterialReviewsPage extends ConsumerStatefulWidget {
  final String materialId;
  final String materialName;

  const MaterialReviewsPage({
    super.key,
    required this.materialId,
    required this.materialName,
  });

  @override
  ConsumerState<MaterialReviewsPage> createState() => _MaterialReviewsPageState();
}

class _MaterialReviewsPageState extends ConsumerState<MaterialReviewsPage> {
  ReviewFilterCriteria _filterCriteria = const ReviewFilterCriteria();
  final ScrollController _scrollController = ScrollController();
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('用户评价'),
            Text(
              widget.materialName,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        actions: [
          // 筛选按钮
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: '筛选评价',
          ),
          
          // 写评价按钮
          IconButton(
            onPressed: _showWriteReviewDialog,
            icon: const Icon(Icons.edit),
            tooltip: '写评价',
          ),
        ],
      ),
      body: Column(
        children: [
          // 评价摘要
          _buildReviewSummary(),
          
          // 排序选项
          _buildSortOptions(),
          
          // 评价列表
          Expanded(
            child: _buildReviewsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSummary() {
    final summaryAsync = ref.watch(materialReviewSummaryProvider(widget.materialId));
    
    return summaryAsync.when(
      data: (summary) => MaterialReviewSummaryWidget(
        summary: summary,
        showWriteReviewButton: false, // 在AppBar中已有写评价按钮
        onViewAllReviews: null, // 当前就在评价列表页面
        onWriteReview: _showWriteReviewDialog,
      ),
      loading: () => const Card(
        margin: EdgeInsets.all(16),
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ),
      error: (error, stack) => Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('加载评价摘要失败: $error'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '排序方式:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: ReviewSortType.values.map((sortType) {
                  final isSelected = _filterCriteria.sortBy == sortType;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(sortType.displayName),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _filterCriteria = _filterCriteria.copyWith(sortBy: sortType);
                          });
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList() {
    final reviewsAsync = ref.watch(materialReviewsProvider(
      widget.materialId,
      filterCriteria: _filterCriteria,
    ));
    
    return reviewsAsync.when(
      data: (reviews) {
        if (reviews.isEmpty) {
          return _buildEmptyState();
        }
        
        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(materialReviewsProvider);
            ref.invalidate(materialReviewSummaryProvider);
          },
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.only(bottom: 16),
            itemCount: reviews.length,
            itemBuilder: (context, index) {
              final review = reviews[index];
              return MaterialReviewCardWidget(
                review: review,
                onLike: () => _handleLikeReview(review),
                onMarkHelpful: () => _handleMarkHelpful(review),
                onReport: () => _handleReportReview(review),
              );
            },
          ),
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                '加载评价失败',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(materialReviewsProvider);
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              '暂无评价',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              '成为第一个评价这个材料的用户',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            ElevatedButton.icon(
              onPressed: _showWriteReviewDialog,
              icon: const Icon(Icons.edit),
              label: const Text('写评价'),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选评价'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 评分筛选
            Text(
              '最低评分',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            
            Slider(
              value: _filterCriteria.minRating ?? 1.0,
              min: 1.0,
              max: 5.0,
              divisions: 4,
              label: '${_filterCriteria.minRating?.toStringAsFixed(1) ?? '1.0'}星',
              onChanged: (value) {
                setState(() {
                  _filterCriteria = _filterCriteria.copyWith(minRating: value);
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // 验证购买筛选
            CheckboxListTile(
              title: const Text('只显示验证购买'),
              value: _filterCriteria.verifiedPurchaseOnly,
              onChanged: (value) {
                setState(() {
                  _filterCriteria = _filterCriteria.copyWith(
                    verifiedPurchaseOnly: value ?? false,
                  );
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
            
            // 包含图片筛选
            CheckboxListTile(
              title: const Text('只显示有图片的评价'),
              value: _filterCriteria.withImagesOnly,
              onChanged: (value) {
                setState(() {
                  _filterCriteria = _filterCriteria.copyWith(
                    withImagesOnly: value ?? false,
                  );
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _filterCriteria = const ReviewFilterCriteria();
              });
              Navigator.of(context).pop();
            },
            child: const Text('重置'),
          ),
          
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 筛选条件已在setState中更新，会自动刷新列表
            },
            child: const Text('应用'),
          ),
        ],
      ),
    );
  }

  void _showWriteReviewDialog() {
    showDialog(
      context: context,
      builder: (context) => WriteReviewDialogWidget(
        materialId: widget.materialId,
        materialName: widget.materialName,
      ),
    ).then((result) {
      if (result == true) {
        // 评价创建成功，刷新列表
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewSummaryProvider);
      }
    });
  }

  void _handleLikeReview(MaterialReview review) {
    // TODO: 实现点赞功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('点赞评价: ${review.userName}')),
    );
  }

  void _handleMarkHelpful(MaterialReview review) {
    // TODO: 实现标记有用功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('标记有用: ${review.userName}')),
    );
  }

  void _handleReportReview(MaterialReview review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('举报评价'),
        content: const Text('确定要举报这条评价吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('举报已提交')),
              );
            },
            child: const Text('确定举报'),
          ),
        ],
      ),
    );
  }
}
