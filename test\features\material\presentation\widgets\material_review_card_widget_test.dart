import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/features/material/domain/entities/material_review.dart';
import 'package:vanhub/features/material/presentation/widgets/material_review_card_widget.dart';

void main() {
  group('MaterialReviewCardWidget', () {
    late MaterialReview testReview;

    setUp(() {
      testReview = MaterialReview(
        id: 'test-review-1',
        materialId: 'test-material-1',
        userId: 'test-user-1',
        userName: 'Test User',
        userAvatarUrl: null,
        content: 'This is a test review content that is long enough to be valid.',
        rating: 4.5,
        qualityRating: 4.0,
        valueRating: 4.5,
        durabilityRating: 4.0,
        installationRating: 5.0,
        vehicleType: 'Test Vehicle',
        systemType: 'Test System',
        usageDuration: '3 months',
        pros: ['Good quality', 'Easy to install'],
        cons: ['Expensive'],
        tips: ['Use with care'],
        isVerifiedPurchase: true,
        purchaseDate: DateTime(2024, 1, 1),
        imageUrls: ['https://example.com/image1.jpg'],
        likedByUserIds: ['user1', 'user2'],
        helpfulUserIds: ['user1', 'user3'],
        helpfulCount: 2,
        createdAt: DateTime(2024, 1, 24),
        updatedAt: DateTime(2024, 1, 24),
      );
    });

    Widget createWidgetUnderTest({
      MaterialReview? review,
      VoidCallback? onLike,
      VoidCallback? onMarkHelpful,
      VoidCallback? onReport,
      bool showActions = true,
      bool isCompact = false,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: MaterialReviewCardWidget(
              review: review ?? testReview,
              onLike: onLike,
              onMarkHelpful: onMarkHelpful,
              onReport: onReport,
              showActions: showActions,
              isCompact: isCompact,
            ),
          ),
        ),
      );
    }

    group('Basic Display', () {
      testWidgets('should display user name and rating', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('Test User'), findsOneWidget);
        expect(find.text('4.5'), findsOneWidget);
        expect(find.byIcon(Icons.star), findsWidgets);
      });

      testWidgets('should display review content', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text(testReview.content), findsOneWidget);
      });

      testWidgets('should display verified purchase badge when verified', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('已验证购买'), findsOneWidget);
      });

      testWidgets('should not display verified purchase badge when not verified', (WidgetTester tester) async {
        // arrange
        final unverifiedReview = testReview.copyWith(isVerifiedPurchase: false);

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: unverifiedReview));

        // assert
        expect(find.text('已验证购买'), findsNothing);
      });

      testWidgets('should display formatted date', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('2024-01-24'), findsOneWidget);
      });
    });

    group('Detailed Ratings', () {
      testWidgets('should display detailed rating dimensions in full mode', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest(isCompact: false));

        // assert
        expect(find.text('详细评分'), findsOneWidget);
        expect(find.text('质量'), findsOneWidget);
        expect(find.text('性价比'), findsOneWidget);
        expect(find.text('耐用性'), findsOneWidget);
        expect(find.text('安装难度'), findsOneWidget);
      });

      testWidgets('should not display detailed ratings in compact mode', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest(isCompact: true));

        // assert
        expect(find.text('详细评分'), findsNothing);
      });
    });

    group('Usage Context', () {
      testWidgets('should display usage context information', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('使用场景'), findsOneWidget);
        expect(find.text('车型: Test Vehicle'), findsOneWidget);
        expect(find.text('系统: Test System'), findsOneWidget);
        expect(find.text('使用时长: 3 months'), findsOneWidget);
      });

      testWidgets('should handle missing usage context gracefully', (WidgetTester tester) async {
        // arrange
        final reviewWithoutContext = testReview.copyWith(
          vehicleType: null,
          systemType: null,
          usageDuration: null,
        );

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: reviewWithoutContext));

        // assert
        expect(find.text('使用场景'), findsNothing);
      });
    });

    group('Pros and Cons', () {
      testWidgets('should display pros and cons', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('优点'), findsOneWidget);
        expect(find.text('Good quality'), findsOneWidget);
        expect(find.text('Easy to install'), findsOneWidget);
        
        expect(find.text('缺点'), findsOneWidget);
        expect(find.text('Expensive'), findsOneWidget);
        
        expect(find.text('使用技巧'), findsOneWidget);
        expect(find.text('Use with care'), findsOneWidget);
      });

      testWidgets('should display appropriate icons for pros, cons, and tips', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.byIcon(Icons.add_circle), findsWidgets);
        expect(find.byIcon(Icons.remove_circle), findsWidgets);
        expect(find.byIcon(Icons.lightbulb), findsWidgets);
      });
    });

    group('Images', () {
      testWidgets('should display image gallery when images are present', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('图片展示'), findsOneWidget);
        expect(find.byType(Image), findsOneWidget);
      });

      testWidgets('should not display image gallery when no images', (WidgetTester tester) async {
        // arrange
        final reviewWithoutImages = testReview.copyWith(imageUrls: []);

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: reviewWithoutImages));

        // assert
        expect(find.text('图片展示'), findsNothing);
      });
    });

    group('Action Buttons', () {
      testWidgets('should display action buttons when showActions is true', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest(showActions: true));

        // assert
        expect(find.text('有用 (2)'), findsOneWidget);
        expect(find.text('点赞 (2)'), findsOneWidget);
        expect(find.text('举报'), findsOneWidget);
      });

      testWidgets('should not display action buttons when showActions is false', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest(showActions: false));

        // assert
        expect(find.text('有用 (2)'), findsNothing);
        expect(find.text('点赞 (2)'), findsNothing);
        expect(find.text('举报'), findsNothing);
      });

      testWidgets('should call onLike when like button is tapped', (WidgetTester tester) async {
        // arrange
        bool likeCalled = false;
        await tester.pumpWidget(createWidgetUnderTest(
          onLike: () => likeCalled = true,
        ));

        // act
        await tester.tap(find.text('点赞 (2)'));
        await tester.pump();

        // assert
        expect(likeCalled, isTrue);
      });

      testWidgets('should call onMarkHelpful when helpful button is tapped', (WidgetTester tester) async {
        // arrange
        bool helpfulCalled = false;
        await tester.pumpWidget(createWidgetUnderTest(
          onMarkHelpful: () => helpfulCalled = true,
        ));

        // act
        await tester.tap(find.text('有用 (2)'));
        await tester.pump();

        // assert
        expect(helpfulCalled, isTrue);
      });

      testWidgets('should call onReport when report button is tapped', (WidgetTester tester) async {
        // arrange
        bool reportCalled = false;
        await tester.pumpWidget(createWidgetUnderTest(
          onReport: () => reportCalled = true,
        ));

        // act
        await tester.tap(find.text('举报'));
        await tester.pump();

        // assert
        expect(reportCalled, isTrue);
      });
    });

    group('Star Rating Display', () {
      testWidgets('should display correct number of filled stars', (WidgetTester tester) async {
        // arrange
        final reviewWith3Stars = testReview.copyWith(rating: 3.0);

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: reviewWith3Stars));

        // assert
        // Should have 3 filled stars and 2 empty stars in the main rating
        final starIcons = tester.widgetList<Icon>(find.byIcon(Icons.star));
        final starBorderIcons = tester.widgetList<Icon>(find.byIcon(Icons.star_border));
        
        // Note: There are multiple star ratings displayed (overall + detailed ratings)
        expect(starIcons.length, greaterThan(0));
        expect(starBorderIcons.length, greaterThan(0));
      });

      testWidgets('should handle half stars correctly', (WidgetTester tester) async {
        // arrange
        final reviewWithHalfStars = testReview.copyWith(rating: 3.5);

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: reviewWithHalfStars));

        // assert
        expect(find.byIcon(Icons.star_half), findsWidgets);
      });
    });

    group('User Avatar', () {
      testWidgets('should display user initial when no avatar URL', (WidgetTester tester) async {
        // arrange & act
        await tester.pumpWidget(createWidgetUnderTest());

        // assert
        expect(find.text('T'), findsOneWidget); // First letter of "Test User"
        expect(find.byType(CircleAvatar), findsOneWidget);
      });

      testWidgets('should display network image when avatar URL is provided', (WidgetTester tester) async {
        // arrange
        final reviewWithAvatar = testReview.copyWith(
          userAvatarUrl: 'https://example.com/avatar.jpg',
        );

        // act
        await tester.pumpWidget(createWidgetUnderTest(review: reviewWithAvatar));

        // assert
        expect(find.byType(CircleAvatar), findsOneWidget);
        // Note: NetworkImage testing requires more complex setup for actual image loading
      });
    });
  });
}
