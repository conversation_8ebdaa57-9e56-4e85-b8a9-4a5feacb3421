# VanHub组件库规范

## 🎯 **组件库架构**

### **原子化设计方法**
```
Atoms (原子组件)
├── VanHubButton          # 高端按钮组件
├── VanHubInput           # 智能输入组件  
├── VanHubIcon            # 动画图标组件
├── VanHubAvatar          # 用户头像组件
├── VanHubBadge           # 徽章组件
├── VanHubChip            # 标签组件
├── VanHubDivider         # 分割线组件
├── VanHubLoader          # 加载指示器
├── VanHubProgress        # 进度条组件
└── VanHubSwitch          # 开关组件

Molecules (分子组件)
├── VanHubCard            # 智能卡片组件
├── VanHubSearchBar       # 搜索栏组件
├── VanHubDropdown        # 下拉菜单组件
├── VanHubTooltip         # 工具提示组件
├── VanHubModal           # 模态框组件
├── VanHubSnackBar        # 消息提示组件
├── VanHubTabs            # 标签页组件
├── VanHubAccordion       # 折叠面板组件
├── VanHubPagination      # 分页组件
└── VanHubBreadcrumb      # 面包屑组件

Organisms (有机体组件)
├── VanHubAppBar          # 应用栏组件
├── VanHubSidebar         # 侧边栏组件
├── VanHubBottomNav       # 底部导航组件
├── VanHubDataTable       # 数据表格组件
├── VanHubDashboard       # 仪表盘组件
├── VanHubProjectCard     # 项目卡片组件
├── VanHubMaterialGrid    # 材料网格组件
├── VanHubBomTree         # BOM树形组件
├── VanHubTimeline        # 时间轴组件
└── VanHubStatistics      # 统计图表组件
```

## 🎨 **核心组件规范**

### **1. VanHubButton 2.0 - 高端按钮组件**

#### **设计特性**
- 8种变体：primary, secondary, tertiary, ghost, outline, danger, success, gradient
- 4种尺寸：xs, sm, md, lg, xl
- 完整动画：hover, press, loading, disabled
- 响应式适配：移动端触摸优化

#### **技术实现**
```dart
class VanHubButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final VanHubButtonVariant variant;
  final VanHubButtonSize size;
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final bool isLoading;
  final bool isFullWidth;
  final Gradient? gradient;
  final Duration animationDuration;
  final Curve animationCurve;
  
  // 响应式配置
  final VanHubButtonSize? mobileSize;
  final VanHubButtonSize? tabletSize;
  final VanHubButtonSize? desktopSize;
  
  // 动画配置
  final bool enableHoverAnimation;
  final bool enablePressAnimation;
  final bool enableLoadingAnimation;
  final double hoverScale;
  final double pressScale;
}
```

#### **动画效果**
```dart
// 悬停动画
AnimatedContainer(
  duration: Duration(milliseconds: 150),
  curve: Curves.easeInOut,
  transform: Matrix4.identity()..scale(_isHovered ? 1.05 : 1.0),
  decoration: BoxDecoration(
    gradient: _getButtonGradient(),
    borderRadius: BorderRadius.circular(_getBorderRadius()),
    boxShadow: _isHovered ? VanHubShadows.elevation3 : VanHubShadows.elevation1,
  ),
)

// 按压动画
GestureDetector(
  onTapDown: (_) => _animationController.forward(),
  onTapUp: (_) => _animationController.reverse(),
  onTapCancel: () => _animationController.reverse(),
)
```

### **2. VanHubCard 2.0 - 智能卡片组件**

#### **设计特性**
- 6种变体：elevated, outlined, filled, glass, gradient, interactive
- 智能悬浮效果：3D变换 + 阴影动画
- 内容自适应：自动调整内边距和布局
- 手势支持：点击、长按、拖拽

#### **技术实现**
```dart
class VanHubCard extends StatefulWidget {
  final Widget child;
  final VanHubCardVariant variant;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool enableHoverEffect;
  final bool enablePressEffect;
  final bool enable3DEffect;
  final Gradient? gradient;
  final double? elevation;
  final BorderRadius? borderRadius;
  
  // 响应式配置
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;
  
  // 动画配置
  final Duration hoverDuration;
  final Duration pressDuration;
  final Curve animationCurve;
}
```

#### **3D悬浮效果**
```dart
// 3D变换动画
Transform(
  alignment: Alignment.center,
  transform: Matrix4.identity()
    ..setEntry(3, 2, 0.001) // 透视效果
    ..rotateX(_rotationX)
    ..rotateY(_rotationY)
    ..scale(_scale),
  child: Container(
    decoration: BoxDecoration(
      gradient: _getCardGradient(),
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1 * _elevation),
          blurRadius: 20 * _elevation,
          offset: Offset(0, 10 * _elevation),
        ),
      ],
    ),
  ),
)
```

### **3. VanHubInput 2.0 - 智能输入组件**

#### **设计特性**
- 浮动标签动画
- 智能验证提示
- 自动完成支持
- 多种输入类型：text, email, password, number, search

#### **技术实现**
```dart
class VanHubInput extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VanHubInputType inputType;
  final bool enableFloatingLabel;
  final bool enableValidation;
  final List<String>? autoCompleteOptions;
  
  // 验证配置
  final String? Function(String?)? validator;
  final bool enableRealTimeValidation;
  final Duration validationDelay;
  
  // 动画配置
  final Duration focusAnimationDuration;
  final Curve focusAnimationCurve;
}
```

## 🌊 **高级组件规范**

### **4. VanHubDashboard - 个性化仪表盘**

#### **设计特性**
- 拖拽重排布局
- 智能卡片推荐
- 实时数据动画
- 个性化配置

#### **技术实现**
```dart
class VanHubDashboard extends StatefulWidget {
  final List<DashboardWidget> widgets;
  final bool enableDragAndDrop;
  final bool enablePersonalization;
  final DashboardLayout layout;
  final Function(List<DashboardWidget>)? onLayoutChanged;
  
  // 响应式配置
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  
  // 动画配置
  final Duration reorderAnimationDuration;
  final Duration dataUpdateAnimationDuration;
}

// 仪表盘小部件
abstract class DashboardWidget {
  final String id;
  final String title;
  final DashboardWidgetSize size;
  final bool isResizable;
  final bool isDraggable;
  
  Widget build(BuildContext context);
  Future<void> refreshData();
}
```

### **5. VanHubProjectCard - 项目卡片组件**

#### **设计特性**
- 项目状态可视化
- 进度动画效果
- 3D翻转交互
- 智能标签系统

#### **技术实现**
```dart
class VanHubProjectCard extends StatefulWidget {
  final Project project;
  final bool enableFlipAnimation;
  final bool enableProgressAnimation;
  final bool enableStatusIndicator;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  
  // 显示配置
  final bool showProgress;
  final bool showTags;
  final bool showStats;
  final bool showActions;
  
  // 动画配置
  final Duration flipDuration;
  final Duration progressAnimationDuration;
}
```

### **6. VanHubMaterialGrid - 材料网格组件**

#### **设计特性**
- Pinterest风格瀑布流
- 智能图片加载
- 无限滚动
- 筛选动画

#### **技术实现**
```dart
class VanHubMaterialGrid extends StatefulWidget {
  final List<Material> materials;
  final bool enableInfiniteScroll;
  final bool enableImagePreview;
  final bool enableFiltering;
  final Function(Material)? onMaterialTap;
  final Function()? onLoadMore;
  
  // 布局配置
  final double itemSpacing;
  final double runSpacing;
  final int crossAxisCount;
  
  // 响应式配置
  final int mobileCrossAxisCount;
  final int tabletCrossAxisCount;
  final int desktopCrossAxisCount;
}
```

## 🎭 **动画系统规范**

### **页面转场动画**
```dart
// 标准转场动画
class VanHubPageTransitions {
  // 滑动转场
  static Route<T> slideTransition<T>(
    Widget page, {
    SlideDirection direction = SlideDirection.right,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  });
  
  // 淡入转场
  static Route<T> fadeTransition<T>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  });
  
  // 缩放转场
  static Route<T> scaleTransition<T>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  });
}
```

### **组件入场动画**
```dart
// 交错动画
class VanHubStaggeredAnimation extends StatelessWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration interval;
  final AnimationType animationType;
  
  // 支持的动画类型
  enum AnimationType {
    fadeInUp,
    slideInLeft,
    scaleIn,
    rotateIn,
  }
}
```

## 📱 **响应式适配规范**

### **断点系统**
```dart
// 标准断点
class VanHubBreakpoints {
  static const double xs = 0;      // 0px - 575px
  static const double sm = 576;    // 576px - 767px
  static const double md = 768;    // 768px - 991px
  static const double lg = 992;    // 992px - 1199px
  static const double xl = 1200;   // 1200px+
}
```

### **响应式组件基类**
```dart
// 所有组件必须继承的响应式基类
abstract class VanHubResponsiveWidget extends StatelessWidget {
  const VanHubResponsiveWidget({Key? key}) : super(key: key);
  
  // 必须实现的方法
  Widget buildMobile(BuildContext context);
  Widget buildTablet(BuildContext context);
  Widget buildDesktop(BuildContext context);
  
  // 可选的大屏幕适配
  Widget buildLarge(BuildContext context) => buildDesktop(context);
  
  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: buildMobile(context),
      tablet: buildTablet(context),
      desktop: buildDesktop(context),
      large: buildLarge(context),
    );
  }
}
```

这个组件库规范将确保VanHub拥有世界级的UI组件体验！
