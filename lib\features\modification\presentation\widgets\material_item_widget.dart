import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/system_material.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import 'material_status_dialog.dart';
import '../../../modification_log/domain/entities/exports.dart';

/// 物料项组件
/// 用于展示单个物料的详细信息
class MaterialItem extends ConsumerWidget {
  const MaterialItem({
    super.key,
    required this.material,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
    this.showActions = true,
    this.compact = false,
  });

  /// 物料数据
  final SystemMaterial material;

  /// 点击回调
  final VoidCallback? onTap;

  /// 编辑回调
  final VoidCallback? onEdit;

  /// 删除回调
  final VoidCallback? onDelete;

  /// 状态变更回调
  final Function(MaterialStatus purchaseStatus, MaterialStatus installStatus)? onStatusChange;

  /// 是否显示操作按钮
  final bool showActions;

  /// 是否使用紧凑布局
  final bool compact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (compact) {
      return _buildCompactItem(context, ref);
    }
    return _buildFullItem(context, ref);
  }

  /// 构建完整物料项
  Widget _buildFullItem(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 物料头部信息
              _buildMaterialHeader(context),
              
              const SizedBox(height: 16),

              // 物料详细信息
              _buildMaterialDetails(context),

              const SizedBox(height: 16),

              // 价格和数量信息
              _buildPriceSection(context),

              const SizedBox(height: 16),
              
              // 状态信息
              _buildStatusSection(context),
              
              // 操作按钮
              if (showActions) ...[
                const SizedBox(height: 16),
                _buildActionButtons(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建紧凑物料项
  Widget _buildCompactItem(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // 状态指示器
              _buildStatusIndicator(context),
              
              const SizedBox(width: 16),
              
              // 物料信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 物料名称
                    Text(
                      material.name,
                      style: VanHubTypography.titleSmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4.0),
                    
                    // 规格和数量
                    Row(
                      children: [
                        if (material.specification.isNotEmpty) ...[
                          Expanded(
                            child: Text(
                              material.specification,
                              style: VanHubTypography.bodySmall.copyWith(
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8.0),
                        ],
                        
                        Text(
                          '×${material.quantity}',
                          style: VanHubTypography.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // 价格和状态
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '¥${material.totalPrice.toStringAsFixed(0)}',
                    style: VanHubTypography.titleMedium.copyWith(
                      color: VanHubColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: 4.0),
                  
                  _buildStatusChip(context),
                ],
              ),
              
              // 操作菜单
              if (showActions)
                PopupMenuButton<String>(
                  onSelected: (action) => _handleMenuAction(action, context),
                  itemBuilder: (context) => _buildMenuItems(),
                  child: Icon(
                    Icons.more_vert,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建物料头部
  Widget _buildMaterialHeader(BuildContext context) {
    return Row(
      children: [
        // 物料图标
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Icon(
            Icons.inventory_2,
            size: 24.0,
            color: _getStatusColor(),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 物料名称和品牌
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                material.name,
                style: VanHubTypography.headlineSmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (material.brand != null && material.brand!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  '品牌: ${material.brand}',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // 操作菜单
        if (showActions)
          PopupMenuButton<String>(
            onSelected: (action) => _handleMenuAction(action, context),
            itemBuilder: (context) => _buildMenuItems(),
            child: Icon(
              Icons.more_vert,
              color: VanHubColors.textSecondary,
            ),
          ),
      ],
    );
  }

  /// 构建物料详细信息
  Widget _buildMaterialDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 规格信息
        if (material.specification.isNotEmpty)
          _buildDetailRow(
            context,
            icon: Icons.info_outline,
            label: '规格',
            value: material.specification,
          ),
        
        // 分类信息
        if (material.category != null && material.category!.isNotEmpty)
          _buildDetailRow(
            context,
            icon: Icons.category,
            label: '分类',
            value: material.category!,
          ),
        
        // 供应商信息
        if (material.supplier != null && material.supplier!.isNotEmpty)
          _buildDetailRow(
            context,
            icon: Icons.store,
            label: '供应商',
            value: material.supplier!,
          ),
        
        // 重量信息
        if (material.weight != null && material.weight! > 0)
          _buildDetailRow(
            context,
            icon: Icons.fitness_center,
            label: '重量',
            value: '${material.weight}kg',
          ),
        
        // 尺寸信息
        if (material.dimensions != null && material.dimensions!.isNotEmpty)
          _buildDetailRow(
            context,
            icon: Icons.straighten,
            label: '尺寸',
            value: material.dimensions!,
          ),
      ],
    );
  }

  /// 构建详细信息行
  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16.0,
            color: Colors.grey[600],
          ),
          
          const SizedBox(width: 8),
          
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: VanHubTypography.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          
          Expanded(
            child: Text(
              value,
              style: VanHubTypography.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格部分
  Widget _buildPriceSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        children: [
          // 数量
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '数量',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '${material.quantity}个',
                  style: VanHubTypography.headlineSmall,
                ),
              ],
            ),
          ),
          
          // 单价
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '单价',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '¥${material.unitPrice.toStringAsFixed(0)}',
                  style: VanHubTypography.headlineSmall,
                ),
              ],
            ),
          ),
          
          // 总价
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '总价',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '¥${material.totalPrice.toStringAsFixed(0)}',
                  style: VanHubTypography.headlineMedium.copyWith(
                    color: VanHubColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态部分
  Widget _buildStatusSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '状态信息',
          style: VanHubTypography.titleSmall.copyWith(
            color: VanHubColors.textSecondary,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Row(
          children: [
            // 购买状态
            Expanded(
              child: _buildStatusCard(
                context,
                title: '购买状态',
                status: material.purchaseStatusDisplayText,
                color: _getPurchaseStatusColor(),
                icon: Icons.shopping_cart,
                date: material.purchaseDate,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 安装状态
            Expanded(
              child: _buildStatusCard(
                context,
                title: '安装状态',
                status: material.installStatusDisplayText,
                color: _getInstallStatusColor(),
                icon: Icons.build,
                date: material.installDate,
              ),
            ),
          ],
        ),
        
        // 交付信息
        if (material.expectedDeliveryDate != null) ...[
          const SizedBox(height: 8),

          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: material.isDelayed
                  ? VanHubColors.errorWithOpacity
                  : VanHubColors.infoWithOpacity,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: material.isDelayed 
                    ? VanHubColors.error.withValues(alpha: 0.3)
                    : VanHubColors.info.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  material.isDelayed ? Icons.warning : Icons.access_time,
                  size: 16,
                  color: material.isDelayed ? VanHubColors.error : VanHubColors.info,
                ),

                const SizedBox(width: 8),
                
                Text(
                  material.deliveryStatusDescription ?? '',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: material.isDelayed ? VanHubColors.error : VanHubColors.info,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // 保修信息
        if (material.warrantyMonths != null && material.warrantyMonths! > 0) ...[
          const SizedBox(height: 8),

          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: material.isWarrantyExpired
                  ? VanHubColors.errorWithOpacity
                  : material.isWarrantyExpiringSoon
                      ? VanHubColors.warningWithOpacity
                      : VanHubColors.successWithOpacity,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.verified_user,
                  size: 16,
                  color: material.isWarrantyExpired
                      ? VanHubColors.error
                      : material.isWarrantyExpiringSoon
                          ? VanHubColors.warning
                          : VanHubColors.success,
                ),

                const SizedBox(width: 8),
                
                Text(
                  '保修期: ${material.warrantyMonths}个月',
                  style: VanHubTypography.bodySmall,
                ),
                
                if (material.warrantyExpiryDate != null) ...[
                  const Spacer(),
                  Text(
                    '到期: ${_formatDate(material.warrantyExpiryDate!)}',
                    style: VanHubTypography.bodySmall,
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard(
    BuildContext context, {
    required String title,
    required String status,
    required Color color,
    required IconData icon,
    DateTime? date,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),

              const SizedBox(width: 4),
              
              Expanded(
                child: Text(
                  title,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: VanHubColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Text(
            status,
            style: VanHubTypography.bodyMedium.copyWith(
              color: color,
            ),
          ),
          
          if (date != null) ...[
            const SizedBox(height: 4),
            Text(
              _formatDate(date),
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(BuildContext context) {
    return Container(
      width: 12,
      height: 60,
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  /// 构建状态芯片
  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        material.overallStatusDescription,
        style: VanHubTypography.bodySmall.copyWith(
          color: _getStatusColor(),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // 编辑按钮
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onEdit,
            icon: const Icon(Icons.edit),
            label: const Text('编辑'),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // 状态变更按钮
        Expanded(
          child: FilledButton.icon(
            onPressed: () => _showStatusDialog(context),
            icon: Icon(Icons.update),
            label: const Text('更新状态'),
          ),
        ),
      ],
    );
  }

  /// 构建菜单项
  List<PopupMenuEntry<String>> _buildMenuItems() {
    return [
      const PopupMenuItem(
        value: 'edit',
        child: ListTile(
          leading: Icon(Icons.edit),
          title: Text('编辑物料'),
          dense: true,
        ),
      ),
      const PopupMenuItem(
        value: 'status',
        child: ListTile(
          leading: Icon(Icons.pending),
          title: Text('更新状态'),
          dense: true,
        ),
      ),
      const PopupMenuItem(
        value: 'delete',
        child: ListTile(
          leading: Icon(Icons.delete),
          title: Text('删除物料'),
          dense: true,
        ),
      ),
    ];
  }

  /// 获取综合状态颜色
  Color _getStatusColor() {
    if (material.isInstalled) {
      return VanHubColors.success;
    } else if (material.isPurchased) {
      return VanHubColors.primary;
    } else if (material.isOrdered) {
      return VanHubColors.warning;
    } else {
      return VanHubColors.textSecondary;
    }
  }

  /// 获取购买状态颜色
  Color _getPurchaseStatusColor() {
    switch (material.purchaseStatus) {
      case MaterialStatus.pending:
        return VanHubColors.textSecondary;
      case MaterialStatus.ordered:
        return VanHubColors.warning;
      case MaterialStatus.received:
        return VanHubColors.primary;
      case MaterialStatus.installed:
        return VanHubColors.success;
      case MaterialStatus.returned:
        return VanHubColors.error;
    }
  }

  /// 获取安装状态颜色
  Color _getInstallStatusColor() {
    switch (material.installStatus) {
      case MaterialStatus.pending:
        return VanHubColors.textSecondary;
      case MaterialStatus.ordered:
        return VanHubColors.warning;
      case MaterialStatus.received:
        return VanHubColors.primary;
      case MaterialStatus.installed:
        return VanHubColors.success;
      case MaterialStatus.returned:
        return VanHubColors.error;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 显示状态对话框
  void _showStatusDialog(BuildContext context) {
    if (onStatusChange != null) {
      showMaterialStatusDialog(
        context: context,
        material: material,
        onStatusChanged: onStatusChange!,
      );
    }
  }

  /// 处理菜单操作
  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'edit':
        onEdit?.call();
        break;
      case 'status':
        _showStatusDialog(context);
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }
}