import 'package:freezed_annotation/freezed_annotation.dart';
import 'bom_item.dart';

part 'tree_node.freezed.dart';
part 'tree_node.g.dart';

/// BOM树节点类型枚举
enum BomTreeNodeType {
  /// 分类节点
  category,
  /// 子分类节点
  subcategory,
  /// BOM项目节点
  bomItem,
}

/// BOM树形结构节点实体
/// 严格遵循Clean Architecture原则，定义BOM树形结构的节点数据
@freezed
class TreeNode with _$TreeNode {
  const factory TreeNode({
    /// 节点唯一标识符
    required String id,
    
    /// 节点显示标签
    required String label,
    
    /// 节点类型
    required BomTreeNodeType type,
    
    /// 子节点列表
    @Default([]) List<TreeNode> children,
    
    /// 是否展开
    @Default(false) bool isExpanded,
    
    /// 材料数量（用于分类节点统计）
    @Default(0) int materialCount,
    
    /// BOM项目数据（仅用于bomItem类型节点）
    BomItem? bomItem,
    
    /// 节点元数据
    @Default({}) Map<String, dynamic> metadata,
    
    /// 父节点ID（根节点为null）
    String? parentId,
    
    /// 树深度（根节点为0）
    @Default(0) int depth,
    
    /// 节点总价值（用于分类节点统计）
    @Default(0.0) double totalValue,
    
    /// 节点状态分布（用于分类节点统计）
    @Default({}) Map<String, int> statusDistribution,
  }) = _TreeNode;

  factory TreeNode.fromJson(Map<String, dynamic> json) => _$TreeNodeFromJson(json);
}

/// TreeNode业务方法扩展
extension TreeNodeX on TreeNode {
  /// 是否为叶子节点
  bool get isLeaf => children.isEmpty;
  
  /// 是否为根节点
  bool get isRoot => parentId == null;
  
  /// 是否为分类节点
  bool get isCategory => type == BomTreeNodeType.category;
  
  /// 是否为BOM项目节点
  bool get isBomItem => type == BomTreeNodeType.bomItem;
  
  /// 获取所有子BOM项目
  List<BomItem> get allBomItems {
    final List<BomItem> items = [];
    
    void collectBomItems(TreeNode node) {
      if (node.bomItem != null) {
        items.add(node.bomItem!);
      }
      for (final child in node.children) {
        collectBomItems(child);
      }
    }
    
    collectBomItems(this);
    return items;
  }
  
  /// 计算节点总价值
  double get calculatedTotalValue {
    if (bomItem != null) {
      return bomItem!.totalCost;
    }
    
    return children.fold(0.0, (sum, child) => sum + child.calculatedTotalValue);
  }
  
  /// 获取节点BOM项目数量
  int get bomItemCount {
    if (bomItem != null) {
      return 1;
    }
    
    return children.fold(0, (sum, child) => sum + child.bomItemCount);
  }
  
  /// 创建分类节点
  static TreeNode createCategoryNode({
    required String id,
    required String label,
    required int depth,
    List<TreeNode> children = const [],
    String? parentId,
  }) {
    return TreeNode(
      id: id,
      label: label,
      type: BomTreeNodeType.category,
      depth: depth,
      children: children,
      parentId: parentId,
      materialCount: children.fold(0, (sum, child) => sum + child.bomItemCount),
      totalValue: children.fold(0.0, (sum, child) => sum + child.calculatedTotalValue),
    );
  }
  
  /// 创建BOM项目节点
  static TreeNode createBomItemNode({
    required String id,
    required BomItem bomItem,
    required int depth,
    String? parentId,
  }) {
    return TreeNode(
      id: id,
      label: bomItem.materialName,
      type: BomTreeNodeType.bomItem,
      depth: depth,
      bomItem: bomItem,
      parentId: parentId,
      materialCount: 1,
      totalValue: bomItem.totalCost,
      metadata: {
        'bomItemId': bomItem.id,
        'materialId': bomItem.materialId,
        'materialName': bomItem.materialName,
        'brand': bomItem.effectiveMaterialBrand,
        'model': bomItem.effectiveMaterialModel,
        'quantity': bomItem.quantity,
        'unitPrice': bomItem.unitPrice,
        'totalPrice': bomItem.totalCost,
        'status': bomItem.status.code,
        'statusDisplay': bomItem.status.displayName,
        'category': bomItem.effectiveMaterialCategory,
        'notes': bomItem.notes,
        'createdAt': bomItem.createdAt.toIso8601String(),
        'updatedAt': bomItem.updatedAt.toIso8601String(),
      },
    );
  }
}
