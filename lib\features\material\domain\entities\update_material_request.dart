import 'package:freezed_annotation/freezed_annotation.dart';

part 'update_material_request.freezed.dart';
part 'update_material_request.g.dart';

/// 更新材料请求实体
/// 严格遵循Clean Architecture原则，Domain层纯Dart实体
@freezed
class UpdateMaterialRequest with _$UpdateMaterialRequest {
  const factory UpdateMaterialRequest({
    required String id,
    required String name,
    required String category,
    String? brand,
    String? model,
    String? specifications,
    double? price,
    String? supplier,
    DateTime? purchaseDate,
    String? description,
    String? imageUrl,
  }) = _UpdateMaterialRequest;

  factory UpdateMaterialRequest.fromJson(Map<String, dynamic> json) => 
      _$UpdateMaterialRequestFromJson(json);
}
