# VanHub Design System

> 专业的房车改装项目管理平台设计系统

## 📋 概述

VanHub Design System 是一个基于 Material Design 3 的完整设计系统，专为房车改装项目管理平台打造。它提供了一套统一、可复用、可扩展的 UI 组件库，确保整个应用的视觉一致性和用户体验。

## 🎯 设计原则

### 1. 一致性 (Consistency)
- 统一的视觉语言和交互模式
- 标准化的组件行为和状态
- 一致的颜色、字体和间距系统

### 2. 可访问性 (Accessibility)
- 符合 WCAG 2.1 AA 标准
- 支持屏幕阅读器和键盘导航
- 高对比度和大字体支持

### 3. 响应式 (Responsive)
- 5 断点响应式设计系统
- 移动优先的设计理念
- 跨设备一致的用户体验

### 4. 可扩展性 (Scalability)
- 模块化的组件架构
- 主题化和定制化支持
- 易于维护和扩展

## 🏗️ 架构设计

### 分层架构

```
Design System
├── Foundation (基础层)
│   ├── Colors (颜色系统)
│   ├── Typography (字体系统)
│   ├── Spacing (间距系统)
│   ├── Animations (动画系统)
│   └── Responsive (响应式系统)
├── Atoms (原子组件)
│   ├── VanHubButton
│   ├── VanHubInput
│   ├── VanHubAvatar
│   └── VanHubBadge
├── Molecules (分子组件)
│   ├── VanHubModal
│   ├── VanHubSearchBar
│   ├── VanHubCard
│   └── VanHubStates
├── Organisms (有机体组件)
│   ├── VanHubForm
│   ├── VanHubDataDisplay
│   ├── VanHubNavigation
│   └── VanHubDashboard
└── Templates (模板)
    ├── PageTemplates
    ├── DialogTemplates
    └── LayoutTemplates
```

## 🎨 基础系统

### 颜色系统

基于 Material Design 3 的动态颜色系统：

```dart
// 主色调
primary: Color(0xFF1976D2)
onPrimary: Color(0xFFFFFFFF)
primaryContainer: Color(0xFFD1E4FF)
onPrimaryContainer: Color(0xFF001D36)

// 次要色调
secondary: Color(0xFF535F70)
onSecondary: Color(0xFFFFFFFF)
secondaryContainer: Color(0xFFD7E3F7)
onSecondaryContainer: Color(0xFF101C2B)

// 表面色调
surface: Color(0xFFFDFCFF)
onSurface: Color(0xFF1A1C1E)
surfaceVariant: Color(0xFFDFE2EB)
onSurfaceVariant: Color(0xFF43474E)
```

### 字体系统

基于 Material Design 3 的字体规范：

```dart
// 显示字体
displayLarge: 57px / 64px
displayMedium: 45px / 52px
displaySmall: 36px / 44px

// 标题字体
headlineLarge: 32px / 40px
headlineMedium: 28px / 36px
headlineSmall: 24px / 32px

// 正文字体
bodyLarge: 16px / 24px
bodyMedium: 14px / 20px
bodySmall: 12px / 16px

// 标签字体
labelLarge: 14px / 20px
labelMedium: 12px / 16px
labelSmall: 11px / 16px
```

### 间距系统

基于 8px 网格的间距系统：

```dart
// 基础间距
xs: 4px
sm: 8px
md: 16px
lg: 24px
xl: 32px
xxl: 48px

// 组件间距
componentPadding: 16px
componentMargin: 8px
sectionSpacing: 24px
pageMargin: 16px
```

### 响应式断点

5 断点响应式系统：

```dart
xs: 0px - 575px    // 小手机
sm: 576px - 767px  // 大手机
md: 768px - 1023px // 平板
lg: 1024px - 1439px // 笔记本
xl: 1440px+        // 桌面
```

## 🧩 组件库

### Atoms (原子组件)

#### VanHubButton
- **用途**: 基础按钮组件
- **变体**: primary, secondary, outlined, text, icon
- **尺寸**: small, medium, large
- **状态**: enabled, disabled, loading, pressed

#### VanHubInput
- **用途**: 基础输入组件
- **类型**: text, password, email, number, search
- **状态**: normal, focused, error, disabled
- **功能**: validation, helper text, prefix/suffix icons

#### VanHubAvatar
- **用途**: 用户头像组件
- **尺寸**: xs(24), sm(32), md(40), lg(48), xl(56), xxl(64)
- **类型**: image, initials, icon
- **功能**: online status, click interaction

#### VanHubBadge
- **用途**: 徽章指示器组件
- **类型**: number, dot, text
- **颜色**: primary, secondary, success, warning, error
- **位置**: topRight, topLeft, bottomRight, bottomLeft

### Molecules (分子组件)

#### VanHubModal
- **用途**: 模态对话框组件
- **尺寸**: sm(320), md(480), lg(640), xl(800), fullscreen
- **动画**: fade, slide, scale
- **功能**: keyboard navigation, focus management

#### VanHubSearchBar
- **用途**: 搜索栏组件
- **功能**: real-time search, history, suggestions, filters
- **特性**: debounce, highlight, voice search
- **存储**: local search history

#### VanHubCard
- **用途**: 卡片容器组件
- **变体**: elevated, filled, outlined
- **布局**: header, content, actions
- **交互**: hover, press, selection

### Organisms (有机体组件)

#### VanHubForm
- **用途**: 表单容器组件
- **功能**: validation, auto-save, dynamic fields
- **特性**: field dependencies, JSON configuration
- **无障碍**: semantic labels, keyboard navigation

#### VanHubDataDisplay
- **用途**: 数据展示组件
- **类型**: table, grid, list, cards
- **功能**: sorting, filtering, pagination, export
- **响应式**: mobile card layout, desktop table

## 📱 响应式设计

### 断点策略

- **xs (0-575px)**: 单列布局，最小化UI元素
- **sm (576-767px)**: 双列布局，触摸优化
- **md (768-1023px)**: 多列布局，平衡设计
- **lg (1024-1439px)**: 桌面布局，丰富交互
- **xl (1440px+)**: 宽屏布局，最大化利用空间

### 组件适配

每个组件都支持响应式属性：

```dart
// 响应式间距
padding: context.responsiveValue(
  xs: 8,
  sm: 12,
  md: 16,
  lg: 20,
  xl: 24,
  defaultValue: 16,
)

// 响应式字体
fontSize: context.responsiveValue(
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  defaultValue: 16,
)
```

## 🎭 主题系统

### 动态主题

支持 Material Design 3 的动态颜色系统：

```dart
// 浅色主题
lightTheme: ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: VanHubColors.primary,
    brightness: Brightness.light,
  ),
)

// 深色主题
darkTheme: ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: VanHubColors.primary,
    brightness: Brightness.dark,
  ),
)
```

### 自定义主题

支持品牌色彩定制：

```dart
// 自定义主题
customTheme: ThemeData.from(
  colorScheme: ColorScheme.fromSeed(
    seedColor: customBrandColor,
  ),
  textTheme: VanHubTypography.textTheme,
)
```

## ♿ 无障碍支持

### 语义化标签

所有组件都包含完整的语义化标签：

```dart
Semantics(
  label: '添加材料按钮',
  hint: '点击打开添加材料对话框',
  button: true,
  child: VanHubButton(...),
)
```

### 键盘导航

支持完整的键盘导航：

- Tab: 焦点移动
- Enter/Space: 激活按钮
- Escape: 关闭对话框
- Arrow Keys: 列表导航

### 屏幕阅读器

优化屏幕阅读器体验：

- 逻辑阅读顺序
- 状态变化通知
- 错误信息朗读
- 进度反馈

## 🚀 性能优化

### 懒加载

组件支持懒加载机制：

```dart
// 图片懒加载
VanHubImage.lazy(
  src: imageUrl,
  placeholder: VanHubShimmer(),
  errorWidget: VanHubErrorState(),
)
```

### 虚拟滚动

大列表使用虚拟滚动：

```dart
VanHubVirtualList(
  itemCount: items.length,
  itemBuilder: (context, index) => VanHubCard(...),
  cacheExtent: 1000,
)
```

### 动画优化

高性能动画实现：

```dart
VanHubAnimatedWidget(
  duration: Duration(milliseconds: 200),
  curve: Curves.easeInOut,
  child: widget,
)
```

## 🌍 国际化支持

### 多语言

支持中英文双语：

```dart
// 中文
VanHubLocalizations.of(context).addMaterial

// 英文
VanHubLocalizations.of(context).addMaterial
```

### RTL 支持

支持从右到左的布局：

```dart
Directionality(
  textDirection: TextDirection.rtl,
  child: VanHubWidget(...),
)
```

## 📚 使用指南

### 安装

```dart
dependencies:
  vanhub_design_system:
    path: lib/core/design_system
```

### 导入

```dart
import 'package:vanhub/core/design_system/design_system.dart';
```

### 基础使用

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return VanHubCard(
      child: Column(
        children: [
          VanHubButton.primary(
            label: '添加材料',
            onPressed: () {},
          ),
          VanHubInput(
            label: '材料名称',
            validator: VanHubValidators.required,
          ),
        ],
      ),
    );
  }
}
```

## 🧪 测试

### 单元测试

每个组件都有完整的单元测试：

```dart
testWidgets('VanHubButton should render correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: VanHubButton.primary(
        label: 'Test',
        onPressed: () {},
      ),
    ),
  );
  
  expect(find.text('Test'), findsOneWidget);
});
```

### 集成测试

关键用户流程的集成测试：

```dart
testWidgets('Material creation flow', (tester) async {
  // 测试完整的材料创建流程
});
```

## 📖 更新日志

### v1.0.0 (2025-01-28)
- 初始版本发布
- 完整的基础组件库
- 响应式设计系统
- 无障碍支持
- 国际化支持

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

### 代码规范

- 遵循 Dart 官方代码规范
- 使用 `flutter analyze` 检查代码质量
- 确保测试覆盖率 > 90%
- 编写清晰的文档注释

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**VanHub Design System** - 让房车改装项目管理更加专业和高效
