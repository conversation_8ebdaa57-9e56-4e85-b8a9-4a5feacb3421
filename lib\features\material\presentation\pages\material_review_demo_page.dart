import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/material_review.dart';
import '../widgets/material_review_card_widget.dart';
import '../widgets/material_review_summary_widget.dart';
import '../widgets/write_review_dialog_widget.dart';
import 'material_reviews_page.dart';

/// 材料评价功能演示页面
/// 展示评论功能的完整实现，帮助用户选择合适的物料
class MaterialReviewDemoPage extends ConsumerWidget {
  const MaterialReviewDemoPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('材料评价功能演示'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能介绍
            _buildIntroduction(context),
            
            // 评价摘要演示
            _buildSummaryDemo(context),
            
            // 评价卡片演示
            _buildReviewCardsDemo(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduction(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🗣️ VanHub专业评价系统',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '帮助用户基于真实使用经验选择合适的房车改装材料',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          
          const SizedBox(height: 12),
          
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              _buildFeatureChip('多维度评分', Icons.star),
              _buildFeatureChip('使用场景标签', Icons.label),
              _buildFeatureChip('验证购买', Icons.verified),
              _buildFeatureChip('图片展示', Icons.photo),
              _buildFeatureChip('专业建议', Icons.lightbulb),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(String label, IconData icon) {
    return Chip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      backgroundColor: Colors.white.withValues(alpha: 0.8),
      labelStyle: const TextStyle(fontSize: 12),
    );
  }

  Widget _buildSummaryDemo(BuildContext context) {
    final demoSummary = MaterialReviewSummary(
      materialId: 'demo-material-1',
      totalReviews: 24,
      averageRating: 4.3,
      qualityAverage: 4.5,
      valueAverage: 4.1,
      durabilityAverage: 4.4,
      installationAverage: 3.8,
      ratingDistribution: {
        5: 12,
        4: 8,
        3: 3,
        2: 1,
        1: 0,
      },
      verifiedPurchaseCount: 18,
      topPros: ['质量优秀', '性价比高', '安装简单'],
      topCons: ['价格偏高', '颜色选择少'],
      commonTips: ['建议配合专用工具使用', '注意防水处理'],
      latestReviewDate: DateTime.now().subtract(const Duration(days: 2)),
      recommendationScore: 4.3,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            '评价摘要组件',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        MaterialReviewSummaryWidget(
          summary: demoSummary,
          onViewAllReviews: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const MaterialReviewsPage(
                  materialId: 'demo-material-1',
                  materialName: '演示材料',
                ),
              ),
            );
          },
          onWriteReview: () {
            showDialog(
              context: context,
              builder: (context) => const WriteReviewDialogWidget(
                materialId: 'demo-material-1',
                materialName: '演示材料',
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildReviewCardsDemo(BuildContext context) {
    final demoReviews = [
      MaterialReview(
        id: 'review-1',
        materialId: 'demo-material-1',
        userId: 'user-1',
        userName: '改装达人小王',
        userAvatarUrl: null,
        content: '这个材料真的很不错！质量很好，安装也比较简单。我用在了我的依维柯房车上，效果很满意。特别是防水性能，经过几次大雨测试都没有问题。',
        rating: 4.5,
        qualityRating: 5.0,
        valueRating: 4.0,
        durabilityRating: 4.5,
        installationRating: 4.0,
        vehicleType: '依维柯Daily',
        systemType: '电气系统',
        usageDuration: '使用3个月',
        pros: ['质量优秀', '防水性能好', '安装简单'],
        cons: ['价格稍高'],
        tips: ['建议配合专用密封胶使用', '安装前仔细阅读说明书'],
        isVerifiedPurchase: true,
        purchaseDate: DateTime.now().subtract(const Duration(days: 90)),
        imageUrls: [],
        videoUrls: [],
        likedByUserIds: ['user-2', 'user-3'],
        helpfulUserIds: ['user-2', 'user-3', 'user-4'],
        helpfulCount: 3,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      
      MaterialReview(
        id: 'review-2',
        materialId: 'demo-material-1',
        userId: 'user-2',
        userName: '房车新手',
        userAvatarUrl: null,
        content: '作为新手，这个材料对我来说安装有点困难，但是质量确实不错。客服很耐心地指导了我安装过程。',
        rating: 4.0,
        qualityRating: 4.5,
        valueRating: 3.5,
        durabilityRating: 4.0,
        installationRating: 3.0,
        vehicleType: '福特全顺',
        systemType: '水路系统',
        usageDuration: '使用1个月',
        pros: ['质量不错', '客服服务好'],
        cons: ['安装有难度', '说明书不够详细'],
        tips: ['建议找专业师傅安装'],
        isVerifiedPurchase: true,
        purchaseDate: DateTime.now().subtract(const Duration(days: 30)),
        imageUrls: [],
        videoUrls: [],
        likedByUserIds: ['user-1'],
        helpfulUserIds: ['user-1', 'user-3'],
        helpfulCount: 2,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      
      MaterialReview(
        id: 'review-3',
        materialId: 'demo-material-1',
        userId: 'user-3',
        userName: '老司机阿强',
        userAvatarUrl: null,
        content: '用过很多品牌的类似产品，这个算是性价比比较高的。做工精细，材质也很好。',
        rating: 4.2,
        qualityRating: 4.0,
        valueRating: 4.5,
        durabilityRating: 4.0,
        installationRating: 4.0,
        vehicleType: '大通V80',
        systemType: '储物系统',
        usageDuration: '使用6个月',
        pros: ['性价比高', '做工精细'],
        cons: ['颜色选择少'],
        tips: ['可以配合其他配件一起使用效果更好'],
        isVerifiedPurchase: false,
        imageUrls: [],
        videoUrls: [],
        likedByUserIds: [],
        helpfulUserIds: ['user-1'],
        helpfulCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            '用户评价列表',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        ...demoReviews.map((review) => MaterialReviewCardWidget(
          review: review,
          onLike: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('点赞评价: ${review.userName}')),
            );
          },
          onMarkHelpful: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('标记有用: ${review.userName}')),
            );
          },
          onReport: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('举报评价: ${review.userName}')),
            );
          },
        )),
        
        const SizedBox(height: 20),
      ],
    );
  }
}
