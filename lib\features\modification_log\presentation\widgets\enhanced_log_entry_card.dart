import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';
import '../../../bom/presentation/providers/bom_provider.dart';
import '../../../bom/domain/entities/bom_item.dart';

/// 增强的改装日志卡片 - 显示关联物料和成本信息
class EnhancedLogEntryCard extends ConsumerWidget {
  final LogEntry log;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const EnhancedLogEntryCard({
    super.key,
    required this.log,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态行
              _buildHeaderRow(context),
              
              const SizedBox(height: 8),
              
              // 内容预览
              _buildContentPreview(context),
              
              const SizedBox(height: 12),
              
              // 关联物料信息
              _buildMaterialsSection(context, ref),
              
              const SizedBox(height: 12),
              
              // 底部信息行
              _buildFooterRow(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建标题和状态行
  Widget _buildHeaderRow(BuildContext context) {
    return Row(
      children: [
        // 状态指示器
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: _getStatusColor(log.status),
            shape: BoxShape.circle,
          ),
        ),
        
        const SizedBox(width: 8),
        
        // 标题
        Expanded(
          child: Text(
            log.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        // 里程碑标记
        if (log.isMilestone) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.flag,
                  size: 12,
                  color: Colors.orange.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '里程碑',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.orange.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // 操作菜单
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEdit?.call();
                break;
              case 'delete':
                onDelete?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: const Icon(Icons.more_vert, size: 20),
        ),
      ],
    );
  }

  /// 构建内容预览
  Widget _buildContentPreview(BuildContext context) {
    // 移除HTML标签，获取纯文本预览
    final plainText = _stripHtmlTags(log.content);
    
    if (plainText.isEmpty) {
      return const Text(
        '暂无内容',
        style: TextStyle(
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      plainText,
      style: const TextStyle(
        fontSize: 14,
        color: Colors.grey,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建关联物料信息
  Widget _buildMaterialsSection(BuildContext context, WidgetRef ref) {
    if (log.relatedBomItemIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.inventory,
                size: 16,
                color: Colors.blue.shade700,
              ),
              const SizedBox(width: 4),
              Text(
                '关联物料 (${log.relatedBomItemIds.length})',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 显示关联的物料信息
          _buildMaterialsList(context, ref),
        ],
      ),
    );
  }

  /// 构建物料列表
  Widget _buildMaterialsList(BuildContext context, WidgetRef ref) {
    // 这里应该根据relatedBomItemIds获取具体的BOM物料信息
    // 为了简化，先显示基本信息
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: log.relatedBomItemIds.take(3).map((itemId) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade300),
          ),
          child: Text(
            '物料 ${itemId.substring(0, 8)}...',
            style: TextStyle(
              fontSize: 10,
              color: Colors.blue.shade700,
            ),
          ),
        );
      }).toList()
        ..addAll([
          if (log.relatedBomItemIds.length > 3)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '+${log.relatedBomItemIds.length - 3}',
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
              ),
            ),
        ]),
    );
  }

  /// 构建底部信息行
  Widget _buildFooterRow(BuildContext context) {
    return Row(
      children: [
        // 日期
        Icon(
          Icons.calendar_today,
          size: 14,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          _formatDate(log.logDate),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        
        // 耗时
        if (log.timeSpentMinutes > 0) ...[
          const SizedBox(width: 16),
          Icon(
            Icons.access_time,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            _formatDuration(log.timeSpentMinutes),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
        
        // 难度
        const SizedBox(width: 16),
        Icon(
          Icons.star,
          size: 14,
          color: _getDifficultyColor(log.difficulty),
        ),
        const SizedBox(width: 4),
        Text(
          _getDifficultyText(log.difficulty),
          style: TextStyle(
            fontSize: 12,
            color: _getDifficultyColor(log.difficulty),
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const Spacer(),
        
        // 成本信息
        if (log.totalCost > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.attach_money,
                  size: 12,
                  color: Colors.green.shade700,
                ),
                Text(
                  '¥${log.totalCost.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // 辅助方法
  Color _getStatusColor(LogStatus status) {
    switch (status) {
      case LogStatus.draft:
        return Colors.grey;
      case LogStatus.published:
        return Colors.green;
      case LogStatus.archived:
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
      case DifficultyLevel.expert:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getDifficultyText(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return '入门';
      case DifficultyLevel.intermediate:
        return '中级';
      case DifficultyLevel.advanced:
        return '高级';
      case DifficultyLevel.expert:
        return '专家';
      default:
        return '未知';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}小时';
      } else {
        return '${hours}小时${remainingMinutes}分钟';
      }
    }
  }

  String _stripHtmlTags(String html) {
    // 简单的HTML标签移除
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .trim();
  }
}
