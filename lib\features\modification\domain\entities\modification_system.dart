import 'package:freezed_annotation/freezed_annotation.dart';
import 'system_material.dart';
import '../../../modification_log/domain/entities/enums.dart';

part 'modification_system.freezed.dart';
part 'modification_system.g.dart';

/// 改装系统实体
/// 代表项目中的一个改装系统（如电路系统、水路系统等）
@freezed
class ModificationSystem with _$ModificationSystem {
  const factory ModificationSystem({
    /// 系统唯一标识
    required String id,
    
    /// 系统名称
    required String name,
    
    /// 系统类型
    required SystemType type,
    
    /// 系统描述
    String? description,
    
    /// 预算金额
    required double budgetAmount,
    
    /// 实际金额
    @Default(0.0) double actualAmount,
    
    /// 系统下的材料列表
    @Default([]) List<SystemMaterial> materials,
    
    /// 系统状态
    @Default(SystemStatus.planning) SystemStatus status,
    
    /// 排序顺序
    @Default(0) int sortOrder,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 系统图标（可选，用于自定义系统）
    String? iconName,
    
    /// 系统颜色（可选，用于自定义系统）
    String? colorHex,
    
    /// 系统备注
    String? notes,
  }) = _ModificationSystem;

  factory ModificationSystem.fromJson(Map<String, dynamic> json) =>
      _$ModificationSystemFromJson(json);
}



/// ModificationSystem扩展方法
extension ModificationSystemX on ModificationSystem {
  /// 系统是否已完成
  bool get isCompleted {
    return status == SystemStatus.completed;
  }
  
  /// 计算系统完成百分比
  double get completionPercentage {
    if (materials.isEmpty) return 0.0;
    
    final completedMaterials = materials.where((m) => m.isInstalled).length;
    return completedMaterials / materials.length;
  }
  
  /// 获取材料总数量
  int get totalMaterialCount {
    return materials.length;
  }
  
  /// 获取已完成材料数量
  int get completedMaterialCount {
    return materials.where((m) => m.isInstalled).length;
  }
  
  /// 获取预算差额（正数表示超支，负数表示节省）
  double get budgetDifference {
    return actualAmount - budgetAmount;
  }
  
  /// 是否超出预算
  bool get isOverBudget {
    return budgetDifference > 0;
  }
  
  /// 获取系统类型显示名称
  String get typeDisplayName {
    switch (type) {
      case SystemType.electrical:
        return '电路系统';
      case SystemType.plumbing:
        return '水路系统';
      case SystemType.storage:
        return '储物系统';
      case SystemType.bedding:
        return '床铺系统';
      case SystemType.kitchen:
        return '厨房系统';
      case SystemType.bathroom:
        return '卫浴系统';
      case SystemType.exterior:
        return '外观改装';
      case SystemType.chassis:
        return '底盘改装';
      case SystemType.custom:
        return '自定义系统';
    }
  }
  
  /// 获取状态显示文本
  String get statusDisplayText {
    switch (status) {
      case SystemStatus.planning:
        return '规划中';
      case SystemStatus.purchasing:
        return '采购中';
      case SystemStatus.installing:
        return '安装中';
      case SystemStatus.completed:
        return '已完成';
      case SystemStatus.paused:
        return '暂停';
    }
  }
  
  /// 获取进度描述
  String get progressDescription {
    final percentage = (completionPercentage * 100).round();
    return '$percentage% ($completedMaterialCount/$totalMaterialCount)';
  }
  
  /// 获取按状态分组的材料统计
  Map<MaterialStatus, int> get materialCountByStatus {
    final Map<MaterialStatus, int> result = {};
    
    for (final material in materials) {
      final purchaseStatus = material.purchaseStatus;
      result[purchaseStatus] = (result[purchaseStatus] ?? 0) + 1;
    }
    
    return result;
  }
  
  /// 获取材料总价值
  double get totalMaterialValue {
    return materials.fold(0.0, (sum, material) => sum + material.totalPrice);
  }
  
  /// 更新实际金额（基于材料总价值）
  ModificationSystem updateActualAmount() {
    return copyWith(actualAmount: totalMaterialValue);
  }
}