import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/components/molecules/vanhub_modal.dart';

void main() {
  group('VanHubModal', () {
    testWidgets('应该显示模态框', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    title: '测试标题',
                    content: const Text('测试内容'),
                  );
                },
                child: const Text('显示模态框'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('显示模态框'));
      await tester.pumpAndSettle();

      expect(find.text('测试标题'), findsOneWidget);
      expect(find.text('测试内容'), findsOneWidget);
    });

    testWidgets('应该支持不同尺寸', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  ElevatedButton(
                    onPressed: () {
                      VanHubModal.show(
                        context: context,
                        size: VanHubModalSize.sm,
                        title: '小尺寸',
                        content: const Text('内容'),
                      );
                    },
                    child: const Text('小尺寸'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      VanHubModal.show(
                        context: context,
                        size: VanHubModalSize.lg,
                        title: '大尺寸',
                        content: const Text('内容'),
                      );
                    },
                    child: const Text('大尺寸'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 测试小尺寸
      await tester.tap(find.text('小尺寸'));
      await tester.pumpAndSettle();
      expect(find.text('小尺寸'), findsOneWidget);
      
      // 关闭模态框
      await tester.tapAt(const Offset(50, 50));
      await tester.pumpAndSettle();

      // 测试大尺寸
      await tester.tap(find.text('大尺寸'));
      await tester.pumpAndSettle();
      expect(find.text('大尺寸'), findsOneWidget);
    });

    testWidgets('应该支持不同动画类型', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    animationType: VanHubModalAnimation.slide,
                    title: '滑动动画',
                    content: const Text('内容'),
                  );
                },
                child: const Text('滑动动画'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('滑动动画'));
      await tester.pumpAndSettle();

      expect(find.text('滑动动画'), findsOneWidget);
    });

    testWidgets('应该支持自定义操作按钮', (WidgetTester tester) async {
      bool confirmPressed = false;
      bool cancelPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    title: '确认操作',
                    content: const Text('确定要执行此操作吗？'),
                    actions: [
                      TextButton(
                        onPressed: () {
                          cancelPressed = true;
                          Navigator.of(context).pop();
                        },
                        child: const Text('取消'),
                      ),
                      FilledButton(
                        onPressed: () {
                          confirmPressed = true;
                          Navigator.of(context).pop();
                        },
                        child: const Text('确认'),
                      ),
                    ],
                  );
                },
                child: const Text('显示确认框'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('显示确认框'));
      await tester.pumpAndSettle();

      expect(find.text('确认操作'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确认'), findsOneWidget);

      // 测试确认按钮
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();
      expect(confirmPressed, isTrue);
    });

    testWidgets('应该支持键盘导航', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    title: '键盘导航测试',
                    content: const Text('按ESC键关闭'),
                  );
                },
                child: const Text('显示模态框'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('显示模态框'));
      await tester.pumpAndSettle();

      expect(find.text('键盘导航测试'), findsOneWidget);

      // 模拟ESC键
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      expect(find.text('键盘导航测试'), findsNothing);
    });

    testWidgets('应该支持响应式设计', (WidgetTester tester) async {
      // 模拟不同屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(400, 800)); // 手机
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    title: '响应式测试',
                    content: const Text('手机端显示'),
                  );
                },
                child: const Text('显示模态框'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('显示模态框'));
      await tester.pumpAndSettle();

      expect(find.text('响应式测试'), findsOneWidget);
      
      // 关闭模态框
      await tester.tapAt(const Offset(50, 50));
      await tester.pumpAndSettle();

      // 模拟桌面端
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      
      await tester.tap(find.text('显示模态框'));
      await tester.pumpAndSettle();

      expect(find.text('响应式测试'), findsOneWidget);
    });
  });
}