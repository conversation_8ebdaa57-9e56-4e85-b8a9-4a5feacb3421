import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/fork_request.dart';
import '../entities/fork_settings.dart';

/// 复刻权限服务接口
/// 遵循Clean Architecture原则，定义项目复刻权限验证的业务逻辑
abstract class ForkPermissionService {
  /// 检查用户是否可以复刻指定项目
  /// 
  /// 验证以下条件：
  /// - 项目是否允许复刻
  /// - 用户是否有访问权限
  /// - 项目可见性设置
  /// - 用户权限级别
  Future<Either<Failure, bool>> canForkProject(String projectId, String userId);
  
  /// 检查项目是否为公开项目
  /// 
  /// 验证项目的可见性设置
  Future<Either<Failure, bool>> isProjectPublic(String projectId);
  
  /// 检查用户是否有项目访问权限
  /// 
  /// 验证用户对项目的访问权限，包括：
  /// - 项目所有者
  /// - 协作者权限
  /// - 公开项目访问
  Future<Either<Failure, bool>> hasUserAccess(String projectId, String userId);
  
  /// 检查项目的复刻设置
  /// 
  /// 获取项目的复刻配置信息
  Future<Either<Failure, ForkSettings>> getProjectForkSettings(String projectId);
  
  /// 验证复刻请求的合法性
  /// 
  /// 验证复刻请求是否符合项目设置和权限要求
  Future<Either<Failure, bool>> validateForkRequest(
    String projectId, 
    String userId, 
    ForkRequest request,
  );
  
  /// 检查用户的复刻配额
  /// 
  /// 验证用户是否还可以创建更多复刻项目
  Future<Either<Failure, bool>> checkUserForkQuota(String userId);
  
  /// 记录复刻权限检查日志
  /// 
  /// 记录权限验证的审计日志
  Future<Either<Failure, void>> logPermissionCheck(
    String projectId,
    String userId,
    String action,
    bool granted,
    String? reason,
  );
}
