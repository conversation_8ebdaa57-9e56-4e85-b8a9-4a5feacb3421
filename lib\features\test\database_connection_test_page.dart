import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/utils/database_connection_test.dart';
import '../../core/api/supabase_config.dart';

/// 数据库连接测试页面
class DatabaseConnectionTestPage extends StatefulWidget {
  const DatabaseConnectionTestPage({super.key});

  @override
  State<DatabaseConnectionTestPage> createState() => _DatabaseConnectionTestPageState();
}

class _DatabaseConnectionTestPageState extends State<DatabaseConnectionTestPage> {
  DatabaseTestResult? _testResult;
  bool _isLoading = false;
  String _statusMessage = '点击"开始测试"按钮检查数据库连接状态';

  @override
  void initState() {
    super.initState();
    // 页面加载时自动执行快速检查
    _performQuickCheck();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据库连接测试'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearCache,
            icon: const Icon(Icons.refresh),
            tooltip: '清除缓存',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态卡片
            _buildStatusCard(),
            
            const SizedBox(height: 20),
            
            // 操作按钮
            _buildActionButtons(),
            
            const SizedBox(height: 20),
            
            // 配置信息
            _buildConfigurationInfo(),
            
            const SizedBox(height: 20),
            
            // 测试结果
            if (_testResult != null) _buildTestResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (_isLoading) {
      statusColor = Colors.blue;
      statusIcon = Icons.hourglass_empty;
      statusText = '正在测试...';
    } else if (_testResult?.isHealthy == true) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = '连接正常';
    } else if (_testResult?.success == true) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
      statusText = '部分功能异常';
    } else if (_testResult != null) {
      statusColor = Colors.red;
      statusIcon = Icons.error;
      statusText = '连接失败';
    } else {
      statusColor = Colors.grey;
      statusIcon = Icons.help_outline;
      statusText = '未测试';
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              statusIcon,
              color: statusColor,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _statusMessage,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (_testResult != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '测试耗时: ${_testResult!.duration}ms',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _performFullTest,
            icon: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(_isLoading ? '测试中...' : '开始测试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepOrange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : _performQuickCheck,
            icon: const Icon(Icons.speed),
            label: const Text('快速检查'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfigurationInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '配置信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('初始化状态', SupabaseConfig.isInitialized ? '✅ 已初始化' : '❌ 未初始化'),
            _buildInfoRow('Supabase URL', SupabaseConfig.supabaseUrl.isNotEmpty 
                ? '${SupabaseConfig.supabaseUrl.substring(0, 30)}...' 
                : '❌ 未配置'),
            _buildInfoRow('API Key', SupabaseConfig.supabaseAnonKey.isNotEmpty 
                ? '${SupabaseConfig.supabaseAnonKey.substring(0, 20)}...' 
                : '❌ 未配置'),
            _buildInfoRow('环境', 'development'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '测试结果',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    TextButton.icon(
                      onPressed: _copyReport,
                      icon: const Icon(Icons.copy, size: 16),
                      label: const Text('复制报告'),
                    ),
                    TextButton.icon(
                      onPressed: _shareReport,
                      icon: const Icon(Icons.share, size: 16),
                      label: const Text('分享'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 测试结果概览
            _buildResultOverview(),
            
            const SizedBox(height: 16),
            
            // 详细结果
            _buildDetailedResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildResultOverview() {
    final result = _testResult!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: result.isHealthy ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: result.isHealthy ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.isHealthy ? Icons.check_circle : Icons.error,
                color: result.isHealthy ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                result.healthStatus,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: result.isHealthy ? Colors.green.shade700 : Colors.red.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '测试耗时: ${result.duration}ms',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          if (result.error != null) ...[
            const SizedBox(height: 8),
            Text(
              '错误: ${result.error}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red.shade700,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailedResults() {
    final result = _testResult!;
    
    return Column(
      children: result.results.entries.map((entry) {
        final category = entry.key;
        final data = entry.value;
        
        return ExpansionTile(
          title: Text(
            _getCategoryDisplayName(category),
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          leading: Icon(
            _getCategoryIcon(category),
            color: _getCategoryColor(category, data),
          ),
          children: [
            if (data is Map<String, dynamic>)
              ...data.entries.map((item) => ListTile(
                dense: true,
                title: Text(item.key),
                trailing: Text(
                  item.value.toString(),
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              )),
          ],
        );
      }).toList(),
    );
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'initialization': return '初始化检查';
      case 'configuration': return '配置检查';
      case 'network': return '网络连接';
      case 'database': return '数据库访问';
      case 'auth': return '认证系统';
      default: return category;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'initialization': return Icons.settings;
      case 'configuration': return Icons.tune;
      case 'network': return Icons.wifi;
      case 'database': return Icons.storage;
      case 'auth': return Icons.security;
      default: return Icons.info;
    }
  }

  Color _getCategoryColor(String category, dynamic data) {
    if (data is Map<String, dynamic>) {
      switch (category) {
        case 'initialization':
          return data['isInitialized'] == true ? Colors.green : Colors.red;
        case 'configuration':
          return (data['hasUrl'] == true && data['hasKey'] == true) ? Colors.green : Colors.red;
        case 'network':
          return data['connected'] == true ? Colors.green : Colors.red;
        case 'database':
          return data['accessible'] == true ? Colors.green : Colors.orange;
        case 'auth':
          return data['authAvailable'] == true ? Colors.green : Colors.orange;
      }
    }
    return Colors.grey;
  }

  Future<void> _performFullTest() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在执行完整的数据库连接测试...';
    });

    try {
      final result = await DatabaseConnectionTest.runFullTest();
      setState(() {
        _testResult = result;
        _statusMessage = result.isHealthy 
            ? '所有测试通过，数据库连接正常'
            : result.success 
                ? '测试完成，但发现一些问题'
                : '测试失败，请检查配置和网络连接';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '测试过程中发生错误: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _performQuickCheck() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在执行快速连接检查...';
    });

    try {
      final isConnected = await DatabaseConnectionTest.quickCheck();
      setState(() {
        _statusMessage = isConnected 
            ? '快速检查通过，数据库连接正常'
            : '快速检查失败，建议执行完整测试';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '快速检查失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearCache() {
    SupabaseConfig.clearConnectionCache();
    setState(() {
      _testResult = null;
      _statusMessage = '缓存已清除，点击"开始测试"重新检查';
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('连接缓存已清除'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _copyReport() {
    if (_testResult != null) {
      final report = DatabaseConnectionTest.generateReport(_testResult!);
      Clipboard.setData(ClipboardData(text: report));
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('测试报告已复制到剪贴板'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _shareReport() {
    if (_testResult != null) {
      final report = DatabaseConnectionTest.generateReport(_testResult!);
      // TODO: 实现分享功能
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分享功能开发中...'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }
}