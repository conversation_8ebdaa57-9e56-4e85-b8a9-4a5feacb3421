/// VanHub Button Component 2.0
/// 
/// 国际化高端按钮组件，支持完整动画和响应式设计
/// 
/// 特性：
/// - 8种变体：primary, secondary, tertiary, ghost, outline, danger, success, gradient
/// - 4种尺寸：xs, sm, md, lg, xl
/// - 完整动画：hover, press, loading, disabled, focus
/// - 深度响应式适配：移动端/平板/桌面
/// - 情感化设计：支持情感状态映射
/// - 无障碍访问：键盘导航、屏幕阅读器
/// - 60FPS流畅动画保证

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/typography/responsive_text.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import '../utils/responsive_utils.dart';

/// 按钮变体枚举
enum VanHubButtonVariant {
  primary,    // 主要按钮 - 品牌色
  secondary,  // 次要按钮 - 次要色
  tertiary,   // 第三级按钮 - 中性色
  ghost,      // 幽灵按钮 - 透明背景
  outline,    // 轮廓按钮 - 边框样式
  danger,     // 危险按钮 - 错误色
  success,    // 成功按钮 - 成功色
  gradient,   // 渐变按钮 - 渐变背景
}

/// 按钮尺寸枚举
enum VanHubButtonSize {
  xs,  // 超小 - 28px
  sm,  // 小 - 32px
  md,  // 中等 - 40px
  lg,  // 大 - 48px
  xl,  // 超大 - 56px
}

/// 按钮尺寸配置
class VanHubButtonSizeConfig {
  final double height;
  final EdgeInsets padding;
  final double fontSize;
  final double iconSize;
  final double borderRadius;

  const VanHubButtonSizeConfig({
    required this.height,
    required this.padding,
    required this.fontSize,
    required this.iconSize,
    required this.borderRadius,
  });

  /// 获取响应式尺寸配置
  static VanHubButtonSizeConfig getConfig(
    BuildContext context,
    VanHubButtonSize size,
  ) {
    final isDesktop = VanHubResponsiveUtils.isDesktop(context);
    final multiplier = isDesktop ? 1.1 : 1.0;

    switch (size) {
      case VanHubButtonSize.xs:
        return VanHubButtonSizeConfig(
          height: 28 * multiplier,
          padding: EdgeInsets.symmetric(
            horizontal: VanHubResponsiveSpacing.sm * multiplier,
            vertical: VanHubResponsiveSpacing.xs * multiplier,
          ),
          fontSize: 12 * multiplier,
          iconSize: 14 * multiplier,
          borderRadius: 6 * multiplier,
        );
      case VanHubButtonSize.sm:
        return VanHubButtonSizeConfig(
          height: 32 * multiplier,
          padding: EdgeInsets.symmetric(
            horizontal: VanHubResponsiveSpacing.md * multiplier,
            vertical: VanHubResponsiveSpacing.sm * multiplier,
          ),
          fontSize: 14 * multiplier,
          iconSize: 16 * multiplier,
          borderRadius: 8 * multiplier,
        );
      case VanHubButtonSize.md:
        return VanHubButtonSizeConfig(
          height: 40 * multiplier,
          padding: EdgeInsets.symmetric(
            horizontal: VanHubResponsiveSpacing.lg * multiplier,
            vertical: VanHubResponsiveSpacing.md * multiplier,
          ),
          fontSize: 16 * multiplier,
          iconSize: 18 * multiplier,
          borderRadius: 10 * multiplier,
        );
      case VanHubButtonSize.lg:
        return VanHubButtonSizeConfig(
          height: 48 * multiplier,
          padding: EdgeInsets.symmetric(
            horizontal: VanHubResponsiveSpacing.xl * multiplier,
            vertical: VanHubResponsiveSpacing.lg * multiplier,
          ),
          fontSize: 18 * multiplier,
          iconSize: 20 * multiplier,
          borderRadius: 12 * multiplier,
        );
      case VanHubButtonSize.xl:
        return VanHubButtonSizeConfig(
          height: 56 * multiplier,
          padding: EdgeInsets.symmetric(
            horizontal: VanHubResponsiveSpacing.xxl * multiplier,
            vertical: VanHubResponsiveSpacing.xl * multiplier,
          ),
          fontSize: 20 * multiplier,
          iconSize: 22 * multiplier,
          borderRadius: 14 * multiplier,
        );
    }
  }
}

/// 按钮颜色配置
class VanHubButtonColorConfig {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final Color hoverBackgroundColor;
  final Color pressedBackgroundColor;
  final Color disabledBackgroundColor;
  final Color disabledForegroundColor;
  final Gradient? gradient;

  const VanHubButtonColorConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
    required this.hoverBackgroundColor,
    required this.pressedBackgroundColor,
    required this.disabledBackgroundColor,
    required this.disabledForegroundColor,
    this.gradient,
  });

  /// 获取变体颜色配置
  static VanHubButtonColorConfig getConfig(
    BuildContext context,
    VanHubButtonVariant variant, {
    EmotionalState? emotionalState,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (variant) {
      case VanHubButtonVariant.primary:
        return VanHubButtonColorConfig(
          backgroundColor: emotionalState != null
              ? VanHubBrandColors.getEmotionalColor(emotionalState)
              : VanHubBrandColors.primary,
          foregroundColor: VanHubBrandColors.onPrimary,
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubBrandColors.primaryLight,
          pressedBackgroundColor: VanHubBrandColors.primaryDark,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
          gradient: emotionalState != null
              ? VanHubBrandColors.getEmotionalGradient(emotionalState)
              : null,
        );

      case VanHubButtonVariant.secondary:
        return VanHubButtonColorConfig(
          backgroundColor: VanHubBrandColors.secondary,
          foregroundColor: VanHubBrandColors.onSecondary,
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubBrandColors.secondaryLight,
          pressedBackgroundColor: VanHubBrandColors.secondaryDark,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
        );

      case VanHubButtonVariant.tertiary:
        return VanHubButtonColorConfig(
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
          foregroundColor: VanHubSemanticColors.getTextColor(context),
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 3),
          pressedBackgroundColor: VanHubSemanticColors.hoverOverlay,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
        );

      case VanHubButtonVariant.ghost:
        return VanHubButtonColorConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: VanHubBrandColors.primary,
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubSemanticColors.hoverOverlay,
          pressedBackgroundColor: VanHubSemanticColors.pressedOverlay,
          disabledBackgroundColor: Colors.transparent,
          disabledForegroundColor: VanHubSemanticColors.textDisabled,
        );

      case VanHubButtonVariant.outline:
        return VanHubButtonColorConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: VanHubBrandColors.primary,
          borderColor: VanHubBrandColors.primary,
          hoverBackgroundColor: VanHubBrandColors.primaryContainer,
          pressedBackgroundColor: VanHubSemanticColors.pressedOverlay,
          disabledBackgroundColor: Colors.transparent,
          disabledForegroundColor: VanHubSemanticColors.textDisabled,
        );

      case VanHubButtonVariant.danger:
        return VanHubButtonColorConfig(
          backgroundColor: VanHubSemanticColors.error,
          foregroundColor: VanHubSemanticColors.onError,
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubSemanticColors.errorLight,
          pressedBackgroundColor: VanHubSemanticColors.errorDark,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
        );

      case VanHubButtonVariant.success:
        return VanHubButtonColorConfig(
          backgroundColor: VanHubSemanticColors.success,
          foregroundColor: VanHubSemanticColors.onSuccess,
          borderColor: Colors.transparent,
          hoverBackgroundColor: VanHubSemanticColors.successLight,
          pressedBackgroundColor: VanHubSemanticColors.successDark,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
        );

      case VanHubButtonVariant.gradient:
        return VanHubButtonColorConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: VanHubBrandColors.onPrimary,
          borderColor: Colors.transparent,
          hoverBackgroundColor: Colors.transparent,
          pressedBackgroundColor: Colors.transparent,
          disabledBackgroundColor: VanHubSemanticColors.textDisabled,
          disabledForegroundColor: VanHubSemanticColors.textDisabledDark,
          gradient: emotionalState != null
              ? VanHubBrandColors.getEmotionalGradient(emotionalState)
              : VanHubBrandColors.primaryGradient,
        );
    }
  }
}

/// VanHub Button 2.0 - 高端按钮组件
class VanHubButtonV2 extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final VanHubButtonVariant variant;
  final VanHubButtonSize size;
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final bool isLoading;
  final bool isFullWidth;
  final EmotionalState? emotionalState;
  
  // 动画配置
  final bool enableHoverAnimation;
  final bool enablePressAnimation;
  final bool enableLoadingAnimation;
  final Duration animationDuration;
  final Curve animationCurve;
  
  // 响应式配置
  final VanHubButtonSize? mobileSize;
  final VanHubButtonSize? tabletSize;
  final VanHubButtonSize? desktopSize;

  const VanHubButtonV2({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = VanHubButtonVariant.primary,
    this.size = VanHubButtonSize.md,
    this.leadingIcon,
    this.trailingIcon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.emotionalState,
    this.enableHoverAnimation = true,
    this.enablePressAnimation = true,
    this.enableLoadingAnimation = true,
    this.animationDuration = VanHubAnimationDurations.fast,
    this.animationCurve = VanHubAnimationCurves.easeOut,
    this.mobileSize,
    this.tabletSize,
    this.desktopSize,
  }) : super(key: key);

  /// 主要按钮构造函数
  const VanHubButtonV2.primary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    VanHubButtonSize size = VanHubButtonSize.md,
    IconData? leadingIcon,
    IconData? trailingIcon,
    bool isLoading = false,
    bool isFullWidth = false,
    EmotionalState? emotionalState,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          variant: VanHubButtonVariant.primary,
          size: size,
          leadingIcon: leadingIcon,
          trailingIcon: trailingIcon,
          isLoading: isLoading,
          isFullWidth: isFullWidth,
          emotionalState: emotionalState,
        );

  /// 次要按钮构造函数
  const VanHubButtonV2.secondary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    VanHubButtonSize size = VanHubButtonSize.md,
    IconData? leadingIcon,
    IconData? trailingIcon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          variant: VanHubButtonVariant.secondary,
          size: size,
          leadingIcon: leadingIcon,
          trailingIcon: trailingIcon,
          isLoading: isLoading,
          isFullWidth: isFullWidth,
        );

  @override
  State<VanHubButtonV2> createState() => _VanHubButtonV2State();
}

/// VanHub Button 2.0 状态类
class _VanHubButtonV2State extends State<VanHubButtonV2>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late AnimationController _loadingController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  bool _isHovered = false;
  bool _isPressed = false;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _hoverController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _pressController = AnimationController(
      duration: VanHubAnimationDurations.ultraFast,
      vsync: this,
    );

    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // 初始化动画
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: widget.animationCurve,
    ));

    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: widget.animationCurve,
    ));

    // 启动加载动画
    if (widget.isLoading && widget.enableLoadingAnimation) {
      _loadingController.repeat();
    }
  }

  @override
  void didUpdateWidget(VanHubButtonV2 oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 处理加载状态变化
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading && widget.enableLoadingAnimation) {
        _loadingController.repeat();
      } else {
        _loadingController.stop();
      }
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  /// 处理悬停进入
  void _onHoverEnter() {
    if (!mounted || !widget.enableHoverAnimation || widget.onPressed == null) return;

    if (mounted) {
      setState(() {
        _isHovered = true;
      });
    }

    if (mounted && _hoverController.isCompleted == false) {
      _hoverController.forward();
    }

    // 触觉反馈
    if (mounted && VanHubResponsiveUtils.isMobile(context)) {
      HapticFeedback.lightImpact();
    }
  }

  /// 处理悬停退出
  void _onHoverExit() {
    if (!mounted || !widget.enableHoverAnimation) return;

    if (mounted) {
      setState(() {
        _isHovered = false;
      });
    }

    if (mounted && _hoverController.isCompleted == true) {
      _hoverController.reverse();
    }
  }

  /// 处理按压开始
  void _onTapDown(TapDownDetails details) {
    if (!mounted || !widget.enablePressAnimation || widget.onPressed == null) return;

    if (mounted) {
      setState(() {
        _isPressed = true;
      });
    }

    if (mounted && _pressController.isCompleted == false) {
      _pressController.forward();
    }

    // 触觉反馈
    if (mounted) {
      HapticFeedback.mediumImpact();
    }
  }

  /// 处理按压结束
  void _onTapUp(TapUpDetails details) {
    if (!mounted || !widget.enablePressAnimation) return;

    if (mounted) {
      setState(() {
        _isPressed = false;
      });
    }

    if (mounted && _pressController.isCompleted == true) {
      _pressController.reverse();
    }
  }

  /// 处理按压取消
  void _onTapCancel() {
    if (!mounted || !widget.enablePressAnimation) return;

    if (mounted) {
      setState(() {
        _isPressed = false;
      });
    }

    if (mounted && _pressController.isCompleted == true) {
      _pressController.reverse();
    }
  }

  /// 处理焦点变化
  void _onFocusChange(bool hasFocus) {
    if (!mounted) return;

    if (mounted) {
      setState(() {
        _isFocused = hasFocus;
      });
    }
  }

  /// 获取响应式尺寸
  VanHubButtonSize _getResponsiveSize() {
    return VanHubResponsiveUtils.getSimpleValue<VanHubButtonSize>(
      context,
      mobile: widget.mobileSize ?? widget.size,
      tablet: widget.tabletSize ?? widget.size,
      desktop: widget.desktopSize ?? widget.size,
    );
  }

  @override
  Widget build(BuildContext context) {
    final responsiveSize = _getResponsiveSize();
    final sizeConfig = VanHubButtonSizeConfig.getConfig(context, responsiveSize);
    final colorConfig = VanHubButtonColorConfig.getConfig(
      context,
      widget.variant,
      emotionalState: widget.emotionalState,
    );

    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController]),
      builder: (context, child) {
        if (!mounted) {
          return const SizedBox.shrink();
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            return Transform.scale(
              scale: _scaleAnimation.value.clamp(0.8, 1.2),
              child: MouseRegion(
                onEnter: (_) => _onHoverEnter(),
                onExit: (_) => _onHoverExit(),
                child: Focus(
                  onFocusChange: _onFocusChange,
                  child: GestureDetector(
                    onTapDown: isEnabled ? _onTapDown : null,
                    onTapUp: isEnabled ? _onTapUp : null,
                    onTapCancel: _onTapCancel,
                    onTap: isEnabled ? widget.onPressed : null,
                    child: AnimatedContainer(
                      duration: widget.animationDuration,
                      curve: widget.animationCurve,
                      width: widget.isFullWidth ? double.infinity : null,
                      height: sizeConfig.height,
                      padding: sizeConfig.padding,
                      constraints: BoxConstraints(
                        minWidth: 0,
                        minHeight: 0,
                        maxWidth: constraints.maxWidth.isFinite ? constraints.maxWidth : double.infinity,
                        maxHeight: constraints.maxHeight.isFinite ? constraints.maxHeight : double.infinity,
                      ),
                      decoration: BoxDecoration(
                        color: _getBackgroundColor(colorConfig, isEnabled),
                        gradient: _getGradient(colorConfig, isEnabled),
                        borderRadius: BorderRadius.circular(sizeConfig.borderRadius),
                        border: _getBorder(colorConfig, isEnabled),
                        boxShadow: _getBoxShadow(colorConfig, isEnabled),
                      ),
                      child: _buildContent(context, sizeConfig, colorConfig, isEnabled),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 获取背景颜色
  Color _getBackgroundColor(VanHubButtonColorConfig colorConfig, bool isEnabled) {
    if (!isEnabled) {
      return colorConfig.disabledBackgroundColor;
    }

    if (_isPressed) {
      return colorConfig.pressedBackgroundColor;
    }

    if (_isHovered) {
      return colorConfig.hoverBackgroundColor;
    }

    return colorConfig.backgroundColor;
  }

  /// 获取渐变
  Gradient? _getGradient(VanHubButtonColorConfig colorConfig, bool isEnabled) {
    if (!isEnabled || colorConfig.gradient == null) {
      return null;
    }

    return colorConfig.gradient;
  }

  /// 获取边框
  Border? _getBorder(VanHubButtonColorConfig colorConfig, bool isEnabled) {
    if (colorConfig.borderColor == Colors.transparent) {
      return null;
    }

    return Border.all(
      color: isEnabled ? colorConfig.borderColor : colorConfig.disabledForegroundColor,
      width: 1.0,
    );
  }

  /// 获取阴影
  List<BoxShadow>? _getBoxShadow(VanHubButtonColorConfig colorConfig, bool isEnabled) {
    if (!isEnabled || widget.variant == VanHubButtonVariant.ghost) {
      return null;
    }

    final elevation = _elevationAnimation.value;

    return [
      BoxShadow(
        color: colorConfig.backgroundColor.withOpacity(0.3),
        blurRadius: elevation * 2,
        offset: Offset(0, elevation / 2),
      ),
    ];
  }

  /// 构建按钮内容
  Widget _buildContent(
    BuildContext context,
    VanHubButtonSizeConfig sizeConfig,
    VanHubButtonColorConfig colorConfig,
    bool isEnabled,
  ) {
    final textColor = isEnabled
        ? colorConfig.foregroundColor
        : colorConfig.disabledForegroundColor;

    final textStyle = VanHubResponsiveText.button(context).copyWith(
      color: textColor,
      fontSize: sizeConfig.fontSize,
    );

    if (widget.isLoading) {
      return _buildLoadingContent(textStyle, sizeConfig.iconSize);
    }

    return _buildNormalContent(textStyle, sizeConfig.iconSize, textColor);
  }

  /// 构建加载状态内容
  Widget _buildLoadingContent(TextStyle textStyle, double iconSize) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: iconSize,
          height: iconSize,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(textStyle.color!),
          ),
        ).animate(controller: _loadingController)
          .rotate(duration: const Duration(milliseconds: 1000)),
        SizedBox(width: VanHubResponsiveSpacing.sm),
        Text('加载中...', style: textStyle),
      ],
    );
  }

  /// 构建正常状态内容
  Widget _buildNormalContent(TextStyle textStyle, double iconSize, Color iconColor) {
    final children = <Widget>[];

    // 前置图标
    if (widget.leadingIcon != null) {
      children.add(
        Icon(
          widget.leadingIcon,
          size: iconSize,
          color: iconColor,
        ),
      );
      children.add(SizedBox(width: VanHubResponsiveSpacing.sm));
    }

    // 文本
    children.add(
      Text(
        widget.text,
        style: textStyle,
        textAlign: TextAlign.center,
      ),
    );

    // 后置图标
    if (widget.trailingIcon != null) {
      children.add(SizedBox(width: VanHubResponsiveSpacing.sm));
      children.add(
        Icon(
          widget.trailingIcon,
          size: iconSize,
          color: iconColor,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }
}
