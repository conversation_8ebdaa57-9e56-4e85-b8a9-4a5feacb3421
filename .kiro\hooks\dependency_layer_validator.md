# Dependency Layer Validator Hook

## Hook Configuration
```yaml
name: "Dependency Layer Validator"
description: "验证分层架构的依赖关系是否正确"
trigger: "on_file_save"
file_patterns: ["lib/features/**/*.dart"]
auto_execute: true
```

## Dependency Rules

### Domain Layer Dependencies
Domain层只能依赖：
```dart
// ✅ 允许的导入
import 'dart:core';
import 'dart:async';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:equatable/equatable.dart';
import '../entities/user.dart';  // 同层导入

// ❌ 禁止的导入
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../data/models/user_model.dart';  // 不能依赖Data层
import '../../presentation/pages/login_page.dart';  // 不能依赖Presentation层
```

### Data Layer Dependencies
Data层可以依赖：
```dart
// ✅ 允许的导入
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart';
import '../../../core/errors/exceptions.dart';
import '../../domain/entities/user.dart';  // 可以依赖Domain层
import '../../domain/repositories/auth_repository.dart';

// ❌ 禁止的导入
import '../../presentation/pages/login_page.dart';  // 不能依赖Presentation层
```

### Presentation Layer Dependencies
Presentation层可以依赖：
```dart
// ✅ 允许的导入
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/user.dart';  // 可以依赖Domain层
import '../../domain/usecases/login_usecase.dart';

// ❌ 禁止的导入
import '../../data/datasources/auth_remote_datasource.dart';  // 不能直接依赖Data层的具体实现
```

### Core Layer Dependencies
Core层是全局共享的，可以被所有层依赖：
```dart
// ✅ 所有层都可以导入Core
import '../../../core/errors/failures.dart';
import '../../../core/utils/constants.dart';
```

## Circular Dependency Detection
检测循环依赖：
- Feature A → Feature B → Feature A
- Layer违规：Domain → Data → Domain

## 执行动作
1. 分析import语句
2. 检查依赖方向是否正确
3. 检测循环依赖
4. 提供重构建议
5. 自动修复简单的导入问题