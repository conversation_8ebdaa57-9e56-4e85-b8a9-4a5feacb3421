import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/injection_container.dart';
import 'bom_controller.dart';
import '../../../bom/domain/entities/bom_item.dart';

/// BOM控制器Provider
final bomControllerProvider = StateNotifierProvider<BomController, AsyncValue<void>>((ref) {
  return BomController(ref);
});

/// BOM项目列表Provider
final bomItemsProvider = FutureProvider.family<List<BomItem>, String>((ref, projectId) async {
  final useCase = ref.read(getBomItemsUseCaseProvider);
  final result = await useCase.call(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
});

// BOM统计Provider已移动到bom_controller_provider.dart
// 请使用 bomStatistics Provider 替代此定义

/// BOM项目详情Provider
final bomItemDetailProvider = FutureProvider.family<BomItem, String>((ref, bomItemId) async {
  final result = await ref.read(bomRepositoryProvider).getBomItemById(bomItemId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItem) => bomItem,
  );
});