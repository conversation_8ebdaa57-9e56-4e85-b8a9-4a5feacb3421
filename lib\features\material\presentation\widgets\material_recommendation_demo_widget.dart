import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material_recommendation.dart';
import '../providers/material_recommendation_provider.dart';

class MaterialRecommendationDemoWidget extends ConsumerStatefulWidget {
  final String projectId;
  
  const MaterialRecommendationDemoWidget({
    Key? key,
    required this.projectId,
  }) : super(key: key);

  @override
  ConsumerState<MaterialRecommendationDemoWidget> createState() => _MaterialRecommendationDemoWidgetState();
}

class _MaterialRecommendationDemoWidgetState extends ConsumerState<MaterialRecommendationDemoWidget> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _systemTypes = ['电路系统', '水路系统', '储物系统', '床铺系统', '厨房系统', '卫浴系统', '外观系统', '底盘系统'];
  String _selectedSystemType = '电路系统';
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 加载项目推荐
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recommendationStateProvider.notifier).loadProjectRecommendations(widget.projectId);
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            '智能推荐',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
        ),
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '项目推荐'),
            Tab(text: '系统推荐'),
            Tab(text: '热门材料'),
          ],
          onTap: (index) {
            if (index == 0) {
              ref.read(recommendationStateProvider.notifier).loadProjectRecommendations(widget.projectId);
            } else if (index == 1) {
              ref.read(recommendationStateProvider.notifier).loadSystemRecommendations(
                widget.projectId,
                _selectedSystemType,
              );
            } else if (index == 2) {
              ref.read(popularMaterialRecommendationsProvider());
            }
          },
        ),
        if (_tabController.index == 1)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: DropdownButton<String>(
              value: _selectedSystemType,
              items: _systemTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedSystemType = value;
                  });
                  ref.read(recommendationStateProvider.notifier).loadSystemRecommendations(
                    widget.projectId,
                    _selectedSystemType,
                  );
                }
              },
            ),
          ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // 项目推荐
              _buildProjectRecommendations(),
              
              // 系统推荐
              _buildSystemRecommendations(),
              
              // 热门材料
              _buildPopularMaterials(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProjectRecommendations() {
    final recommendationsState = ref.watch(recommendationStateProvider);
    
    return recommendationsState.when(
      data: (recommendations) {
        if (recommendations.isEmpty) {
          return const Center(
            child: Text('没有找到推荐材料'),
          );
        }
        
        return ListView.builder(
          itemCount: recommendations.length,
          itemBuilder: (context, index) {
            return _buildRecommendationCard(recommendations[index]);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, _) => Center(
        child: Text('加载推荐失败: ${error.toString()}'),
      ),
    );
  }

  Widget _buildSystemRecommendations() {
    final recommendationsState = ref.watch(recommendationStateProvider);
    
    return recommendationsState.when(
      data: (recommendations) {
        if (recommendations.isEmpty) {
          return const Center(
            child: Text('没有找到推荐材料'),
          );
        }
        
        return ListView.builder(
          itemCount: recommendations.length,
          itemBuilder: (context, index) {
            return _buildRecommendationCard(recommendations[index]);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, _) => Center(
        child: Text('加载推荐失败: ${error.toString()}'),
      ),
    );
  }

  Widget _buildPopularMaterials() {
    final popularRecommendations = ref.watch(popularMaterialRecommendationsProvider());
    
    return popularRecommendations.when(
      data: (recommendations) {
        if (recommendations.isEmpty) {
          return const Center(
            child: Text('没有找到热门材料'),
          );
        }
        
        return ListView.builder(
          itemCount: recommendations.length,
          itemBuilder: (context, index) {
            return _buildRecommendationCard(recommendations[index]);
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, _) => Center(
        child: Text('加载热门材料失败: ${error.toString()}'),
      ),
    );
  }

  Widget _buildRecommendationCard(MaterialRecommendation recommendation) {
    final material = recommendation.material;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: material.imageUrl != null && material.imageUrl!.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4.0),
                    child: Image.network(
                      material.imageUrl!,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => const Icon(Icons.image, size: 50),
                    ),
                  )
                : Container(
                    width: 50,
                    height: 50,
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.category,
                      color: Colors.grey.shade600,
                    ),
                  ),
            title: Text(material.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('分类: ${material.category}'),
                if (material.brand != null && material.brand!.isNotEmpty)
                  Text('品牌: ${material.brand}'),
                Text('价格: ¥${material.price.toStringAsFixed(2)}'),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildRelevanceIndicator(recommendation.relevanceScore),
                Text(
                  recommendation.strengthDescription,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            isThreeLine: true,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                      decoration: BoxDecoration(
                        color: _getTypeColor(recommendation.type),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Text(
                        recommendation.type.displayName,
                        style: const TextStyle(color: Colors.white, fontSize: 12.0),
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Expanded(
                      child: Text(
                        recommendation.reason,
                        style: const TextStyle(fontStyle: FontStyle.italic),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    OutlinedButton(
                      onPressed: () {
                        // 查看详情
                      },
                      child: const Text('查看详情'),
                    ),
                    const SizedBox(width: 8.0),
                    ElevatedButton(
                      onPressed: () {
                        // 添加到BOM
                      },
                      child: const Text('添加到BOM'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelevanceIndicator(double score) {
    Color color;
    if (score >= 80) {
      color = Colors.green;
    } else if (score >= 60) {
      color = Colors.blue;
    } else if (score >= 40) {
      color = Colors.orange;
    } else {
      color = Colors.grey;
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey.shade200,
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: score / 100,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '${score.toInt()}%',
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getTypeColor(RecommendationType type) {
    switch (type) {
      case RecommendationType.projectBased:
        return Colors.green;
      case RecommendationType.systemBased:
        return Colors.blue;
      case RecommendationType.similarMaterial:
        return Colors.purple;
      case RecommendationType.complementary:
        return Colors.orange;
      case RecommendationType.popular:
        return Colors.red;
      case RecommendationType.valueForMoney:
        return Colors.teal;
      case RecommendationType.recentlyUsed:
        return Colors.blueGrey;
      case RecommendationType.frequentlyUsed:
        return Colors.brown;
    }
  }
}