# VanHub评论功能使用指南

## 📋 **功能概述**

VanHub评论功能是一个专业的材料评价系统，旨在帮助房车改装爱好者基于真实的使用经验选择合适的材料。该功能采用Clean Architecture架构，提供了完整的评价创建、查看、筛选和互动体验。

## 🎯 **核心特性**

### 1. **专业评价体系**
- **四维度评分**：质量、性价比、耐用性、安装难度
- **总体评分**：综合评价材料整体表现
- **推荐度计算**：基于多因素的智能推荐算法

### 2. **使用场景标签**
- **车型适配**：记录材料适用的车型
- **系统分类**：标记改装系统类型（电气、水路、储物等）
- **使用时长**：记录实际使用经验时间

### 3. **验证购买系统**
- **购买验证**：提升评价可信度
- **购买日期**：记录购买时间
- **验证标识**：区分验证和未验证评价

### 4. **多媒体支持**
- **图片展示**：直观展示材料使用效果
- **视频支持**：支持视频附件（架构已完成）

### 5. **智能互动**
- **点赞功能**：表达对评价的认同
- **有用标记**：标记对选择有帮助的评价
- **举报机制**：维护社区内容质量

## 🛠️ **开发者使用指南**

### **1. 基础集成**

#### 在页面中显示评价摘要：
```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vanhub/features/material/presentation/providers/material_review_provider.dart';
import 'package:vanhub/features/material/presentation/widgets/material_review_summary_widget.dart';

class MaterialDetailPage extends ConsumerWidget {
  final String materialId;
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final summaryAsync = ref.watch(materialReviewSummaryProvider(materialId));
    
    return summaryAsync.when(
      data: (summary) => MaterialReviewSummaryWidget(
        summary: summary,
        onViewAllReviews: () => _navigateToReviewsList(context),
        onWriteReview: () => _showWriteReviewDialog(context),
      ),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('加载失败: $error'),
    );
  }
}
```

#### 显示评价列表：
```dart
final reviewsAsync = ref.watch(materialReviewsProvider(
  materialId,
  filterCriteria: ReviewFilterCriteria(
    sortBy: ReviewSortType.newest,
    verifiedPurchaseOnly: true,
  ),
));

return reviewsAsync.when(
  data: (reviews) => ListView.builder(
    itemCount: reviews.length,
    itemBuilder: (context, index) => MaterialReviewCardWidget(
      review: reviews[index],
      onLike: () => _handleLike(reviews[index]),
      onMarkHelpful: () => _handleMarkHelpful(reviews[index]),
    ),
  ),
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => Text('加载失败: $error'),
);
```

### **2. 创建评价**

#### 显示写评价对话框：
```dart
void _showWriteReviewDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => WriteReviewDialogWidget(
      materialId: materialId,
      materialName: materialName,
    ),
  ).then((result) {
    if (result == true) {
      // 评价创建成功，刷新数据
      ref.invalidate(materialReviewsProvider);
      ref.invalidate(materialReviewSummaryProvider);
    }
  });
}
```

#### 编程方式创建评价：
```dart
Future<void> createReview() async {
  final review = MaterialReview(
    id: DateTime.now().millisecondsSinceEpoch.toString(),
    materialId: 'material-id',
    userId: 'user-id',
    userName: 'User Name',
    content: '评价内容...',
    rating: 4.5,
    qualityRating: 4.0,
    valueRating: 4.5,
    durabilityRating: 4.0,
    installationRating: 5.0,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  await ref.read(createReviewNotifierProvider.notifier).createReview(review);
}
```

### **3. 性能优化**

#### 使用缓存Provider：
```dart
// 使用带缓存的Provider提升性能
final summaryAsync = ref.watch(cachedMaterialReviewSummaryProvider(materialId));
final reviewsAsync = ref.watch(cachedMaterialReviewsProvider(materialId));
```

#### 预加载数据：
```dart
// 在页面初始化时预加载数据
@override
void initState() {
  super.initState();
  WidgetsBinding.instance.addPostFrameCallback((_) {
    ref.read(preloadReviewsNotifierProvider.notifier)
        .preloadMaterialReviews(['material1', 'material2', 'material3']);
  });
}
```

#### 批量获取摘要：
```dart
// 批量获取多个材料的评价摘要
final summariesAsync = ref.watch(batchCachedReviewSummariesProvider([
  'material1', 'material2', 'material3'
]));
```

### **4. 筛选和排序**

#### 创建筛选条件：
```dart
final filterCriteria = ReviewFilterCriteria(
  minRating: 4.0,                    // 最低评分
  verifiedPurchaseOnly: true,        // 只显示验证购买
  withImagesOnly: true,              // 只显示有图片的评价
  sortBy: ReviewSortType.mostHelpful, // 按有用性排序
  vehicleType: '依维柯Daily',         // 车型筛选
  systemType: '电气系统',             // 系统类型筛选
);

final filteredReviews = ref.watch(materialReviewsProvider(
  materialId,
  filterCriteria: filterCriteria,
));
```

#### 排序选项：
```dart
enum ReviewSortType {
  newest,           // 最新优先
  oldest,           // 最旧优先
  ratingHighToLow,  // 评分从高到低
  ratingLowToHigh,  // 评分从低到高
  mostHelpful,      // 最有用优先
  verifiedFirst,    // 验证购买优先
}
```

### **5. 错误处理**

#### 统一错误处理：
```dart
reviewsAsync.when(
  data: (reviews) => _buildReviewsList(reviews),
  loading: () => _buildLoadingState(),
  error: (error, stack) => _buildErrorState(error),
);

Widget _buildErrorState(Object error) {
  return Column(
    children: [
      Icon(Icons.error_outline, size: 64, color: Colors.red),
      Text('加载失败: $error'),
      ElevatedButton(
        onPressed: () => ref.invalidate(materialReviewsProvider),
        child: Text('重试'),
      ),
    ],
  );
}
```

## 📊 **数据库集成**

### **现有表结构**
评论功能完全兼容现有的Supabase数据库：

- **material_reviews表**：存储材料评价数据
- **comments表**：存储通用评论数据
- **RLS策略**：已配置行级安全策略

### **数据库函数**
需要在Supabase中创建以下数据库函数：

```sql
-- 添加有用用户
CREATE OR REPLACE FUNCTION add_helpful_user(review_id TEXT, user_id TEXT)
RETURNS void AS $$
BEGIN
  UPDATE material_reviews 
  SET helpful_user_ids = array_append(helpful_user_ids, user_id),
      helpful_count = helpful_count + 1
  WHERE id = review_id 
    AND NOT (user_id = ANY(helpful_user_ids));
END;
$$ LANGUAGE plpgsql;

-- 移除有用用户
CREATE OR REPLACE FUNCTION remove_helpful_user(review_id TEXT, user_id TEXT)
RETURNS void AS $$
BEGIN
  UPDATE material_reviews 
  SET helpful_user_ids = array_remove(helpful_user_ids, user_id),
      helpful_count = helpful_count - 1
  WHERE id = review_id 
    AND user_id = ANY(helpful_user_ids);
END;
$$ LANGUAGE plpgsql;

-- 添加点赞用户
CREATE OR REPLACE FUNCTION add_like_user(review_id TEXT, user_id TEXT)
RETURNS void AS $$
BEGIN
  UPDATE material_reviews 
  SET liked_by_user_ids = array_append(liked_by_user_ids, user_id)
  WHERE id = review_id 
    AND NOT (user_id = ANY(liked_by_user_ids));
END;
$$ LANGUAGE plpgsql;

-- 移除点赞用户
CREATE OR REPLACE FUNCTION remove_like_user(review_id TEXT, user_id TEXT)
RETURNS void AS $$
BEGIN
  UPDATE material_reviews 
  SET liked_by_user_ids = array_remove(liked_by_user_ids, user_id)
  WHERE id = review_id 
    AND user_id = ANY(liked_by_user_ids);
END;
$$ LANGUAGE plpgsql;
```

## 🧪 **测试指南**

### **单元测试**
```bash
# 运行评论功能相关的单元测试
flutter test test/features/material/domain/usecases/
flutter test test/features/material/data/models/
```

### **Widget测试**
```bash
# 运行UI组件测试
flutter test test/features/material/presentation/widgets/
```

### **集成测试**
```bash
# 运行完整的集成测试
flutter test integration_test/
```

## 🚀 **部署和监控**

### **性能监控**
```dart
// 获取缓存统计信息
final cacheStats = ref.watch(reviewCacheStatsProvider);
print('缓存统计: $cacheStats');
```

### **错误监控**
建议集成Firebase Crashlytics或Sentry来监控评论功能的错误：

```dart
try {
  await ref.read(createReviewNotifierProvider.notifier).createReview(review);
} catch (error, stackTrace) {
  // 上报错误到监控系统
  FirebaseCrashlytics.instance.recordError(error, stackTrace);
}
```

## 📝 **最佳实践**

### **1. 用户体验**
- 使用缓存Provider提升加载速度
- 提供加载状态和错误处理
- 实现下拉刷新功能
- 支持离线浏览（缓存数据）

### **2. 性能优化**
- 使用分页加载大量评价
- 预加载热门材料的评价数据
- 实现图片懒加载
- 使用批量API减少网络请求

### **3. 数据质量**
- 实施评价内容验证
- 提供举报和审核机制
- 鼓励验证购买
- 定期清理垃圾评价

### **4. 安全性**
- 验证用户身份
- 防止刷评价
- 保护用户隐私
- 实施内容过滤

## 🔧 **故障排除**

### **常见问题**

1. **评价不显示**
   - 检查RLS策略配置
   - 验证数据库连接
   - 确认材料ID正确

2. **创建评价失败**
   - 检查用户认证状态
   - 验证表单数据
   - 查看网络连接

3. **性能问题**
   - 启用缓存Provider
   - 实施分页加载
   - 优化图片加载

4. **UI显示异常**
   - 检查数据格式
   - 验证Widget状态
   - 查看控制台错误

## 📞 **技术支持**

如需技术支持，请：
1. 查看本文档的相关章节
2. 检查代码注释和示例
3. 运行相关测试用例
4. 查看项目的GitHub Issues

---

**VanHub评论功能 - 让每一次改装经验都成为宝贵的知识财富！** 🚀
