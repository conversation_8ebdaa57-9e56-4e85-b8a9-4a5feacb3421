import 'package:freezed_annotation/freezed_annotation.dart';

part 'fork_result.freezed.dart';
part 'fork_result.g.dart';

@freezed
class ForkResult with _$ForkResult {
  const factory ForkResult({
    required String newProjectId,
    required String sourceProjectId,
    required String sourceProjectTitle,
    required String sourceAuthorId,
    required String sourceAuthorName,
    required int copiedSystems,
    required int copiedBomItems,
    required int copiedImages,
    required DateTime forkedAt,
  }) = _ForkResult;

  factory ForkResult.fromJson(Map<String, dynamic> json) =>
      _$ForkResultFromJson(json);
}

extension ForkResultX on ForkResult {
  /// 获取复制的总项目数
  int get totalCopiedItems => copiedSystems + copiedBomItems + copiedImages;
  
  /// 是否成功复制了内容
  bool get hasContent => totalCopiedItems > 0;
  
  /// 获取复制摘要
  String get copySummary {
    final parts = <String>[];
    if (copiedSystems > 0) parts.add('$copiedSystems个系统');
    if (copiedBomItems > 0) parts.add('$copiedBomItems个BOM项目');
    if (copiedImages > 0) parts.add('$copiedImages张图片');
    
    if (parts.isEmpty) return '未复制任何内容';
    return '已复制：${parts.join('、')}';
  }
}
