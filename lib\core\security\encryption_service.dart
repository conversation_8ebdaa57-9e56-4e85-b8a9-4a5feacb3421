import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/// 数据加密服务
class EncryptionService {
  static const String _defaultKey = 'VanHub_Default_Key_2024'; // 在生产环境中应该从环境变量获取
  
  /// 加密字符串
  static String encryptString(String plainText, {String? key}) {
    if (plainText.isEmpty) return plainText;
    
    try {
      final encryptionKey = key ?? _defaultKey;
      final bytes = utf8.encode(plainText);
      final keyBytes = utf8.encode(encryptionKey);
      
      // 使用简单的XOR加密（在生产环境中应该使用更强的加密算法）
      final encrypted = _xorEncrypt(bytes, keyBytes);
      
      // 转换为Base64编码
      return base64.encode(encrypted);
    } catch (e) {
      // 如果加密失败，返回原文（在生产环境中应该抛出异常）
      return plainText;
    }
  }
  
  /// 解密字符串
  static String decryptString(String encryptedText, {String? key}) {
    if (encryptedText.isEmpty) return encryptedText;
    
    try {
      final encryptionKey = key ?? _defaultKey;
      
      // 从Base64解码
      final encrypted = base64.decode(encryptedText);
      final keyBytes = utf8.encode(encryptionKey);
      
      // 使用XOR解密
      final decrypted = _xorEncrypt(encrypted, keyBytes);
      
      // 转换为字符串
      return utf8.decode(decrypted);
    } catch (e) {
      // 如果解密失败，返回原文（可能是未加密的数据）
      return encryptedText;
    }
  }
  
  /// 加密价格（保留两位小数）
  static String encryptPrice(double price, {String? key}) {
    final priceString = price.toStringAsFixed(2);
    return encryptString(priceString, key: key);
  }
  
  /// 解密价格
  static double decryptPrice(String encryptedPrice, {String? key}) {
    final decryptedString = decryptString(encryptedPrice, key: key);
    return double.tryParse(decryptedString) ?? 0.0;
  }
  
  /// 加密敏感字段的Map
  static Map<String, dynamic> encryptSensitiveFields(
    Map<String, dynamic> data,
    List<String> sensitiveFields,
    {String? key}
  ) {
    final result = Map<String, dynamic>.from(data);
    
    for (final field in sensitiveFields) {
      if (result.containsKey(field) && result[field] != null) {
        if (result[field] is String) {
          result[field] = encryptString(result[field] as String, key: key);
        } else if (result[field] is double || result[field] is num) {
          result[field] = encryptPrice((result[field] as num).toDouble(), key: key);
        }
      }
    }
    
    return result;
  }
  
  /// 解密敏感字段的Map
  static Map<String, dynamic> decryptSensitiveFields(
    Map<String, dynamic> data,
    List<String> sensitiveFields,
    {String? key}
  ) {
    final result = Map<String, dynamic>.from(data);
    
    for (final field in sensitiveFields) {
      if (result.containsKey(field) && result[field] != null && result[field] is String) {
        final encryptedValue = result[field] as String;
        
        // 尝试解密为价格（如果字段名包含price或cost）
        if (field.toLowerCase().contains('price') || 
            field.toLowerCase().contains('cost') ||
            field.toLowerCase().contains('budget')) {
          result[field] = decryptPrice(encryptedValue, key: key);
        } else {
          result[field] = decryptString(encryptedValue, key: key);
        }
      }
    }
    
    return result;
  }
  
  /// XOR加密/解密
  static Uint8List _xorEncrypt(List<int> data, List<int> key) {
    final result = Uint8List(data.length);
    
    for (int i = 0; i < data.length; i++) {
      result[i] = data[i] ^ key[i % key.length];
    }
    
    return result;
  }
  
  /// 生成数据哈希（用于数据完整性验证）
  static String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// 验证数据哈希
  static bool verifyHash(String data, String hash) {
    final generatedHash = generateHash(data);
    return generatedHash == hash;
  }
  
  /// 敏感字段列表（根据项目文档要求）
  static const List<String> defaultSensitiveFields = [
    'price',
    'unit_price',
    'total_price',
    'estimated_budget',
    'actual_budget',
    'description',
    'notes',
    'supplier_url',
    'metadata',
  ];
}
