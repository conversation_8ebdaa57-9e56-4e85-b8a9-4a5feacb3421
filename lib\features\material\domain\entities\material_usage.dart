import 'package:freezed_annotation/freezed_annotation.dart';

part 'material_usage.freezed.dart';
part 'material_usage.g.dart';

/// Material usage context enum
enum UsageContext {
  installation,    // Installation/setup
  maintenance,     // Maintenance work
  repair,         // Repair work
  upgrade,        // System upgrade
  replacement,    // Component replacement
  testing,        // Testing/prototyping
  other,          // Other usage
}

extension UsageContextX on UsageContext {
  String get displayName {
    switch (this) {
      case UsageContext.installation:
        return 'Installation';
      case UsageContext.maintenance:
        return 'Maintenance';
      case UsageContext.repair:
        return 'Repair';
      case UsageContext.upgrade:
        return 'Upgrade';
      case UsageContext.replacement:
        return 'Replacement';
      case UsageContext.testing:
        return 'Testing';
      case UsageContext.other:
        return 'Other';
    }
  }

  String get icon {
    switch (this) {
      case UsageContext.installation:
        return '🔧';
      case UsageContext.maintenance:
        return '⚙️';
      case UsageContext.repair:
        return '🔨';
      case UsageContext.upgrade:
        return '⬆️';
      case UsageContext.replacement:
        return '🔄';
      case UsageContext.testing:
        return '🧪';
      case UsageContext.other:
        return '📝';
    }
  }
}

/// Material usage status enum
enum UsageStatus {
  planned,     // Planned usage
  inProgress,  // Currently being used
  completed,   // Usage completed
  cancelled,   // Usage cancelled
  wasted,      // Material wasted
}

extension UsageStatusX on UsageStatus {
  String get displayName {
    switch (this) {
      case UsageStatus.planned:
        return 'Planned';
      case UsageStatus.inProgress:
        return 'In Progress';
      case UsageStatus.completed:
        return 'Completed';
      case UsageStatus.cancelled:
        return 'Cancelled';
      case UsageStatus.wasted:
        return 'Wasted';
    }
  }

  String get color {
    switch (this) {
      case UsageStatus.planned:
        return '#2196F3'; // Blue
      case UsageStatus.inProgress:
        return '#FF9800'; // Orange
      case UsageStatus.completed:
        return '#4CAF50'; // Green
      case UsageStatus.cancelled:
        return '#9E9E9E'; // Grey
      case UsageStatus.wasted:
        return '#F44336'; // Red
    }
  }
}

/// Material usage location information
@freezed
class UsageLocation with _$UsageLocation {
  const factory UsageLocation({
    required String name,
    String? description,
    String? address,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? metadata,
  }) = _UsageLocation;

  factory UsageLocation.fromJson(Map<String, dynamic> json) => 
      _$UsageLocationFromJson(json);
}

/// Material usage cost breakdown
@freezed
class UsageCost with _$UsageCost {
  const factory UsageCost({
    required double materialCost,
    required double laborCost,
    required double toolCost,
    required double overheadCost,
    required double totalCost,
    String? currency,
    Map<String, double>? breakdown,
  }) = _UsageCost;

  factory UsageCost.fromJson(Map<String, dynamic> json) => 
      _$UsageCostFromJson(json);
}

/// Material usage quality metrics
@freezed
class UsageQuality with _$UsageQuality {
  const factory UsageQuality({
    required double qualityRating,
    required double performanceRating,
    required double durabilityRating,
    required double satisfactionRating,
    String? qualityNotes,
    List<String>? issues,
    List<String>? improvements,
  }) = _UsageQuality;

  factory UsageQuality.fromJson(Map<String, dynamic> json) => 
      _$UsageQualityFromJson(json);
}

/// Material usage entity following Clean Architecture
@freezed
class MaterialUsage with _$MaterialUsage {
  const factory MaterialUsage({
    required String id,
    required String materialId,
    required String materialName,
    required String projectId,
    required String projectName,
    required String userId,
    required String userName,
    required double quantity,
    required String unit,
    required UsageContext context,
    required UsageStatus status,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? plannedDate,
    DateTime? startDate,
    DateTime? completedDate,
    String? notes,
    String? instructions,
    UsageLocation? location,
    UsageCost? cost,
    UsageQuality? quality,
    List<String>? images,
    List<String>? documents,
    Map<String, dynamic>? metadata,
    String? batchNumber,
    String? serialNumber,
    double? wasteQuantity,
    String? wasteReason,
    @Default(false) bool isRecurring,
    String? recurringPattern,
    @Default(false) bool isTemplate,
    @Default(false) bool isArchived,
  }) = _MaterialUsage;

  factory MaterialUsage.fromJson(Map<String, dynamic> json) => 
      _$MaterialUsageFromJson(json);
}

/// Material usage statistics
@freezed
class MaterialUsageStatistics with _$MaterialUsageStatistics {
  const factory MaterialUsageStatistics({
    required int totalUsages,
    required double totalQuantity,
    required String unit,
    required double totalCost,
    required Map<String, int> usageByContext,
    required Map<String, int> usageByStatus,
    required Map<String, double> monthlyUsage,
    required double averageQuantityPerUsage,
    required double averageCostPerUsage,
    required double wastePercentage,
    required List<String> topProjects,
    required DateTime firstUsage,
    required DateTime lastUsage,
    @Default(0.0) double averageQualityRating,
    @Default(0.0) double averagePerformanceRating,
    @Default(0) int completedUsages,
    @Default(0) int plannedUsages,
    @Default(0) int cancelledUsages,
  }) = _MaterialUsageStatistics;

  factory MaterialUsageStatistics.fromJson(Map<String, dynamic> json) => 
      _$MaterialUsageStatisticsFromJson(json);
}

/// Trending material data
@freezed
class TrendingMaterial with _$TrendingMaterial {
  const factory TrendingMaterial({
    required String materialId,
    required String materialName,
    required String category,
    required int usageCount,
    required double totalQuantity,
    required String unit,
    required double growthRate,
    required String trendDirection,
    String? brand,
    String? model,
    double? averagePrice,
    double? totalCost,
    Map<String, dynamic>? metadata,
  }) = _TrendingMaterial;

  factory TrendingMaterial.fromJson(Map<String, dynamic> json) => 
      _$TrendingMaterialFromJson(json);
}

/// Material recommendation based on usage
@freezed
class MaterialRecommendation with _$MaterialRecommendation {
  const factory MaterialRecommendation({
    required String materialId,
    required String materialName,
    required String category,
    required double confidence,
    required String reason,
    required List<String> basedOnMaterials,
    String? brand,
    String? model,
    double? estimatedQuantity,
    String? unit,
    double? estimatedCost,
    Map<String, dynamic>? metadata,
  }) = _MaterialRecommendation;

  factory MaterialRecommendation.fromJson(Map<String, dynamic> json) => 
      _$MaterialRecommendationFromJson(json);
}

/// Material combination (frequently used together)
@freezed
class MaterialCombination with _$MaterialCombination {
  const factory MaterialCombination({
    required String primaryMaterialId,
    required String secondaryMaterialId,
    required String secondaryMaterialName,
    required double confidence,
    required int coOccurrences,
    required String relationship,
    String? category,
    String? brand,
    double? averageQuantityRatio,
    Map<String, dynamic>? metadata,
  }) = _MaterialCombination;

  factory MaterialCombination.fromJson(Map<String, dynamic> json) => 
      _$MaterialCombinationFromJson(json);
}

/// Material usage business logic extensions
extension MaterialUsageX on MaterialUsage {
  /// Check if usage is active
  bool get isActive => status == UsageStatus.inProgress;

  /// Check if usage is completed
  bool get isCompleted => status == UsageStatus.completed;

  /// Check if usage is planned
  bool get isPlanned => status == UsageStatus.planned;

  /// Check if usage resulted in waste
  bool get hasWaste => wasteQuantity != null && wasteQuantity! > 0;

  /// Get waste percentage
  double get wastePercentage {
    if (wasteQuantity == null || quantity == 0) return 0.0;
    return (wasteQuantity! / quantity) * 100;
  }

  /// Get effective quantity (quantity - waste)
  double get effectiveQuantity {
    return quantity - (wasteQuantity ?? 0);
  }

  /// Get usage duration in days
  int? get usageDurationDays {
    if (startDate == null || completedDate == null) return null;
    return completedDate!.difference(startDate!).inDays;
  }

  /// Get usage duration in hours
  int? get usageDurationHours {
    if (startDate == null || completedDate == null) return null;
    return completedDate!.difference(startDate!).inHours;
  }

  /// Check if usage is overdue
  bool get isOverdue {
    if (plannedDate == null || isCompleted) return false;
    return DateTime.now().isAfter(plannedDate!);
  }

  /// Get days until planned date
  int? get daysUntilPlanned {
    if (plannedDate == null) return null;
    return plannedDate!.difference(DateTime.now()).inDays;
  }

  /// Check if usage has quality metrics
  bool get hasQualityMetrics => quality != null;

  /// Check if usage has cost information
  bool get hasCostInformation => cost != null;

  /// Check if usage has location information
  bool get hasLocation => location != null;

  /// Check if usage has images
  bool get hasImages => images != null && images!.isNotEmpty;

  /// Check if usage has documents
  bool get hasDocuments => documents != null && documents!.isNotEmpty;

  /// Get overall quality score (0-100)
  double get overallQualityScore {
    if (quality == null) return 0.0;
    
    final scores = [
      quality!.qualityRating,
      quality!.performanceRating,
      quality!.durabilityRating,
      quality!.satisfactionRating,
    ];
    
    return (scores.reduce((a, b) => a + b) / scores.length) * 20; // Convert to 0-100 scale
  }

  /// Get cost per unit
  double get costPerUnit {
    if (cost == null || quantity == 0) return 0.0;
    return cost!.totalCost / quantity;
  }

  /// Get effective cost per unit (excluding waste)
  double get effectiveCostPerUnit {
    if (cost == null || effectiveQuantity == 0) return 0.0;
    return cost!.totalCost / effectiveQuantity;
  }

  /// Get usage efficiency (0-1)
  double get usageEfficiency {
    if (quantity == 0) return 0.0;
    return effectiveQuantity / quantity;
  }

  /// Get time since creation
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get usage tags based on properties
  List<String> get tags {
    final tagList = <String>[];
    
    if (isRecurring) tagList.add('Recurring');
    if (isTemplate) tagList.add('Template');
    if (hasWaste) tagList.add('Has Waste');
    if (isOverdue) tagList.add('Overdue');
    if (hasQualityMetrics) tagList.add('Quality Tracked');
    if (hasCostInformation) tagList.add('Cost Tracked');
    if (hasImages) tagList.add('With Photos');
    if (batchNumber != null) tagList.add('Batch Tracked');
    if (serialNumber != null) tagList.add('Serial Tracked');
    
    return tagList;
  }

  /// Check if usage can be edited
  bool canBeEditedBy(String currentUserId) {
    return userId == currentUserId && !isArchived;
  }

  /// Check if usage can be deleted
  bool canBeDeletedBy(String currentUserId) {
    return userId == currentUserId && !isCompleted && !isArchived;
  }

  /// Get status color
  String get statusColor => status.color;

  /// Get context icon
  String get contextIcon => context.icon;

  /// Calculate similarity with another usage (0-1)
  double calculateSimilarityWith(MaterialUsage other) {
    double similarity = 0.0;
    int factors = 0;
    
    // Material similarity
    if (materialId == other.materialId) {
      similarity += 1.0;
    }
    factors++;
    
    // Context similarity
    if (context == other.context) {
      similarity += 1.0;
    }
    factors++;
    
    // Quantity similarity (within 20% range)
    final quantityDiff = (quantity - other.quantity).abs();
    final avgQuantity = (quantity + other.quantity) / 2;
    if (avgQuantity > 0) {
      final quantitySimilarity = 1.0 - (quantityDiff / avgQuantity).clamp(0.0, 1.0);
      similarity += quantitySimilarity;
    }
    factors++;
    
    // Project similarity
    if (projectId == other.projectId) {
      similarity += 1.0;
    }
    factors++;
    
    return factors > 0 ? similarity / factors : 0.0;
  }
}

/// Material usage statistics business logic extensions
extension MaterialUsageStatisticsX on MaterialUsageStatistics {
  /// Get completion rate (0-1)
  double get completionRate {
    return totalUsages > 0 ? completedUsages / totalUsages : 0.0;
  }

  /// Get cancellation rate (0-1)
  double get cancellationRate {
    return totalUsages > 0 ? cancelledUsages / totalUsages : 0.0;
  }

  /// Get usage frequency (usages per month)
  double get usageFrequency {
    final monthsDiff = lastUsage.difference(firstUsage).inDays / 30.0;
    return monthsDiff > 0 ? totalUsages / monthsDiff : 0.0;
  }

  /// Get most common usage context
  String get mostCommonContext {
    if (usageByContext.isEmpty) return 'Unknown';
    
    final sortedEntries = usageByContext.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.first.key;
  }

  /// Get efficiency score (0-100)
  double get efficiencyScore {
    final completionScore = completionRate * 40;
    final wasteScore = (100 - wastePercentage) * 0.3;
    final qualityScore = averageQualityRating * 20;
    final performanceScore = averagePerformanceRating * 10;
    
    return completionScore + wasteScore + qualityScore + performanceScore;
  }

  /// Check if statistics show good performance
  bool get showsGoodPerformance {
    return completionRate >= 0.8 && 
           wastePercentage <= 10 && 
           averageQualityRating >= 4.0;
  }

  /// Get trend direction based on monthly usage
  String get trendDirection {
    if (monthlyUsage.length < 2) return 'Stable';
    
    final values = monthlyUsage.values.toList();
    final recent = values.length >= 3 ? values.sublist(values.length - 3) : values;
    final earlier = values.take(values.length - 3).toList();
    
    if (recent.isEmpty || earlier.isEmpty) return 'Stable';
    
    final recentAvg = recent.reduce((a, b) => a + b) / recent.length;
    final earlierAvg = earlier.reduce((a, b) => a + b) / earlier.length;
    
    if (recentAvg > earlierAvg * 1.1) return 'Increasing';
    if (recentAvg < earlierAvg * 0.9) return 'Decreasing';
    return 'Stable';
  }
}

/// Trending material business logic extensions
extension TrendingMaterialX on TrendingMaterial {
  /// Check if material is trending up
  bool get isTrendingUp => trendDirection == 'Up' || growthRate > 0;

  /// Check if material is trending down
  bool get isTrendingDown => trendDirection == 'Down' || growthRate < 0;

  /// Get growth rate percentage
  String get growthRatePercentage {
    return '${(growthRate * 100).toStringAsFixed(1)}%';
  }

  /// Get average cost per unit
  double get averageCostPerUnit {
    if (averagePrice != null && totalQuantity > 0) {
      return averagePrice! / totalQuantity;
    }
    return 0.0;
  }
}

/// Material recommendation business logic extensions
extension MaterialRecommendationX on MaterialRecommendation {
  /// Check if recommendation has high confidence
  bool get hasHighConfidence => confidence >= 0.8;

  /// Check if recommendation has medium confidence
  bool get hasMediumConfidence => confidence >= 0.5 && confidence < 0.8;

  /// Check if recommendation has low confidence
  bool get hasLowConfidence => confidence < 0.5;

  /// Get confidence percentage
  String get confidencePercentage {
    return '${(confidence * 100).toStringAsFixed(0)}%';
  }

  /// Get confidence level description
  String get confidenceLevel {
    if (hasHighConfidence) return 'High';
    if (hasMediumConfidence) return 'Medium';
    return 'Low';
  }
}

/// Material combination business logic extensions
extension MaterialCombinationX on MaterialCombination {
  /// Check if combination has high confidence
  bool get hasHighConfidence => confidence >= 0.8;

  /// Get confidence percentage
  String get confidencePercentage {
    return '${(confidence * 100).toStringAsFixed(0)}%';
  }

  /// Get relationship description
  String get relationshipDescription {
    switch (relationship.toLowerCase()) {
      case 'complementary':
        return 'Often used together';
      case 'alternative':
        return 'Can be used as alternative';
      case 'prerequisite':
        return 'Required before use';
      case 'followup':
        return 'Used after';
      default:
        return relationship;
    }
  }
}
