import 'dart:async';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/create_material_request.dart';
import '../repositories/material_repository.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/domain/repositories/bom_repository.dart';
import 'data_sync_service.dart';

/// 数据同步服务实现
class DataSyncServiceImpl implements DataSyncService {
  final MaterialRepository _materialRepository;
  final BomRepository _bomRepository;
  final StreamController<SyncOperation> _syncOperationController;
  final List<SyncOperation> _syncHistory = [];

  DataSyncServiceImpl({
    required MaterialRepository materialRepository,
    required BomRepository bomRepository,
  })  : _materialRepository = materialRepository,
        _bomRepository = bomRepository,
        _syncOperationController = StreamController<SyncOperation>.broadcast();

  @override
  Future<Either<Failure, void>> syncMaterialToBom(
    String materialId,
    String bomItemId,
  ) async {
    final operation = SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.materialToBom,
      sourceId: materialId,
      targetId: bomItemId,
      data: {},
      timestamp: DateTime.now(),
    );

    _syncOperationController.add(operation);

    try {
      // 获取材料信息
      final materialResult = await _materialRepository.getMaterialById(materialId);
      if (materialResult.isLeft()) {
        final failedOperation = operation.copyWith(
          status: SyncStatus.failed,
          errorMessage: '材料不存在',
        );
        _syncHistory.add(failedOperation);
        _syncOperationController.add(failedOperation);
        return Left(materialResult.fold((l) => l, (r) => throw Exception()));
      }

      final material = materialResult.fold((l) => throw Exception(), (r) => r);

      // 获取BOM项目信息
      final bomItemResult = await _bomRepository.getBomItemById(bomItemId);
      if (bomItemResult.isLeft()) {
        final failedOperation = operation.copyWith(
          status: SyncStatus.failed,
          errorMessage: 'BOM项目不存在',
        );
        _syncHistory.add(failedOperation);
        _syncOperationController.add(failedOperation);
        return Left(bomItemResult.fold((l) => l, (r) => throw Exception()));
      }

      final bomItem = bomItemResult.fold((l) => throw Exception(), (r) => r);

      // 同步材料信息到BOM项目
      final updatedBomItem = bomItem.copyWith(
        materialName: material.name,
        specifications: material.specifications ?? bomItem.specifications,
        category: material.category,
        unitPrice: material.price,
        // totalPrice 是通过 getter 计算的，不需要在这里设置
        imageUrl: material.imageUrl,
        updatedAt: DateTime.now(),
      );

      final updateResult = await _bomRepository.updateBomItem(updatedBomItem);
      
      return updateResult.fold(
        (failure) {
          final failedOperation = operation.copyWith(
            status: SyncStatus.failed,
            errorMessage: failure.message,
          );
          _syncHistory.add(failedOperation);
          _syncOperationController.add(failedOperation);
          return Left(failure);
        },
        (_) {
          final completedOperation = operation.copyWith(
            status: SyncStatus.completed,
            data: {
              'materialName': material.name,
              'bomItemName': bomItem.name,
              'syncedFields': ['name', 'specification', 'category', 'price', 'image'],
            },
          );
          _syncHistory.add(completedOperation);
          _syncOperationController.add(completedOperation);
          return const Right(null);
        },
      );
    } catch (e) {
      final failedOperation = operation.copyWith(
        status: SyncStatus.failed,
        errorMessage: e.toString(),
      );
      _syncHistory.add(failedOperation);
      _syncOperationController.add(failedOperation);
      return Left(ServerFailure(message: '同步失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> syncBomToMaterial(String bomItemId) async {
    final operation = SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.bomToMaterial,
      sourceId: bomItemId,
      targetId: '',
      data: {},
      timestamp: DateTime.now(),
    );

    _syncOperationController.add(operation);

    try {
      // 获取BOM项目信息
      final bomItemResult = await _bomRepository.getBomItemById(bomItemId);
      if (bomItemResult.isLeft()) {
        final failedOperation = operation.copyWith(
          status: SyncStatus.failed,
          errorMessage: 'BOM项目不存在',
        );
        _syncHistory.add(failedOperation);
        _syncOperationController.add(failedOperation);
        return Left(bomItemResult.fold((l) => l, (r) => throw Exception()));
      }

      final bomItem = bomItemResult.fold((l) => throw Exception(), (r) => r);

      // 检查是否已存在相同的材料
      final existingMaterialsResult = await _materialRepository.searchMaterials(
        bomItem.userId,
        bomItem.name,
        category: bomItem.category,
      );

      if (existingMaterialsResult.isLeft()) {
        final failedOperation = operation.copyWith(
          status: SyncStatus.failed,
          errorMessage: '搜索现有材料失败',
        );
        _syncHistory.add(failedOperation);
        _syncOperationController.add(failedOperation);
        return Left(existingMaterialsResult.fold((l) => l, (r) => throw Exception()));
      }

      final existingMaterials = existingMaterialsResult.fold((l) => throw Exception(), (r) => r);
      
      String materialId;
      
      if (existingMaterials.isNotEmpty) {
        // 更新现有材料
        final existingMaterial = existingMaterials.first;
        final updatedMaterial = existingMaterial.copyWith(
          price: bomItem.unitPrice,
          specifications: bomItem.specifications,
          usageCount: existingMaterial.usageCount + 1,
          lastUsedAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updateResult = await _materialRepository.updateMaterial(
          existingMaterial.id,
          {
            'price': bomItem.unitPrice,
            'specifications': bomItem.specifications,
            'usage_count': existingMaterial.usageCount + 1,
            'last_used_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
        );
        if (updateResult.isLeft()) {
          final failedOperation = operation.copyWith(
            status: SyncStatus.failed,
            errorMessage: '更新材料失败',
          );
          _syncHistory.add(failedOperation);
          _syncOperationController.add(failedOperation);
          return Left(updateResult.fold((l) => l, (r) => throw Exception()));
        }

        materialId = existingMaterial.id;
      } else {
        // 创建新材料请求
        final createMaterialRequest = CreateMaterialRequest(
          name: bomItem.materialName,
          category: bomItem.category ?? '未分类',
          price: bomItem.unitPrice,
          description: bomItem.description,
          specifications: bomItem.specifications,
          brand: bomItem.brand,
          model: bomItem.model,
          supplier: bomItem.supplier,
          supplierUrl: bomItem.supplierUrl,
          imageUrl: bomItem.imageUrl,
          tags: bomItem.tags,
          metadata: bomItem.metadata,
        );

        final createResult = await _materialRepository.createMaterial(createMaterialRequest);
        if (createResult.isLeft()) {
          final failedOperation = operation.copyWith(
            status: SyncStatus.failed,
            errorMessage: '创建材料失败',
          );
          _syncHistory.add(failedOperation);
          _syncOperationController.add(failedOperation);
          return Left(createResult.fold((l) => l, (r) => throw Exception()));
        }

        materialId = createResult.fold((l) => throw Exception(), (r) => r.id);
      }

      final completedOperation = operation.copyWith(
        targetId: materialId,
        status: SyncStatus.completed,
        data: {
          'bomItemName': bomItem.name,
          'materialId': materialId,
          'action': existingMaterials.isNotEmpty ? 'updated' : 'created',
        },
      );
      _syncHistory.add(completedOperation);
      _syncOperationController.add(completedOperation);

      return Right(materialId);
    } catch (e) {
      final failedOperation = operation.copyWith(
        status: SyncStatus.failed,
        errorMessage: e.toString(),
      );
      _syncHistory.add(failedOperation);
      _syncOperationController.add(failedOperation);
      return Left(ServerFailure(message: '同步失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<PriceSyncNotification>>> syncPriceUpdates(
    String materialId,
  ) async {
    try {
      // 获取材料信息
      final materialResult = await _materialRepository.getMaterialById(materialId);
      if (materialResult.isLeft()) {
        return Left(materialResult.fold((l) => l, (r) => throw Exception()));
      }

      final material = materialResult.fold((l) => throw Exception(), (r) => r);

      // 获取使用该材料的所有BOM项目
      final bomItemsResult = await _bomRepository.getBomItemsByMaterialId(materialId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }

      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      final notifications = <PriceSyncNotification>[];

      // 为每个不同的价格创建通知
      final priceGroups = <double, List<BomItem>>{};
      for (final bomItem in bomItems) {
        if (bomItem.unitPrice != material.price) {
          priceGroups.putIfAbsent(bomItem.unitPrice, () => []).add(bomItem);
        }
      }

      for (final entry in priceGroups.entries) {
        final oldPrice = entry.key;
        final affectedItems = entry.value;
        
        notifications.add(PriceSyncNotification(
          materialId: materialId,
          materialName: material.name,
          oldPrice: oldPrice,
          newPrice: material.price,
          affectedBomItemIds: affectedItems.map((item) => item.id).toList(),
          timestamp: DateTime.now(),
        ));
      }

      return Right(notifications);
    } catch (e) {
      return Left(ServerFailure(message: '获取价格同步提醒失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMaterialUsageStats(
    String materialId,
    int usageIncrement,
  ) async {
    try {
      final materialResult = await _materialRepository.getMaterialById(materialId);
      if (materialResult.isLeft()) {
        return Left(materialResult.fold((l) => l, (r) => throw Exception()));
      }

      final material = materialResult.fold((l) => throw Exception(), (r) => r);
      
      final updatedMaterial = material.copyWith(
        usageCount: material.usageCount + usageIncrement,
        lastUsedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final updateResult = await _materialRepository.updateMaterial(
        material.id,
        {
          'usage_count': material.usageCount + usageIncrement,
          'last_used_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      );
      return updateResult.fold(
        (failure) => Left(failure),
        (_) => const Right(null),
      );
    } catch (e) {
      return Left(ServerFailure(message: '更新使用统计失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMaterialLastUsed(String materialId) async {
    return updateMaterialUsageStats(materialId, 0);
  }

  @override
  Future<Either<Failure, List<PriceSyncNotification>>> getPriceSyncNotifications(
    String userId,
  ) async {
    try {
      // 获取用户的所有项目
      final userProjectsResult = await _bomRepository.getUserProjects(userId);
      if (userProjectsResult.isLeft()) {
        return Left(userProjectsResult.fold((l) => l, (r) => throw Exception()));
      }

      final userProjects = userProjectsResult.fold((l) => throw Exception(), (r) => r);
      final notifications = <PriceSyncNotification>[];

      // 检查每个项目的BOM项目是否有价格更新
      for (final project in userProjects) {
        final bomItemsResult = await _bomRepository.getBomItemsByProjectId(project.id);
        if (bomItemsResult.isRight()) {
          final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
          
          for (final bomItem in bomItems) {
            if (bomItem.materialId != null) {
              final syncNotifications = await syncPriceUpdates(bomItem.materialId!);
              if (syncNotifications.isRight()) {
                notifications.addAll(syncNotifications.fold((l) => throw Exception(), (r) => r));
              }
            }
          }
        }
      }

      return Right(notifications);
    } catch (e) {
      return Left(ServerFailure(message: '获取价格同步提醒失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> confirmPriceSync(
    String materialId,
    List<String> bomItemIds,
    bool syncPrice,
  ) async {
    if (!syncPrice) {
      return const Right(null);
    }

    try {
      final materialResult = await _materialRepository.getMaterialById(materialId);
      if (materialResult.isLeft()) {
        return Left(materialResult.fold((l) => l, (r) => throw Exception()));
      }

      final material = materialResult.fold((l) => throw Exception(), (r) => r);

      // 更新所有相关的BOM项目价格
      for (final bomItemId in bomItemIds) {
        final bomItemResult = await _bomRepository.getBomItemById(bomItemId);
        if (bomItemResult.isRight()) {
          final bomItem = bomItemResult.fold((l) => throw Exception(), (r) => r);
          final updatedBomItem = bomItem.copyWith(
            unitPrice: material.price,
            // totalPrice 是通过 getter 计算的，不需要在这里设置
            updatedAt: DateTime.now(),
          );
          await _bomRepository.updateBomItem(updatedBomItem);
        }
      }

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: '确认价格同步失败: ${e.toString()}'));
    }
  }

  @override
  Stream<SyncOperation> watchSyncOperations() {
    return _syncOperationController.stream;
  }

  @override
  Future<Either<Failure, List<SyncOperation>>> getSyncHistory({
    String? materialId,
    String? bomItemId,
    SyncOperationType? type,
    int limit = 50,
  }) async {
    try {
      var filteredHistory = _syncHistory.toList();

      if (materialId != null) {
        filteredHistory = filteredHistory.where((op) => 
          op.sourceId == materialId || op.targetId == materialId).toList();
      }

      if (bomItemId != null) {
        filteredHistory = filteredHistory.where((op) => 
          op.sourceId == bomItemId || op.targetId == bomItemId).toList();
      }

      if (type != null) {
        filteredHistory = filteredHistory.where((op) => op.type == type).toList();
      }

      // 按时间倒序排列
      filteredHistory.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // 限制数量
      if (filteredHistory.length > limit) {
        filteredHistory = filteredHistory.take(limit).toList();
      }

      return Right(filteredHistory);
    } catch (e) {
      return Left(ServerFailure(message: '获取同步历史失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> retrySyncOperation(String operationId) async {
    try {
      final operation = _syncHistory.firstWhere(
        (op) => op.id == operationId,
        orElse: () => throw Exception('同步操作不存在'),
      );

      if (operation.status != SyncStatus.failed) {
        return Left(ValidationFailure(message: '只能重试失败的同步操作'));
      }

      // 根据操作类型重新执行
      switch (operation.type) {
        case SyncOperationType.materialToBom:
          return syncMaterialToBom(operation.sourceId, operation.targetId);
        case SyncOperationType.bomToMaterial:
          final result = await syncBomToMaterial(operation.sourceId);
          return result.fold(
            (failure) => Left(failure),
            (_) => const Right(null),
          );
        case SyncOperationType.priceUpdate:
          final result = await syncPriceUpdates(operation.sourceId);
          return result.fold(
            (failure) => Left(failure),
            (_) => const Right(null),
          );
        case SyncOperationType.statusUpdate:
          return const Right(null);
      }
    } catch (e) {
      return Left(ServerFailure(message: '重试同步操作失败: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelSyncOperation(String operationId) async {
    try {
      final operationIndex = _syncHistory.indexWhere((op) => op.id == operationId);
      if (operationIndex == -1) {
        return Left(ValidationFailure(message: '同步操作不存在'));
      }

      final operation = _syncHistory[operationIndex];
      if (operation.status != SyncStatus.pending) {
        return Left(ValidationFailure(message: '只能取消待处理的同步操作'));
      }

      final cancelledOperation = operation.copyWith(status: SyncStatus.cancelled);
      _syncHistory[operationIndex] = cancelledOperation;
      _syncOperationController.add(cancelledOperation);

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: '取消同步操作失败: ${e.toString()}'));
    }
  }

  void dispose() {
    _syncOperationController.close();
  }
}