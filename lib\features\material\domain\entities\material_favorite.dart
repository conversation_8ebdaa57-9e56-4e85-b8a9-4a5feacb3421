import 'package:freezed_annotation/freezed_annotation.dart';

part 'material_favorite.freezed.dart';
part 'material_favorite.g.dart';

/// 材料收藏实体
/// 记录用户对材料的收藏状态和相关信息
@freezed
class MaterialFavorite with _$MaterialFavorite {
  const factory MaterialFavorite({
    /// 收藏记录ID
    required String id,
    
    /// 用户ID
    required String userId,
    
    /// 材料ID
    required String materialId,
    
    /// 收藏时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 收藏标签（可选）
    List<String>? tags,
    
    /// 收藏备注（可选）
    String? notes,
    
    /// 收藏分类（可选）
    String? category,
    
    /// 优先级（1-5，5为最高）
    @Default(3) int priority,
    
    /// 是否置顶
    @Default(false) bool isPinned,
    
    /// 扩展属性
    Map<String, dynamic>? metadata,
  }) = _MaterialFavorite;

  factory MaterialFavorite.fromJson(Map<String, dynamic> json) => 
      _$MaterialFavoriteFromJson(json);
}

/// 收藏优先级枚举
enum FavoritePriority {
  @JsonValue(1)
  lowest,   // 最低

  @JsonValue(2)
  low,      // 低

  @JsonValue(3)
  normal,   // 普通

  @JsonValue(4)
  high,     // 高

  @JsonValue(5)
  highest,  // 最高
}

/// 收藏优先级扩展
extension FavoritePriorityX on FavoritePriority {
  /// 从数值创建优先级枚举
  static FavoritePriority fromValue(int value) {
    switch (value) {
      case 1:
        return FavoritePriority.lowest;
      case 2:
        return FavoritePriority.low;
      case 3:
        return FavoritePriority.normal;
      case 4:
        return FavoritePriority.high;
      case 5:
        return FavoritePriority.highest;
      default:
        return FavoritePriority.normal;
    }
  }

  /// 获取数值
  int get value {
    switch (this) {
      case FavoritePriority.lowest:
        return 1;
      case FavoritePriority.low:
        return 2;
      case FavoritePriority.normal:
        return 3;
      case FavoritePriority.high:
        return 4;
      case FavoritePriority.highest:
        return 5;
    }
  }
}

/// 收藏优先级显示扩展
extension FavoritePriorityDisplayX on FavoritePriority {
  String get displayName {
    switch (this) {
      case FavoritePriority.lowest:
        return '最低';
      case FavoritePriority.low:
        return '低';
      case FavoritePriority.normal:
        return '普通';
      case FavoritePriority.high:
        return '高';
      case FavoritePriority.highest:
        return '最高';
    }
  }
  
  String get color {
    switch (this) {
      case FavoritePriority.lowest:
        return '#9E9E9E'; // 灰色
      case FavoritePriority.low:
        return '#2196F3'; // 蓝色
      case FavoritePriority.normal:
        return '#4CAF50'; // 绿色
      case FavoritePriority.high:
        return '#FF9800'; // 橙色
      case FavoritePriority.highest:
        return '#F44336'; // 红色
    }
  }
  
  String get icon {
    switch (this) {
      case FavoritePriority.lowest:
        return '⭐';
      case FavoritePriority.low:
        return '⭐⭐';
      case FavoritePriority.normal:
        return '⭐⭐⭐';
      case FavoritePriority.high:
        return '⭐⭐⭐⭐';
      case FavoritePriority.highest:
        return '⭐⭐⭐⭐⭐';
    }
  }
  
  int get value {
    switch (this) {
      case FavoritePriority.lowest:
        return 1;
      case FavoritePriority.low:
        return 2;
      case FavoritePriority.normal:
        return 3;
      case FavoritePriority.high:
        return 4;
      case FavoritePriority.highest:
        return 5;
    }
  }
  
  static FavoritePriority fromValue(int value) {
    switch (value) {
      case 1:
        return FavoritePriority.lowest;
      case 2:
        return FavoritePriority.low;
      case 3:
        return FavoritePriority.normal;
      case 4:
        return FavoritePriority.high;
      case 5:
        return FavoritePriority.highest;
      default:
        return FavoritePriority.normal;
    }
  }
}

/// 材料收藏扩展方法
extension MaterialFavoriteX on MaterialFavorite {
  /// 获取优先级枚举
  FavoritePriority get priorityEnum => FavoritePriorityX.fromValue(priority);
  
  /// 是否为高优先级（4或5）
  bool get isHighPriority => priority >= 4;
  
  /// 是否为低优先级（1或2）
  bool get isLowPriority => priority <= 2;
  
  /// 获取收藏时长（天数）
  int get daysSinceFavorited {
    return DateTime.now().difference(createdAt).inDays;
  }
  
  /// 是否为最近收藏（7天内）
  bool get isRecentlyFavorited => daysSinceFavorited <= 7;
  
  /// 是否有标签
  bool get hasTags => tags != null && tags!.isNotEmpty;
  
  /// 是否有备注
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  
  /// 获取标签字符串
  String get tagsString => hasTags ? tags!.join(', ') : '';
  
  /// 创建副本并更新优先级
  MaterialFavorite updatePriority(int newPriority) {
    return copyWith(
      priority: newPriority.clamp(1, 5),
      updatedAt: DateTime.now(),
    );
  }
  
  /// 创建副本并切换置顶状态
  MaterialFavorite togglePin() {
    return copyWith(
      isPinned: !isPinned,
      updatedAt: DateTime.now(),
    );
  }
  
  /// 创建副本并添加标签
  MaterialFavorite addTag(String tag) {
    final currentTags = tags ?? [];
    if (!currentTags.contains(tag)) {
      return copyWith(
        tags: [...currentTags, tag],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }
  
  /// 创建副本并移除标签
  MaterialFavorite removeTag(String tag) {
    final currentTags = tags ?? [];
    if (currentTags.contains(tag)) {
      return copyWith(
        tags: currentTags.where((t) => t != tag).toList(),
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }
  
  /// 创建副本并更新备注
  MaterialFavorite updateNotes(String? newNotes) {
    return copyWith(
      notes: newNotes,
      updatedAt: DateTime.now(),
    );
  }
  
  /// 创建副本并更新分类
  MaterialFavorite updateCategory(String? newCategory) {
    return copyWith(
      category: newCategory,
      updatedAt: DateTime.now(),
    );
  }
}

/// 收藏统计信息
class FavoriteStats {
  final int totalCount;
  final int pinnedCount;
  final int highPriorityCount;
  final int recentCount;
  final Map<String, int> categoryCount;
  final Map<String, int> tagCount;

  const FavoriteStats({
    required this.totalCount,
    required this.pinnedCount,
    required this.highPriorityCount,
    required this.recentCount,
    required this.categoryCount,
    required this.tagCount,
  });

  factory FavoriteStats.empty() {
    return const FavoriteStats(
      totalCount: 0,
      pinnedCount: 0,
      highPriorityCount: 0,
      recentCount: 0,
      categoryCount: {},
      tagCount: {},
    );
  }

  factory FavoriteStats.fromFavorites(List<MaterialFavorite> favorites) {
    final categoryCount = <String, int>{};
    final tagCount = <String, int>{};
    int pinnedCount = 0;
    int highPriorityCount = 0;
    int recentCount = 0;

    for (final favorite in favorites) {
      // 统计分类
      if (favorite.category != null) {
        categoryCount[favorite.category!] = 
            (categoryCount[favorite.category!] ?? 0) + 1;
      }
      
      // 统计标签
      if (favorite.hasTags) {
        for (final tag in favorite.tags!) {
          tagCount[tag] = (tagCount[tag] ?? 0) + 1;
        }
      }
      
      // 统计置顶
      if (favorite.isPinned) pinnedCount++;
      
      // 统计高优先级
      if (favorite.isHighPriority) highPriorityCount++;
      
      // 统计最近收藏
      if (favorite.isRecentlyFavorited) recentCount++;
    }

    return FavoriteStats(
      totalCount: favorites.length,
      pinnedCount: pinnedCount,
      highPriorityCount: highPriorityCount,
      recentCount: recentCount,
      categoryCount: categoryCount,
      tagCount: tagCount,
    );
  }
}
