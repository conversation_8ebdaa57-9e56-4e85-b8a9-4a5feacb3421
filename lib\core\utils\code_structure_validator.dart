import 'package:flutter/foundation.dart';

/// 代码结构验证工具
class CodeStructureValidator {
  /// 验证括号匹配
  static bool validateBrackets(String code) {
    final stack = <String>[];
    final openBrackets = {'(', '[', '{'};
    final closeBrackets = {')', ']', '}'};
    final bracketPairs = {'(': ')', '[': ']', '{': '}'};
    
    for (int i = 0; i < code.length; i++) {
      final char = code[i];
      
      if (openBrackets.contains(char)) {
        stack.add(char);
      } else if (closeBrackets.contains(char)) {
        if (stack.isEmpty) {
          if (kDebugMode) {
            print('❌ 多余的闭合括号: $char at position $i');
          }
          return false;
        }
        
        final lastOpen = stack.removeLast();
        if (bracketPairs[lastOpen] != char) {
          if (kDebugMode) {
            print('❌ 括号不匹配: $lastOpen 和 $char at position $i');
          }
          return false;
        }
      }
    }
    
    if (stack.isNotEmpty) {
      if (kDebugMode) {
        print('❌ 未闭合的括号: ${stack.join(', ')}');
      }
      return false;
    }
    
    if (kDebugMode) {
      print('✅ 括号匹配验证通过');
    }
    return true;
  }
  
  /// 验证Widget结构
  static bool validateWidgetStructure(String code) {
    // 检查常见的Widget结构问题
    final issues = <String>[];
    
    // 检查Scaffold是否正确闭合
    final scaffoldMatches = RegExp(r'Scaffold\s*\(').allMatches(code);
    if (scaffoldMatches.isNotEmpty) {
      // 简单检查：确保有对应的闭合
      final openCount = scaffoldMatches.length;
      // 这里可以添加更复杂的验证逻辑
      if (kDebugMode) {
        print('🔍 发现 $openCount 个Scaffold组件');
      }
    }
    
    // 检查build方法是否正确闭合
    final buildMethodPattern = RegExp(r'Widget\s+build\s*\([^)]*\)\s*\{');
    final buildMatches = buildMethodPattern.allMatches(code);
    if (buildMatches.isNotEmpty && kDebugMode) {
      debugPrint('🔍 发现 ${buildMatches.length} 个build方法');
    }
    
    return issues.isEmpty;
  }
  
  /// 生成安全的Widget结构模板
  static String generateSafeWidgetTemplate({
    required String className,
    required String widgetType,
    List<String> imports = const [],
  }) {
    final buffer = StringBuffer();
    
    // 添加导入
    for (final import in imports) {
      buffer.writeln("import '$import';");
    }
    if (imports.isNotEmpty) buffer.writeln();
    
    // 生成类结构
    buffer.writeln('class $className extends $widgetType {');
    buffer.writeln('  const $className({super.key});');
    buffer.writeln();
    
    if (widgetType == 'StatefulWidget') {
      buffer.writeln('  @override');
      buffer.writeln('  State<$className> createState() => _${className}State();');
      buffer.writeln('}');
      buffer.writeln();
      buffer.writeln('class _${className}State extends State<$className> {');
    }
    
    buffer.writeln('  @override');
    buffer.writeln('  Widget build(BuildContext context) {');
    buffer.writeln('    return Scaffold(');
    buffer.writeln('      appBar: AppBar(');
    buffer.writeln('        title: const Text(\'$className\'),');
    buffer.writeln('      ),');
    buffer.writeln('      body: const Center(');
    buffer.writeln('        child: Text(\'$className Content\'),');
    buffer.writeln('      ),');
    buffer.writeln('    );');
    buffer.writeln('  }');
    buffer.writeln('}');
    
    return buffer.toString();
  }
}

/// Widget构建器辅助类
class SafeWidgetBuilder {
  final StringBuffer _buffer = StringBuffer();
  final List<String> _openTags = [];
  
  /// 开始一个Widget
  SafeWidgetBuilder startWidget(String widgetName, {Map<String, String>? properties}) {
    _buffer.write('$widgetName(');
    _openTags.add(widgetName);
    
    if (properties != null && properties.isNotEmpty) {
      _buffer.writeln();
      for (final entry in properties.entries) {
        _buffer.writeln('  ${entry.key}: ${entry.value},');
      }
    }
    
    return this;
  }
  
  /// 添加子Widget
  SafeWidgetBuilder child(String childWidget) {
    _buffer.writeln('  child: $childWidget,');
    return this;
  }
  
  /// 添加children
  SafeWidgetBuilder children(List<String> childrenWidgets) {
    _buffer.writeln('  children: [');
    for (final child in childrenWidgets) {
      _buffer.writeln('    $child,');
    }
    _buffer.writeln('  ],');
    return this;
  }
  
  /// 结束当前Widget
  SafeWidgetBuilder endWidget() {
    if (_openTags.isNotEmpty) {
      _openTags.removeLast();
      _buffer.write(')');
      if (_openTags.isNotEmpty) {
        _buffer.writeln(',');
      }
    }
    return this;
  }
  
  /// 构建最终代码
  String build() {
    // 确保所有标签都已闭合
    while (_openTags.isNotEmpty) {
      endWidget();
    }
    return _buffer.toString();
  }
  
  /// 验证结构
  bool validate() {
    return _openTags.isEmpty;
  }
  
  /// 获取当前未闭合的标签
  List<String> getOpenTags() {
    return List.from(_openTags);
  }
}

/// 安全的Scaffold构建器
class SafeScaffoldBuilder {
  String? _appBarTitle;
  String? _body;
  List<String>? _actions;
  String? _floatingActionButton;
  String? _bottomNavigationBar;
  
  SafeScaffoldBuilder appBar({required String title, List<String>? actions}) {
    _appBarTitle = title;
    _actions = actions;
    return this;
  }
  
  SafeScaffoldBuilder body(String bodyWidget) {
    _body = bodyWidget;
    return this;
  }
  
  SafeScaffoldBuilder floatingActionButton(String fab) {
    _floatingActionButton = fab;
    return this;
  }
  
  SafeScaffoldBuilder bottomNavigationBar(String bottomNav) {
    _bottomNavigationBar = bottomNav;
    return this;
  }
  
  String build() {
    final buffer = StringBuffer();
    buffer.writeln('Scaffold(');
    
    // AppBar
    if (_appBarTitle != null) {
      buffer.writeln('  appBar: AppBar(');
      buffer.writeln('    title: Text(\'$_appBarTitle\'),');
      if (_actions != null && _actions!.isNotEmpty) {
        buffer.writeln('    actions: [');
        for (final action in _actions!) {
          buffer.writeln('      $action,');
        }
        buffer.writeln('    ],');
      }
      buffer.writeln('  ),');
    }
    
    // Body
    if (_body != null) {
      buffer.writeln('  body: $_body,');
    }
    
    // FloatingActionButton
    if (_floatingActionButton != null) {
      buffer.writeln('  floatingActionButton: $_floatingActionButton,');
    }
    
    // BottomNavigationBar
    if (_bottomNavigationBar != null) {
      buffer.writeln('  bottomNavigationBar: $_bottomNavigationBar,');
    }
    
    buffer.writeln(')');
    return buffer.toString();
  }
}

/// 代码模板生成器
class CodeTemplateGenerator {
  /// 生成安全的StatefulWidget模板
  static String generateStatefulWidget(String className) {
    return '''
class $className extends StatefulWidget {
  const $className({super.key});

  @override
  State<$className> createState() => _${className}State();
}

class _${className}State extends State<$className> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$className'),
      ),
      body: const Center(
        child: Text('$className Content'),
      ),
    );
  }
}
''';
  }
  
  /// 生成安全的StatelessWidget模板
  static String generateStatelessWidget(String className) {
    return '''
class $className extends StatelessWidget {
  const $className({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$className'),
      ),
      body: const Center(
        child: Text('$className Content'),
      ),
    );
  }
}
''';
  }
}
