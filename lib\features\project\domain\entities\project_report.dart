/// 项目报告实体
class ProjectReport {
  final String projectId;
  final ProjectReportType type;
  final String title;
  final String summary;
  final Map<String, dynamic> data;
  final List<ReportSection> sections;
  final DateTime generatedAt;
  final String generatedBy;

  const ProjectReport({
    required this.projectId,
    required this.type,
    required this.title,
    required this.summary,
    required this.data,
    required this.sections,
    required this.generatedAt,
    required this.generatedBy,
  });

  factory ProjectReport.fromJson(Map<String, dynamic> json) {
    return ProjectReport(
      projectId: json['project_id'],
      type: ProjectReportType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => ProjectReportType.summary,
      ),
      title: json['title'],
      summary: json['summary'],
      data: Map<String, dynamic>.from(json['data']),
      sections: (json['sections'] as List<dynamic>)
          .map((item) => ReportSection.fromJson(item))
          .toList(),
      generatedAt: DateTime.parse(json['generated_at']),
      generatedBy: json['generated_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'project_id': projectId,
      'type': type.name,
      'title': title,
      'summary': summary,
      'data': data,
      'sections': sections.map((section) => section.toJson()).toList(),
      'generated_at': generatedAt.toIso8601String(),
      'generated_by': generatedBy,
    };
  }
}

/// 项目报告类型枚举
enum ProjectReportType {
  summary,     // 摘要报告
  detailed,    // 详细报告
  financial,   // 财务报告
  progress,    // 进度报告
  risk,        // 风险报告
  comparison,  // 比较报告
}

/// 报告章节
class ReportSection {
  final String title;
  final String content;
  final List<ReportChart> charts;
  final List<ReportTable> tables;
  final int order;

  const ReportSection({
    required this.title,
    required this.content,
    required this.charts,
    required this.tables,
    required this.order,
  });

  factory ReportSection.fromJson(Map<String, dynamic> json) {
    return ReportSection(
      title: json['title'],
      content: json['content'],
      charts: (json['charts'] as List<dynamic>)
          .map((item) => ReportChart.fromJson(item))
          .toList(),
      tables: (json['tables'] as List<dynamic>)
          .map((item) => ReportTable.fromJson(item))
          .toList(),
      order: json['order'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'charts': charts.map((chart) => chart.toJson()).toList(),
      'tables': tables.map((table) => table.toJson()).toList(),
      'order': order,
    };
  }
}

/// 报告图表
class ReportChart {
  final String title;
  final String type;
  final Map<String, dynamic> data;

  const ReportChart({
    required this.title,
    required this.type,
    required this.data,
  });

  factory ReportChart.fromJson(Map<String, dynamic> json) {
    return ReportChart(
      title: json['title'],
      type: json['type'],
      data: Map<String, dynamic>.from(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'type': type,
      'data': data,
    };
  }
}

/// 报告表格
class ReportTable {
  final String title;
  final List<String> headers;
  final List<List<String>> rows;

  const ReportTable({
    required this.title,
    required this.headers,
    required this.rows,
  });

  factory ReportTable.fromJson(Map<String, dynamic> json) {
    return ReportTable(
      title: json['title'],
      headers: List<String>.from(json['headers']),
      rows: (json['rows'] as List<dynamic>)
          .map((row) => List<String>.from(row))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'headers': headers,
      'rows': rows,
    };
  }
}