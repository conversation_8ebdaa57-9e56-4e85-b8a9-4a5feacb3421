import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/search_criteria.dart';
import '../../domain/repositories/material_repository.dart';
import '../../domain/services/material_search_service.dart';

/// 材料搜索服务实现
class MaterialSearchServiceImpl implements MaterialSearchService {
  final MaterialRepository materialRepository;

  const MaterialSearchServiceImpl({required this.materialRepository});

  @override
  Future<Either<Failure, List<Material>>> searchMaterials(
    String userId,
    SearchCriteria criteria,
  ) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (criteria.isEmpty) {
        return const Left(ValidationFailure(message: '搜索条件不能为空'));
      }

      // 基础搜索
      final result = await materialRepository.searchMaterials(
        userId,
        criteria.query ?? '',
        category: criteria.category,
        minPrice: criteria.minPrice,
        maxPrice: criteria.maxPrice,
        limit: criteria.limit,
        offset: criteria.offset,
      );

      return result.fold(
        (failure) => Left(failure),
        (materials) {
          // 应用额外的过滤条件
          var filteredMaterials = materials;

          // 按品牌过滤
          if (criteria.brand != null && criteria.brand!.isNotEmpty) {
            filteredMaterials = filteredMaterials
                .where((m) => m.brand?.toLowerCase() == criteria.brand!.toLowerCase())
                .toList();
          }

          // 按标签过滤
          if (criteria.tags != null && criteria.tags!.isNotEmpty) {
            filteredMaterials = filteredMaterials.where((m) {
              if (m.tags == null) return false;
              return criteria.tags!.any((tag) => m.tags!.contains(tag));
            }).toList();
          }

          // 按供应商过滤
          if (criteria.supplier != null && criteria.supplier!.isNotEmpty) {
            filteredMaterials = filteredMaterials
                .where((m) => m.supplier?.toLowerCase() == criteria.supplier!.toLowerCase())
                .toList();
          }

          // 按使用次数过滤
          if (criteria.minUsageCount != null) {
            filteredMaterials = filteredMaterials
                .where((m) => m.usageCount >= criteria.minUsageCount!)
                .toList();
          }

          // 按最近使用时间过滤
          if (criteria.recentlyUsedDays != null) {
            final cutoffDate = DateTime.now().subtract(Duration(days: criteria.recentlyUsedDays!));
            filteredMaterials = filteredMaterials.where((m) {
              if (m.lastUsedAt == null) return false;
              return m.lastUsedAt!.isAfter(cutoffDate);
            }).toList();
          }

          // 按图片过滤
          if (criteria.onlyWithImages) {
            filteredMaterials = filteredMaterials
                .where((m) => m.imageUrl != null && m.imageUrl!.isNotEmpty)
                .toList();
          }

          // 排序
          if (criteria.sortBy.isNotEmpty) {
            filteredMaterials.sort((a, b) {
              dynamic valueA;
              dynamic valueB;

              switch (criteria.sortBy) {
                case 'name':
                  valueA = a.name;
                  valueB = b.name;
                  break;
                case 'price':
                  valueA = a.price;
                  valueB = b.price;
                  break;
                case 'usage_count':
                  valueA = a.usageCount;
                  valueB = b.usageCount;
                  break;
                case 'last_used_at':
                  valueA = a.lastUsedAt ?? DateTime(1970);
                  valueB = b.lastUsedAt ?? DateTime(1970);
                  break;
                case 'created_at':
                default:
                  valueA = a.createdAt;
                  valueB = b.createdAt;
              }

              int comparison;
              if (valueA is String && valueB is String) {
                comparison = valueA.compareTo(valueB);
              } else if (valueA is num && valueB is num) {
                comparison = valueA.compareTo(valueB);
              } else if (valueA is DateTime && valueB is DateTime) {
                comparison = valueA.compareTo(valueB);
              } else {
                comparison = 0;
              }

              return criteria.ascending ? comparison : -comparison;
            });
          }

          return Right(filteredMaterials);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '搜索材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> advancedSearch(
    String userId,
    Map<String, dynamic> filters, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (filters.isEmpty) {
        return const Left(ValidationFailure(message: '过滤条件不能为空'));
      }

      // 构建搜索条件
      final criteria = SearchCriteria(
        query: filters['query'] as String?,
        category: filters['category'] as String?,
        brand: filters['brand'] as String?,
        minPrice: filters['min_price'] as double?,
        maxPrice: filters['max_price'] as double?,
        tags: filters['tags'] as List<String>?,
        supplier: filters['supplier'] as String?,
        sortBy: filters['sort_by'] as String? ?? 'created_at',
        ascending: filters['ascending'] as bool? ?? false,
        limit: limit,
        offset: offset,
        includeDiscontinued: filters['include_discontinued'] as bool? ?? false,
        onlyInStock: filters['only_in_stock'] as bool? ?? false,
        onlyWithImages: filters['only_with_images'] as bool? ?? false,
        minUsageCount: filters['min_usage_count'] as int?,
        recentlyUsedDays: filters['recently_used_days'] as int?,
        customFilters: filters,
      );

      return searchMaterials(userId, criteria);
    } catch (e) {
      return Left(UnknownFailure(message: '高级搜索失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> searchByTags(
    String userId,
    List<String> tags, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (tags.isEmpty) {
        return const Left(ValidationFailure(message: '标签列表不能为空'));
      }

      // 获取用户所有材料
      final result = await materialRepository.getUserMaterials(userId);

      return result.fold(
        (failure) => Left(failure),
        (materials) {
          // 按标签过滤
          final filteredMaterials = materials.where((m) {
            if (m.tags == null) return false;
            return tags.any((tag) => m.tags!.contains(tag));
          }).toList();

          // 应用分页
          final startIndex = offset;
          final endIndex = offset + limit;
          final pagedMaterials = filteredMaterials.length > startIndex
              ? filteredMaterials.sublist(
                  startIndex,
                  endIndex < filteredMaterials.length ? endIndex : filteredMaterials.length,
                )
              : <Material>[];

          return Right(pagedMaterials);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '按标签搜索失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> searchByProjectType(
    String userId,
    String vehicleBrand,
    String vehicleModel, {
    String? systemType,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (vehicleBrand.isEmpty) {
        return const Left(ValidationFailure(message: '车辆品牌不能为空'));
      }

      // 构建搜索条件
      final criteria = SearchCriteria(
        customFilters: {
          'vehicle_brand': vehicleBrand,
          'vehicle_model': vehicleModel,
          'system_type': systemType,
        },
        limit: limit,
        offset: offset,
      );

      return searchMaterials(userId, criteria);
    } catch (e) {
      return Left(UnknownFailure(message: '按项目类型搜索失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> searchBySystemType(
    String userId,
    String systemType, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (systemType.isEmpty) {
        return const Left(ValidationFailure(message: '系统类型不能为空'));
      }

      // 构建搜索条件
      final criteria = SearchCriteria(
        customFilters: {
          'system_type': systemType,
        },
        limit: limit,
        offset: offset,
      );

      return searchMaterials(userId, criteria);
    } catch (e) {
      return Left(UnknownFailure(message: '按系统类型搜索失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getSuggestions(
    String userId,
    String query, {
    int limit = 10,
  }) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      if (query.isEmpty) {
        return const Left(ValidationFailure(message: '查询关键词不能为空'));
      }

      // 获取用户所有材料
      final result = await materialRepository.getUserMaterials(userId);

      return result.fold(
        (failure) => Left(failure),
        (materials) {
          final suggestions = <String>{};
          final lowerQuery = query.toLowerCase();

          // 从名称中提取建议
          for (final material in materials) {
            if (material.name.toLowerCase().contains(lowerQuery)) {
              suggestions.add(material.name);
            }
          }

          // 从品牌中提取建议
          for (final material in materials) {
            if (material.brand != null &&
                material.brand!.isNotEmpty &&
                material.brand!.toLowerCase().contains(lowerQuery)) {
              suggestions.add(material.brand!);
            }
          }

          // 从型号中提取建议
          for (final material in materials) {
            if (material.model != null &&
                material.model!.isNotEmpty &&
                material.model!.toLowerCase().contains(lowerQuery)) {
              suggestions.add(material.model!);
            }
          }

          // 从标签中提取建议
          for (final material in materials) {
            if (material.tags != null) {
              for (final tag in material.tags!) {
                if (tag.toLowerCase().contains(lowerQuery)) {
                  suggestions.add(tag);
                }
              }
            }
          }

          // 限制结果数量
          final limitedSuggestions = suggestions.take(limit).toList();
          return Right(limitedSuggestions);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取搜索建议失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getPopularSearchTerms({int limit = 10}) async {
    // 这里应该从后端获取热门搜索词，但由于没有相应的API，我们返回一些预设的热门词
    try {
      final popularTerms = [
        '电路板',
        '太阳能',
        '水泵',
        '电池',
        '逆变器',
        '储物箱',
        '床垫',
        '冰箱',
        '灯带',
        '水箱',
        '排风扇',
        '隔热材料',
        '车顶帐篷',
        '折叠桌',
        '车载冰箱',
      ];

      return Right(popularTerms.take(limit).toList());
    } catch (e) {
      return Left(UnknownFailure(message: '获取热门搜索词失败: $e'));
    }
  }
}