import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/material_usage_history.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../providers/material_sync_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 材料同步信息组件
/// 显示材料与BOM之间的关联信息和同步状态
class MaterialSyncInfoWidget extends ConsumerWidget {
  final domain.Material material;
  final bool showSyncActions;

  const MaterialSyncInfoWidget({
    super.key,
    required this.material,
    this.showSyncActions = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final linkedBomItemsAsync = ref.watch(linkedBomItemsProvider(material.id));

    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            linkedBomItemsAsync.when(
              data: (bomItems) => _buildSyncInfo(context, ref, bomItems),
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => _buildErrorState(context, error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.sync,
          color: VanHubColors.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          '同步信息',
          style: VanHubTypography.titleMedium,
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: VanHubColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '使用${material.usageCount}次',
            style: VanHubTypography.bodySmall.copyWith(
              color: VanHubColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSyncInfo(BuildContext context, WidgetRef ref, List<BomItem> bomItems) {
    if (bomItems.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUsageStats(context, bomItems),
        const SizedBox(height: 16),
        _buildLinkedProjects(context, bomItems),
        if (showSyncActions) ...[
          const SizedBox(height: 16),
          _buildSyncActions(context, ref),
        ],
      ],
    );
  }

  Widget _buildUsageStats(BuildContext context, List<BomItem> bomItems) {
    final totalQuantity = bomItems.fold<int>(0, (sum, item) => sum + item.quantity);
    final totalCost = bomItems.fold<double>(0, (sum, item) => sum + item.totalCost);
    final completedItems = bomItems.where((item) => item.status.name == 'completed').length;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              context,
              '总数量',
              totalQuantity.toString(),
              Icons.inventory,
              Colors.blue,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              context,
              '总成本',
              '¥${totalCost.toStringAsFixed(2)}',
              Icons.attach_money,
              Colors.green,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              context,
              '已完成',
              '$completedItems/${bomItems.length}',
              Icons.check_circle,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: VanHubTypography.titleSmall.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: VanHubTypography.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLinkedProjects(BuildContext context, List<BomItem> bomItems) {
    // 按项目分组
    final projectGroups = <String, List<BomItem>>{};
    for (final item in bomItems) {
      projectGroups.putIfAbsent(item.projectId, () => []).add(item);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '关联项目 (${projectGroups.length})',
          style: VanHubTypography.titleSmall,
        ),
        const SizedBox(height: 8),
        ...projectGroups.entries.map((entry) => _buildProjectItem(
          context,
          entry.key,
          entry.value,
        )),
      ],
    );
  }

  Widget _buildProjectItem(BuildContext context, String projectId, List<BomItem> items) {
    final totalQuantity = items.fold<int>(0, (sum, item) => sum + item.quantity);
    final completedItems = items.where((item) => item.status.name == 'completed').length;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.folder_open,
            color: VanHubColors.secondary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '项目 ${projectId.substring(0, 8)}...',
                  style: VanHubTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '使用 $totalQuantity 个，完成 $completedItems/${items.length} 项',
                  style: VanHubTypography.bodySmall,
                ),
              ],
            ),
          ),
          Icon(
            completedItems == items.length ? Icons.check_circle : Icons.pending,
            color: completedItems == items.length ? Colors.green : Colors.orange,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildSyncActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '同步操作',
          style: VanHubTypography.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showSyncDialog(context, ref),
                icon: const Icon(Icons.sync),
                label: const Text('同步到BOM'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showUsageHistory(context, ref),
                icon: const Icon(Icons.history),
                label: const Text('使用历史'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.link_off,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无关联的BOM项目',
            style: VanHubTypography.bodyLarge.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '此材料尚未在任何项目中使用',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            '加载同步信息失败',
            style: VanHubTypography.bodyLarge.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.red[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showSyncDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('同步材料信息'),
        content: const Text('是否将此材料的最新信息同步到所有关联的BOM项目？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              // 执行同步操作
              final result = await ref.read(materialSyncControllerProvider.notifier)
                  .syncMaterialUpdateToBom(
                materialId: material.id,
                updatedFields: {
                  'name': material.name,
                  'category': material.category,
                  'price': material.price,
                  'brand': material.brand,
                  'model': material.model,
                  'specifications': material.specifications,
                },
                syncStrategy: 'auto',
              );

              if (context.mounted) {
                result.fold(
                  (failure) => ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('同步失败: ${failure.message}'),
                      backgroundColor: Colors.red,
                    ),
                  ),
                  (bomItems) => ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('成功同步 ${bomItems.length} 个BOM项目'),
                      backgroundColor: Colors.green,
                    ),
                  ),
                );
              }
            },
            child: const Text('确认同步'),
          ),
        ],
      ),
    );
  }

  void _showUsageHistory(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.history),
                  const SizedBox(width: 8),
                  const Text(
                    '使用历史',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final historyAsync = ref.watch(materialUsageHistoryProvider(material.id));
                    
                    return historyAsync.when(
                      data: (history) => history.isEmpty
                          ? const Center(child: Text('暂无使用历史'))
                          : ListView.builder(
                              itemCount: history.length,
                              itemBuilder: (context, index) {
                                final item = history[index];
                                return ListTile(
                                  leading: Text(item.typeIcon),
                                  title: Text('${item.usageType.displayName} - ${item.quantity}个'),
                                  subtitle: Text('¥${item.totalPrice.toStringAsFixed(2)}'),
                                  trailing: Text(
                                    '${item.createdAt.month}/${item.createdAt.day}',
                                  ),
                                );
                              },
                            ),
                      loading: () => const Center(child: CircularProgressIndicator()),
                      error: (error, stack) => Center(
                        child: Text('加载失败: $error'),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
