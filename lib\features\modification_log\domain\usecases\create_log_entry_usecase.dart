import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../repositories/log_repository.dart';

/// 创建日志条目用例
class CreateLogEntryUseCase implements UseCase<LogEntry, CreateLogEntryParams> {
  final LogRepository repository;

  CreateLogEntryUseCase(this.repository);

  @override
  Future<Either<Failure, LogEntry>> call(CreateLogEntryParams params) async {
    return await repository.createLogEntry(params.logEntry);
  }
}

/// 创建日志条目参数
class CreateLogEntryParams {
  final LogEntry logEntry;

  CreateLogEntryParams({required this.logEntry});
}