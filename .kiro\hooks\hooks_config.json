{"hooks": [{"name": "clean_architecture_validator", "enabled": true, "trigger": "on_file_save", "file_patterns": ["lib/**/*.dart"], "auto_execute": true, "description": "验证VanHub项目是否遵循Clean Architecture原则"}, {"name": "code_structure_enforcer", "enabled": true, "trigger": "on_file_create", "file_patterns": ["lib/features/**/*.dart"], "auto_execute": true, "description": "强制执行VanHub项目的代码结构规范"}, {"name": "either_type_enforcer", "enabled": true, "trigger": "on_file_save", "file_patterns": ["lib/features/**/repositories/*.dart", "lib/features/**/usecases/*.dart"], "auto_execute": true, "description": "确保所有可能失败的操作使用Either<Failure, Success>类型"}, {"name": "riverpod_state_validator", "enabled": true, "trigger": "on_file_save", "file_patterns": ["lib/features/**/presentation/**/*.dart"], "auto_execute": true, "description": "验证Riverpod状态管理的正确使用"}, {"name": "freezed_entity_validator", "enabled": true, "trigger": "on_file_save", "file_patterns": ["lib/features/**/domain/entities/*.dart"], "auto_execute": true, "description": "验证Domain层实体是否正确使用freezed"}, {"name": "dependency_layer_validator", "enabled": true, "trigger": "on_file_save", "file_patterns": ["lib/features/**/*.dart"], "auto_execute": true, "description": "验证分层架构的依赖关系是否正确"}], "global_settings": {"auto_fix_enabled": true, "show_notifications": true, "log_level": "info"}}