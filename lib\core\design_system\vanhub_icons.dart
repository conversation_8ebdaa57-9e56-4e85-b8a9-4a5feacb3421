import 'package:flutter/material.dart';

class VanHubIcons {
  static const IconData add = Icons.add;
  static const IconData close = Icons.close;
  static const IconData search = Icons.search;
  static const IconData filter = Icons.filter_list;
  static const IconData sort = Icons.sort;
  static const IconData more = Icons.more_vert;
  
  static const IconData home = Icons.home;
  static const IconData projects = Icons.folder;
  static const IconData materials = Icons.inventory_2;
  static const IconData bom = Icons.list_alt;
  static const IconData timeline = Icons.timeline;
  static const IconData settings = Icons.settings;
  
  static const IconData edit = Icons.edit;
  static const IconData delete = Icons.delete;
  static const IconData copy = Icons.copy;
  static const IconData share = Icons.share;
  static const IconData download = Icons.download;
  static const IconData upload = Icons.upload;
  
  static const IconData success = Icons.check_circle;
  static const IconData warning = Icons.warning;
  static const IconData error = Icons.error;
  static const IconData info = Icons.info;
  
  static const IconData user = Icons.person;
  static const IconData login = Icons.login;
  static const IconData logout = Icons.logout;

  // 兼容性图标 - 添加缺失的图标定义
  static const IconData van = Icons.directions_car;
  static const IconData addCircle = Icons.add_circle;
  static const IconData analytics = Icons.analytics;
  static const IconData right = Icons.arrow_forward;
  static const IconData materialsOutlined = Icons.inventory_2_outlined;
  static const IconData solar = Icons.wb_sunny;
  static const IconData storage = Icons.storage;
  static const IconData budget = Icons.account_balance_wallet;
  static const IconData progress = Icons.trending_up;

  static IconData getMaterialIcon(String category) {
    switch (category.toLowerCase()) {
      case 'electrical':
        return Icons.electrical_services;
      case 'plumbing':
        return Icons.plumbing;
      case 'storage':
        return Icons.storage;
      case 'bedding':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'bathroom':
        return Icons.bathroom;
      case 'exterior':
        return Icons.directions_car;
      case 'chassis':
        return Icons.build;
      default:
        return Icons.inventory_2;
    }
  }
}