import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/comment.dart';

/// 评论仓库接口
abstract class CommentRepository {
  /// 获取日志的所有评论
  Future<Either<Failure, List<Comment>>> getLogComments(String logId);
  
  /// 获取单个评论详情
  Future<Either<Failure, Comment>> getComment(String commentId);
  
  /// 创建新评论
  Future<Either<Failure, Comment>> createComment(Comment comment);
  
  /// 更新评论
  Future<Either<Failure, Comment>> updateComment(Comment comment);
  
  /// 删除评论
  Future<Either<Failure, void>> deleteComment(String commentId);
  
  /// 获取评论的回复
  Future<Either<Failure, List<Comment>>> getCommentReplies(String commentId);
  
  /// 点赞评论
  Future<Either<Failure, Comment>> likeComment(String commentId, String userId);
  
  /// 取消点赞评论
  Future<Either<Failure, Comment>> unlikeComment(String commentId, String userId);
  
  /// 获取用户的所有评论
  Future<Either<Failure, List<Comment>>> getUserComments(String userId, {int? limit, int? offset});
  
  /// 获取提及用户的评论
  Future<Either<Failure, List<Comment>>> getMentionedComments(String userId, {int? limit, int? offset});
}