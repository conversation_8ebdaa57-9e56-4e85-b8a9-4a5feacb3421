import 'package:flutter/foundation.dart';
import '../api/supabase_config.dart';

/// 数据库初始化工具
class DatabaseInitializer {
  /// 初始化数据库表结构
  static Future<void> initializeTables() async {
    if (kDebugMode) {
      print('🗄️ 开始初始化数据库表结构...');
    }

    try {
      // 首先尝试创建表结构
      await _createTablesIfNotExist();

      // 然后插入默认数据
      await _insertDefaultData();

      if (kDebugMode) {
        print('✅ 数据库初始化完成');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 数据库初始化失败: $e');
      }
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 创建表结构（如果不存在）
  static Future<void> _createTablesIfNotExist() async {
    try {
      // 尝试创建projects表
      await _createProjectsTableSafe();

      // 尝试创建material_categories表
      await _createMaterialCategoriesTableSafe();

      // 尝试创建materials表
      await _createMaterialsTableSafe();

      // 尝试创建bom_items表
      await _createBOMItemsTableSafe();

    } catch (e) {
      if (kDebugMode) {
        print('创建表结构时出现错误: $e');
      }
    }
  }

  /// 安全创建projects表
  static Future<void> _createProjectsTableSafe() async {
    try {
      // 先检查表是否存在
      await SupabaseConfig.client.from('projects').select('id').limit(1);
      if (kDebugMode) {
        print('✅ projects表已存在');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ projects表不存在，需要手动创建');
        print('请在Supabase SQL编辑器中执行以下SQL:');
        print('''
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'planning',
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);

ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING (auth.uid() = user_id);
        ''');
      }
    }
  }

  /// 安全创建material_categories表
  static Future<void> _createMaterialCategoriesTableSafe() async {
    try {
      await SupabaseConfig.client.from('material_categories').select('id').limit(1);
      if (kDebugMode) {
        print('✅ material_categories表已存在');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ material_categories表不存在，需要手动创建');
        print('请在Supabase SQL编辑器中执行以下SQL:');
        print('''
CREATE TABLE IF NOT EXISTS material_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view material categories" ON material_categories
    FOR SELECT USING (true);
        ''');
      }
    }
  }

  /// 安全创建materials表
  static Future<void> _createMaterialsTableSafe() async {
    try {
      await SupabaseConfig.client.from('materials').select('id').limit(1);
      if (kDebugMode) {
        print('✅ materials表已存在');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ materials表不存在，需要手动创建');
        print('请在Supabase SQL编辑器中执行以下SQL:');
        print('''
CREATE TABLE IF NOT EXISTS materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES material_categories(id) ON DELETE SET NULL,
    unit VARCHAR(50) DEFAULT '个',
    price DECIMAL(10,2) DEFAULT 0,
    supplier VARCHAR(255),
    model VARCHAR(255),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_materials_user_id ON materials(user_id);
CREATE INDEX IF NOT EXISTS idx_materials_category_id ON materials(category_id);

ALTER TABLE materials ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all materials" ON materials
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own materials" ON materials
    FOR ALL USING (auth.uid() = user_id);
        ''');
      }
    }
  }

  /// 安全创建bom_items表
  static Future<void> _createBOMItemsTableSafe() async {
    try {
      await SupabaseConfig.client.from('bom_items').select('id').limit(1);
      if (kDebugMode) {
        print('✅ bom_items表已存在');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ bom_items表不存在，需要手动创建');
        print('请在Supabase SQL编辑器中执行以下SQL:');
        print('''
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, material_id)
);

CREATE INDEX IF NOT EXISTS idx_bom_items_project_id ON bom_items(project_id);
CREATE INDEX IF NOT EXISTS idx_bom_items_material_id ON bom_items(material_id);

ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage BOM items for their projects" ON bom_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM projects
            WHERE projects.id = bom_items.project_id
            AND projects.user_id = auth.uid()
        )
    );
        ''');
      }
    }
  }

  // 移除了直接创建表的方法，因为Supabase不支持客户端执行DDL

  /// 插入默认数据
  static Future<void> _insertDefaultData() async {
    try {
      // 检查material_categories表是否存在
      final categoriesExist = await checkTableExists('material_categories');
      if (!categoriesExist) {
        if (kDebugMode) {
          print('⚠️ material_categories表不存在，跳过默认数据插入');
        }
        return;
      }

      // 插入默认材料分类
      const categories = [
        {'name': '电气系统', 'description': '电池、逆变器、充电器等电气设备'},
        {'name': '水系统', 'description': '水泵、水箱、净水器等水系统设备'},
        {'name': '燃气系统', 'description': '燃气灶、热水器、燃气罐等燃气设备'},
        {'name': '家具', 'description': '床、桌椅、储物柜等家具'},
        {'name': '装饰材料', 'description': '地板、墙面、窗帘等装饰材料'},
        {'name': '工具配件', 'description': '螺丝、胶水、线材等工具配件'},
      ];

      for (final category in categories) {
        try {
          // 检查是否已存在
          final existing = await SupabaseConfig.client
              .from('material_categories')
              .select('id')
              .eq('name', category['name']!)
              .maybeSingle();

          if (existing == null) {
            await SupabaseConfig.client
                .from('material_categories')
                .insert(category);
            if (kDebugMode) {
              print('✅ 插入分类: ${category['name']}');
            }
          }
        } catch (e) {
          // 忽略重复插入错误
          if (kDebugMode) {
            print('插入分类 ${category['name']} 时出现错误（可能已存在）: $e');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('插入默认数据时出现错误: $e');
      }
    }
  }

  /// 检查表是否存在
  static Future<bool> checkTableExists(String tableName) async {
    try {
      await SupabaseConfig.client
          .from(tableName)
          .select('*')
          .limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取数据库状态
  static Future<Map<String, dynamic>> getDatabaseStatus() async {
    final status = <String, dynamic>{};
    
    final tables = ['projects', 'material_categories', 'materials', 'bom_items'];
    
    for (final table in tables) {
      try {
        final exists = await checkTableExists(table);
        status[table] = {
          'exists': exists,
          'status': exists ? 'ready' : 'missing',
        };
        
        if (exists) {
          // 获取记录数量
          final response = await SupabaseConfig.client
              .from(table)
              .select('*')
              .limit(1);
          status[table]['count'] = response.length;
        }
      } catch (e) {
        status[table] = {
          'exists': false,
          'status': 'error',
          'error': e.toString(),
        };
      }
    }
    
    return status;
  }
}
