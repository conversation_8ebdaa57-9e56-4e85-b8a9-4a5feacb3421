import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/create_material_request.dart';
import '../../domain/entities/update_material_request.dart';
import '../../domain/usecases/create_material_usecase.dart';
import '../../domain/usecases/get_materials_usecase.dart';
import '../../../../core/di/injection_container.dart';

part 'material_provider.g.dart';

// Use Cases Providers
@riverpod
CreateMaterialUseCase createMaterialUseCase(Ref ref) {
  return CreateMaterialUseCase(ref.read(materialRepositoryProvider));
}

@riverpod
GetMaterialsUseCase getMaterialsUseCase(Ref ref) {
  return GetMaterialsUseCase(ref.read(materialRepositoryProvider));
}

// Material List Providers
@riverpod
Future<List<domain.Material>> userMaterials(Ref ref, String userId) async {
  final result = await ref.read(getMaterialsUseCaseProvider).getUserMaterials(userId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (materials) => materials,
  );
}

// Materials Notifier Provider - 用于UI
@riverpod
class MaterialsNotifier extends _$MaterialsNotifier {
  @override
  Future<List<domain.Material>> build() async {
    // 获取当前用户的材料列表，如果是游客模式则获取公开材料
    final currentUserId = ref.watch(currentUserIdProvider);

    final result = currentUserId != null
        ? await ref.read(getMaterialsUseCaseProvider).getUserMaterials(currentUserId)
        : await ref.read(getMaterialsUseCaseProvider).getPublicMaterials();

    return result.fold(
      (failure) => [],
      (materials) => materials,
    );
  }

  Future<void> refresh() async {
    // 只重新构建自身状态，避免循环依赖
    ref.invalidateSelf();
  }
}

// Filtered Materials Provider
@riverpod
Future<List<domain.Material>> filteredMaterials(
  FilteredMaterialsRef ref,
  String userId,
  String searchQuery,
  String selectedCategory,
) async {
  final materials = await ref.watch(userMaterialsProvider(userId).future);
  
  return materials.where((material) {
    final matchesSearch = searchQuery.trim().isEmpty ||
        material.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
        (material.brand?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
        (material.model?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false);
    
    final matchesCategory = selectedCategory == '全部' ||
        material.category == selectedCategory;
    
    return matchesSearch && matchesCategory;
  }).toList();
}

// Material Controller Notifier
@riverpod
class MaterialController extends _$MaterialController {
  @override
  FutureOr<void> build() {
    // 初始化状态
  }

  Future<Either<Failure, domain.Material>> createMaterial(CreateMaterialRequest request) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(createMaterialUseCaseProvider).call(request);

      // 简化状态管理，避免在fold中重复设置状态
      result.fold(
        (failure) => state = AsyncError(failure, StackTrace.current),
        (material) => state = const AsyncData(null),
      );

      // 延迟刷新，避免状态竞争
      if (result.isRight()) {
        await Future.delayed(const Duration(milliseconds: 100));
        try {
          ref.invalidate(materialsNotifierProvider);
        } catch (e) {
          // 忽略Provider刷新错误，避免影响主流程
        }
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '创建材料失败: $e'));
    }
  }

  Future<Either<Failure, void>> deleteMaterial(String materialId) async {
    final result = await ref.read(materialRepositoryProvider).deleteMaterial(materialId);
    
    result.fold(
      (failure) => null,
      (_) {
        // 刷新材料列表
        ref.invalidate(userMaterialsProvider);
        ref.invalidate(materialsNotifierProvider);
      },
    );
    
    return result;
  }



  Future<Either<Failure, void>> incrementUsageCount(String materialId) async {
    final result = await ref.read(materialRepositoryProvider).incrementUsageCount(materialId);
    
    result.fold(
      (failure) => null,
      (_) {
        // 刷新相关状态
        ref.invalidate(materialDetailProvider(materialId));
        ref.invalidate(materialsNotifierProvider);
      },
    );
    
    return result;
  }

  Future<Either<Failure, domain.Material>> updateMaterial(UpdateMaterialRequest request) async {
    state = const AsyncLoading();

    try {
      // 将UpdateMaterialRequest转换为Map格式，因为Repository期望Map
      final updates = {
        'name': request.name,
        'category': request.category,
        if (request.brand != null) 'brand': request.brand,
        if (request.model != null) 'model': request.model,
        if (request.specifications != null) 'specifications': request.specifications,
        if (request.price != null) 'price': request.price,
        if (request.supplier != null) 'supplier': request.supplier,
        if (request.purchaseDate != null) 'purchase_date': request.purchaseDate!.toIso8601String(),
        if (request.description != null) 'description': request.description,
        if (request.imageUrl != null) 'image_url': request.imageUrl,
      };

      final result = await ref.read(materialRepositoryProvider).updateMaterial(request.id, updates);

      // 使用更简单的状态管理
      if (result.isRight()) {
        state = const AsyncData(null);

        // 延迟刷新，确保当前操作完成
        Future.delayed(const Duration(milliseconds: 100), () {
          try {
            ref.invalidate(userMaterialsProvider);
            ref.invalidate(materialsNotifierProvider);
            ref.invalidate(materialDetailProvider(request.id));
          } catch (e) {
            // 忽略可能的状态错误
          }
        });
      } else {
        state = AsyncError(result.fold((l) => l, (r) => null)!, StackTrace.current);
      }

      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(ServerFailure(message: '更新材料失败: $e'));
    }
  }
}

// Material Detail Provider
@riverpod
Future<domain.Material> materialDetail(Ref ref, String materialId) async {
  final result = await ref.read(materialRepositoryProvider).getMaterialById(materialId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (material) => material,
  );
}