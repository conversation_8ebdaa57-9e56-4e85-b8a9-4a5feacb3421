/// VanHub Chart Component 2.0
/// 
/// 高端数据可视化组件，支持交互式图表和动画
/// 
/// 特性：
/// - 7种图表类型：线图、柱图、饼图、环图、雷达图、散点图、面积图
/// - 交互式动画：悬停、点击、缩放、平移
/// - 响应式设计：自适应布局
/// - 情感化配色：基于情感状态的颜色映射
/// - 实时数据更新：流畅的数据变化动画

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';

/// 图表类型枚举
enum VanHubChartType {
  line,      // 线图
  bar,       // 柱图
  pie,       // 饼图
  doughnut,  // 环图
  radar,     // 雷达图
  scatter,   // 散点图
  area,      // 面积图
}

/// 图表数据点
class ChartDataPoint {
  final double x;
  final double y;
  final String? label;
  final Color? color;
  final dynamic metadata;

  const ChartDataPoint({
    required this.x,
    required this.y,
    this.label,
    this.color,
    this.metadata,
  });
}

/// 图表数据系列
class ChartDataSeries {
  final String name;
  final List<ChartDataPoint> data;
  final Color color;
  final bool showInLegend;
  final double strokeWidth;
  final bool isCurved;

  const ChartDataSeries({
    required this.name,
    required this.data,
    required this.color,
    this.showInLegend = true,
    this.strokeWidth = 2.0,
    this.isCurved = true,
  });
}

/// VanHub Chart 2.0 - 高端图表组件
class VanHubChart extends StatefulWidget {
  final VanHubChartType type;
  final List<ChartDataSeries> series;
  final String? title;
  final String? subtitle;
  final bool enableInteraction;
  final bool enableAnimation;
  final bool showLegend;
  final bool showGrid;
  final bool showTooltip;
  final EmotionalState? emotionalState;
  final double? width;
  final double? height;
  
  // 动画配置
  final Duration animationDuration;
  final Curve animationCurve;
  
  // 交互回调
  final Function(ChartDataPoint)? onPointTap;
  final Function(ChartDataPoint)? onPointHover;

  const VanHubChart({
    Key? key,
    required this.type,
    required this.series,
    this.title,
    this.subtitle,
    this.enableInteraction = true,
    this.enableAnimation = true,
    this.showLegend = true,
    this.showGrid = true,
    this.showTooltip = true,
    this.emotionalState,
    this.width,
    this.height,
    this.animationDuration = VanHubAnimationDurations.normal,
    this.animationCurve = VanHubAnimationCurves.easeOut,
    this.onPointTap,
    this.onPointHover,
  }) : super(key: key);

  /// 线图构造函数
  const VanHubChart.line({
    Key? key,
    required List<ChartDataSeries> series,
    String? title,
    bool enableAnimation = true,
    EmotionalState? emotionalState,
  }) : this(
          key: key,
          type: VanHubChartType.line,
          series: series,
          title: title,
          enableAnimation: enableAnimation,
          emotionalState: emotionalState,
        );

  /// 柱图构造函数
  const VanHubChart.bar({
    Key? key,
    required List<ChartDataSeries> series,
    String? title,
    bool enableAnimation = true,
    EmotionalState? emotionalState,
  }) : this(
          key: key,
          type: VanHubChartType.bar,
          series: series,
          title: title,
          enableAnimation: enableAnimation,
          emotionalState: emotionalState,
        );

  /// 饼图构造函数
  const VanHubChart.pie({
    Key? key,
    required List<ChartDataSeries> series,
    String? title,
    bool enableAnimation = true,
    EmotionalState? emotionalState,
  }) : this(
          key: key,
          type: VanHubChartType.pie,
          series: series,
          title: title,
          enableAnimation: enableAnimation,
          emotionalState: emotionalState,
        );

  @override
  State<VanHubChart> createState() => _VanHubChartState();
}

class _VanHubChartState extends State<VanHubChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  int _hoveredIndex = -1;
  ChartDataPoint? _hoveredPoint;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    ));

    if (widget.enableAnimation) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height ?? 300,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.title != null) _buildTitle(),
          if (widget.subtitle != null) _buildSubtitle(),
          Expanded(
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildChart();
              },
            ),
          ),
          if (widget.showLegend) _buildLegend(),
        ],
      ),
    ).animate()
      .fadeIn(duration: widget.animationDuration)
      .slideY(begin: 0.1, end: 0);
  }

  /// 构建标题
  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Text(
        widget.title!,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: VanHubSemanticColors.getTextColor(context),
        ),
      ),
    );
  }

  /// 构建副标题
  Widget _buildSubtitle() {
    return Padding(
      padding: EdgeInsets.only(bottom: VanHubResponsiveSpacing.md),
      child: Text(
        widget.subtitle!,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: VanHubSemanticColors.getTextColor(context, secondary: true),
        ),
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    switch (widget.type) {
      case VanHubChartType.line:
        return _buildLineChart();
      case VanHubChartType.bar:
        return _buildBarChart();
      case VanHubChartType.pie:
        return _buildPieChart();
      case VanHubChartType.doughnut:
        return _buildDoughnutChart();
      default:
        return _buildLineChart();
    }
  }

  /// 构建线图
  Widget _buildLineChart() {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: widget.showGrid,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: VanHubSemanticColors.getBorderColor(context),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: VanHubSemanticColors.getBorderColor(context),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        ),
        lineBarsData: widget.series.map((series) {
          return LineChartBarData(
            spots: series.data.map((point) {
              return FlSpot(point.x, point.y * _animation.value);
            }).toList(),
            isCurved: series.isCurved,
            color: series.color,
            barWidth: series.strokeWidth,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: series.color,
                  strokeWidth: 2,
                  strokeColor: VanHubSemanticColors.getBackgroundColor(context),
                );
              },
            ),
            belowBarData: BarAreaData(
              show: widget.type == VanHubChartType.area,
              color: series.color.withOpacity(0.3),
            ),
          );
        }).toList(),
        lineTouchData: LineTouchData(
          enabled: widget.enableInteraction,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => VanHubSemanticColors.getBackgroundColor(context, level: 2),
            tooltipRoundedRadius: 8,
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((spot) {
                return LineTooltipItem(
                  '${spot.y.toStringAsFixed(1)}',
                  TextStyle(
                    color: VanHubSemanticColors.getTextColor(context),
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// 构建柱图
  Widget _buildBarChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxY() * 1.2,
        barTouchData: BarTouchData(
          enabled: widget.enableInteraction,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => VanHubSemanticColors.getBackgroundColor(context, level: 2),
            tooltipRoundedRadius: 8,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                '${rod.toY.toStringAsFixed(1)}',
                TextStyle(
                  color: VanHubSemanticColors.getTextColor(context),
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        ),
        barGroups: _buildBarGroups(),
        gridData: FlGridData(
          show: widget.showGrid,
          drawVerticalLine: false,
          drawHorizontalLine: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: VanHubSemanticColors.getBorderColor(context),
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  /// 构建饼图
  Widget _buildPieChart() {
    return PieChart(
      PieChartData(
        sections: _buildPieSections(),
        centerSpaceRadius: widget.type == VanHubChartType.doughnut ? 60 : 0,
        sectionsSpace: 2,
        pieTouchData: PieTouchData(
          enabled: widget.enableInteraction,
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            if (pieTouchResponse?.touchedSection != null) {
              final index = pieTouchResponse!.touchedSection!.touchedSectionIndex;
              setState(() {
                _hoveredIndex = index;
              });
            }
          },
        ),
      ),
    );
  }

  /// 构建环图
  Widget _buildDoughnutChart() {
    return _buildPieChart();
  }

  /// 构建柱状图组
  List<BarChartGroupData> _buildBarGroups() {
    final List<BarChartGroupData> groups = [];
    
    for (int i = 0; i < widget.series.first.data.length; i++) {
      final rods = <BarChartRodData>[];
      
      for (int j = 0; j < widget.series.length; j++) {
        final series = widget.series[j];
        final point = series.data[i];
        
        rods.add(
          BarChartRodData(
            toY: point.y * _animation.value,
            color: series.color,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }
      
      groups.add(
        BarChartGroupData(
          x: i,
          barRods: rods,
        ),
      );
    }
    
    return groups;
  }

  /// 构建饼图扇形
  List<PieChartSectionData> _buildPieSections() {
    final sections = <PieChartSectionData>[];
    final total = widget.series.first.data.fold<double>(
      0, (sum, point) => sum + point.y,
    );
    
    for (int i = 0; i < widget.series.first.data.length; i++) {
      final point = widget.series.first.data[i];
      final isHovered = i == _hoveredIndex;
      
      sections.add(
        PieChartSectionData(
          value: point.y * _animation.value,
          color: point.color ?? widget.series.first.color,
          radius: isHovered ? 60 : 50,
          title: '${((point.y / total) * 100).toStringAsFixed(1)}%',
          titleStyle: TextStyle(
            fontSize: isHovered ? 16 : 14,
            fontWeight: FontWeight.bold,
            color: VanHubBrandColors.onPrimary,
          ),
        ),
      );
    }
    
    return sections;
  }

  /// 构建图例
  Widget _buildLegend() {
    return Padding(
      padding: EdgeInsets.only(top: VanHubResponsiveSpacing.md),
      child: Wrap(
        spacing: VanHubResponsiveSpacing.md,
        runSpacing: VanHubResponsiveSpacing.sm,
        children: widget.series.where((series) => series.showInLegend).map((series) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: series.color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              SizedBox(width: VanHubResponsiveSpacing.xs),
              Text(
                series.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: VanHubSemanticColors.getTextColor(context),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 获取最大Y值
  double _getMaxY() {
    double maxY = 0;
    for (final series in widget.series) {
      for (final point in series.data) {
        if (point.y > maxY) {
          maxY = point.y;
        }
      }
    }
    return maxY;
  }
}
