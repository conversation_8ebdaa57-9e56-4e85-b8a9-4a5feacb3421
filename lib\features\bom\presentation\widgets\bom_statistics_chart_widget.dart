import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/bom_statistics.dart';
import '../../domain/entities/bom_item.dart';
import '../../../../core/design_system/vanhub_design_system.dart';

/// 完整功能的BOM统计图表组件
/// 包含柱状图、折线图、饼图等多种图表类型
class BomStatisticsChartWidget extends ConsumerStatefulWidget {
  const BomStatisticsChartWidget({
    super.key,
    required this.statistics,
    this.height = 400,
    this.showChartTypeSelector = true,
  });

  final BomStatistics statistics;
  final double height;
  final bool showChartTypeSelector;

  @override
  ConsumerState<BomStatisticsChartWidget> createState() => _BomStatisticsChartWidgetState();
}

class _BomStatisticsChartWidgetState extends ConsumerState<BomStatisticsChartWidget> {
  ChartType _selectedChartType = ChartType.bar;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(VanHubDesignSystem.spacing4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和图表类型选择器
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getChartTitle(),
                  style: TextStyle(
                    fontSize: VanHubDesignSystem.fontSize2xl,
                    fontWeight: VanHubDesignSystem.fontWeightSemiBold,
                    color: VanHubDesignSystem.neutralGray900,
                  ),
                ),
                if (widget.showChartTypeSelector)
                  _buildChartTypeSelector(),
              ],
            ),
            
            SizedBox(height: VanHubDesignSystem.spacing4),
            
            // 图表内容
            SizedBox(
              height: widget.height,
              child: _buildChart(),
            ),
            
            SizedBox(height: VanHubDesignSystem.spacing4),
            
            // 统计摘要
            _buildStatisticsSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeSelector() {
    return SegmentedButton<ChartType>(
      segments: const [
        ButtonSegment<ChartType>(
          value: ChartType.bar,
          label: Text('柱状图'),
          icon: Icon(Icons.bar_chart),
        ),
        ButtonSegment<ChartType>(
          value: ChartType.line,
          label: Text('折线图'),
          icon: Icon(Icons.show_chart),
        ),
        ButtonSegment<ChartType>(
          value: ChartType.pie,
          label: Text('饼图'),
          icon: Icon(Icons.pie_chart),
        ),
      ],
      selected: {_selectedChartType},
      onSelectionChanged: (Set<ChartType> newSelection) {
        setState(() {
          _selectedChartType = newSelection.first;
        });
      },
    );
  }

  Widget _buildChart() {
    switch (_selectedChartType) {
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.line:
        return _buildLineChart();
      case ChartType.pie:
        return _buildPieChart();
    }
  }

  Widget _buildBarChart() {
    final categories = widget.statistics.costByCategory.keys.toList();
    final costs = widget.statistics.costByCategory.values.toList();
    
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: costs.isNotEmpty ? costs.reduce((a, b) => a > b ? a : b) * 1.2 : 100,
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (_) => VanHubDesignSystem.neutralWhite,
            tooltipBorder: BorderSide(color: VanHubDesignSystem.neutralGray300),
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final category = categories[groupIndex];
              final cost = costs[groupIndex];
              return BarTooltipItem(
                '$category\\n¥${cost.toStringAsFixed(2)}',
                TextStyle(
                  fontSize: VanHubDesignSystem.fontSizeSm,
                  color: VanHubDesignSystem.neutralGray900,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                if (value.toInt() >= categories.length) return const SizedBox();
                return Padding(
                  padding: EdgeInsets.only(top: VanHubDesignSystem.spacing1),
                  child: Text(
                    categories[value.toInt()],
                    style: TextStyle(
                      fontSize: VanHubDesignSystem.fontSizeXs,
                      color: VanHubDesignSystem.neutralGray600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 60,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  '¥${(value / 1000).toStringAsFixed(0)}K',
                  style: TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeXs,
                    color: VanHubDesignSystem.neutralGray600,
                  ),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: VanHubDesignSystem.neutralGray300,
            width: 1,
          ),
        ),
        barGroups: List.generate(categories.length, (index) {
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: costs[index],
                color: _getCategoryColor(categories[index]),
                width: 20,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          );
        }),
        gridData: FlGridData(
          show: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: VanHubDesignSystem.neutralGray200,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: VanHubDesignSystem.neutralGray200,
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  Widget _buildLineChart() {
    final categories = widget.statistics.costByCategory.keys.toList();
    final costs = widget.statistics.costByCategory.values.toList();
    
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: VanHubDesignSystem.neutralGray200,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: VanHubDesignSystem.neutralGray200,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                if (value.toInt() >= categories.length) return const SizedBox();
                return Padding(
                  padding: EdgeInsets.only(top: VanHubDesignSystem.spacing1),
                  child: Text(
                    categories[value.toInt()].substring(0, 2),
                    style: TextStyle(
                      fontSize: VanHubDesignSystem.fontSizeXs,
                      color: VanHubDesignSystem.neutralGray600,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 60,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  '¥${(value / 1000).toStringAsFixed(0)}K',
                  style: TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeXs,
                    color: VanHubDesignSystem.neutralGray600,
                  ),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: VanHubDesignSystem.neutralGray300,
            width: 1,
          ),
        ),
        minX: 0,
        maxX: (categories.length - 1).toDouble(),
        minY: 0,
        maxY: costs.isNotEmpty ? costs.reduce((a, b) => a > b ? a : b) * 1.2 : 100,
        lineBarsData: [
          LineChartBarData(
            spots: _createLineChartSpots(),
            isCurved: true,
            color: VanHubDesignSystem.brandPrimary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: VanHubDesignSystem.brandPrimary,
                  strokeWidth: 2,
                  strokeColor: VanHubDesignSystem.neutralWhite,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: VanHubDesignSystem.brandPrimary.withValues(alpha: 0.1),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (_) => VanHubDesignSystem.neutralWhite,
            tooltipBorder: BorderSide(color: VanHubDesignSystem.neutralGray300),
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final category = categories[barSpot.x.toInt()];
                final cost = costs[barSpot.x.toInt()];
                return LineTooltipItem(
                  '$category\\n¥${cost.toStringAsFixed(2)}',
                  TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeSm,
                    color: VanHubDesignSystem.neutralGray900,
                  ),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    final entries = widget.statistics.costByCategory.entries.toList();
    final total = entries.fold<double>(0, (sum, entry) => sum + entry.value);
    
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: PieChart(
            PieChartData(
              sections: entries.asMap().entries.map((entry) {
                final index = entry.key;
                final data = entry.value;
                final percentage = (data.value / total) * 100;
                
                return PieChartSectionData(
                  color: _getCategoryColor(data.key),
                  value: data.value,
                  title: '${percentage.toStringAsFixed(1)}%',
                  radius: 80,
                  titleStyle: TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeSm,
                    fontWeight: VanHubDesignSystem.fontWeightMedium,
                    color: VanHubDesignSystem.neutralWhite,
                  ),
                );
              }).toList(),
              sectionsSpace: 2,
              centerSpaceRadius: 40,
            ),
          ),
        ),
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: entries.map((entry) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing1),
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(entry.key),
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: VanHubDesignSystem.spacing2),
                    Expanded(
                      child: Text(
                        '${entry.key} (¥${entry.value.toStringAsFixed(0)})',
                        style: TextStyle(
                          fontSize: VanHubDesignSystem.fontSizeSm,
                          color: VanHubDesignSystem.neutralGray700,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsSummary() {
    return Container(
      padding: EdgeInsets.all(VanHubDesignSystem.spacing4),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.neutralGray50,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
      ),
      child: Column(
        children: [
          // 进度指示器
          _buildProgressSection(),
          
          SizedBox(height: VanHubDesignSystem.spacing4),
          
          // 基本统计
          _buildBasicStatsRow(),
          
          SizedBox(height: VanHubDesignSystem.spacing4),
          
          // 预算状态
          _buildBudgetSection(),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '项目进度',
          style: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeLg,
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            color: VanHubDesignSystem.neutralGray900,
          ),
        ),
        
        SizedBox(height: VanHubDesignSystem.spacing2),
        
        Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CircularProgressIndicator(
                      value: widget.statistics.completionPercentage / 100,
                      strokeWidth: 8,
                      backgroundColor: VanHubDesignSystem.neutralGray200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _getProgressColor(widget.statistics.completionPercentage),
                      ),
                    ),
                  ),
                  SizedBox(height: VanHubDesignSystem.spacing2),
                  Column(
                    children: [
                      Text(
                        '${widget.statistics.completionPercentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: VanHubDesignSystem.fontSizeXl,
                          fontWeight: VanHubDesignSystem.fontWeightBold,
                          color: _getProgressColor(widget.statistics.completionPercentage),
                        ),
                      ),
                      Text(
                        '已完成',
                        style: TextStyle(
                          fontSize: VanHubDesignSystem.fontSizeBase,
                          color: VanHubDesignSystem.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  _buildStatItem(
                    '总项目',
                    widget.statistics.totalItems.toString(),
                    Icons.inventory,
                    VanHubDesignSystem.brandPrimary,
                  ),
                  SizedBox(height: VanHubDesignSystem.spacing2),
                  _buildStatItem(
                    '已完成',
                    widget.statistics.completedItems.toString(),
                    Icons.check_circle,
                    VanHubDesignSystem.semanticSuccess,
                  ),
                  SizedBox(height: VanHubDesignSystem.spacing2),
                  _buildStatItem(
                    '待处理',
                    widget.statistics.pendingItems.toString(),
                    Icons.pending,
                    VanHubDesignSystem.semanticWarning,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBasicStatsRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          '实际成本',
          '¥${widget.statistics.actualCost.toStringAsFixed(2)}',
          Icons.attach_money,
          VanHubDesignSystem.brandSecondary,
        ),
        _buildStatItem(
          '平均成本',
          '¥${widget.statistics.averageItemCost.toStringAsFixed(2)}',
          Icons.analytics,
          VanHubDesignSystem.brandAccent,
        ),
        if (widget.statistics.mostExpensiveItem != null)
          _buildStatItem(
            '最贵项目',
            widget.statistics.mostExpensiveItem!.name,
            Icons.trending_up,
            VanHubDesignSystem.semanticWarning,
          ),
      ],
    );
  }

  Widget _buildBudgetSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '预算状态',
          style: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeLg,
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            color: VanHubDesignSystem.neutralGray900,
          ),
        ),
        
        SizedBox(height: VanHubDesignSystem.spacing2),
        
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '预算使用率',
                    style: TextStyle(
                      fontSize: VanHubDesignSystem.fontSizeBase,
                      color: VanHubDesignSystem.neutralGray600,
                    ),
                  ),
                  Text(
                    '${widget.statistics.budgetUtilization.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: VanHubDesignSystem.fontSizeBase,
                      fontWeight: VanHubDesignSystem.fontWeightBold,
                      color: _getBudgetStatusColor(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        SizedBox(height: VanHubDesignSystem.spacing2),
        
        LinearProgressIndicator(
          value: (widget.statistics.actualCost / widget.statistics.totalBudget).clamp(0.0, 1.0),
          backgroundColor: VanHubDesignSystem.neutralGray200,
          valueColor: AlwaysStoppedAnimation<Color>(_getBudgetStatusColor()),
        ),
        
        SizedBox(height: VanHubDesignSystem.spacing2),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildBudgetItem(
              '总预算',
              widget.statistics.totalBudget,
              Icons.account_balance_wallet,
              VanHubDesignSystem.brandPrimary,
            ),
            _buildBudgetItem(
              '已用',
              widget.statistics.actualCost,
              Icons.payments,
              _getBudgetStatusColor(),
            ),
            _buildBudgetItem(
              '剩余',
              widget.statistics.remainingBudget,
              Icons.savings,
              widget.statistics.remainingBudget >= 0 
                  ? VanHubDesignSystem.semanticSuccess 
                  : VanHubDesignSystem.semanticError,
            ),
          ],
        ),
        
        if (widget.statistics.actualCost > widget.statistics.totalBudget) ...[
          SizedBox(height: VanHubDesignSystem.spacing3),
          Container(
            padding: EdgeInsets.all(VanHubDesignSystem.spacing3),
            decoration: BoxDecoration(
              color: VanHubDesignSystem.semanticError.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
              border: Border.all(
                color: VanHubDesignSystem.semanticError.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: VanHubDesignSystem.semanticError,
                  size: 20,
                ),
                SizedBox(width: VanHubDesignSystem.spacing2),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '预算超支警告',
                        style: TextStyle(
                          fontSize: VanHubDesignSystem.fontSizeBase,
                          fontWeight: VanHubDesignSystem.fontWeightBold,
                          color: VanHubDesignSystem.semanticError,
                        ),
                      ),
                      Text(
                        '已超出预算 ¥${widget.statistics.overBudgetAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: VanHubDesignSystem.fontSizeSm,
                          color: VanHubDesignSystem.semanticError,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        SizedBox(height: VanHubDesignSystem.spacing1),
        Text(
          value,
          style: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeLg,
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeSm,
            color: VanHubDesignSystem.neutralGray600,
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetItem(String label, double amount, IconData icon, Color color) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            SizedBox(width: VanHubDesignSystem.spacing1),
            Text(
              label,
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeBase,
                color: VanHubDesignSystem.neutralGray600,
              ),
            ),
          ],
        ),
        Text(
          '¥${amount.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeBase,
            fontWeight: VanHubDesignSystem.fontWeightBold,
            color: color,
          ),
        ),
      ],
    );
  }

  List<FlSpot> _createLineChartSpots() {
    final costs = widget.statistics.costByCategory.values.toList();
    return costs.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value);
    }).toList();
  }

  Color _getCategoryColor(String category) {
    final colors = [
      VanHubDesignSystem.brandPrimary,
      VanHubDesignSystem.brandSecondary,
      VanHubDesignSystem.brandAccent,
      VanHubDesignSystem.semanticSuccess,
      VanHubDesignSystem.semanticWarning,
      VanHubDesignSystem.semanticError,
      VanHubDesignSystem.semanticInfo,
    ];
    
    final index = category.hashCode % colors.length;
    return colors[index.abs()];
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) {
      return VanHubDesignSystem.semanticSuccess;
    } else if (percentage >= 50) {
      return VanHubDesignSystem.semanticWarning;
    } else {
      return VanHubDesignSystem.brandPrimary;
    }
  }

  Color _getBudgetStatusColor() {
    final utilization = widget.statistics.actualCost / widget.statistics.totalBudget;
    if (utilization > 1.0) {
      return VanHubDesignSystem.semanticError;
    } else if (utilization > 0.8) {
      return VanHubDesignSystem.semanticWarning;
    } else {
      return VanHubDesignSystem.semanticSuccess;
    }
  }

  String _getChartTitle() {
    switch (_selectedChartType) {
      case ChartType.bar:
        return 'BOM成本分析 - 柱状图';
      case ChartType.line:
        return 'BOM成本趋势 - 折线图';
      case ChartType.pie:
        return 'BOM成本分布 - 饼图';
    }
  }
}

enum ChartType {
  bar,
  line,
  pie,
}