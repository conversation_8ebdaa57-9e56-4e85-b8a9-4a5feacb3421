import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/bom_item.dart' as domain;
import '../../domain/entities/bom_item_status.dart';
import '../../domain/entities/bom_statistics.dart';
import '../../domain/entities/create_bom_item_request.dart';
import '../../domain/entities/update_bom_item_request.dart';
import '../../domain/usecases/create_bom_item_usecase.dart';
import '../../domain/usecases/add_material_to_bom_usecase.dart';
import '../../domain/services/bom_statistics_service.dart';
import '../../domain/services/bom_statistics_service_impl.dart';
import '../../../../core/di/injection_container.dart';

part 'bom_provider.g.dart';

// 移除重复的Provider定义，直接使用injection_container.dart中的Provider
// 避免循环依赖问题

// BOM Statistics Service Provider
@riverpod
BomStatisticsService bomStatisticsService(BomStatisticsServiceRef ref) {
  return const BomStatisticsServiceImpl();
}

/// BOM UI状态管理 - 专门处理UI层的加载状态
@riverpod
class BomUiState extends _$BomUiState {
  @override
  bool build() => false; // 初始状态：未加载

  void setLoading(bool loading) {
    state = loading;
  }
}

// BOM Items Provider
@riverpod
Future<List<domain.BomItem>> projectBomItems(ProjectBomItemsRef ref, String projectId) async {
  final result = await ref.read(bomRepositoryProvider).getProjectBomItems(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
}

// BOM Items by Status Provider
@riverpod
Future<List<domain.BomItem>> bomItemsByStatus(BomItemsByStatusRef ref, String projectId, BomItemStatus status) async {
  final result = await ref.read(bomRepositoryProvider).getBomItemsByStatus(projectId, status);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
}

// BOM Item Detail Provider
@riverpod
Future<domain.BomItem> bomItemDetail(BomItemDetailRef ref, String bomItemId) async {
  final result = await ref.read(bomRepositoryProvider).getBomItemById(bomItemId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItem) => bomItem,
  );
}

// Project BOM Statistics Provider
@riverpod
Future<BomStatistics> projectBomStatistics(ProjectBomStatisticsRef ref, String projectId) async {
  final result = await ref.read(bomRepositoryProvider).getProjectBomStatistics(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (statistics) => statistics,
  );
}

// Real-time BOM Statistics Provider - 基于BOM项目列表实时计算统计
@riverpod
Future<BomStatistics> realTimeBomStatistics(RealTimeBomStatisticsRef ref, String projectId) async {
  // 获取项目的BOM项目列表
  final bomItems = await ref.watch(projectBomItemsProvider(projectId).future);

  // 使用BomStatisticsService计算实时统计
  final statisticsService = ref.read(bomStatisticsServiceProvider);
  final statistics = statisticsService.calculateStatistics(bomItems);

  return statistics;
}

// BOM Cost Trend Provider - 成本趋势数据
@riverpod
Future<Map<String, double>> bomCostTrend(BomCostTrendRef ref, String projectId) async {
  final bomItems = await ref.watch(projectBomItemsProvider(projectId).future);
  final statisticsService = ref.read(bomStatisticsServiceProvider);

  return statisticsService.calculateCostTrend(bomItems);
}

// BOM Overdue Items Provider - 逾期项目
@riverpod
Future<List<domain.BomItem>> bomOverdueItems(BomOverdueItemsRef ref, String projectId) async {
  final bomItems = await ref.watch(projectBomItemsProvider(projectId).future);
  final statisticsService = ref.read(bomStatisticsServiceProvider);

  return statisticsService.findOverdueItems(bomItems);
}

// Search BOM Items Provider
@riverpod
Future<List<domain.BomItem>> searchBomItems(
  SearchBomItemsRef ref,
  String projectId,
  String query, {
  BomItemStatus? status,
  int limit = 20,
  int offset = 0,
}) async {
  final result = await ref.read(bomRepositoryProvider).searchBomItems(
    projectId,
    query,
    status: status,
    limit: limit,
    offset: offset,
  );
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
}

// Filtered BOM Items Provider - 将过滤逻辑从Widget移到Provider
@riverpod
Future<List<domain.BomItem>> filteredBomItems(
  Ref ref,
  String projectId,
  String searchQuery,
  String selectedStatus,
) async {
  final bomItems = await ref.watch(projectBomItemsProvider(projectId).future);
  
  return bomItems.where((item) {
    // 搜索过滤
    final matchesSearch = searchQuery.trim().isEmpty ||
        item.materialName.toLowerCase().contains(searchQuery.toLowerCase()) ||
        (item.category?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false);

    // 状态过滤
    final matchesStatus = selectedStatus == '全部' ||
        _getStatusDisplayName(item.status) == selectedStatus;

    return matchesSearch && matchesStatus;
  }).toList();
}

String _getStatusDisplayName(BomItemStatus status) {
  switch (status) {
    case BomItemStatus.pending:
      return '待采购';
    case BomItemStatus.ordered:
      return '已下单';
    case BomItemStatus.received:
      return '已收货';
    case BomItemStatus.installed:
      return '已安装';
    case BomItemStatus.cancelled:
      return '已取消';
  }
}

// BOM Items Notifier Provider - 用于UI
@riverpod
class BomItemsNotifier extends _$BomItemsNotifier {
  @override
  Future<List<domain.BomItem>> build() async {
    // 初始状态返回空列表
    return [];
  }

  Future<void> loadBomItems(String projectId) async {
    state = const AsyncLoading();
    final result = await ref.read(bomRepositoryProvider).getProjectBomItems(projectId);
    
    result.fold(
      (failure) => state = AsyncError(failure, StackTrace.current),
      (bomItems) => state = AsyncData(bomItems),
    );
  }

  Future<void> refresh(String projectId) async {
    loadBomItems(projectId);
  }
}

// BOM Controller Notifier
@riverpod
class BomController extends _$BomController {
  @override
  FutureOr<void> build() {
    // 初始化状态
  }

  /// 创建BOM项目 - 严格遵循Clean Architecture原则
  /// 不管理UI状态，只处理业务逻辑
  Future<Either<Failure, domain.BomItem>> createBomItem(
    String projectId,
    String name,
    String description,
    int quantity,
    double unitPrice, {
    String? materialId,
    String? category,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 获取当前用户ID
      final userId = await ref.read(requireCurrentUserIdProvider.future);

      // 创建请求对象 - Domain层实体
      final request = CreateBomItemRequest(
        projectId: projectId,
        userId: userId,
        name: name,
        description: description,
        quantity: quantity,
        unitPrice: unitPrice,
        category: category,
        brand: brand,
        model: model,
        specifications: specifications,
        supplier: supplier,
        supplierUrl: supplierUrl,
        imageUrl: imageUrl,
        notes: notes,
      );

      // 调用Use Case - 严格遵循Clean Architecture
      final result = await ref.read(createBomItemUseCaseProvider).call(
        CreateBomItemParams(
          projectId: projectId,
          request: request,
        ),
      );

      // 处理结果 - 使用Either模式，不设置UI状态
      return result.fold(
        (failure) => Left(failure),
        (bomItem) {
          // 成功后异步刷新相关数据 - 不影响主流程
          _scheduleProviderRefresh(projectId);
          return Right<Failure, domain.BomItem>(bomItem);
        },
      );
    } catch (e) {
      // 统一错误处理
      return Left(ServerFailure(message: '创建BOM项失败: $e'));
    }
  }

  /// 添加材料到BOM - 严格遵循Clean Architecture原则
  /// 不管理UI状态，只处理业务逻辑
  Future<Either<Failure, void>> addMaterialToBom({
    required String projectId,
    required String materialId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  }) async {
    try {
      // 调用Use Case - 严格遵循Clean Architecture
      final result = await ref.read(addMaterialToBomUseCaseProvider).call(
        AddMaterialToBomParams(
          projectId: projectId,
          materialId: materialId,
          quantity: quantity,
          notes: notes,
        ),
      );

      // 处理结果 - 使用Either模式，不设置UI状态
      return result.fold(
        (failure) => Left(failure),
        (_) {
          // 成功后异步刷新相关数据 - 不影响主流程
          Future.microtask(() {
            try {
              ref.invalidate(projectBomItemsProvider);
            } catch (e) {
              debugPrint('Provider刷新失败: $e');
            }
          });
          // 返回成功结果
          return const Right(null);
        },
      );
    } catch (e) {
      // 统一错误处理
      return Left(UnknownFailure(message: '添加材料到BOM失败: $e'));
    }
  }

  /// 安全地调度Provider刷新 - 符合Clean Architecture原则
  void _scheduleProviderRefresh(String projectId) {
    // 使用Future.microtask确保在当前同步执行完成后执行
    // 避免在fold回调中直接操作Provider状态
    Future.microtask(() {
      try {
        // 只刷新必要的Provider，避免级联刷新
        ref.invalidate(projectBomItemsProvider);
      } catch (e) {
        // 静默处理刷新错误，不影响主要业务流程
        debugPrint('Provider刷新失败: $e');
      }
    });
  }

  Future<Either<Failure, domain.BomItem>> updateBomItem(UpdateBomItemRequest request) async {
    state = const AsyncLoading();

    // 将UpdateBomItemRequest转换为Map格式，因为Repository期望Map
    final updates = {
      'item_name': request.name,  // 修复字段名：name -> item_name
      'description': request.description,
      'category': request.category,
      'quantity': request.quantity,
      'price': request.unitPrice,  // 修复字段名：unit_price -> price
      'status': request.status.code,
      if (request.brand != null) 'brand': request.brand,
      if (request.model != null) 'model': request.model,
      if (request.specifications != null) 'specifications': request.specifications,
      if (request.supplier != null) 'supplier': request.supplier,
      if (request.supplierUrl != null) 'supplier_url': request.supplierUrl,
      if (request.imageUrl != null) 'image_url': request.imageUrl,
      if (request.plannedDate != null) 'planned_date': request.plannedDate!.toIso8601String(),
      if (request.notes != null) 'notes': request.notes,
      if (request.metadata != null) 'metadata': request.metadata,
    };

    final result = await ref.read(bomRepositoryProvider).updateBomItemById(request.id, updates);

    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
        return Left(failure);
      },
      (bomItem) {
        // 使用AsyncValue.data构造函数，避免使用void结果
        state = const AsyncData(null);
        // 刷新相关状态
        ref.invalidate(projectBomItemsProvider);
        ref.invalidate(bomItemDetailProvider(request.id));
        return Right(bomItem);
      },
    );
  }

  Future<Either<Failure, void>> deleteBomItem(String bomItemId) async {
    state = const AsyncLoading();

    final result = await ref.read(bomRepositoryProvider).deleteBomItem(bomItemId);

    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
        return Left(failure);
      },
      (_) {
        state = const AsyncData(null);
        // 刷新BOM列表
        ref.invalidate(projectBomItemsProvider);
        ref.invalidate(bomItemDetailProvider(bomItemId));
        return const Right(null);
      },
    );
  }

  Future<Either<Failure, domain.BomItem>> updateBomItemStatus(String bomItemId, BomItemStatus newStatus) async {
    try {
      // 直接调用Repository，不设置AsyncLoading状态避免状态竞争
      final result = await ref.read(bomRepositoryProvider).updateBomItemStatus(bomItemId, newStatus);

      return result.fold(
        (failure) {
          return Left(failure);
        },
        (bomItem) {
          // 使用独立方法刷新相关Provider，避免状态竞争
          _refreshBomItemProviders(bomItemId);
          return Right(bomItem);
        },
      );
    } catch (e) {
      final failure = UnknownFailure(message: '更新BOM项状态失败: $e');
      return Left(failure);
    }
  }

  /// 刷新BOM项相关Provider的独立方法
  void _refreshBomItemProviders(String bomItemId) {
    // 使用调度器确保在下一个事件循环中执行，并分批刷新避免状态竞争
    SchedulerBinding.instance.addPostFrameCallback((_) {
      try {
        // 分批刷新Provider，避免同时刷新导致状态竞争
        ref.invalidate(projectBomItemsProvider);

        // 延迟刷新详情Provider，确保列表Provider先完成
        Future.delayed(const Duration(milliseconds: 50), () {
          try {
            ref.invalidate(bomItemDetailProvider(bomItemId));
          } catch (e) {
            debugPrint('刷新BOM详情Provider时出错: $e');
          }
        });
      } catch (e) {
        // 记录错误但不影响主要功能
        debugPrint('刷新BOM项Provider时出错: $e');
      }
    });
  }



  Future<Either<Failure, String>> saveBomItemToMaterialLibrary(String bomItemId) async {
    final result = await ref.read(bomRepositoryProvider).saveBomItemToMaterialLibrary(bomItemId);
    
    return result;
  }
}