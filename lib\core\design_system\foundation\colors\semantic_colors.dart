/// VanHub语义化颜色系统
/// 
/// 功能性颜色的语义化定义
/// 支持状态指示、反馈信息、数据可视化

import 'package:flutter/material.dart';

/// 语义化颜色系统
class VanHubSemanticColors {
  VanHubSemanticColors._();

  // ============ 状态颜色 ============
  
  /// 成功状态
  static const Color success = Color(0xFF10B981);
  static const Color successLight = Color(0xFF34D399);
  static const Color successDark = Color(0xFF059669);
  static const Color successContainer = Color(0xFFECFDF5);
  static const Color onSuccess = Color(0xFFFFFFFF);
  static const Color onSuccessContainer = Color(0xFF064E3B);

  /// 警告状态
  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFBBF24);
  static const Color warningDark = Color(0xFFD97706);
  static const Color warningContainer = Color(0xFFFEF3C7);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color onWarningContainer = Color(0xFF92400E);

  /// 错误状态
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFF87171);
  static const Color errorDark = Color(0xFFDC2626);
  static const Color errorContainer = Color(0xFFFEF2F2);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color onErrorContainer = Color(0xFF991B1B);

  /// 信息状态
  static const Color info = Color(0xFF3B82F6);
  static const Color infoLight = Color(0xFF60A5FA);
  static const Color infoDark = Color(0xFF2563EB);
  static const Color infoContainer = Color(0xFFEBF4FF);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color onInfoContainer = Color(0xFF1E3A8A);

  // ============ 文本颜色 ============
  
  /// 主要文本
  static const Color textPrimary = Color(0xFF111827);
  static const Color textPrimaryDark = Color(0xFFF9FAFB);

  /// 次要文本
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textSecondaryDark = Color(0xFF9CA3AF);

  /// 辅助文本
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textTertiaryDark = Color(0xFF6B7280);

  /// 禁用文本
  static const Color textDisabled = Color(0xFFD1D5DB);
  static const Color textDisabledDark = Color(0xFF4B5563);

  /// 占位符文本
  static const Color textPlaceholder = Color(0xFFA1A1AA);
  static const Color textPlaceholderDark = Color(0xFF71717A);

  // ============ 背景颜色 ============
  
  /// 主背景
  static const Color backgroundPrimary = Color(0xFFFFFFFF);
  static const Color backgroundPrimaryDark = Color(0xFF111827);

  /// 次背景
  static const Color backgroundSecondary = Color(0xFFF9FAFB);
  static const Color backgroundSecondaryDark = Color(0xFF1F2937);

  /// 三级背景
  static const Color backgroundTertiary = Color(0xFFF3F4F6);
  static const Color backgroundTertiaryDark = Color(0xFF374151);

  /// 悬浮背景
  static const Color backgroundElevated = Color(0xFFFFFFFF);
  static const Color backgroundElevatedDark = Color(0xFF1F2937);

  // ============ 边框颜色 ============
  
  /// 主边框
  static const Color borderPrimary = Color(0xFFE5E7EB);
  static const Color borderPrimaryDark = Color(0xFF374151);

  /// 次边框
  static const Color borderSecondary = Color(0xFFF3F4F6);
  static const Color borderSecondaryDark = Color(0xFF4B5563);

  /// 强调边框
  static const Color borderAccent = Color(0xFF2563EB);
  static const Color borderAccentDark = Color(0xFF3B82F6);

  /// 分割线
  static const Color divider = Color(0xFFE5E7EB);
  static const Color dividerDark = Color(0xFF374151);

  // ============ 交互颜色 ============
  
  /// 悬停状态
  static const Color hoverOverlay = Color(0x0A000000);
  static const Color hoverOverlayDark = Color(0x14FFFFFF);

  /// 按压状态
  static const Color pressedOverlay = Color(0x14000000);
  static const Color pressedOverlayDark = Color(0x1FFFFFFF);

  /// 焦点状态
  static const Color focusOverlay = Color(0x1F2563EB);
  static const Color focusOverlayDark = Color(0x1F3B82F6);

  /// 选中状态
  static const Color selectedOverlay = Color(0x142563EB);
  static const Color selectedOverlayDark = Color(0x143B82F6);

  // ============ 数据可视化颜色 ============
  
  /// 图表颜色序列
  static const List<Color> chartColors = [
    Color(0xFF2563EB), // 蓝色
    Color(0xFF10B981), // 绿色
    Color(0xFFF59E0B), // 黄色
    Color(0xFFEF4444), // 红色
    Color(0xFF8B5CF6), // 紫色
    Color(0xFF06B6D4), // 青色
    Color(0xFFEC4899), // 粉色
    Color(0xFF84CC16), // 青绿色
  ];

  /// 渐变图表颜色
  static const List<LinearGradient> chartGradients = [
    LinearGradient(
      colors: [Color(0xFF2563EB), Color(0xFF3B82F6)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [Color(0xFF10B981), Color(0xFF34D399)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [Color(0xFFF59E0B), Color(0xFFFBBF24)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    LinearGradient(
      colors: [Color(0xFFEF4444), Color(0xFFF87171)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
  ];

  // ============ 特殊效果颜色 ============
  
  /// 玻璃态效果
  static const Color glassBackground = Color(0x80FFFFFF);
  static const Color glassBackgroundDark = Color(0x40000000);
  static const Color glassBorder = Color(0x40FFFFFF);
  static const Color glassBorderDark = Color(0x40FFFFFF);

  /// 毛玻璃效果
  static const Color frostedGlass = Color(0xCCFFFFFF);
  static const Color frostedGlassDark = Color(0xCC000000);

  /// 阴影颜色
  static const Color shadowLight = Color(0x0A000000);
  static const Color shadowMedium = Color(0x14000000);
  static const Color shadowHeavy = Color(0x1F000000);
  static const Color shadowDark = Color(0x40000000);

  // ============ 工具方法 ============
  
  /// 根据主题获取文本颜色
  static Color getTextColor(BuildContext context, {bool secondary = false}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (secondary) {
      return isDark ? textSecondaryDark : textSecondary;
    }
    return isDark ? textPrimaryDark : textPrimary;
  }

  /// 根据主题获取背景颜色
  static Color getBackgroundColor(BuildContext context, {int level = 1}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    switch (level) {
      case 1:
        return isDark ? backgroundPrimaryDark : backgroundPrimary;
      case 2:
        return isDark ? backgroundSecondaryDark : backgroundSecondary;
      case 3:
        return isDark ? backgroundTertiaryDark : backgroundTertiary;
      default:
        return isDark ? backgroundPrimaryDark : backgroundPrimary;
    }
  }

  /// 根据主题获取边框颜色
  static Color getBorderColor(BuildContext context, {bool accent = false}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (accent) {
      return isDark ? borderAccentDark : borderAccent;
    }
    return isDark ? borderPrimaryDark : borderPrimary;
  }

  /// 获取状态颜色
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return success;
      case 'warning':
      case 'pending':
      case 'in_progress':
        return warning;
      case 'error':
      case 'failed':
      case 'cancelled':
        return error;
      case 'info':
      case 'draft':
      case 'planned':
        return info;
      default:
        return textSecondary;
    }
  }

  /// 获取图表颜色
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }

  /// 获取图表渐变
  static LinearGradient getChartGradient(int index) {
    return chartGradients[index % chartGradients.length];
  }
}
