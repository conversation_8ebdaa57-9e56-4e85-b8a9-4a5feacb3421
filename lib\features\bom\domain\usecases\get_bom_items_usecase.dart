import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../repositories/bom_repository.dart';

class GetBomItemsUseCase {
  final BomRepository repository;

  GetBomItemsUseCase(this.repository);

  /// 标准call方法，用于Provider调用
  Future<Either<Failure, List<BomItem>>> call(String projectId) async {
    return await getProjectBomItems(projectId);
  }

  /// 获取项目的BOM项目列表
  Future<Either<Failure, List<BomItem>>> getProjectBomItems(String projectId) async {
    if (projectId.isEmpty) {
      return Left(ValidationFailure(message: '项目ID不能为空'));
    }
    
    return await repository.getProjectBomItems(projectId);
  }

  /// 获取BOM项目详情
  Future<Either<Failure, BomItem>> getBomItemById(String bomItemId) async {
    if (bomItemId.isEmpty) {
      return Left(ValidationFailure(message: 'BOM项目ID不能为空'));
    }
    
    return await repository.getBomItemById(bomItemId);
  }

  /// 获取用户的所有BOM项目
  Future<Either<Failure, List<BomItem>>> getUserBomItems(String userId) async {
    if (userId.isEmpty) {
      return Left(ValidationFailure(message: '用户ID不能为空'));
    }
    
    return await repository.getUserBomItems(userId);
  }
}