import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/components/atoms/vanhub_avatar.dart';
import 'package:vanhub/core/design_system/components/atoms/vanhub_badge.dart';
import 'package:vanhub/core/design_system/components/molecules/vanhub_modal.dart';
import 'package:vanhub/core/design_system/components/molecules/vanhub_search_bar.dart';
import 'package:vanhub/core/theme/vanhub_theme_v2.dart';
import 'package:vanhub/core/accessibility/vanhub_accessibility.dart';

void main() {
  group('设计系统集成测试', () {
    testWidgets('应该正确集成所有设计组件', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: Scaffold(
            appBar: AppBar(
              title: const Text('设计系统测试'),
              actions: [
                VanHubBadge(
                  count: 5,
                  child: Icon<PERSON>utton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () {},
                  ),
                ),
              ],
            ),
            body: const Column(
              children: [
                // 头像组件
                VanHubAvatar(
                  name: '测试用户',
                  showOnlineStatus: true,
                  isOnline: true,
                ),
                SizedBox(height: 16),
                
                // 搜索栏组件
                VanHubSearchBar(
                  hintText: '搜索材料...',
                  showClearButton: true,
                ),
                SizedBox(height: 16),
                
                // 徽章组件
                VanHubBadge(
                  count: 99,
                  child: Icon(Icons.mail),
                ),
              ],
            ),
            floatingActionButton: Builder(
              builder: (context) => FloatingActionButton(
                onPressed: () {
                  VanHubModal.show(
                    context: context,
                    title: '测试模态框',
                    content: const Text('这是一个测试模态框'),
                  );
                },
                child: const Icon(Icons.add),
              ),
            ),
          ),
        ),
      );

      // 验证组件渲染
      expect(find.text('设计系统测试'), findsOneWidget);
      expect(find.byType(VanHubAvatar), findsOneWidget);
      expect(find.byType(VanHubSearchBar), findsOneWidget);
      expect(find.byType(VanHubBadge), findsNWidgets(2));
      
      // 测试模态框
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();
      
      expect(find.text('测试模态框'), findsOneWidget);
      expect(find.text('这是一个测试模态框'), findsOneWidget);
    });

    testWidgets('应该支持主题切换', (WidgetTester tester) async {
      // 测试浅色主题
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: const Scaffold(
            body: VanHubAvatar(name: '测试'),
          ),
        ),
      );

      expect(find.byType(VanHubAvatar), findsOneWidget);

      // 切换到深色主题
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getDarkTheme(),
          home: const Scaffold(
            body: VanHubAvatar(name: '测试'),
          ),
        ),
      );

      expect(find.byType(VanHubAvatar), findsOneWidget);
    });

    testWidgets('应该支持无障碍功能', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: Scaffold(
            body: Column(
              children: [
                VanHubAccessibleButton(
                  semanticLabel: '提交按钮',
                  onPressed: () {},
                  child: const Text('提交'),
                ),
                const VanHubAccessibleTextField(
                  labelText: '用户名',
                  semanticLabel: '用户名输入框',
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('提交'), findsOneWidget);
      expect(find.text('用户名'), findsOneWidget);
    });

    testWidgets('应该支持响应式设计', (WidgetTester tester) async {
      // 测试手机端
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: const Scaffold(
            body: VanHubSearchBar(
              hintText: '搜索...',
            ),
          ),
        ),
      );

      expect(find.byType(VanHubSearchBar), findsOneWidget);

      // 测试桌面端
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: const Scaffold(
            body: VanHubSearchBar(
              hintText: '搜索...',
            ),
          ),
        ),
      );

      expect(find.byType(VanHubSearchBar), findsOneWidget);
    });

    testWidgets('应该支持动画和交互', (WidgetTester tester) async {
      bool buttonPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: VanHubThemeV2.getLightTheme(),
          home: Scaffold(
            body: Column(
              children: [
                VanHubBadge(
                  count: 5,
                  showAnimation: true,
                  child: IconButton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () => buttonPressed = true,
                  ),
                ),
                VanHubAvatar(
                  name: '测试',
                  onTap: () => buttonPressed = true,
                ),
              ],
            ),
          ),
        ),
      );

      // 测试徽章动画
      expect(find.byType(AnimatedScale), findsOneWidget);
      
      // 测试交互
      await tester.tap(find.byIcon(Icons.notifications));
      expect(buttonPressed, isTrue);
      
      buttonPressed = false;
      await tester.tap(find.byType(VanHubAvatar));
      expect(buttonPressed, isTrue);
    });
  });
}