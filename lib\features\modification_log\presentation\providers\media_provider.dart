import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';
import '../../domain/repositories/media_repository.dart';
import '../../di/providers.dart';
import '../../domain/usecases/get_log_media_usecase.dart';
import '../../domain/usecases/upload_media_usecase.dart';

/// 删除媒体参数
class DeleteMediaParams {
  final String mediaId;
  DeleteMediaParams({required this.mediaId});
}

/// 删除媒体用例
class DeleteMediaUseCase {
  final MediaRepository repository;
  DeleteMediaUseCase(this.repository);
  
  Future<Either<Failure, void>> call(DeleteMediaParams params) async {
    return await repository.deleteMedia(params.mediaId);
  }
}

/// 媒体状态
class MediaState {
  final bool isLoading;
  final List<LogMedia> mediaList;
  final LogMedia? selectedMedia;
  final Failure? failure;
  final double uploadProgress;

  const MediaState({
    this.isLoading = false,
    this.mediaList = const [],
    this.selectedMedia,
    this.failure,
    this.uploadProgress = 0.0,
  });

  MediaState copyWith({
    bool? isLoading,
    List<LogMedia>? mediaList,
    LogMedia? selectedMedia,
    Failure? failure,
    double? uploadProgress,
  }) {
    return MediaState(
      isLoading: isLoading ?? this.isLoading,
      mediaList: mediaList ?? this.mediaList,
      selectedMedia: selectedMedia ?? this.selectedMedia,
      failure: failure,
      uploadProgress: uploadProgress ?? this.uploadProgress,
    );
  }
}

/// 媒体提供者
class MediaNotifier extends StateNotifier<MediaState> {
  final UploadMediaUseCase _uploadMediaUseCase;
  final GetLogMediaUseCase _getLogMediaUseCase;
  final DeleteMediaUseCase _deleteMediaUseCase;

  MediaNotifier({
    required UploadMediaUseCase uploadMediaUseCase,
    required GetLogMediaUseCase getLogMediaUseCase,
    required DeleteMediaUseCase deleteMediaUseCase,
  })  : _uploadMediaUseCase = uploadMediaUseCase,
        _getLogMediaUseCase = getLogMediaUseCase,
        _deleteMediaUseCase = deleteMediaUseCase,
        super(const MediaState());

  /// 获取日志媒体
  Future<void> getLogMedia(String logId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _getLogMediaUseCase(GetLogMediaParams(logId: logId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (mediaList) => state = state.copyWith(isLoading: false, mediaList: mediaList),
    );
  }

  /// 上传媒体
  Future<Either<Failure, LogMedia>> uploadMedia({
    required File file,
    required String logId,
    required MediaType type,
    required String uploadedBy,
    MediaMetadata? metadata,
  }) async {
    state = state.copyWith(isLoading: true, failure: null, uploadProgress: 0.0);

    // 模拟上传进度
    _simulateUploadProgress();

    final result = await _uploadMediaUseCase(UploadMediaParams(
      file: file,
      logId: logId,
      type: type,
      uploadedBy: uploadedBy,
      metadata: metadata,
    ));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure, uploadProgress: 0.0),
      (media) {
        final updatedMediaList = [...state.mediaList, media];
        state = state.copyWith(
          isLoading: false,
          mediaList: updatedMediaList,
          selectedMedia: media,
          uploadProgress: 1.0,
        );
      },
    );

    return result;
  }

  /// 删除媒体
  Future<Either<Failure, void>> deleteMedia(String mediaId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _deleteMediaUseCase(DeleteMediaParams(mediaId: mediaId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (_) {
        final updatedMediaList = state.mediaList.where((media) => media.id != mediaId).toList();
        state = state.copyWith(
          isLoading: false,
          mediaList: updatedMediaList,
          selectedMedia: null,
        );
      },
    );

    return result;
  }

  /// 选择媒体
  void selectMedia(LogMedia media) {
    state = state.copyWith(selectedMedia: media);
  }

  /// 清除选中的媒体
  void clearSelectedMedia() {
    state = state.copyWith(selectedMedia: null);
  }

  /// 清除错误
  void clearFailure() {
    state = state.copyWith(failure: null);
  }

  /// 模拟上传进度
  void _simulateUploadProgress() {
    double progress = 0.0;
    const step = 0.1;
    const interval = Duration(milliseconds: 300);

    Future.doWhile(() async {
      if (!state.isLoading || progress >= 0.9) {
        return false;
      }

      await Future.delayed(interval);
      progress += step;
      state = state.copyWith(uploadProgress: progress);
      return true;
    });
  }
}

/// 媒体提供者
final mediaProvider = StateNotifierProvider<MediaNotifier, MediaState>((ref) {
  // 使用依赖注入获取Repository
  final mediaRepository = ref.read(mediaRepositoryProvider);
  
  // 创建所需的用例
  final uploadMediaUseCase = UploadMediaUseCase(mediaRepository);
  final getLogMediaUseCase = GetLogMediaUseCase(mediaRepository);
  final deleteMediaUseCase = DeleteMediaUseCase(mediaRepository);
  
  return MediaNotifier(
    uploadMediaUseCase: uploadMediaUseCase,
    getLogMediaUseCase: getLogMediaUseCase,
    deleteMediaUseCase: deleteMediaUseCase,
  );
});