import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/api/supabase_config.dart';
import '../core/utils/performance_monitor.dart';
import '../core/utils/error_handler.dart';
import '../core/utils/loading_manager.dart';
import '../core/errors/app_error.dart';

/// BOM清单管理页面
class BOMManagementPage extends ConsumerStatefulWidget {
  const BOMManagementPage({super.key});

  @override
  ConsumerState<BOMManagementPage> createState() => _BOMManagementPageState();
}

class _BOMManagementPageState extends ConsumerState<BOMManagementPage> {
  List<Map<String, dynamic>> projects = [];
  List<Map<String, dynamic>> bomItems = [];
  List<Map<String, dynamic>> materials = [];
  bool isLoading = false;
  String? errorMessage;
  String? selectedProjectId;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  /// 加载项目列表
  Future<void> _loadProjects() async {
    try {
      final response = await SupabaseConfig.client
          .from('projects')
          .select('*')
          .order('created_at', ascending: false);
      
      if (mounted) {
        setState(() {
          projects = response.cast<Map<String, dynamic>>();
          if (projects.isNotEmpty && selectedProjectId == null) {
            selectedProjectId = projects.first['id'];
            _loadBOMItems();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('加载项目列表失败', details: e.toString()),
          customMessage: '无法加载项目列表',
        );
      }
    }
  }

  /// 加载BOM清单
  Future<void> _loadBOMItems() async {
    if (selectedProjectId == null) return;

    const loadingKey = 'load_bom_items';
    
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      globalLoadingManager.startLoading(loadingKey, message: '加载BOM清单...');
      
      final result = await PerformanceMonitor.monitor('load_bom_items', () async {
        // bom_items表已经包含了所有需要的字段，不需要关联查询
        return await SupabaseConfig.client
            .from('bom_items')
            .select('*')
            .eq('project_id', selectedProjectId!)
            .order('created_at', ascending: false);
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: 'BOM清单加载完成');
      
      if (mounted) {
        setState(() {
          bomItems = result.cast<Map<String, dynamic>>();
          isLoading = false;
        });
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: 'BOM清单加载失败');
      
      if (mounted) {
        setState(() {
          errorMessage = e.toString();
          isLoading = false;
        });

        ErrorHandler.handle(
          context,
          AppError.network('加载BOM清单失败', details: e.toString()),
          customMessage: '无法加载BOM清单，请检查网络连接',
        );
      }
    }
  }

  /// 加载材料列表
  Future<void> _loadMaterials() async {
    try {
      final response = await SupabaseConfig.client
          .from('materials')
          .select('*')
          .order('name');
      
      if (mounted) {
        setState(() {
          materials = response.cast<Map<String, dynamic>>();
        });
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('加载材料列表失败', details: e.toString()),
          customMessage: '无法加载材料列表',
        );
      }
    }
  }

  /// 添加BOM项目
  Future<void> _addBOMItem() async {
    if (selectedProjectId == null) return;

    // 先加载材料列表
    await _loadMaterials();

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => BOMItemCreateDialog(
        materials: materials,
        projectId: selectedProjectId!,
      ),
    );

    if (result != null) {
      await _saveBOMItem(result);
    }
  }

  /// 保存BOM项目
  Future<void> _saveBOMItem(Map<String, dynamic> bomData) async {
    const loadingKey = 'save_bom_item';
    
    try {
      globalLoadingManager.startLoading(loadingKey, message: '保存BOM项目...');
      
      await PerformanceMonitor.monitor('save_bom_item', () async {
        await SupabaseConfig.client.from('bom_items').insert({
          ...bomData,
          'created_at': DateTime.now().toIso8601String(),
        });
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: 'BOM项目保存成功');
      
      // 重新加载BOM清单
      await _loadBOMItems();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('BOM项目添加成功！'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: 'BOM项目保存失败');
      
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('保存BOM项目失败', details: e.toString()),
          customMessage: '无法保存BOM项目，请重试',
        );
      }
    }
  }

  /// 删除BOM项目
  Future<void> _deleteBOMItem(String bomItemId, String materialName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要从BOM清单中删除"$materialName"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      const loadingKey = 'delete_bom_item';
      
      try {
        globalLoadingManager.startLoading(loadingKey, message: '删除BOM项目...');
        
        await PerformanceMonitor.monitor('delete_bom_item', () async {
          await SupabaseConfig.client
              .from('bom_items')
              .delete()
              .eq('id', bomItemId);
        });
        
        globalLoadingManager.completeLoading(loadingKey, message: 'BOM项目删除成功');
        
        // 重新加载BOM清单
        await _loadBOMItems();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.delete, color: Colors.white),
                  SizedBox(width: 8),
                  Text('BOM项目删除成功！'),
                ],
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        globalLoadingManager.failLoading(loadingKey, message: 'BOM项目删除失败');
        
        if (mounted) {
          ErrorHandler.handle(
            context,
            AppError.database('删除BOM项目失败', details: e.toString()),
            customMessage: '无法删除BOM项目，请重试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BOM清单'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBOMItems,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProjectSelector(),
          Expanded(child: _buildBody()),
          _buildSummary(),
        ],
      ),
      floatingActionButton: selectedProjectId != null
          ? FloatingActionButton(
              onPressed: _addBOMItem,
              backgroundColor: Colors.purple,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildProjectSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade50,
      child: Row(
        children: [
          const Text(
            '选择项目: ',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButton<String>(
              value: selectedProjectId,
              isExpanded: true,
              hint: const Text('请选择项目'),
              items: projects.map((project) {
                return DropdownMenuItem<String>(
                  value: project['id'],
                  child: Text(project['name'] ?? '未命名项目'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedProjectId = value;
                });
                if (value != null) {
                  _loadBOMItems();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (selectedProjectId == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '请先选择一个项目',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('加载BOM清单中...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadBOMItems,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (bomItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.list_alt,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无BOM项目',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              '点击右下角的 + 按钮添加第一个BOM项目',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bomItems.length,
      itemBuilder: (context, index) {
        final bomItem = bomItems[index];
        return _buildBOMItemCard(bomItem);
      },
    );
  }

  Widget _buildBOMItemCard(Map<String, dynamic> bomItem) {
    // 直接从bom_items表获取数据，不需要关联查询
    final itemName = bomItem['item_name'] ?? '未知材料';
    final quantity = bomItem['quantity'] ?? 1;
    final price = bomItem['price'] ?? 0.0;
    final totalPrice = quantity * price;
    final status = bomItem['status'] ?? 'planned';
    final category = bomItem['category'] ?? '未分类';
    final description = bomItem['description'] ?? '';
    final notes = bomItem['notes'] ?? '';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    itemName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: _getStatusColor(status)),
                  ),
                  child: Text(
                    _getStatusText(status),
                    style: TextStyle(
                      color: _getStatusColor(status),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '数量: $quantity 个',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '单价: ¥${price.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      if (category.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          '分类: $category',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '小计',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '¥${totalPrice.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.description, size: 16, color: Colors.blue),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '描述: $description',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
            if (bomItem['notes'] != null && bomItem['notes'].isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.note, size: 16, color: Colors.orange),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '备注: ${bomItem['notes']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _editBOMItem(bomItem),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('编辑'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteBOMItem(
                    bomItem['id'].toString(),
                    itemName,
                  ),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('删除'),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummary() {
    if (bomItems.isEmpty) return const SizedBox.shrink();

    double totalCost = 0;
    int totalItems = bomItems.length;

    for (final item in bomItems) {
      final quantity = item['quantity'] ?? 1.0;
      final unitPrice = item['unit_price'] ?? 0.0;
      totalCost += quantity * unitPrice;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        border: Border(top: BorderSide(color: Colors.purple.shade200)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '共 $totalItems 项材料',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            '总计: ¥${totalCost.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'planned':
        return Colors.grey;
      case 'pending':
        return Colors.orange;
      case 'ordered':
        return Colors.blue;
      case 'received':
        return Colors.green;
      case 'installed':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'planned':
        return '规划中';
      case 'pending':
        return '待采购';
      case 'ordered':
        return '已下单';
      case 'received':
        return '已到货';
      case 'installed':
        return '已安装';
      default:
        return '未知';
    }
  }

  void _editBOMItem(Map<String, dynamic> bomItem) {
    // TODO: 实现编辑功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('编辑功能即将推出'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// BOM项目创建对话框
class BOMItemCreateDialog extends StatefulWidget {
  final List<Map<String, dynamic>> materials;
  final String projectId;

  const BOMItemCreateDialog({
    super.key,
    required this.materials,
    required this.projectId,
  });

  @override
  State<BOMItemCreateDialog> createState() => _BOMItemCreateDialogState();
}

class _BOMItemCreateDialogState extends State<BOMItemCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController(text: '1');
  final _unitPriceController = TextEditingController();
  final _notesController = TextEditingController();
  String? _selectedMaterialId;
  String _selectedStatus = 'pending';

  @override
  void dispose() {
    _quantityController.dispose();
    _unitPriceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('添加BOM项目'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedMaterialId,
                  decoration: const InputDecoration(
                    labelText: '选择材料',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.materials.map((material) {
                    return DropdownMenuItem<String>(
                      value: material['id'],
                      child: Text('${material['name']} (¥${material['price']})'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedMaterialId = value;
                      // 自动填充单价
                      if (value != null) {
                        final material = widget.materials.firstWhere(
                          (m) => m['id'] == value,
                        );
                        _unitPriceController.text = material['price'].toString();
                      }
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return '请选择材料';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: '数量',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入数量';
                          }
                          if (double.tryParse(value) == null) {
                            return '请输入有效的数量';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _unitPriceController,
                        decoration: const InputDecoration(
                          labelText: '单价',
                          border: OutlineInputBorder(),
                          prefixText: '¥ ',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入单价';
                          }
                          if (double.tryParse(value) == null) {
                            return '请输入有效的单价';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: '状态',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'pending', child: Text('待采购')),
                    DropdownMenuItem(value: 'ordered', child: Text('已下单')),
                    DropdownMenuItem(value: 'received', child: Text('已到货')),
                    DropdownMenuItem(value: 'installed', child: Text('已安装')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: '备注',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop({
                'project_id': widget.projectId,
                'material_id': _selectedMaterialId,
                'quantity': double.parse(_quantityController.text.trim()),
                'unit_price': double.parse(_unitPriceController.text.trim()),
                'status': _selectedStatus,
                'notes': _notesController.text.trim(),
              });
            }
          },
          child: const Text('添加'),
        ),
      ],
    );
  }
}
