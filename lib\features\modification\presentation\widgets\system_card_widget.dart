import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/modification_system.dart';
import '../../domain/entities/system_material.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../modification_log/domain/entities/enums.dart';

/// 改装系统卡片组件
/// 用于展示改装系统的详细信息，支持展开/折叠显示物料列表
class SystemCard extends ConsumerStatefulWidget {
  const SystemCard({
    super.key,
    required this.system,
    this.isExpanded = false,
    this.onToggle,
    this.onEdit,
    this.onDelete,
    this.onAddMaterial,
    this.onMaterialTap,
    this.showActions = true,
  });

  /// 改装系统数据
  final ModificationSystem system;

  /// 是否展开
  final bool isExpanded;

  /// 展开/折叠回调
  final VoidCallback? onToggle;

  /// 编辑回调
  final VoidCallback? onEdit;

  /// 删除回调
  final VoidCallback? onDelete;

  /// 添加物料回调
  final VoidCallback? onAddMaterial;

  /// 物料点击回调
  final Function(SystemMaterial)? onMaterialTap;

  /// 是否显示操作按钮
  final bool showActions;

  @override
  ConsumerState<SystemCard> createState() => _SystemCardState();
}

class _SystemCardState extends ConsumerState<SystemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(SystemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Column(
        children: [
          // 系统头部信息
          _buildSystemHeader(context),
          
          // 可展开的物料列表
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: _buildMaterialList(context),
          ),
        ],
      ),
    );
  }

  /// 构建系统头部
  Widget _buildSystemHeader(BuildContext context) {
    final systemColor = Colors.blue; // 临时使用固定颜色
    final containerColor = Colors.blue.withOpacity(0.1); // 临时使用固定颜色

    return InkWell(
      onTap: widget.onToggle,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: containerColor,
          border: Border(
            left: BorderSide(
              color: systemColor,
              width: 4,
            ),
          ),
        ),
        child: Column(
          children: [
            // 系统标题行
            Row(
              children: [
                // 系统图标
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: systemColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getSystemIcon(widget.system.type.name),
                    size: 24,
                    color: systemColor,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // 系统名称和费用
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              widget.system.name,
                              style: VanHubTypography.titleMedium.copyWith(
                                color: systemColor,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          
                          // 费用信息
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '¥${widget.system.actualAmount.toStringAsFixed(0)}',
                                style: VanHubTypography.titleMedium.copyWith(
                                  color: widget.system.isOverBudget
                                      ? VanHubColors.error
                                      : VanHubColors.success,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (widget.system.budgetAmount > 0)
                                Text(
                                  '预算 ¥${widget.system.budgetAmount.toStringAsFixed(0)}',
                                  style: VanHubTypography.bodySmall.copyWith(
                                    color: VanHubColors.textSecondary,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // 系统类型和状态
                      Row(
                        children: [
                          Text(
                            widget.system.typeDisplayName,
                            style: VanHubTypography.bodySmall.copyWith(
                              color: VanHubColors.textSecondary,
                            ),
                          ),
                          
                          const SizedBox(width: 8),

                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor().withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              widget.system.statusDisplayText,
                              style: VanHubTypography.bodySmall.copyWith(
                                color: _getStatusColor(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 展开/折叠图标
                AnimatedRotation(
                  turns: widget.isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.expand_more,
                    color: systemColor,
                  ),
                ),
                
                // 操作菜单
                if (widget.showActions)
                  PopupMenuButton<String>(
                    onSelected: _handleMenuAction,
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('编辑系统'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'add_material',
                        child: ListTile(
                          leading: Icon(Icons.add),
                          title: Text('添加物料'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('删除系统'),
                          dense: true,
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert,
                      color: systemColor,
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: VanHubSpacing.md),
            
            // 进度信息
            _buildProgressSection(context, systemColor),
          ],
        ),
      ),
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection(BuildContext context, Color systemColor) {
    final percentage = widget.system.completionPercentage;
    
    return Column(
      children: [
        // 进度标题和百分比
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '进度: ${widget.system.progressDescription}',
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubColors.textSecondary,
              ),
            ),
            
            Text(
              '${(percentage * 100).round()}%',
              style: VanHubTypography.bodySmall.copyWith(
                color: systemColor,
              ),
            ),
          ],
        ),
        
        SizedBox(height: VanHubSpacing.xs),
        
        // 进度条
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: systemColor.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(systemColor),
        ),
        
        SizedBox(height: VanHubSpacing.sm),
        
        // 统计信息
        Row(
          children: [
            _buildStatChip(
              context,
              icon: Icons.inventory,
              label: '${widget.system.totalMaterialCount}个物料',
              color: systemColor,
            ),
            
            SizedBox(width: VanHubSpacing.sm),
            
            if (widget.system.isOverBudget)
              _buildStatChip(
                context,
                icon: Icons.warning,
                label: '超支 ¥${widget.system.budgetDifference.toStringAsFixed(0)}',
                color: VanHubColors.warning,
              ),
          ],
        ),
      ],
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: VanHubSpacing.symmetric(
        horizontal: VanHubSpacing.sm,
        vertical: VanHubSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16, // 临时使用固定大小
            color: color,
          ),
          SizedBox(width: VanHubSpacing.xs),
          Text(
            label,
            style: VanHubTypography.bodySmall.copyWith(
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建物料列表
  Widget _buildMaterialList(BuildContext context) {
    if (widget.system.materials.isEmpty) {
      return _buildEmptyMaterialList(context);
    }

    return Container(
      color: VanHubColors.surface,
      child: Column(
        children: [
          // 物料列表头部
          Container(
            padding: VanHubSpacing.symmetric(
              horizontal: VanHubSpacing.md,
              vertical: VanHubSpacing.sm,
            ),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: VanHubColors.divider),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.inventory,
                  size: 16,
                  color: VanHubColors.textSecondary,
                ),
                SizedBox(width: VanHubSpacing.sm),
                Text(
                  '物料清单 (${widget.system.materials.length})',
                  style: VanHubTypography.titleSmall.copyWith(
                    color: VanHubColors.textSecondary,
                  ),
                ),
                
                const Spacer(),
                
                if (widget.showActions)
                  TextButton.icon(
                    onPressed: widget.onAddMaterial,
                    icon: const Icon(Icons.add),
                    label: const Text('添加物料'),
                  ),
              ],
            ),
          ),
          
          // 物料项列表
          ...widget.system.materials.map(
            (material) => _buildMaterialItem(context, material),
          ),
        ],
      ),
    );
  }

  /// 构建空物料列表
  Widget _buildEmptyMaterialList(BuildContext context) {
    return Container(
      padding: VanHubSpacing.all(VanHubSpacing.xl),
      child: Column(
        children: [
          Icon(
            Icons.inventory,
            size: 32,
            color: VanHubColors.textSecondary,
          ),
          
          SizedBox(height: VanHubSpacing.md),
          
          Text(
            '暂无物料',
            style: VanHubTypography.titleSmall.copyWith(
              color: VanHubColors.textSecondary,
            ),
          ),
          
          SizedBox(height: VanHubSpacing.sm),
          
          Text(
            '点击"添加物料"开始添加改装物料',
            style: VanHubTypography.bodySmall.copyWith(
              color: VanHubColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          
          if (widget.showActions) ...[
            SizedBox(height: VanHubSpacing.md),
            
            FilledButton.icon(
              onPressed: widget.onAddMaterial,
              icon: const Icon(Icons.add),
              label: const Text('添加物料'),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建物料项
  Widget _buildMaterialItem(BuildContext context, SystemMaterial material) {
    return InkWell(
      onTap: () => widget.onMaterialTap?.call(material),
      child: Container(
        padding: VanHubSpacing.all(VanHubSpacing.md),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: VanHubColors.divider),
          ),
        ),
        child: Row(
          children: [
            // 物料状态图标
            Container(
              padding: VanHubSpacing.all(VanHubSpacing.sm),
              decoration: BoxDecoration(
                color: _getMaterialStatusColor(material).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
              ),
              child: Icon(
                Icons.circle,
                size: 24,
                color: _getMaterialStatusColor(material),
              ),
            ),
            
            SizedBox(width: VanHubSpacing.md),
            
            // 物料信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 物料名称
                  Text(
                    material.name,
                    style: VanHubTypography.titleSmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: VanHubSpacing.xs),
                  
                  // 规格信息
                  if (material.specification.isNotEmpty)
                    Text(
                      material.specification,
                      style: VanHubTypography.bodySmall.copyWith(
                        color: VanHubColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  
                  SizedBox(height: VanHubSpacing.xs),
                  
                  // 数量和价格
                  Row(
                    children: [
                      Text(
                        '数量: ${material.quantity}',
                        style: VanHubTypography.bodySmall.copyWith(
                          color: VanHubColors.textSecondary,
                        ),
                      ),
                      
                      SizedBox(width: VanHubSpacing.md),
                      
                      Text(
                        '单价: ¥${material.unitPrice.toStringAsFixed(0)}',
                        style: VanHubTypography.bodySmall.copyWith(
                          color: VanHubColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 总价和状态
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '¥${material.totalPrice.toStringAsFixed(0)}',
                  style: VanHubTypography.titleMedium.copyWith(
                    color: VanHubColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                SizedBox(height: VanHubSpacing.xs),
                
                Container(
                  padding: VanHubSpacing.symmetric(
                    horizontal: VanHubSpacing.sm,
                    vertical: VanHubSpacing.xs,
                  ),
                  decoration: BoxDecoration(
                    color: _getMaterialStatusColor(material).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                  ),
                  child: Text(
                    material.overallStatusDescription,
                    style: VanHubTypography.bodySmall.copyWith(
                      color: _getMaterialStatusColor(material),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (widget.system.status) {
      case SystemStatus.planning:
        return VanHubColors.info;
      case SystemStatus.purchasing:
        return VanHubColors.warning;
      case SystemStatus.installing:
        return VanHubColors.primary;
      case SystemStatus.completed:
        return VanHubColors.success;
      case SystemStatus.paused:
        return VanHubColors.error;
    }
  }

  /// 获取物料状态颜色
  Color _getMaterialStatusColor(SystemMaterial material) {
    if (material.isInstalled) {
      return VanHubColors.success;
    } else if (material.isPurchased) {
      return VanHubColors.primary;
    } else if (material.isOrdered) {
      return VanHubColors.warning;
    } else {
      return VanHubColors.textSecondary;
    }
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        widget.onEdit?.call();
        break;
      case 'add_material':
        widget.onAddMaterial?.call();
        break;
      case 'delete':
        widget.onDelete?.call();
        break;
    }
  }
}

/// 获取系统类型对应的图标
IconData _getSystemIcon(String systemType) {
  switch (systemType) {
    case 'electrical':
      return Icons.electrical_services;
    case 'plumbing':
      return Icons.plumbing;
    case 'storage':
      return Icons.storage;
    case 'bed':
      return Icons.bed;
    case 'kitchen':
      return Icons.kitchen;
    case 'bathroom':
      return Icons.bathroom;
    case 'exterior':
      return Icons.directions_car;
    case 'chassis':
      return Icons.build;
    default:
      return Icons.settings;
  }
}