import 'package:freezed_annotation/freezed_annotation.dart';

part 'material_review.freezed.dart';
part 'material_review.g.dart';

/// 材料评价实体
/// 代表用户对材料的专业评价，帮助其他用户选择合适的物料
@freezed
class MaterialReview with _$MaterialReview {
  const factory MaterialReview({
    /// 评价唯一标识
    required String id,
    
    /// 关联的材料ID
    required String materialId,
    
    /// 评价用户ID
    required String userId,
    
    /// 用户名称
    required String userName,
    
    /// 用户头像URL
    String? userAvatarUrl,
    
    /// 评价内容
    required String content,
    
    /// 总体评分 (1-5星)
    required double rating,
    
    /// 专业评价维度
    /// 质量评分 (1-5星)
    required double qualityRating,
    
    /// 性价比评分 (1-5星)
    required double valueRating,
    
    /// 耐用性评分 (1-5星)
    required double durabilityRating,
    
    /// 安装难度评分 (1-5星，5表示很容易)
    required double installationRating,
    
    /// 使用场景信息
    /// 适用车型
    String? vehicleType,
    
    /// 改装系统类型
    String? systemType,
    
    /// 使用时长描述
    String? usageDuration,
    
    /// 优点列表
    @Default([]) List<String> pros,
    
    /// 缺点列表
    @Default([]) List<String> cons,
    
    /// 使用技巧和建议
    @Default([]) List<String> tips,
    
    /// 验证信息
    /// 是否验证购买
    @Default(false) bool isVerifiedPurchase,
    
    /// 购买日期
    DateTime? purchaseDate,
    
    /// 购买凭证URL
    String? purchaseProofUrl,
    
    /// 媒体附件
    /// 图片URL列表
    @Default([]) List<String> imageUrls,
    
    /// 视频URL列表
    @Default([]) List<String> videoUrls,
    
    /// 互动数据
    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,
    
    /// 标记为有用的用户ID列表
    @Default([]) List<String> helpfulUserIds,
    
    /// 有用评价数量
    @Default(0) int helpfulCount,
    
    /// 时间戳
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _MaterialReview;

  factory MaterialReview.fromJson(Map<String, dynamic> json) =>
      _$MaterialReviewFromJson(json);
}

/// 材料评价摘要实体
/// 用于显示材料的整体评价统计信息
@freezed
class MaterialReviewSummary with _$MaterialReviewSummary {
  const factory MaterialReviewSummary({
    /// 材料ID
    required String materialId,
    
    /// 总评价数量
    required int totalReviews,
    
    /// 平均评分
    required double averageRating,
    
    /// 各维度平均评分
    required double qualityAverage,
    required double valueAverage,
    required double durabilityAverage,
    required double installationAverage,
    
    /// 评分分布 (1-5星的数量分布)
    required Map<int, int> ratingDistribution,
    
    /// 验证购买的评价数量
    required int verifiedPurchaseCount,
    
    /// 最常提到的优点 (前5个)
    @Default([]) List<String> topPros,
    
    /// 最常提到的缺点 (前5个)
    @Default([]) List<String> topCons,
    
    /// 最有用的技巧 (前5个)
    @Default([]) List<String> commonTips,
    
    /// 最新评价时间
    DateTime? latestReviewDate,
    
    /// 推荐度 (基于评分和评价质量计算)
    required double recommendationScore,
  }) = _MaterialReviewSummary;

  factory MaterialReviewSummary.fromJson(Map<String, dynamic> json) =>
      _$MaterialReviewSummaryFromJson(json);
}

/// 评价过滤条件
@freezed
class ReviewFilterCriteria with _$ReviewFilterCriteria {
  const factory ReviewFilterCriteria({
    /// 最低评分过滤
    double? minRating,
    
    /// 最高评分过滤
    double? maxRating,
    
    /// 是否只显示验证购买
    @Default(false) bool verifiedPurchaseOnly,
    
    /// 车型过滤
    String? vehicleType,
    
    /// 系统类型过滤
    String? systemType,
    
    /// 排序方式
    @Default(ReviewSortType.newest) ReviewSortType sortBy,
    
    /// 是否包含图片
    @Default(false) bool withImagesOnly,
    
    /// 时间范围过滤
    DateTime? fromDate,
    DateTime? toDate,
  }) = _ReviewFilterCriteria;

  factory ReviewFilterCriteria.fromJson(Map<String, dynamic> json) =>
      _$ReviewFilterCriteriaFromJson(json);
}

/// 评价排序类型
enum ReviewSortType {
  /// 最新优先
  newest,
  
  /// 最旧优先
  oldest,
  
  /// 评分从高到低
  ratingHighToLow,
  
  /// 评分从低到高
  ratingLowToHigh,
  
  /// 最有用优先
  mostHelpful,
  
  /// 验证购买优先
  verifiedFirst,
}

/// 评价排序类型扩展
extension ReviewSortTypeExtension on ReviewSortType {
  String get displayName {
    switch (this) {
      case ReviewSortType.newest:
        return '最新优先';
      case ReviewSortType.oldest:
        return '最旧优先';
      case ReviewSortType.ratingHighToLow:
        return '评分从高到低';
      case ReviewSortType.ratingLowToHigh:
        return '评分从低到高';
      case ReviewSortType.mostHelpful:
        return '最有用优先';
      case ReviewSortType.verifiedFirst:
        return '验证购买优先';
    }
  }
}
