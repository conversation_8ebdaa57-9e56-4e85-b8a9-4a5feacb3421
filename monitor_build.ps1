# VanHub构建进度监控脚本
# 使用方法: 在新的PowerShell窗口中运行 .\monitor_build.ps1

Write-Host "🚀 VanHub构建进度监控器启动" -ForegroundColor Green
Write-Host "=" * 50

while ($true) {
    Clear-Host
    Write-Host "🚀 VanHub构建进度监控器" -ForegroundColor Green
    Write-Host "时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow
    Write-Host "=" * 50

    # 检查Flutter/Gradle进程
    Write-Host "`n📊 构建进程状态:" -ForegroundColor Cyan
    $processes = Get-Process | Where-Object {
        $_.ProcessName -like "*flutter*" -or 
        $_.ProcessName -like "*gradle*" -or 
        $_.ProcessName -like "*java*" -or
        $_.ProcessName -like "*dart*"
    }
    
    if ($processes) {
        $processes | Select-Object ProcessName, Id, CPU, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet/1MB,2)}} | Format-Table -AutoSize
    } else {
        Write-Host "❌ 未检测到构建进程" -ForegroundColor Red
    }

    # 检查网络活动（下载依赖时会有活动）
    Write-Host "`n🌐 网络连接状态:" -ForegroundColor Cyan
    $connections = netstat -an | Select-String ":443|:80" | Measure-Object
    Write-Host "活跃网络连接数: $($connections.Count)"

    # 检查Gradle缓存目录大小
    Write-Host "`n💾 Gradle缓存状态:" -ForegroundColor Cyan
    $gradleDir = "$env:USERPROFILE\.gradle"
    if (Test-Path $gradleDir) {
        $size = (Get-ChildItem -Path $gradleDir -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $sizeMB = [math]::Round($size / 1MB, 2)
        Write-Host "Gradle缓存大小: $sizeMB MB"
    }

    # 检查项目构建目录
    Write-Host "`n📁 项目构建状态:" -ForegroundColor Cyan
    $buildDir = "D:\AIAPP\VanHub\build"
    if (Test-Path $buildDir) {
        $buildSize = (Get-ChildItem -Path $buildDir -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $buildSizeMB = [math]::Round($buildSize / 1MB, 2)
        Write-Host "构建目录大小: $buildSizeMB MB"
        
        # 检查APK是否已生成
        $apkPath = "$buildDir\app\outputs\flutter-apk\app-debug.apk"
        if (Test-Path $apkPath) {
            $apkInfo = Get-Item $apkPath
            $apkSizeMB = [math]::Round($apkInfo.Length / 1MB, 2)
            Write-Host "✅ APK已生成! 大小: $apkSizeMB MB" -ForegroundColor Green
            Write-Host "📍 APK位置: $apkPath" -ForegroundColor Green
            break
        } else {
            Write-Host "⏳ APK构建中..." -ForegroundColor Yellow
        }
    }

    # 检查Android设备连接
    Write-Host "`n📱 Android设备状态:" -ForegroundColor Cyan
    try {
        $adbPath = "D:\AndroidSDK\platform-tools\adb.exe"
        if (Test-Path $adbPath) {
            $devices = & $adbPath devices 2>$null
            if ($devices -match "device$") {
                Write-Host "✅ Android设备已连接" -ForegroundColor Green
            } else {
                Write-Host "❌ 未检测到Android设备" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "❌ ADB不可用" -ForegroundColor Red
    }

    # 显示Web服务器状态
    Write-Host "`n🌐 Web服务器状态:" -ForegroundColor Cyan
    try {
        $webResponse = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($webResponse.StatusCode -eq 200) {
            Write-Host "✅ Web版本可用: http://*************:8080" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Web服务器未运行" -ForegroundColor Red
    }

    Write-Host "`n" + "=" * 50
    Write-Host "💡 提示: 可以同时使用Web版本测试 http://*************:8080" -ForegroundColor Yellow
    Write-Host "⏹️  按 Ctrl+C 停止监控" -ForegroundColor Gray
    
    Start-Sleep -Seconds 5
}

Write-Host "`n🎉 APK构建完成!" -ForegroundColor Green
Write-Host "📱 现在可以安装到Android设备进行测试" -ForegroundColor Green
