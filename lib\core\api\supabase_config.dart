import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

/// Supabase配置类
class SupabaseConfig {
  // 缓存配置值以避免重复读取
  static String? _cachedUrl;
  static String? _cachedAnonKey;
  static bool _isInitialized = false;

  // 从环境变量读取配置（带缓存）
  static String get supabaseUrl {
    _cachedUrl ??= dotenv.env['SUPABASE_URL'] ?? '';
    return _cachedUrl!;
  }

  static String get supabaseAnonKey {
    _cachedAnonKey ??= dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    return _cachedAnonKey!;
  }

  /// 快速初始化Supabase（优化版本）
  static Future<void> initialize() async {
    // 避免重复初始化
    if (_isInitialized) {
      if (kDebugMode) print('Supabase已经初始化，跳过重复初始化');
      return;
    }

    final stopwatch = Stopwatch()..start();

    try {
      // 并行加载环境变量和验证配置
      await dotenv.load(fileName: '.env.local');

      // 验证配置
      if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
        throw Exception('Supabase配置不完整：请检查.env.local文件中的SUPABASE_URL和SUPABASE_ANON_KEY');
      }

      if (kDebugMode) {
        print('正在初始化Supabase...');
        print('URL: $supabaseUrl');
        print('API Key: ${supabaseAnonKey.substring(0, 20)}...');
      }

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: kDebugMode, // 只在调试模式下启用调试
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce, // 使用更安全的PKCE流程
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info, // 减少日志输出
        ),
      );

      _isInitialized = true;
      stopwatch.stop();

      if (kDebugMode) {
        print('Supabase初始化完成 (耗时: ${stopwatch.elapsedMilliseconds}ms)');
      }
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        print('Supabase初始化失败 (耗时: ${stopwatch.elapsedMilliseconds}ms): $e');
      }
      rethrow;
    }
  }
  
  /// 获取Supabase客户端实例
  static SupabaseClient get client => Supabase.instance.client;
  
  /// 优化的连接检查（带缓存和超时）
  static DateTime? _lastConnectionCheck;
  static bool? _lastConnectionResult;
  static const Duration _connectionCacheTimeout = Duration(minutes: 5);

  static Future<bool> checkConnection({Duration timeout = const Duration(seconds: 10)}) async {
    // 使用缓存结果避免频繁检查
    if (_lastConnectionCheck != null &&
        _lastConnectionResult != null &&
        DateTime.now().difference(_lastConnectionCheck!) < _connectionCacheTimeout) {
      if (kDebugMode) print('使用缓存的连接状态: $_lastConnectionResult');
      return _lastConnectionResult!;
    }

    final stopwatch = Stopwatch()..start();

    try {
      // 检查初始化状态
      if (!_isInitialized) {
        if (kDebugMode) print('Supabase未初始化');
        return false;
      }

      // 检查配置是否有效
      if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
        if (kDebugMode) print('Supabase配置为空');
        return false;
      }

      // 使用超时的连接测试
      final result = await Future.any([
        _performConnectionTest(),
        Future.delayed(timeout, () => throw TimeoutException('连接超时', timeout)),
      ]);

      _lastConnectionCheck = DateTime.now();
      _lastConnectionResult = result;
      stopwatch.stop();

      if (kDebugMode) {
        print('Supabase连接检查完成: $result (耗时: ${stopwatch.elapsedMilliseconds}ms)');
      }

      return result;
    } catch (e) {
      stopwatch.stop();
      _lastConnectionCheck = DateTime.now();
      _lastConnectionResult = false;

      if (kDebugMode) {
        print('Supabase连接检查失败 (耗时: ${stopwatch.elapsedMilliseconds}ms): $e');
      }

      // 如果是认证相关错误，实际上连接是正常的
      if (e.toString().contains('AuthSessionMissingException') ||
          e.toString().contains('Auth session missing')) {
        if (kDebugMode) print('Supabase连接正常，但用户未登录');
        _lastConnectionResult = true;
        return true;
      }

      return false;
    }
  }

  /// 执行实际的连接测试
  static Future<bool> _performConnectionTest() async {
    try {
      // 尝试执行一个简单的查询来测试连接（不需要认证）
      await client.from('_health_check').select('*').limit(1);
      return true;
    } catch (e) {
      // 如果是表不存在的错误，说明连接是正常的
      if (e.toString().contains('relation') ||
          e.toString().contains('does not exist') ||
          e.toString().contains('table')) {
        if (kDebugMode) print('Supabase连接检查成功（通过表查询测试）');
        return true;
      }
      // 其他错误可能是网络问题
      throw e;
    }
  }
  
  /// 获取连接状态信息（优化版本）
  static Future<Map<String, dynamic>> getConnectionInfo() async {
    final stopwatch = Stopwatch()..start();

    try {
      // 并行获取连接状态和用户信息
      final futures = await Future.wait([
        checkConnection(),
        Future.value(client.auth.currentUser), // 同步操作包装为Future
      ]);

      final isConnected = futures[0] as bool;
      final user = futures[1] as User?;

      stopwatch.stop();

      final result = {
        'isConnected': isConnected,
        'url': supabaseUrl,
        'hasUser': user != null,
        'userId': user?.id,
        'userEmail': user?.email,
        'lastSignInAt': user?.lastSignInAt?.toString(),
        'checkDuration': stopwatch.elapsedMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (kDebugMode) {
        print('连接信息获取完成 (耗时: ${stopwatch.elapsedMilliseconds}ms)');
      }

      return result;
    } catch (e) {
      stopwatch.stop();

      if (kDebugMode) {
        print('连接信息获取失败 (耗时: ${stopwatch.elapsedMilliseconds}ms): $e');
      }

      return {
        'isConnected': false,
        'error': e.toString(),
        'checkDuration': stopwatch.elapsedMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 清除连接缓存（用于强制重新检查）
  static void clearConnectionCache() {
    _lastConnectionCheck = null;
    _lastConnectionResult = null;
    if (kDebugMode) print('连接缓存已清除');
  }

  /// 获取初始化状态
  static bool get isInitialized => _isInitialized;
}
