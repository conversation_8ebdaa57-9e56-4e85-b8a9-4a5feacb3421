import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../widgets/material_card_widget.dart';
import '../widgets/create_material_dialog_widget.dart';
import '../widgets/edit_material_dialog_widget.dart';
import '../widgets/enhanced_material_detail_dialog.dart';
import '../../../bom/presentation/widgets/add_material_to_bom_dialog_widget.dart';

/// VanHub材料库页面 - 房车改装材料管理
class MaterialLibraryPage extends ConsumerStatefulWidget {
  final String? userId;

  const MaterialLibraryPage({
    super.key,
    this.userId,
  });

  @override
  ConsumerState<MaterialLibraryPage> createState() => _MaterialLibraryPageState();
}

class _MaterialLibraryPageState extends ConsumerState<MaterialLibraryPage> {
  String _searchQuery = '';
  String _selectedCategory = '全部';

  // 房车改装专业分类 - 11个专业分类
  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装',
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '车顶改装',
    '底盘改装', '其他配件'
  ];

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final userId = widget.userId ?? currentUserId;

    // 允许游客模式访问材料库（userId为null时）
    // 游客模式下只能查看公开材料，不能进行增删改操作

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '材料库',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _buildListView(userId),
      floatingActionButton: userId != null ? FloatingActionButton.extended(
        onPressed: () => _showCreateMaterialDialog(context),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('添加材料'),
      ) : null, // 游客模式下不显示添加按钮
    );
  }

  /// 构建列表视图
  Widget _buildListView(String? userId) {
    return CustomScrollView(
      slivers: [
        // 搜索和筛选栏
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              children: [
                // 搜索框
                TextField(
                  decoration: InputDecoration(
                    hintText: '搜索材料名称、品牌、型号...',
                    prefixIcon: const Icon(Icons.search, color: Colors.teal),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.teal, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // 分类筛选
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = _selectedCategory == category;
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                          backgroundColor: Colors.grey.shade100,
                          selectedColor: Colors.teal.shade100,
                          checkmarkColor: Colors.teal,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.teal.shade700 : Colors.grey.shade700,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        // 材料列表
        _buildMaterialList(userId),
      ],
    );
  }



  /// 构建材料列表
  Widget _buildMaterialList(String? userId) {
    return Consumer(
      builder: (context, ref, child) {
        // 监听材料状态
        ref.listen<AsyncValue<List<domain.Material>>>(
          materialsNotifierProvider,
          (previous, next) {
            next.whenOrNull(
              error: (error, stackTrace) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('加载材料失败: $error'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
            );
          },
        );

        final materialsAsync = ref.watch(materialsNotifierProvider);

        return materialsAsync.when(
          loading: () => const SliverFillRemaining(
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stackTrace) => SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    '加载失败: $error',
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      ref.invalidate(materialsNotifierProvider);
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('重试'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          data: (materials) {
            // 过滤材料
            final filteredMaterials = _filterMaterials(materials);

        if (filteredMaterials.isEmpty) {
          return SliverFillRemaining(
            child: EmptyStateWidget(
              icon: Icons.inventory_2_outlined,
              title: _searchQuery.isNotEmpty ? '未找到匹配的材料' : '暂无材料',
              subtitle: _searchQuery.isNotEmpty
                  ? '尝试调整搜索条件或分类筛选'
                  : userId != null
                      ? '点击右下角按钮添加您的第一个材料'
                      : '当前没有公开的材料数据，请登录后查看您的材料库',
              actionText: _searchQuery.isNotEmpty
                  ? '清除搜索'
                  : userId != null
                      ? '添加材料'
                      : '立即登录',
              onAction: _searchQuery.isNotEmpty
                  ? () {
                      setState(() {
                        _searchQuery = '';
                        _selectedCategory = '全部';
                      });
                    }
                  : userId != null
                      ? () => _showCreateMaterialDialog(context)
                      : () => _showLoginPrompt(context),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final material = filteredMaterials[index];
                return MaterialCardWidget(
                  material: material,
                  onTap: () => _showMaterialDetails(context, material),
                  onEdit: () => _showEditMaterialDialog(context, material.id),
                  onDelete: () => _deleteMaterial(context, material.id),
                  onAddToBom: () => _showAddToBomDialog(context, material),
                );
              },
              childCount: filteredMaterials.length,
            ),
          ),
        );
          },
        );
      },
    );
  }

  /// 过滤材料
  List<domain.Material> _filterMaterials(List<domain.Material> materials) {
    return materials.where((material) {
      // 搜索过滤
      final matchesSearch = _searchQuery.isEmpty ||
          material.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (material.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
          (material.model?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

      // 分类过滤
      final matchesCategory = _selectedCategory == '全部' || material.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  /// 显示材料详情
  void _showMaterialDetails(BuildContext context, domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => EnhancedMaterialDetailDialog(
        material: material,
        projectId: null, // MaterialLibraryPage没有projectId
      ),
    );
  }

  /// 显示创建材料对话框
  void _showCreateMaterialDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateMaterialDialogWidget(),
    ).then((result) {
      // 如果创建成功，刷新材料列表
      if (result != null) {
        ref.invalidate(materialsNotifierProvider);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('材料创建成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _showLoginPrompt(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('请登录后查看和管理您的材料库'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 导航到登录页面
            },
            child: const Text('立即登录'),
          ),
        ],
      ),
    );
  }

  /// 显示编辑材料对话框
  void _showEditMaterialDialog(BuildContext context, String materialId) {
    // 首先获取材料数据
    final materialsAsync = ref.read(materialsNotifierProvider);
    materialsAsync.whenOrNull(
      data: (materials) {
        final material = materials.firstWhere(
          (m) => m.id == materialId,
          orElse: () => throw Exception('Material not found'),
        );

        showDialog(
          context: context,
          builder: (context) => EditMaterialDialogWidget(
            material: material,
          ),
        ).then((result) {
          // 如果编辑成功，刷新材料列表
          if (result != null) {
            ref.invalidate(materialsNotifierProvider);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('材料更新成功'),
                backgroundColor: Colors.green,
              ),
            );
          }
        });
      },
      error: (error, stackTrace) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('获取材料信息失败: $error'),
            backgroundColor: Colors.red,
          ),
        );
      },
    );
  }

  /// 删除材料
  void _deleteMaterial(BuildContext context, String materialId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个材料吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                // TODO: 实现删除材料的逻辑
                // await ref.read(materialsNotifierProvider.notifier).deleteMaterial(materialId);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('材料删除成功'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('删除失败: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 显示添加到BOM对话框
  void _showAddToBomDialog(BuildContext context, domain.Material material) {
    // TODO: 实现项目选择逻辑，这里暂时使用固定的项目ID
    const projectId = 'default-project-id';

    showDialog(
      context: context,
      builder: (context) => AddMaterialToBomDialogWidget(
        projectId: projectId,
        preselectedMaterial: material,
      ),
    ).then((result) {
      // 如果添加成功，显示成功消息
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('材料已添加到BOM'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

}