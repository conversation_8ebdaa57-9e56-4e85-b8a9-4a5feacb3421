import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/log_entry.dart';
import '../entities/log_search_criteria.dart';
import '../entities/enums.dart';

/// 日志仓库接口
abstract class LogRepository {
  /// 获取项目的所有日志条目
  Future<Either<Failure, List<LogEntry>>> getProjectLogs(String projectId);
  
  /// 获取特定系统的日志条目
  Future<Either<Failure, List<LogEntry>>> getSystemLogs(String systemId);
  
  /// 获取单个日志条目详情
  Future<Either<Failure, LogEntry>> getLogEntry(String logId);
  
  /// 创建新的日志条目
  Future<Either<Failure, LogEntry>> createLogEntry(LogEntry log);
  
  /// 更新日志条目
  Future<Either<Failure, LogEntry>> updateLogEntry(LogEntry log);
  
  /// 删除日志条目
  Future<Either<Failure, void>> deleteLogEntry(String logId);
  
  /// 搜索日志条目
  Future<Either<Failure, List<LogEntry>>> searchLogs(LogSearchCriteria criteria);
  
  /// 获取用户的所有日志条目
  Future<Either<Failure, List<LogEntry>>> getUserLogs(String userId);
  
  /// 获取最近的日志条目
  Future<Either<Failure, List<LogEntry>>> getRecentLogs({int limit = 10});
  
  /// 更新日志状态
  Future<Either<Failure, LogEntry>> updateLogStatus(String logId, LogStatus status);
  
  /// 获取日志条目数量
  Future<Either<Failure, int>> getLogCount(LogSearchCriteria criteria);
}