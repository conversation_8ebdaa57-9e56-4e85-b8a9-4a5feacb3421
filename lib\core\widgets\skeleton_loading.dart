import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// 骨架屏加载组件
class SkeletonLoading extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Color baseColor;
  final Color highlightColor;

  const SkeletonLoading({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return child;
    }

    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: child,
    );
  }

  /// 创建骨架容器
  static Widget container({
    required double width,
    required double height,
    double borderRadius = 8.0,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }

  /// 创建圆形骨架
  static Widget circle({
    required double size,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      width: size,
      height: size,
      margin: margin,
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
    );
  }

  /// 创建文本行骨架
  static Widget text({
    double width = double.infinity,
    double height = 16.0,
    double borderRadius = 4.0,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}

/// 项目卡片骨架
class ProjectCardSkeleton extends StatelessWidget {
  const ProjectCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和状态
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SkeletonLoading.text(width: 200, height: 20),
                SkeletonLoading.container(width: 80, height: 24, borderRadius: 12),
              ],
            ),
            const SizedBox(height: 12),
            
            // 描述
            SkeletonLoading.text(height: 14, margin: const EdgeInsets.only(bottom: 8)),
            SkeletonLoading.text(width: 300, height: 14),
            const SizedBox(height: 16),
            
            // 进度条
            SkeletonLoading.container(
              width: double.infinity,
              height: 8,
              borderRadius: 4,
            ),
            const SizedBox(height: 16),
            
            // 底部信息
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SkeletonLoading.container(width: 100, height: 16),
                SkeletonLoading.container(width: 80, height: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 材料卡片骨架
class MaterialCardSkeleton extends StatelessWidget {
  const MaterialCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 材料头部信息
            Row(
              children: [
                // 材料图片或图标
                SkeletonLoading.container(width: 60, height: 60, borderRadius: 8),
                const SizedBox(width: 12),
                
                // 材料基本信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonLoading.text(width: 150, height: 18),
                      const SizedBox(height: 8),
                      SkeletonLoading.container(width: 80, height: 20, borderRadius: 12),
                    ],
                  ),
                ),
                
                // 价格
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    SkeletonLoading.text(width: 60, height: 18),
                    const SizedBox(height: 4),
                    SkeletonLoading.text(width: 40, height: 12),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 材料描述
            SkeletonLoading.text(height: 14, margin: const EdgeInsets.only(bottom: 8)),
            SkeletonLoading.text(width: 300, height: 14),
            
            // 操作按钮
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SkeletonLoading.container(width: 80, height: 32, borderRadius: 16),
                const SizedBox(width: 8),
                SkeletonLoading.container(width: 80, height: 32, borderRadius: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// BOM项目骨架
class BOMItemSkeleton extends StatelessWidget {
  const BOMItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // 状态指示器
            SkeletonLoading.circle(size: 16),
            const SizedBox(width: 12),
            
            // 主要信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonLoading.text(width: 200, height: 16),
                  const SizedBox(height: 8),
                  SkeletonLoading.text(width: 150, height: 12),
                ],
              ),
            ),
            
            // 数量和价格
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SkeletonLoading.text(width: 60, height: 16),
                const SizedBox(height: 4),
                SkeletonLoading.text(width: 80, height: 14),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 项目列表骨架
class ProjectListSkeleton extends StatelessWidget {
  final int itemCount;

  const ProjectListSkeleton({
    super.key,
    this.itemCount = 3,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonLoading(
      isLoading: true,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: itemCount,
        itemBuilder: (context, index) => const ProjectCardSkeleton(),
      ),
    );
  }
}

/// 材料列表骨架
class MaterialListSkeleton extends StatelessWidget {
  final int itemCount;

  const MaterialListSkeleton({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonLoading(
      isLoading: true,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: itemCount,
        itemBuilder: (context, index) => const MaterialCardSkeleton(),
      ),
    );
  }
}

/// BOM列表骨架
class BOMListSkeleton extends StatelessWidget {
  final int itemCount;

  const BOMListSkeleton({
    super.key,
    this.itemCount = 6,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonLoading(
      isLoading: true,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: itemCount,
        itemBuilder: (context, index) => const BOMItemSkeleton(),
      ),
    );
  }
}
