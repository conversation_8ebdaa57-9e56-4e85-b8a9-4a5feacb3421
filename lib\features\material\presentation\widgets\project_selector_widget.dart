import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../../project/domain/entities/project.dart';
import '../../../project/presentation/providers/project_provider.dart';

class ProjectSelectorWidget extends ConsumerStatefulWidget {
  final Function(String) onProjectSelected;
  
  const ProjectSelectorWidget({
    Key? key,
    required this.onProjectSelected,
  }) : super(key: key);

  @override
  ConsumerState<ProjectSelectorWidget> createState() => _ProjectSelectorWidgetState();
}

class _ProjectSelectorWidgetState extends ConsumerState<ProjectSelectorWidget> {
  String? _selectedProjectId;
  
  @override
  Widget build(BuildContext context) {
    final userId = ref.watch(currentUserIdProvider);
    
    if (userId == null) {
      return const Center(
        child: Text('请先登录'),
      );
    }
    
    final userProjectsAsync = ref.watch(userProjectsProvider(userId));
    
    return userProjectsAsync.when(
      data: (projects) {
        if (projects.isEmpty) {
          return const Center(
            child: Text('没有找到项目，请先创建项目'),
          );
        }
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                '选择项目',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: projects.length,
                itemBuilder: (context, index) {
                  final project = projects[index];
                  return _buildProjectCard(project);
                },
              ),
            ),
          ],
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, _) => Center(
        child: Text('加载项目失败: ${error.toString()}'),
      ),
    );
  }

  Widget _buildProjectCard(Project project) {
    final isSelected = _selectedProjectId == project.id;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: isSelected ? Colors.blue.shade50 : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(
          color: isSelected ? Colors.blue : Colors.transparent,
          width: 2.0,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedProjectId = project.id;
          });
          widget.onProjectSelected(project.id);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      project.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: _getStatusColor(project.status),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Text(
                      project.status.displayName,
                      style: const TextStyle(color: Colors.white, fontSize: 12.0),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8.0),
              Text(project.description),
              const SizedBox(height: 8.0),
              Row(
                children: [
                  if (project.vehicleBrand != null || project.vehicleModel != null)
                    Expanded(
                      child: Text(
                        '车型: ${project.fullVehicleInfo}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ),
                  Text(
                    '创建于: ${_formatDate(project.createdAt)}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.planning:
        return Colors.blue;
      case ProjectStatus.inProgress:
        return Colors.green;
      case ProjectStatus.completed:
        return Colors.purple;
      case ProjectStatus.paused:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}