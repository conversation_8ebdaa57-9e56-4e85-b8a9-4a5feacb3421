import 'package:flutter/material.dart';

/// 通用空状态组件
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? iconColor;
  final double iconSize;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onAction,
    this.iconColor,
    this.iconSize = 64,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add),
                label: Text(actionText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 项目空状态
  static Widget projects({VoidCallback? onCreateProject}) {
    return EmptyStateWidget(
      icon: Icons.folder_open,
      title: '还没有项目',
      subtitle: '创建您的第一个改装项目，开始记录改装之旅',
      actionText: '创建项目',
      onAction: onCreateProject,
      iconColor: Colors.blue,
    );
  }

  /// 材料库空状态
  static Widget materials({VoidCallback? onAddMaterial}) {
    return EmptyStateWidget(
      icon: Icons.inventory,
      title: '材料库为空',
      subtitle: '添加您的第一个材料，建立专属材料库',
      actionText: '添加材料',
      onAction: onAddMaterial,
      iconColor: Colors.green,
    );
  }

  /// BOM空状态
  static Widget bom({VoidCallback? onAddBOMItem}) {
    return EmptyStateWidget(
      icon: Icons.list_alt,
      title: 'BOM清单为空',
      subtitle: '添加物料到BOM清单，开始规划您的项目',
      actionText: '添加物料',
      onAction: onAddBOMItem,
      iconColor: Colors.orange,
    );
  }

  /// 搜索结果空状态
  static Widget searchResults({String? query}) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: '没有找到结果',
      subtitle: query != null ? '没有找到与"$query"相关的内容' : '请尝试其他搜索关键词',
      iconColor: Colors.grey,
    );
  }

  /// 网络错误状态
  static Widget networkError({VoidCallback? onRetry}) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      title: '网络连接失败',
      subtitle: '请检查您的网络连接并重试',
      actionText: '重试',
      onAction: onRetry,
      iconColor: Colors.red,
    );
  }

  /// 加载失败状态
  static Widget loadError({
    required String message,
    VoidCallback? onRetry,
  }) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: '加载失败',
      subtitle: message,
      actionText: '重试',
      onAction: onRetry,
      iconColor: Colors.red,
    );
  }

  /// 权限不足状态
  static Widget noPermission({String? message}) {
    return EmptyStateWidget(
      icon: Icons.lock,
      title: '权限不足',
      subtitle: message ?? '您没有权限访问此内容',
      iconColor: Colors.orange,
    );
  }

  /// 功能开发中状态
  static Widget comingSoon({String? feature}) {
    return EmptyStateWidget(
      icon: Icons.construction,
      title: '功能开发中',
      subtitle: feature != null ? '$feature功能即将上线，敬请期待' : '此功能即将上线，敬请期待',
      iconColor: Colors.blue,
    );
  }
}
