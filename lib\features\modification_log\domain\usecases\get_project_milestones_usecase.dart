import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/milestone.dart';
import '../repositories/timeline_repository.dart';

/// 获取项目里程碑用例
class GetProjectMilestonesUseCase implements UseCase<List<Milestone>, GetProjectMilestonesParams> {
  final TimelineRepository repository;

  GetProjectMilestonesUseCase(this.repository);

  @override
  Future<Either<Failure, List<Milestone>>> call(GetProjectMilestonesParams params) async {
    return await repository.getProjectMilestones(params.projectId);
  }
}

/// 获取项目里程碑参数
class GetProjectMilestonesParams {
  final String projectId;

  GetProjectMilestonesParams({required this.projectId});
}