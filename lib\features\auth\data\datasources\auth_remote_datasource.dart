import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';
import '../../domain/entities/login_request.dart';
import '../../domain/entities/register_request.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> login(LoginRequest request);
  Future<UserModel> register(RegisterRequest request);
  Future<void> logout();
  Future<UserModel?> getCurrentUser();
  Future<UserModel> signInAnonymously();
  Future<UserModel> signInAsGuest();
  Future<void> resetPassword(String email);
  Stream<UserModel?> get authStateChanges;
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final supabase.SupabaseClient supabaseClient;

  // 本地认证状态管理
  final StreamController<UserModel?> _authStateController = StreamController<UserModel?>.broadcast();
  UserModel? _currentLocalUser;

  AuthRemoteDataSourceImpl({required this.supabaseClient}) {
    // 监听Supabase认证状态变化
    supabaseClient.auth.onAuthStateChange.listen((authState) {
      final user = authState.session?.user;
      if (user == null) {
        // 只有在没有本地用户时才设置为null
        if (_currentLocalUser == null || !_currentLocalUser!.isAnonymous) {
          _currentLocalUser = null;
          _authStateController.add(null);
        }
      } else {
        final userModel = _mapSupabaseUserToModel(user);
        _currentLocalUser = userModel;
        _authStateController.add(userModel);
      }
    });
  }

  @override
  Future<UserModel> login(LoginRequest request) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: request.email,
        password: request.password,
      );

      if (response.user == null) {
        throw const AuthException(message: '登录失败：用户信息为空');
      }

      return _mapSupabaseUserToModel(response.user!);
    } on AuthException catch (e) {
      throw AuthException(message: '登录失败: ${e.message}');
    } catch (e) {
      throw AuthException(message: '登录失败: $e');
    }
  }

  @override
  Future<UserModel> register(RegisterRequest request) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: request.email,
        password: request.password,
        data: request.name != null ? {'name': request.name} : null,
      );

      if (response.user == null) {
        throw const AuthException(message: '注册失败：用户信息为空');
      }

      return _mapSupabaseUserToModel(response.user!);
    } on AuthException catch (e) {
      throw AuthException(message: '注册失败: ${e.message}');
    } catch (e) {
      throw AuthException(message: '注册失败: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      // 清除本地用户状态
      _currentLocalUser = null;
      _authStateController.add(null);

      // 退出Supabase登录
      await supabaseClient.auth.signOut();
    } catch (e) {
      throw AuthException(message: '退出登录失败: $e');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      // 优先返回本地用户（游客用户）
      if (_currentLocalUser != null) {
        return _currentLocalUser;
      }

      // 否则返回Supabase用户
      final user = supabaseClient.auth.currentUser;
      if (user == null) return null;

      return _mapSupabaseUserToModel(user);
    } catch (e) {
      throw AuthException(message: '获取当前用户失败: $e');
    }
  }

  @override
  Future<UserModel> signInAnonymously() async {
    try {
      final response = await supabaseClient.auth.signInAnonymously();

      if (response.user == null) {
        throw const AuthException(message: '匿名登录失败：用户信息为空');
      }

      return _mapSupabaseUserToModel(response.user!, isAnonymous: true);
    } catch (e) {
      throw AuthException(message: '匿名登录失败: $e');
    }
  }

  @override
  Future<UserModel> signInAsGuest() async {
    try {
      debugPrint('🔍 AuthDataSource: Creating guest user...');

      // 创建本地游客用户，不依赖Supabase
      final now = DateTime.now();
      final guestUser = UserModel(
        id: 'guest_${now.millisecondsSinceEpoch}',
        email: '<EMAIL>',
        name: '游客用户',
        avatarUrl: null,
        createdAt: now.toIso8601String(),
        updatedAt: now.toIso8601String(),
        isAnonymous: true,
      );

      debugPrint('✅ AuthDataSource: Guest user created - ${guestUser.id}');

      // 设置本地用户状态并通知监听者
      _currentLocalUser = guestUser;
      debugPrint('🔄 AuthDataSource: Adding guest user to stream...');
      _authStateController.add(guestUser);
      debugPrint('✅ AuthDataSource: Guest user added to stream');

      return guestUser;
    } catch (e) {
      debugPrint('❌ AuthDataSource: Guest sign in failed - $e');
      throw AuthException(message: '游客模式登录失败: $e');
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw AuthException(message: '重置密码失败: $e');
    }
  }

  @override
  Stream<UserModel?> get authStateChanges {
    // 返回本地认证状态流，包含游客用户状态
    return _authStateController.stream;
  }

  UserModel _mapSupabaseUserToModel(supabase.User user, {bool isAnonymous = false}) {
    return UserModel(
      id: user.id,
      email: user.email ?? '',
      name: user.userMetadata?['name'] as String?,
      avatarUrl: user.userMetadata?['avatar_url'] as String?,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isAnonymous: isAnonymous || user.isAnonymous,
    );
  }
}