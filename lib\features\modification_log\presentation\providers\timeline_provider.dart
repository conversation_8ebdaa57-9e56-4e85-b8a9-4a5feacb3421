import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../../domain/entities/milestone.dart';
import '../../domain/entities/timeline.dart';
import '../../domain/usecases/add_milestone_usecase.dart';
import '../../domain/usecases/get_project_milestones_usecase.dart';
import '../../domain/usecases/get_project_timeline_usecase.dart';
import '../../../../core/di/injection_container.dart';

/// 时间轴状态
class TimelineState {
  final bool isLoading;
  final Timeline? timeline;
  final List<Milestone> milestones;
  final Milestone? selectedMilestone;
  final Failure? failure;

  const TimelineState({
    this.isLoading = false,
    this.timeline,
    this.milestones = const [],
    this.selectedMilestone,
    this.failure,
  });

  TimelineState copyWith({
    bool? isLoading,
    Timeline? timeline,
    List<Milestone>? milestones,
    Milestone? selectedMilestone,
    Failure? failure,
  }) {
    return TimelineState(
      isLoading: isLoading ?? this.isLoading,
      timeline: timeline ?? this.timeline,
      milestones: milestones ?? this.milestones,
      selectedMilestone: selectedMilestone ?? this.selectedMilestone,
      failure: failure,
    );
  }
}

/// 时间轴提供者
class TimelineNotifier extends StateNotifier<TimelineState> {
  final GetProjectTimelineUseCase _getProjectTimelineUseCase;
  final GetProjectMilestonesUseCase _getProjectMilestonesUseCase;
  final AddMilestoneUseCase _addMilestoneUseCase;

  TimelineNotifier({
    required GetProjectTimelineUseCase getProjectTimelineUseCase,
    required GetProjectMilestonesUseCase getProjectMilestonesUseCase,
    required AddMilestoneUseCase addMilestoneUseCase,
  })  : _getProjectTimelineUseCase = getProjectTimelineUseCase,
        _getProjectMilestonesUseCase = getProjectMilestonesUseCase,
        _addMilestoneUseCase = addMilestoneUseCase,
        super(const TimelineState());

  /// 获取项目时间轴
  Future<void> getProjectTimeline(String projectId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _getProjectTimelineUseCase(GetProjectTimelineParams(projectId: projectId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (timeline) => state = state.copyWith(isLoading: false, timeline: timeline),
    );
  }

  /// 获取项目里程碑
  Future<void> getProjectMilestones(String projectId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _getProjectMilestonesUseCase(GetProjectMilestonesParams(projectId: projectId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (milestones) => state = state.copyWith(isLoading: false, milestones: milestones),
    );
  }

  /// 添加里程碑
  Future<Either<Failure, Milestone>> addMilestone(Milestone milestone) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _addMilestoneUseCase(AddMilestoneParams(milestone: milestone));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (createdMilestone) {
        final updatedMilestones = [...state.milestones, createdMilestone];
        state = state.copyWith(
          isLoading: false,
          milestones: updatedMilestones,
          selectedMilestone: createdMilestone,
        );
      },
    );

    return result;
  }

  /// 选择里程碑
  void selectMilestone(Milestone milestone) {
    state = state.copyWith(selectedMilestone: milestone);
  }

  /// 清除选中的里程碑
  void clearSelectedMilestone() {
    state = state.copyWith(selectedMilestone: null);
  }

  /// 清除错误
  void clearFailure() {
    state = state.copyWith(failure: null);
  }
}

/// 时间轴提供者
final timelineProvider = StateNotifierProvider<TimelineNotifier, TimelineState>((ref) {
  return TimelineNotifier(
    getProjectTimelineUseCase: ref.read(getProjectTimelineUseCaseProvider),
    getProjectMilestonesUseCase: ref.read(getProjectMilestonesUseCaseProvider),
    addMilestoneUseCase: ref.read(addMilestoneUseCaseProvider),
  );
});