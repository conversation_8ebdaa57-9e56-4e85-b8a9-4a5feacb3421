import '../error/ui_failure.dart';

/// 应用错误工厂类
/// 提供便捷的错误创建方法，兼容现有的UIFailure系统
class AppError {
  // 私有构造函数，防止实例化
  AppError._();

  /// 数据库相关错误
  static UIFailure database(String message, {String? details}) {
    return UIFailure.storage(
      operation: 'database',
      message: message,
      details: details,
      code: 'database_error',
    );
  }

  /// 网络相关错误
  static UIFailure network(String message, {String? details}) {
    return UIFailure.network(
      message: message,
      details: details,
      code: 'network_error',
    );
  }

  /// 认证相关错误
  static UIFailure authentication(String message, {String? details}) {
    return UIFailure.authentication(
      message: message,
      details: details,
      code: 'auth_error',
    );
  }

  /// 权限相关错误
  static UIFailure permission(String action, String message, {String? details}) {
    return UIFailure.permission(
      action: action,
      message: message,
      details: details,
      code: 'permission_error',
    );
  }

  /// 验证相关错误
  static UIFailure validation(String field, String message, {String? details}) {
    return UIFailure.validation(
      field: field,
      message: message,
      details: details,
      code: 'validation_error',
    );
  }

  /// 业务逻辑错误
  static UIFailure business(String message, {String? details}) {
    return UIFailure.business(
      message: message,
      details: details,
      code: 'business_error',
    );
  }

  /// 服务器错误
  static UIFailure server(String message, {String? details, int? statusCode}) {
    return UIFailure.server(
      message: message,
      details: details,
      statusCode: statusCode,
      code: 'server_error',
    );
  }

  /// 超时错误
  static UIFailure timeout(String message, {String? details, int? timeoutSeconds}) {
    return UIFailure.timeout(
      message: message,
      details: details,
      timeoutSeconds: timeoutSeconds,
      code: 'timeout_error',
    );
  }

  /// 格式错误
  static UIFailure format(String message, {String? details, String? expectedFormat}) {
    return UIFailure.format(
      message: message,
      details: details,
      expectedFormat: expectedFormat,
      code: 'format_error',
    );
  }

  /// 未知错误
  static UIFailure unknown(String message, {String? details}) {
    return UIFailure.unknown(
      message: message,
      details: details,
      code: 'unknown_error',
    );
  }
}
