import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material.dart';
import '../entities/create_material_request.dart';
import '../repositories/material_repository.dart';

class CreateMaterialUseCase {
  final MaterialRepository repository;

  const CreateMaterialUseCase(this.repository);

  Future<Either<Failure, Material>> call(CreateMaterialRequest request) async {
    // 业务逻辑验证
    if (request.name.trim().isEmpty) {
      return const Left(ValidationFailure(message: '材料名称不能为空'));
    }
    
    if (request.name.trim().length < 2) {
      return const Left(ValidationFailure(message: '材料名称至少需要2个字符'));
    }
    
    if (request.description != null && request.description!.trim().isEmpty) {
      return const Left(ValidationFailure(message: '材料描述不能为空'));
    }
    
    // 价格验证
    if (request.price < 0) {
      return const Left(ValidationFailure(message: '价格不能为负数'));
    }

    return await repository.createMaterial(request);
  }
}