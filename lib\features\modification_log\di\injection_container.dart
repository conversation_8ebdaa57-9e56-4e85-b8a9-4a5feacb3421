import 'package:get_it/get_it.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../data/datasources/log_remote_datasource.dart';
import '../data/datasources/media_remote_datasource.dart';
import '../data/datasources/timeline_remote_datasource.dart';
import '../data/repositories/log_repository_impl.dart';
import '../data/repositories/media_repository_impl.dart';
import '../data/repositories/timeline_repository_impl.dart';
import '../domain/repositories/log_repository.dart';
import '../domain/repositories/media_repository.dart';
import '../domain/repositories/timeline_repository.dart';
import '../domain/usecases/add_milestone_usecase.dart';
import '../domain/usecases/create_log_entry_usecase.dart';
import '../domain/usecases/delete_log_entry_usecase.dart';
import '../domain/usecases/delete_media_usecase.dart';
import '../domain/usecases/get_log_entry_usecase.dart';
import '../domain/usecases/get_log_media_usecase.dart';
import '../domain/usecases/get_project_logs_usecase.dart';
import '../domain/usecases/get_project_milestones_usecase.dart';
import '../domain/usecases/get_project_timeline_usecase.dart';
import '../domain/usecases/get_system_logs_usecase.dart';
import '../domain/usecases/search_logs_usecase.dart';
import '../domain/usecases/update_log_entry_usecase.dart';
import '../domain/usecases/upload_media_usecase.dart';

final sl = GetIt.instance;

/// 注册改装日志系统的依赖
Future<void> initModificationLogDependencies() async {
  // 数据源
  sl.registerLazySingleton<LogRemoteDataSource>(
    () => LogRemoteDataSourceImpl(supabaseClient: sl<SupabaseClient>()),
  );

  sl.registerLazySingleton<MediaRemoteDataSource>(
    () => MediaRemoteDataSourceImpl(supabaseClient: sl<SupabaseClient>()),
  );

  sl.registerLazySingleton<TimelineRemoteDataSource>(
    () => TimelineRemoteDataSourceImpl(supabaseClient: sl<SupabaseClient>()),
  );
  
  // 仓库
  sl.registerLazySingleton<LogRepository>(
    () => LogRepositoryImpl(remoteDataSource: sl<LogRemoteDataSource>()),
  );
  
  sl.registerLazySingleton<MediaRepository>(
    () => MediaRepositoryImpl(remoteDataSource: sl<MediaRemoteDataSource>()),
  );
  
  sl.registerLazySingleton<TimelineRepository>(
    () => TimelineRepositoryImpl(remoteDataSource: sl<TimelineRemoteDataSource>()),
  );
  
  // 用例
  sl.registerLazySingleton(() => CreateLogEntryUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => UpdateLogEntryUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => DeleteLogEntryUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => GetLogEntryUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => GetProjectLogsUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => GetSystemLogsUseCase(sl<LogRepository>()));
  sl.registerLazySingleton(() => SearchLogsUseCase(sl<LogRepository>()));
  
  sl.registerLazySingleton(() => UploadMediaUseCase(sl<MediaRepository>()));
  sl.registerLazySingleton(() => GetLogMediaUseCase(sl<MediaRepository>()));
  sl.registerLazySingleton(() => DeleteMediaUseCase(sl<MediaRepository>()));
  
  sl.registerLazySingleton(() => GetProjectTimelineUseCase(sl<TimelineRepository>()));
  sl.registerLazySingleton(() => AddMilestoneUseCase(sl<TimelineRepository>()));
  sl.registerLazySingleton(() => GetProjectMilestonesUseCase(sl<TimelineRepository>()));
}