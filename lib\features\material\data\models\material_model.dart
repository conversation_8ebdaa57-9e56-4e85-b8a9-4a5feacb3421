import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/material.dart' as domain;

part 'material_model.freezed.dart';

@freezed
class MaterialModel with _$MaterialModel {
  const factory MaterialModel({
    required String id,
    required String userId,
    required String name,
    required String description,
    required String category,
    required String createdAt,
    required String updatedAt,
    String? brand,
    String? model,
    String? specifications,
    double? price,
    double? minPrice,
    double? maxPrice,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    List<String>? tags,
    @Default(0) int usageCount,
    String? lastUsedAt,
    String? purchaseDate,
    Map<String, dynamic>? metadata,
    // 新增数据库中存在的字段
    double? weight,                    // 重量字段
    @Default(0.0) double rating,       // 评分字段
    @Default(0) int reviewCount,       // 评价数量字段
  }) = _MaterialModel;

  factory MaterialModel.fromJson(Map<String, dynamic> json) {
    return MaterialModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['item_name'] as String,  // 修复字段映射
      description: json['description'] as String? ?? '',
      category: json['category'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? DateTime.now().toIso8601String(),
      updatedAt: json['updated_at'] as String? ?? DateTime.now().toIso8601String(),
      brand: json['brand'] as String?,
      model: json['model'] as String?,
      specifications: json['specification'] as String?,  // 修复字段映射
      price: (json['reference_price'] as num?)?.toDouble(),  // 修复字段映射
      supplier: null,  // 数据库中没有这个字段
      supplierUrl: json['purchase_link'] as String?,  // 修复字段映射
      imageUrl: null,  // 数据库中没有这个字段
      tags: null,  // 数据库中没有这个字段
      usageCount: json['usage_count'] as int? ?? 0,
      lastUsedAt: null,  // 数据库中没有这个字段
      purchaseDate: null,  // 数据库中没有这个字段
      metadata: json['attributes'] as Map<String, dynamic>?,  // 修复字段映射
      // 新增字段映射
      weight: (json['weight'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] as int? ?? 0,
    );
  }
}

extension MaterialModelX on MaterialModel {
  domain.Material toEntity() {
    return domain.Material(
      id: id,
      userId: userId,
      name: name,
      description: description,
      category: category,
      createdAt: DateTime.parse(createdAt),
      updatedAt: DateTime.parse(updatedAt),
      brand: brand,
      model: model,
      specifications: specifications,
      price: price ?? 0.0,
      minPrice: minPrice,
      maxPrice: maxPrice,
      supplier: supplier,
      supplierUrl: supplierUrl,
      imageUrl: imageUrl,
      tags: tags,
      usageCount: usageCount,
      lastUsedAt: lastUsedAt != null ? DateTime.tryParse(lastUsedAt!) : null,
      purchaseDate: purchaseDate != null ? DateTime.tryParse(purchaseDate!) : null,
      metadata: metadata,
    );
  }

  String _parseCategory(String category) {
    return category; // 直接返回字符串，不需要转换
  }
}

extension MaterialX on domain.Material {
  MaterialModel toModel() {
    return MaterialModel(
      id: id,
      userId: userId,
      name: name,
      description: description,
      category: category,
      createdAt: createdAt.toIso8601String(),
      updatedAt: updatedAt.toIso8601String(),
      brand: brand,
      model: model,
      specifications: specifications,
      price: price,
      minPrice: minPrice,
      maxPrice: maxPrice,
      supplier: supplier,
      supplierUrl: supplierUrl,
      imageUrl: imageUrl,
      tags: tags,
      usageCount: usageCount,
      lastUsedAt: lastUsedAt?.toIso8601String(),
      purchaseDate: purchaseDate?.toIso8601String(),
      metadata: metadata,
    );
  }
}