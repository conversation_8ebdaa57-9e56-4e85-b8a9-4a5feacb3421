import 'package:flutter/material.dart';
import '../../domain/entities/tree_node.dart';
import 'enhanced_bom_item_card_widget.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 增强版BOM树节点组件
/// 现代化设计，流畅的动画效果和优秀的用户体验
class EnhancedBomTreeNodeWidget extends StatefulWidget {
  final TreeNode node;
  final bool isSelected;
  final bool isHighlighted;
  final bool isDragEnabled;
  final bool isDragging;
  final bool isDropTarget;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final void Function(bool isExpanded)? onExpansionChanged;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onDragStart;
  final VoidCallback? onDragEnd;
  final VoidCallback? onDragEnter;
  final VoidCallback? onDragLeave;

  const EnhancedBomTreeNodeWidget({
    super.key,
    required this.node,
    this.isSelected = false,
    this.isHighlighted = false,
    this.isDragEnabled = false,
    this.isDragging = false,
    this.isDropTarget = false,
    this.onTap,
    this.onDoubleTap,
    this.onExpansionChanged,
    this.onEdit,
    this.onDelete,
    this.onDragStart,
    this.onDragEnd,
    this.onDragEnter,
    this.onDragLeave,
  });

  @override
  State<EnhancedBomTreeNodeWidget> createState() => _EnhancedBomTreeNodeWidgetState();
}

class _EnhancedBomTreeNodeWidgetState extends State<EnhancedBomTreeNodeWidget>
    with TickerProviderStateMixin {
  late AnimationController _expansionController;
  late AnimationController _hoverController;
  late AnimationController _selectionController;
  late Animation<double> _expansionAnimation;
  late Animation<double> _hoverAnimation;
  late Animation<double> _selectionAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _expansionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    
    _expansionAnimation = CurvedAnimation(
      parent: _expansionController,
      curve: Curves.easeInOutCubic,
    );
    _hoverAnimation = CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    );
    _selectionAnimation = CurvedAnimation(
      parent: _selectionController,
      curve: Curves.easeOutCubic,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.25,
    ).animate(_expansionAnimation);
    
    // 初始化展开状态
    if (widget.node.isExpanded) {
      _expansionController.value = 1.0;
    }
    
    // 初始化选中状态
    if (widget.isSelected) {
      _selectionController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(EnhancedBomTreeNodeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 处理展开状态变化
    if (widget.node.isExpanded != oldWidget.node.isExpanded) {
      if (widget.node.isExpanded) {
        _expansionController.forward();
      } else {
        _expansionController.reverse();
      }
    }
    
    // 处理选中状态变化
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _selectionController.forward();
      } else {
        _selectionController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _expansionController.dispose();
    _hoverController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _expansionAnimation,
        _hoverAnimation,
        _selectionAnimation,
      ]),
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 节点本身
            _buildNodeItem(),
            
            // 子节点（带动画）
            if (widget.node.children.isNotEmpty)
              SizeTransition(
                sizeFactor: _expansionAnimation,
                child: _buildChildren(),
              ),
          ],
        );
      },
    );
  }

  Widget _buildNodeItem() {
    if (widget.node.type == BomTreeNodeType.bomItem && widget.node.bomItem != null) {
      // 使用增强版BOM项目卡片
      return Container(
        margin: EdgeInsets.only(left: widget.node.depth * 20.0),
        child: EnhancedBomItemCardWidget(
          item: widget.node.bomItem!,
          isEditable: true,
          showCost: true,
          onEdit: widget.node.bomItem != null ? (_) => widget.onEdit?.call() : null,
          onDelete: widget.node.bomItem != null ? (_) => widget.onDelete?.call() : null,
        ),
      );
    } else {
      // 分类节点
      return _buildCategoryNode();
    }
  }

  Widget _buildCategoryNode() {
    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: GestureDetector(
        onTap: () {
          widget.onTap?.call();
          _toggleExpansion();
        },
        onDoubleTap: widget.onDoubleTap,
        child: Container(
          margin: EdgeInsets.only(
            left: widget.node.depth * 20.0,
            bottom: 8,
          ),
          decoration: BoxDecoration(
            color: _getBackgroundColor(),
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
            border: Border.all(
              color: widget.isHighlighted
                  ? VanHubColors.primary
                  : widget.isSelected
                      ? VanHubColors.primary.withOpacity(0.5)
                      : Colors.transparent,
              width: widget.isHighlighted ? 2 : 1,
            ),
            boxShadow: [
              if (_isHovered || widget.isSelected)
                BoxShadow(
                  color: VanHubColors.primary.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: VanHubSpacing.md, vertical: VanHubSpacing.sm),
            child: Row(
              children: [
                // 展开/折叠图标（带动画）
                if (widget.node.children.isNotEmpty)
                  Transform.rotate(
                    angle: _rotationAnimation.value * 2 * 3.14159,
                    child: Icon(
                      Icons.chevron_right,
                      color: VanHubColors.primary,
                      size: 20,
                    ),
                  )
                else
                  const SizedBox(width: 20),
                
                SizedBox(width: VanHubSpacing.xs),
                
                // 分类图标
                Container(
                  padding: EdgeInsets.all(VanHubSpacing.sm),
                  decoration: BoxDecoration(
                    color: _getCategoryColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                  ),
                  child: Icon(
                    _getCategoryIcon(),
                    color: _getCategoryColor(),
                    size: 20,
                  ),
                ),
                
                SizedBox(width: VanHubSpacing.sm),
                
                // 分类名称
                Expanded(
                  child: Text(
                    widget.node.label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: widget.isSelected 
                          ? VanHubColors.primary
                          : VanHubColors.textPrimary,
                    ),
                  ),
                ),
                
                // 统计信息
                _buildCategoryStats(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryStats() {
    final itemCount = widget.node.bomItemCount;
    final totalCost = widget.node.calculatedTotalValue;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: VanHubSpacing.sm, vertical: VanHubSpacing.xs),
          decoration: BoxDecoration(
            color: VanHubColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
          ),
          child: Text(
            '$itemCount项',
            style: TextStyle(
              fontSize: 12,
              color: VanHubColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (totalCost > 0) ...[
          SizedBox(height: VanHubSpacing.xs),
          Text(
            '¥${totalCost.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 12,
              color: VanHubColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildChildren() {
    return Container(
      margin: EdgeInsets.only(left: VanHubSpacing.sm),
      child: Column(
        children: widget.node.children.map((child) {
          return EnhancedBomTreeNodeWidget(
            node: child,
            isSelected: widget.isSelected,
            isHighlighted: widget.isHighlighted,
            isDragEnabled: widget.isDragEnabled,
            isDragging: widget.isDragging,
            isDropTarget: widget.isDropTarget,
            onTap: widget.onTap,
            onDoubleTap: widget.onDoubleTap,
            onExpansionChanged: widget.onExpansionChanged,
            onEdit: widget.onEdit,
            onDelete: widget.onDelete,
            onDragStart: widget.onDragStart,
            onDragEnd: widget.onDragEnd,
            onDragEnter: widget.onDragEnter,
            onDragLeave: widget.onDragLeave,
          );
        }).toList(),
      ),
    );
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  void _toggleExpansion() {
    widget.onExpansionChanged?.call(!widget.node.isExpanded);
  }

  Color _getBackgroundColor() {
    if (widget.isDropTarget) {
      return VanHubColors.primary.withOpacity(0.1);
    }
    if (widget.isSelected) {
      return VanHubColors.primary.withOpacity(0.05);
    }
    if (_isHovered) {
      return Colors.grey.withOpacity(0.05);
    }
    return Colors.transparent;
  }

  Color _getCategoryColor() {
    switch (widget.node.label.toLowerCase()) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.purple;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.indigo;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '安全设备':
        return Colors.red;
      case '通讯设备':
        return Colors.teal;
      case '娱乐设备':
        return Colors.pink;
      default:
        return VanHubColors.primary;
    }
  }

  IconData _getCategoryIcon() {
    switch (widget.node.label.toLowerCase()) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.directions_car;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathroom;
      case '安全设备':
        return Icons.security;
      case '通讯设备':
        return Icons.wifi;
      case '娱乐设备':
        return Icons.tv;
      default:
        return Icons.folder;
    }
  }
}