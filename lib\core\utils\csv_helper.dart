// 暂时禁用CSV助手，等待实体迁移完成
/*
import 'dart:convert';
import 'package:csv/csv.dart';
import '../../features/bom/domain/entities/bom_item.dart';
import '../../features/material/domain/entities/material.dart';

/// CSV数据处理工具类
class CSVHelper {
  /// 将BOM数据导出为CSV格式
  static String exportBOMToCSV(List<BOMItem> bomItems) {
    final List<List<String>> csvData = [];
    
    // 添加表头
    csvData.add([
      'ID',
      '名称',
      '描述',
      '分类',
      '数量',
      '单位',
      '单价',
      '总价',
      '状态',
      '品牌',
      '型号',
      '规格',
      '供应商',
      '供应商链接',
      '计划日期',
      '采购日期',
      '使用日期',
      '备注',
    ]);
    
    // 添加数据行
    for (final item in bomItems) {
      csvData.add([
        item.id,
        item.name,
        item.description,
        item.category.displayName,
        item.quantity.toString(),
        item.unit,
        item.unitPrice.toString(),
        item.totalPrice.toString(),
        item.status.displayName,
        item.brand ?? '',
        item.model ?? '',
        item.specifications ?? '',
        item.supplier ?? '',
        item.supplierUrl ?? '',
        item.plannedDate?.toIso8601String() ?? '',
        item.purchasedDate?.toIso8601String() ?? '',
        item.usedDate?.toIso8601String() ?? '',
        item.notes ?? '',
      ]);
    }
    
    return const ListToCsvConverter().convert(csvData);
  }

  /// 将材料数据导出为CSV格式
  static String exportMaterialsToCSV(List<Material> materials) {
    final List<List<String>> csvData = [];
    
    // 添加表头
    csvData.add([
      'ID',
      '名称',
      '描述',
      '分类',
      '品牌',
      '型号',
      '规格',
      '价格',
      '供应商',
      '供应商链接',
      '标签',
      '使用次数',
      '最后使用时间',
      '备注',
    ]);
    
    // 添加数据行
    for (final material in materials) {
      csvData.add([
        material.id,
        material.name,
        material.description,
        material.category.displayName,
        material.brand ?? '',
        material.model ?? '',
        material.specifications ?? '',
        material.price.toString(),
        material.supplier ?? '',
        material.supplierUrl ?? '',
        material.tags.join(';'),
        material.usageCount.toString(),
        material.lastUsedAt?.toIso8601String() ?? '',
        material.notes ?? '',
      ]);
    }
    
    return const ListToCsvConverter().convert(csvData);
  }

  /// 从CSV数据解析BOM项目
  static List<CreateBOMItemRequest> parseBOMFromCSV(
    String csvData,
    String projectId,
  ) {
    final List<CreateBOMItemRequest> bomItems = [];
    
    try {
      final List<List<dynamic>> csvTable = const CsvToListConverter().convert(csvData);
      
      // 跳过表头
      if (csvTable.isEmpty) return bomItems;
      
      final headers = csvTable.first.map((e) => e.toString().toLowerCase()).toList();
      
      // 查找列索引
      final nameIndex = _findColumnIndex(headers, ['名称', 'name']);
      final descriptionIndex = _findColumnIndex(headers, ['描述', 'description']);
      final categoryIndex = _findColumnIndex(headers, ['分类', 'category']);
      final quantityIndex = _findColumnIndex(headers, ['数量', 'quantity']);
      final unitIndex = _findColumnIndex(headers, ['单位', 'unit']);
      final unitPriceIndex = _findColumnIndex(headers, ['单价', 'unit_price', 'price']);
      final brandIndex = _findColumnIndex(headers, ['品牌', 'brand']);
      final modelIndex = _findColumnIndex(headers, ['型号', 'model']);
      final specificationsIndex = _findColumnIndex(headers, ['规格', 'specifications']);
      final supplierIndex = _findColumnIndex(headers, ['供应商', 'supplier']);
      final supplierUrlIndex = _findColumnIndex(headers, ['供应商链接', 'supplier_url']);
      final notesIndex = _findColumnIndex(headers, ['备注', 'notes']);
      
      // 处理数据行
      for (int i = 1; i < csvTable.length; i++) {
        final row = csvTable[i];
        
        if (nameIndex == -1 || descriptionIndex == -1 || 
            categoryIndex == -1 || quantityIndex == -1 || unitIndex == -1) {
          continue; // 跳过缺少必要字段的行
        }
        
        try {
          final name = _getStringValue(row, nameIndex);
          final description = _getStringValue(row, descriptionIndex);
          final categoryStr = _getStringValue(row, categoryIndex);
          final quantity = _getIntValue(row, quantityIndex);
          final unit = _getStringValue(row, unitIndex);
          
          if (name.isEmpty || description.isEmpty || quantity <= 0 || unit.isEmpty) {
            continue; // 跳过无效数据
          }
          
          final category = _parseMaterialCategory(categoryStr);
          final unitPrice = _getDoubleValue(row, unitPriceIndex);
          
          bomItems.add(CreateBOMItemRequest(
            projectId: projectId,
            name: name,
            description: description,
            category: category,
            quantity: quantity,
            unit: unit,
            unitPrice: unitPrice,
            brand: brandIndex != -1 ? _getStringValue(row, brandIndex) : null,
            model: modelIndex != -1 ? _getStringValue(row, modelIndex) : null,
            specifications: specificationsIndex != -1 ? _getStringValue(row, specificationsIndex) : null,
            supplier: supplierIndex != -1 ? _getStringValue(row, supplierIndex) : null,
            supplierUrl: supplierUrlIndex != -1 ? _getStringValue(row, supplierUrlIndex) : null,
            notes: notesIndex != -1 ? _getStringValue(row, notesIndex) : null,
          ));
        } catch (e) {
          // 跳过解析失败的行
          continue;
        }
      }
    } catch (e) {
      throw Exception('CSV格式错误: ${e.toString()}');
    }
    
    return bomItems;
  }

  /// 从CSV数据解析材料
  static List<CreateMaterialRequest> parseMaterialsFromCSV(String csvData) {
    final List<CreateMaterialRequest> materials = [];
    
    try {
      final List<List<dynamic>> csvTable = const CsvToListConverter().convert(csvData);
      
      // 跳过表头
      if (csvTable.isEmpty) return materials;
      
      final headers = csvTable.first.map((e) => e.toString().toLowerCase()).toList();
      
      // 查找列索引
      final nameIndex = _findColumnIndex(headers, ['名称', 'name']);
      final descriptionIndex = _findColumnIndex(headers, ['描述', 'description']);
      final categoryIndex = _findColumnIndex(headers, ['分类', 'category']);
      final brandIndex = _findColumnIndex(headers, ['品牌', 'brand']);
      final modelIndex = _findColumnIndex(headers, ['型号', 'model']);
      final specificationsIndex = _findColumnIndex(headers, ['规格', 'specifications']);
      final priceIndex = _findColumnIndex(headers, ['价格', 'price']);
      final supplierIndex = _findColumnIndex(headers, ['供应商', 'supplier']);
      final supplierUrlIndex = _findColumnIndex(headers, ['供应商链接', 'supplier_url']);
      final tagsIndex = _findColumnIndex(headers, ['标签', 'tags']);
      final notesIndex = _findColumnIndex(headers, ['备注', 'notes']);
      
      // 处理数据行
      for (int i = 1; i < csvTable.length; i++) {
        final row = csvTable[i];
        
        if (nameIndex == -1 || descriptionIndex == -1 || categoryIndex == -1) {
          continue; // 跳过缺少必要字段的行
        }
        
        try {
          final name = _getStringValue(row, nameIndex);
          final description = _getStringValue(row, descriptionIndex);
          final categoryStr = _getStringValue(row, categoryIndex);
          
          if (name.isEmpty || description.isEmpty) {
            continue; // 跳过无效数据
          }
          
          final category = _parseMaterialCategory(categoryStr);
          final price = _getDoubleValue(row, priceIndex);
          final tagsStr = tagsIndex != -1 ? _getStringValue(row, tagsIndex) : '';
          final tags = tagsStr.isNotEmpty ? tagsStr.split(';') : <String>[];
          
          materials.add(CreateMaterialRequest(
            name: name,
            description: description,
            category: category,
            brand: brandIndex != -1 ? _getStringValue(row, brandIndex) : null,
            model: modelIndex != -1 ? _getStringValue(row, modelIndex) : null,
            specifications: specificationsIndex != -1 ? _getStringValue(row, specificationsIndex) : null,
            price: price,
            supplier: supplierIndex != -1 ? _getStringValue(row, supplierIndex) : null,
            supplierUrl: supplierUrlIndex != -1 ? _getStringValue(row, supplierUrlIndex) : null,
            tags: tags,
            notes: notesIndex != -1 ? _getStringValue(row, notesIndex) : null,
          ));
        } catch (e) {
          // 跳过解析失败的行
          continue;
        }
      }
    } catch (e) {
      throw Exception('CSV格式错误: ${e.toString()}');
    }
    
    return materials;
  }

  /// 查找列索引
  static int _findColumnIndex(List<String> headers, List<String> possibleNames) {
    for (final name in possibleNames) {
      final index = headers.indexOf(name.toLowerCase());
      if (index != -1) return index;
    }
    return -1;
  }

  /// 获取字符串值
  static String _getStringValue(List<dynamic> row, int index) {
    if (index == -1 || index >= row.length) return '';
    return row[index]?.toString().trim() ?? '';
  }

  /// 获取整数值
  static int _getIntValue(List<dynamic> row, int index) {
    if (index == -1 || index >= row.length) return 0;
    return int.tryParse(row[index]?.toString() ?? '') ?? 0;
  }

  /// 获取浮点数值
  static double _getDoubleValue(List<dynamic> row, int index) {
    if (index == -1 || index >= row.length) return 0.0;
    return double.tryParse(row[index]?.toString() ?? '') ?? 0.0;
  }

  /// 解析材料分类
  static MaterialCategory _parseMaterialCategory(String categoryStr) {
    final category = categoryStr.toLowerCase();
    
    for (final materialCategory in MaterialCategory.values) {
      if (materialCategory.displayName == categoryStr ||
          materialCategory.name.toLowerCase() == category) {
        return materialCategory;
      }
    }

    return MaterialCategory.other;
  }
}
*/
