import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../vanhub_design_system.dart';

/// VanHub搜索栏组件
/// 
/// 支持实时搜索、历史记录、筛选器和搜索建议
/// 遵循Material Design 3规范
class VanHubSearchBar extends StatefulWidget {
  /// 搜索提示文本
  final String hintText;
  
  /// 搜索回调
  final ValueChanged<String>? onSearch;
  
  /// 搜索变化回调（实时）
  final ValueChanged<String>? onChanged;
  
  /// 是否启用搜索历史
  final bool enableHistory;
  
  /// 历史记录最大数量
  final int maxHistoryCount;
  
  /// 搜索建议列表
  final List<String>? suggestions;
  
  /// 是否显示筛选按钮
  final bool showFilterButton;
  
  /// 筛选按钮点击回调
  final VoidCallback? onFilterTap;
  
  /// 是否显示语音搜索按钮
  final bool showVoiceButton;
  
  /// 语音搜索回调
  final VoidCallback? onVoiceTap;
  
  /// 搜索防抖延迟（毫秒）
  final int debounceDelay;
  
  /// 初始搜索文本
  final String? initialText;
  
  /// 是否自动获取焦点
  final bool autofocus;

  const VanHubSearchBar({
    super.key,
    this.hintText = '搜索...',
    this.onSearch,
    this.onChanged,
    this.enableHistory = true,
    this.maxHistoryCount = 10,
    this.suggestions,
    this.showFilterButton = false,
    this.onFilterTap,
    this.showVoiceButton = false,
    this.onVoiceTap,
    this.debounceDelay = 300,
    this.initialText,
    this.autofocus = false,
  });

  @override
  State<VanHubSearchBar> createState() => _VanHubSearchBarState();
}

class _VanHubSearchBarState extends State<VanHubSearchBar> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;
  Timer? _debounceTimer;
  List<String> _searchHistory = [];
  List<String> _filteredSuggestions = [];
  bool _showSuggestions = false;
  OverlayEntry? _overlayEntry;

  static const String _historyKey = 'vanhub_search_history';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    
    _focusNode.addListener(_onFocusChanged);
    _controller.addListener(_onTextChanged);
    
    if (widget.enableHistory) {
      _loadSearchHistory();
    }
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _removeOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _updateSuggestions();
      _showSuggestionsOverlay();
    } else {
      _hideSuggestionsOverlay();
    }
  }

  void _onTextChanged() {
    final text = _controller.text;
    
    // 实时搜索回调
    widget.onChanged?.call(text);
    
    // 更新建议列表
    _updateSuggestions();
    
    // 防抖搜索
    _debounceTimer?.cancel();
    _debounceTimer = Timer(
      Duration(milliseconds: widget.debounceDelay),
      () => widget.onSearch?.call(text),
    );
  }

  void _updateSuggestions() {
    final text = _controller.text.toLowerCase();
    final suggestions = <String>[];
    
    // 添加搜索历史
    if (widget.enableHistory && text.isNotEmpty) {
      suggestions.addAll(
        _searchHistory
            .where((item) => item.toLowerCase().contains(text))
            .take(5),
      );
    }
    
    // 添加建议列表
    if (widget.suggestions != null) {
      suggestions.addAll(
        widget.suggestions!
            .where((item) => item.toLowerCase().contains(text))
            .where((item) => !suggestions.contains(item))
            .take(5),
      );
    }
    
    setState(() {
      _filteredSuggestions = suggestions;
      _showSuggestions = _focusNode.hasFocus && 
          (text.isNotEmpty || _searchHistory.isNotEmpty);
    });
    
    if (_showSuggestions) {
      _showSuggestionsOverlay();
    } else {
      _hideSuggestionsOverlay();
    }
  }

  void _showSuggestionsOverlay() {
    _removeOverlay();
    
    if (!_showSuggestions) return;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => _buildSuggestionsOverlay(),
    );
    
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideSuggestionsOverlay() {
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildSuggestionsOverlay() {
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return const SizedBox.shrink();
    
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    
    return Positioned(
      left: position.dx,
      top: position.dy + size.height + 4,
      width: size.width,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: _buildSuggestionsList(),
        ),
      ),
    );
  }

  Widget _buildSuggestionsList() {
    final suggestions = _controller.text.isEmpty 
        ? _searchHistory.take(widget.maxHistoryCount).toList()
        : _filteredSuggestions;
    
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing2),
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        final isHistory = _searchHistory.contains(suggestion);
        
        return ListTile(
          dense: true,
          leading: Icon(
            isHistory ? Icons.history : Icons.search,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          title: _buildHighlightedText(suggestion),
          trailing: isHistory
              ? IconButton(
                  icon: const Icon(Icons.close, size: 16),
                  onPressed: () => _removeFromHistory(suggestion),
                )
              : null,
          onTap: () => _selectSuggestion(suggestion),
        );
      },
    );
  }

  Widget _buildHighlightedText(String text) {
    final query = _controller.text.toLowerCase();
    if (query.isEmpty) {
      return Text(text);
    }
    
    final index = text.toLowerCase().indexOf(query);
    if (index == -1) {
      return Text(text);
    }
    
    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(text: text.substring(0, index)),
          TextSpan(
            text: text.substring(index, index + query.length),
            style: TextStyle(
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          TextSpan(text: text.substring(index + query.length)),
        ],
      ),
    );
  }

  void _selectSuggestion(String suggestion) {
    _controller.text = suggestion;
    _focusNode.unfocus();
    _addToHistory(suggestion);
    widget.onSearch?.call(suggestion);
  }

  void _onSubmitted(String text) {
    if (text.isNotEmpty) {
      _addToHistory(text);
      widget.onSearch?.call(text);
    }
    _focusNode.unfocus();
  }

  void _addToHistory(String text) {
    if (!widget.enableHistory || text.isEmpty) return;
    
    setState(() {
      _searchHistory.remove(text);
      _searchHistory.insert(0, text);
      if (_searchHistory.length > widget.maxHistoryCount) {
        _searchHistory = _searchHistory.take(widget.maxHistoryCount).toList();
      }
    });
    
    _saveSearchHistory();
  }

  void _removeFromHistory(String text) {
    setState(() {
      _searchHistory.remove(text);
    });
    _saveSearchHistory();
    _updateSuggestions();
  }

  void _clearHistory() {
    setState(() {
      _searchHistory.clear();
    });
    _saveSearchHistory();
    _updateSuggestions();
  }

  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_historyKey) ?? [];
      setState(() {
        _searchHistory = history;
      });
    } catch (e) {
      // 忽略加载错误
    }
  }

  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_historyKey, _searchHistory);
    } catch (e) {
      // 忽略保存错误
    }
  }

  @override
  Widget build(BuildContext context) {
    return SearchBar(
      controller: _controller,
      focusNode: _focusNode,
      hintText: widget.hintText,
      onSubmitted: _onSubmitted,
      leading: const Icon(Icons.search),
      trailing: _buildTrailingActions(),
      elevation: MaterialStateProperty.all(0),
      backgroundColor: MaterialStateProperty.all(
        Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
      ),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusFull),
        ),
      ),
    );
  }

  List<Widget> _buildTrailingActions() {
    final actions = <Widget>[];
    
    // 清除按钮
    if (_controller.text.isNotEmpty) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            _controller.clear();
            _focusNode.requestFocus();
          },
        ),
      );
    }
    
    // 筛选按钮
    if (widget.showFilterButton) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.tune),
          onPressed: widget.onFilterTap,
        ),
      );
    }
    
    // 语音搜索按钮
    if (widget.showVoiceButton) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.mic),
          onPressed: widget.onVoiceTap,
        ),
      );
    }
    
    return actions;
  }
}
