import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/performance/vanhub_performance.dart';

void main() {
  group('VanHubPerformance', () {
    late VanHubPerformance performance;

    setUp(() {
      performance = VanHubPerformance();
    });

    tearDown(() {
      performance.stopMonitoring();
    });

    test('应该是单例模式', () {
      final instance1 = VanHubPerformance();
      final instance2 = VanHubPerformance();
      
      expect(instance1, equals(instance2));
    });

    test('应该能够开始和停止监控', () {
      expect(() => performance.startMonitoring(), returnsNormally);
      expect(() => performance.stopMonitoring(), returnsNormally);
    });

    test('应该记录性能指标', () {
      performance.startMonitoring();
      
      // 等待一些指标被记录
      Future.delayed(const Duration(milliseconds: 100));
      
      final metrics = performance.getMetrics();
      expect(metrics, isA<List<PerformanceMetric>>());
    });

    test('应该计算平均帧率', () {
      performance.startMonitoring();
      
      final fps = performance.getAverageFrameRate();
      expect(fps, isA<double>());
      expect(fps, greaterThanOrEqualTo(0));
    });

    test('应该检测性能问题', () {
      performance.startMonitoring();
      
      final issues = performance.detectIssues();
      expect(issues, isA<List<PerformanceIssue>>());
    });

    test('应该按类型筛选指标', () {
      performance.startMonitoring();
      
      final memoryMetrics = performance.getMetrics(type: MetricType.memory);
      final frameMetrics = performance.getMetrics(type: MetricType.frame);
      
      expect(memoryMetrics, isA<List<PerformanceMetric>>());
      expect(frameMetrics, isA<List<PerformanceMetric>>());
    });
  });

  group('VanHubMemoryLeakDetector', () {
    tearDown(() {
      VanHubMemoryLeakDetector.stop();
    });

    test('应该跟踪对象', () {
      final testObject = Object();
      
      VanHubMemoryLeakDetector.track('test_object', testObject);
      
      expect(VanHubMemoryLeakDetector.getTrackedObjectCount(), equals(1));
    });

    test('应该停止跟踪对象', () {
      final testObject = Object();
      
      VanHubMemoryLeakDetector.track('test_object', testObject);
      VanHubMemoryLeakDetector.untrack('test_object');
      
      expect(VanHubMemoryLeakDetector.getTrackedObjectCount(), equals(0));
    });
  });

  group('VanHubNetworkMonitor', () {
    tearDown(() {
      VanHubNetworkMonitor.clearRequests();
    });

    test('应该监控网络请求', () {
      final requestId = VanHubNetworkMonitor.startRequest(
        'https://api.example.com/data',
        'GET',
      );
      
      expect(requestId, isNotEmpty);
      
      VanHubNetworkMonitor.endRequest(
        requestId,
        statusCode: 200,
        responseSize: 1024,
      );
      
      final requests = VanHubNetworkMonitor.getRequests();
      expect(requests, hasLength(1));
      expect(requests.first.statusCode, equals(200));
      expect(requests.first.responseSize, equals(1024));
    });

    test('应该检测慢请求', () {
      final requestId = VanHubNetworkMonitor.startRequest(
        'https://api.example.com/slow',
        'GET',
      );
      
      // 模拟慢请求
      Future.delayed(const Duration(seconds: 4), () {
        VanHubNetworkMonitor.endRequest(requestId, statusCode: 200);
      });
      
      final slowRequests = VanHubNetworkMonitor.getSlowRequests(
        threshold: const Duration(seconds: 3),
      );
      
      expect(slowRequests, isA<List<NetworkRequest>>());
    });
  });

  group('VanHubPerformanceOptimizer', () {
    test('应该生成优化建议', () {
      final suggestions = VanHubPerformanceOptimizer.analyzeAndSuggest();
      
      expect(suggestions, isA<List<OptimizationSuggestion>>());
    });
  });

  group('VanHubPerformanceUtils', () {
    test('应该测量异步函数执行时间', () async {
      final result = await VanHubPerformanceUtils.measureAsync(
        '测试异步函数',
        () async {
          await Future.delayed(const Duration(milliseconds: 100));
          return '完成';
        },
      );
      
      expect(result, equals('完成'));
    });

    test('应该测量同步函数执行时间', () {
      final result = VanHubPerformanceUtils.measureSync(
        '测试同步函数',
        () {
          return '完成';
        },
      );
      
      expect(result, equals('完成'));
    });

    test('应该支持防抖', () {
      int callCount = 0;
      
      // 快速调用多次
      VanHubPerformanceUtils.debounce(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      VanHubPerformanceUtils.debounce(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      VanHubPerformanceUtils.debounce(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      
      // 应该只执行最后一次
      Future.delayed(const Duration(milliseconds: 150), () {
        expect(callCount, equals(1));
      });
    });

    test('应该支持节流', () {
      int callCount = 0;
      
      // 快速调用多次
      VanHubPerformanceUtils.throttle(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      VanHubPerformanceUtils.throttle(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      VanHubPerformanceUtils.throttle(
        const Duration(milliseconds: 100),
        () => callCount++,
      );
      
      // 应该只执行第一次
      expect(callCount, equals(1));
    });
  });
}