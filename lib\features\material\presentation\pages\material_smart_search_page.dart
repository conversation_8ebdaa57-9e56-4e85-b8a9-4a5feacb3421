import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/material_search_demo_widget.dart';
import '../widgets/material_recommendation_demo_widget.dart';
import '../widgets/project_selector_widget.dart';
import '../../../auth/presentation/providers/auth_provider.dart';

class MaterialSmartSearchPage extends ConsumerStatefulWidget {
  final String? projectId;
  
  const MaterialSmartSearchPage({
    Key? key,
    this.projectId,
  }) : super(key: key);

  @override
  ConsumerState<MaterialSmartSearchPage> createState() => _MaterialSmartSearchPageState();
}

class _MaterialSmartSearchPageState extends ConsumerState<MaterialSmartSearchPage> {
  int _selectedIndex = 0;
  late String _projectId;
  bool _projectSelected = false;
  
  @override
  void initState() {
    super.initState();
    _projectId = widget.projectId ?? '';
    _projectSelected = _projectId.isNotEmpty;
  }

  void _onProjectSelected(String projectId) {
    setState(() {
      _projectId = projectId;
      _projectSelected = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final userState = ref.watch(authNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('材料智能搜索与推荐'),
        actions: [
          if (_projectSelected)
            IconButton(
              icon: const Icon(Icons.swap_horiz),
              tooltip: '切换项目',
              onPressed: () {
                setState(() {
                  _projectSelected = false;
                });
              },
            ),
        ],
      ),
      body: userState.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: Text('请先登录'),
            );
          }
          
          if (!_projectSelected) {
            return ProjectSelectorWidget(
              onProjectSelected: _onProjectSelected,
            );
          }
          
          return Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: _selectedIndex == 0
                    ? const MaterialSearchDemoWidget()
                    : MaterialRecommendationDemoWidget(projectId: _projectId),
              ),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, _) => Center(
          child: Text('加载失败: ${error.toString()}'),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).primaryColor,
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedIndex = 0;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: _selectedIndex == 0
                          ? Colors.white
                          : Colors.transparent,
                      width: 3.0,
                    ),
                  ),
                ),
                child: const Center(
                  child: Text(
                    '材料搜索',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () {
                setState(() {
                  _selectedIndex = 1;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: _selectedIndex == 1
                          ? Colors.white
                          : Colors.transparent,
                      width: 3.0,
                    ),
                  ),
                ),
                child: const Center(
                  child: Text(
                    '智能推荐',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}