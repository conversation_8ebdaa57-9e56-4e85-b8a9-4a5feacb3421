import '../../domain/entities/milestone.dart';
import '../../domain/entities/enums.dart';

/// 里程碑数据模型
class MilestoneModel {
  final String id;
  final String project_id;
  final String title;
  final String? description;
  final String date;
  final String status;
  final String? system_id;
  final List<String>? related_log_ids;
  final String created_at;
  final String updated_at;
  final String created_by;
  final String? icon_name;
  final String? color_hex;
  final String priority;

  MilestoneModel({
    required this.id,
    required this.project_id,
    required this.title,
    this.description,
    required this.date,
    required this.status,
    this.system_id,
    this.related_log_ids,
    required this.created_at,
    required this.updated_at,
    required this.created_by,
    this.icon_name,
    this.color_hex,
    required this.priority,
  });

  factory MilestoneModel.fromJson(Map<String, dynamic> json) {
    return MilestoneModel(
      id: json['id'],
      project_id: json['project_id'],
      title: json['title'],
      description: json['description'],
      date: json['date'],
      status: json['status'],
      system_id: json['system_id'],
      related_log_ids: json['related_log_ids'] != null
          ? List<String>.from(json['related_log_ids'])
          : null,
      created_at: json['created_at'],
      updated_at: json['updated_at'],
      created_by: json['created_by'],
      icon_name: json['icon_name'],
      color_hex: json['color_hex'],
      priority: json['priority'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'project_id': project_id,
      'title': title,
      'description': description,
      'date': date,
      'status': status,
      'system_id': system_id,
      'related_log_ids': related_log_ids,
      'created_at': created_at,
      'updated_at': updated_at,
      'created_by': created_by,
      'icon_name': icon_name,
      'color_hex': color_hex,
      'priority': priority,
    };
  }

  /// 转换为领域实体
  Milestone toEntity() {
    return Milestone(
      id: id,
      projectId: project_id,
      title: title,
      description: description,
      date: DateTime.parse(date),
      status: _parseMilestoneStatus(status),
      systemId: system_id,
      relatedLogIds: related_log_ids ?? [],
      createdAt: DateTime.parse(created_at),
      updatedAt: DateTime.parse(updated_at),
      createdBy: created_by,
      iconName: icon_name,
      colorHex: color_hex,
      priority: _parseMilestonePriority(priority),
    );
  }

  /// 从领域实体创建
  factory MilestoneModel.fromEntity(Milestone entity) {
    return MilestoneModel(
      id: entity.id,
      project_id: entity.projectId,
      title: entity.title,
      description: entity.description,
      date: entity.date.toIso8601String(),
      status: milestoneStatusToString(entity.status),
      system_id: entity.systemId,
      related_log_ids: entity.relatedLogIds.isEmpty ? null : entity.relatedLogIds,
      created_at: entity.createdAt.toIso8601String(),
      updated_at: entity.updatedAt.toIso8601String(),
      created_by: entity.createdBy,
      icon_name: entity.iconName,
      color_hex: entity.colorHex,
      priority: _milestonePriorityToString(entity.priority),
    );
  }

  /// 从API响应创建
  factory MilestoneModel.fromApiResponse(Map<String, dynamic> response) {
    return MilestoneModel(
      id: response['id'] as String,
      project_id: response['project_id'] as String,
      title: response['title'] as String,
      description: response['description'] as String?,
      date: response['date'] as String,
      status: response['status'] as String,
      system_id: response['system_id'] as String?,
      related_log_ids: (response['related_log_ids'] as List<dynamic>?)?.cast<String>(),
      created_at: response['created_at'] as String,
      updated_at: response['updated_at'] as String,
      created_by: response['created_by'] as String? ?? '',
      icon_name: response['icon_name'] as String?,
      color_hex: response['color_hex'] as String?,
      priority: response['priority'] as String? ?? 'medium',
    );
  }

  /// 解析里程碑状态
  static MilestoneStatus _parseMilestoneStatus(String status) {
    switch (status) {
      case 'in_progress':
        return MilestoneStatus.inProgress;
      case 'completed':
        return MilestoneStatus.completed;
      case 'delayed':
      case 'overdue':
        return MilestoneStatus.overdue;
      case 'cancelled':
        return MilestoneStatus.cancelled;
      case 'overdue':
        return MilestoneStatus.overdue;
      case 'planned':
      default:
        return MilestoneStatus.planned;
    }
  }

  /// 解析里程碑优先级
  static MilestonePriority _parseMilestonePriority(String priority) {
    switch (priority) {
      case 'low':
        return MilestonePriority.low;
      case 'high':
        return MilestonePriority.high;
      case 'critical':
        return MilestonePriority.critical;
      case 'medium':
      default:
        return MilestonePriority.medium;
    }
  }

  /// 里程碑状态转字符串
  static String milestoneStatusToString(MilestoneStatus status) {
    switch (status) {
      case MilestoneStatus.inProgress:
        return 'in_progress';
      case MilestoneStatus.completed:
        return 'completed';
      case MilestoneStatus.overdue:
        return 'overdue';
      case MilestoneStatus.cancelled:
        return 'cancelled';
      case MilestoneStatus.planned:
        return 'planned';
    }
  }

  /// 里程碑优先级转字符串
  static String _milestonePriorityToString(MilestonePriority priority) {
    switch (priority) {
      case MilestonePriority.low:
        return 'low';
      case MilestonePriority.high:
        return 'high';
      case MilestonePriority.critical:
        return 'critical';
      case MilestonePriority.medium:
        return 'medium';
    }
  }
}