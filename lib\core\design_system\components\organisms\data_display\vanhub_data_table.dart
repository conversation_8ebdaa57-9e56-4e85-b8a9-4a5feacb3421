import 'package:flutter/material.dart';
import 'vanhub_data_models.dart';
import 'vanhub_data_exporter.dart';
import 'vanhub_data_table_controller.dart';
import 'vanhub_data_table_style.dart';

/// VanHub数据表格组件
/// 提供响应式数据表格、虚拟滚动分页、多列排序和筛选功能
class VanHubDataTable extends StatefulWidget {
  final List<VanHubDataColumn> columns;
  final List<VanHubDataRow> rows;
  final bool showCheckboxColumn;
  final bool sortable;
  final bool filterable;
  final bool paginated;
  final int? rowsPerPage;
  final List<int>? availableRowsPerPage;
  final ValueChanged<List<VanHubDataRow>>? onSelectChanged;
  final VanHubDataTableController? controller;
  final double? width;
  final double? height;
  final bool responsive;
  final VanHubDataTableLayout mobileLayout;
  final bool showBorders;
  final bool zebra;
  final bool hoverable;
  final VanHubDataTableExportOptions? exportOptions;
  final Widget? emptyPlaceholder;
  final Widget? loadingPlaceholder;
  final Widget? errorPlaceholder;
  final VanHubDataTableStyle? style;

  const VanHubDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.showCheckboxColumn = false,
    this.sortable = true,
    this.filterable = false,
    this.paginated = false,
    this.rowsPerPage,
    this.availableRowsPerPage,
    this.onSelectChanged,
    this.controller,
    this.width,
    this.height,
    this.responsive = true,
    this.mobileLayout = VanHubDataTableLayout.card,
    this.showBorders = true,
    this.zebra = true,
    this.hoverable = true,
    this.exportOptions,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
    this.errorPlaceholder,
    this.style,
  });

  @override
  State<VanHubDataTable> createState() => _VanHubDataTableState();
}

class _VanHubDataTableState extends State<VanHubDataTable> {
  late VanHubDataTableController _controller;
  late List<VanHubDataRow> _displayedRows;
  late List<VanHubDataRow> _selectedRows;
  late int _currentPage;
  late int _rowsPerPage;
  late String _searchQuery;
  late Map<String, VanHubSortDirection> _sortColumns;
  late Map<String, List<String>> _filterValues;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? VanHubDataTableController();
    _displayedRows = List.from(widget.rows);
    _selectedRows = [];
    _currentPage = 1;
    _rowsPerPage = widget.rowsPerPage ?? 10;
    _searchQuery = '';
    _sortColumns = {};
    _filterValues = {};
    
    _applyInitialSorting();
  }

  @override
  void didUpdateWidget(VanHubDataTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.rows != oldWidget.rows) {
      _displayedRows = List.from(widget.rows);
      _applyFilters();
      _applySorting();
    }
  }

  void _applyInitialSorting() {
    // 应用初始排序
    for (final column in widget.columns) {
      if (column.initialSortDirection != null) {
        _sortColumns[column.id] = column.initialSortDirection!;
      }
    }
    
    if (_sortColumns.isNotEmpty) {
      _applySorting();
    }
  }

  void _applySorting() {
    if (_sortColumns.isEmpty) {
      return;
    }
    
    _displayedRows.sort((a, b) {
      int result = 0;
      
      for (final entry in _sortColumns.entries) {
        final columnId = entry.key;
        final direction = entry.value;
        final column = widget.columns.firstWhere((col) => col.id == columnId);
        
        if (column.comparator != null) {
          final aCell = a.cells[columnId];
          final bCell = b.cells[columnId];
          if (aCell != null && bCell != null) {
            result = column.comparator!(aCell, bCell);
          } else if (aCell == null && bCell == null) {
            result = 0;
          } else if (aCell == null) {
            result = -1;
          } else {
            result = 1;
          }
        } else {
          final aValue = a.cells[columnId]?.value;
          final bValue = b.cells[columnId]?.value;
          
          if (aValue == null && bValue == null) {
            result = 0;
          } else if (aValue == null) {
            result = -1;
          } else if (bValue == null) {
            result = 1;
          } else if (aValue is Comparable && bValue is Comparable) {
            result = Comparable.compare(aValue, bValue);
          } else {
            result = aValue.toString().compareTo(bValue.toString());
          }
        }
        
        if (result != 0) {
          return direction == VanHubSortDirection.ascending ? result : -result;
        }
      }
      
      return result;
    });
    
    setState(() {});
  }

  void _applyFilters() {
    _displayedRows = List.from(widget.rows);
    
    // 应用搜索过滤
    if (_searchQuery.isNotEmpty) {
      _displayedRows = _displayedRows.where((row) {
        return row.cells.values.any((cell) {
          final value = cell.value;
          if (value == null) return false;
          return value.toString().toLowerCase().contains(_searchQuery.toLowerCase());
        });
      }).toList();
    }
    
    // 应用列过滤
    if (_filterValues.isNotEmpty) {
      _displayedRows = _displayedRows.where((row) {
        return _filterValues.entries.every((entry) {
          final columnId = entry.key;
          final filterValues = entry.value;
          final cellValue = row.cells[columnId]?.value;
          
          if (cellValue == null) return false;
          if (filterValues.isEmpty) return true;
          
          return filterValues.contains(cellValue.toString());
        });
      }).toList();
    }
    
    setState(() {});
  }

  void _handleSort(String columnId) {
    if (!widget.sortable) return;
    
    setState(() {
      if (_sortColumns.containsKey(columnId)) {
        if (_sortColumns[columnId] == VanHubSortDirection.ascending) {
          _sortColumns[columnId] = VanHubSortDirection.descending;
        } else {
          _sortColumns.remove(columnId);
        }
      } else {
        _sortColumns[columnId] = VanHubSortDirection.ascending;
      }
      
      _applySorting();
    });
  }

  void _handleFilter(String columnId, List<String> values) {
    setState(() {
      if (values.isEmpty) {
        _filterValues.remove(columnId);
      } else {
        _filterValues[columnId] = values;
      }
      
      _applyFilters();
      _applySorting();
    });
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
      _applySorting();
    });
  }

  void _handlePageChange(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _handleRowsPerPageChange(int? rowsPerPage) {
    if (rowsPerPage == null) return;
    
    setState(() {
      _rowsPerPage = rowsPerPage;
      _currentPage = 1;
    });
  }

  void _handleSelectAll(bool? selected) {
    if (selected == null) return;
    
    setState(() {
      if (selected) {
        _selectedRows = List.from(_displayedRows);
      } else {
        _selectedRows = [];
      }
      
      if (widget.onSelectChanged != null) {
        widget.onSelectChanged!(_selectedRows);
      }
    });
  }

  void _handleSelectRow(VanHubDataRow row, bool? selected) {
    if (selected == null) return;
    
    setState(() {
      if (selected) {
        if (!_selectedRows.contains(row)) {
          _selectedRows.add(row);
        }
      } else {
        _selectedRows.remove(row);
      }
      
      if (widget.onSelectChanged != null) {
        widget.onSelectChanged!(_selectedRows);
      }
    });
  }

  void _exportData(VanHubExportFormat format) {
    if (widget.exportOptions == null) return;
    
    final exporter = VanHubDataExporter(
      columns: widget.columns,
      rows: _displayedRows,
      format: format,
      fileName: widget.exportOptions!.fileName ?? 'export',
    );
    
    exporter.export();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && widget.loadingPlaceholder != null) {
      return widget.loadingPlaceholder!;
    }
    
    if (_errorMessage != null && widget.errorPlaceholder != null) {
      return widget.errorPlaceholder!;
    }
    
    if (_displayedRows.isEmpty && widget.emptyPlaceholder != null) {
      return widget.emptyPlaceholder!;
    }
    
    // 确定当前视图是否为移动视图
    final isMobile = widget.responsive && MediaQuery.of(context).size.width < 600;
    
    // 分页处理
    List<VanHubDataRow> paginatedRows = _displayedRows;
    int totalPages = 1;
    
    if (widget.paginated) {
      totalPages = (_displayedRows.length / _rowsPerPage).ceil();
      final startIndex = (_currentPage - 1) * _rowsPerPage;
      final endIndex = startIndex + _rowsPerPage;
      
      if (startIndex < _displayedRows.length) {
        paginatedRows = _displayedRows.sublist(
          startIndex,
          endIndex > _displayedRows.length ? _displayedRows.length : endIndex,
        );
      } else {
        paginatedRows = [];
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (widget.filterable || widget.exportOptions != null)
          _buildTableToolbar(),
        
        Expanded(
          child: isMobile
              ? _buildMobileLayout(paginatedRows)
              : _buildDesktopLayout(paginatedRows),
        ),
        
        if (widget.paginated)
          _buildPagination(totalPages),
      ],
    );
  }

  Widget _buildTableToolbar() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          if (widget.filterable)
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  hintText: '搜索...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                ),
                onChanged: _handleSearch,
              ),
            ),
          
          if (widget.exportOptions != null) ...[
            const SizedBox(width: 16),
            PopupMenuButton<VanHubExportFormat>(
              tooltip: '导出数据',
              icon: const Icon(Icons.download),
              onSelected: _exportData,
              itemBuilder: (context) => [
                if (widget.exportOptions!.allowCsv)
                  const PopupMenuItem(
                    value: VanHubExportFormat.csv,
                    child: Text('导出为CSV'),
                  ),
                if (widget.exportOptions!.allowExcel)
                  const PopupMenuItem(
                    value: VanHubExportFormat.excel,
                    child: Text('导出为Excel'),
                  ),
                if (widget.exportOptions!.allowPdf)
                  const PopupMenuItem(
                    value: VanHubExportFormat.pdf,
                    child: Text('导出为PDF'),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(List<VanHubDataRow> rows) {
    final theme = Theme.of(context);
    final style = widget.style ?? const VanHubDataTableStyle();
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: [
            if (widget.showCheckboxColumn)
              DataColumn(
                label: Checkbox(
                  value: _selectedRows.length == _displayedRows.length && _displayedRows.isNotEmpty,
                  onChanged: _handleSelectAll,
                ),
              ),
            ...widget.columns.map((column) {
              return DataColumn(
                label: Row(
                  children: [
                    Expanded(child: Text(column.label)),
                    if (widget.sortable && column.sortable)
                      _buildSortIcon(column.id),
                    if (widget.filterable && column.filterable)
                      _buildFilterIcon(column),
                  ],
                ),
                numeric: column.numeric,
                tooltip: column.tooltip,
                onSort: widget.sortable && column.sortable
                    ? (_, __) => _handleSort(column.id)
                    : null,
              );
            }),
          ],
          rows: rows.map((row) {
            return DataRow(
              selected: _selectedRows.contains(row),
              onSelectChanged: widget.showCheckboxColumn
                  ? (selected) => _handleSelectRow(row, selected)
                  : null,
              color: widget.zebra
                  ? MaterialStateProperty.resolveWith<Color?>(
                      (Set<MaterialState> states) {
                        if (states.contains(MaterialState.selected)) {
                          return theme.colorScheme.primary.withOpacity(0.08);
                        }
                        if (widget.zebra && rows.indexOf(row) % 2 == 1) {
                          return theme.colorScheme.surfaceVariant.withOpacity(0.1);
                        }
                        return null;
                      },
                    )
                  : null,
              cells: [
                if (widget.showCheckboxColumn)
                  DataCell(
                    Checkbox(
                      value: _selectedRows.contains(row),
                      onChanged: (selected) => _handleSelectRow(row, selected),
                    ),
                  ),
                ...widget.columns.map((column) {
                  final cell = row.cells[column.id];
                  if (cell == null) {
                    return const DataCell(Text('-'));
                  }
                  
                  return DataCell(
                    cell.customBuilder != null
                        ? cell.customBuilder!(context, cell.value)
                        : Text(cell.value?.toString() ?? '-'),
                    onTap: cell.onTap,
                    showEditIcon: cell.showEditIcon ?? false,
                    placeholder: cell.placeholder ?? false,
                  );
                }),
              ],
            );
          }).toList(),
          columnSpacing: style.columnSpacing,
          dataRowHeight: style.rowHeight,
          headingRowHeight: style.headerHeight,
          horizontalMargin: style.horizontalMargin,
          dividerThickness: widget.showBorders ? style.dividerThickness : 0,
          showCheckboxColumn: widget.showCheckboxColumn,
        ),
      ),
    );
  }

  Widget _buildMobileLayout(List<VanHubDataRow> rows) {
    switch (widget.mobileLayout) {
      case VanHubDataTableLayout.card:
        return _buildCardLayout(rows);
      case VanHubDataTableLayout.list:
        return _buildListLayout(rows);
      default:
        return _buildCardLayout(rows);
    }
  }

  Widget _buildCardLayout(List<VanHubDataRow> rows) {
    return ListView.builder(
      itemCount: rows.length,
      itemBuilder: (context, index) {
        final row = rows[index];
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: InkWell(
            onTap: () {
              if (widget.showCheckboxColumn) {
                _handleSelectRow(row, !_selectedRows.contains(row));
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.showCheckboxColumn)
                    Row(
                      children: [
                        Checkbox(
                          value: _selectedRows.contains(row),
                          onChanged: (selected) => _handleSelectRow(row, selected),
                        ),
                        const Spacer(),
                        if (row.actions != null) row.actions!,
                      ],
                    ),
                  ...widget.columns.map((column) {
                    final cell = row.cells[column.id];
                    if (cell == null) {
                      return const SizedBox.shrink();
                    }
                    
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 100,
                            child: Text(
                              column.label,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                          Expanded(
                            child: cell.customBuilder != null
                                ? cell.customBuilder!(context, cell.value)
                                : Text(cell.value?.toString() ?? '-'),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListLayout(List<VanHubDataRow> rows) {
    return ListView.builder(
      itemCount: rows.length,
      itemBuilder: (context, index) {
        final row = rows[index];
        
        // 获取主要列和次要列
        final primaryColumn = widget.columns.firstWhere(
          (col) => col.primary,
          orElse: () => widget.columns.first,
        );
        
        final secondaryColumns = widget.columns.where(
          (col) => col.id != primaryColumn.id && col.showInMobileList,
        ).toList();
        
        final primaryCell = row.cells[primaryColumn.id];
        
        return ListTile(
          title: primaryCell?.customBuilder != null
              ? primaryCell!.customBuilder!(context, primaryCell.value)
              : Text(primaryCell?.value?.toString() ?? '-'),
          subtitle: secondaryColumns.isEmpty
              ? null
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: secondaryColumns.map((column) {
                    final cell = row.cells[column.id];
                    if (cell == null) {
                      return const SizedBox.shrink();
                    }
                    
                    return cell.customBuilder != null
                        ? cell.customBuilder!(context, cell.value)
                        : Text(
                            '${column.label}: ${cell.value?.toString() ?? '-'}',
                            style: const TextStyle(fontSize: 12),
                          );
                  }).toList(),
                ),
          leading: widget.showCheckboxColumn
              ? Checkbox(
                  value: _selectedRows.contains(row),
                  onChanged: (selected) => _handleSelectRow(row, selected),
                )
              : null,
          trailing: row.actions,
          onTap: () {
            if (widget.showCheckboxColumn) {
              _handleSelectRow(row, !_selectedRows.contains(row));
            }
          },
        );
      },
    );
  }

  Widget _buildPagination(int totalPages) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (widget.availableRowsPerPage != null)
            Row(
              children: [
                const Text('每页行数:'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _rowsPerPage,
                  items: widget.availableRowsPerPage!.map((count) {
                    return DropdownMenuItem<int>(
                      value: count,
                      child: Text('$count'),
                    );
                  }).toList(),
                  onChanged: _handleRowsPerPageChange,
                ),
              ],
            )
          else
            Text('每页 $_rowsPerPage 行'),
          
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.first_page),
                onPressed: _currentPage > 1
                    ? () => _handlePageChange(1)
                    : null,
              ),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: _currentPage > 1
                    ? () => _handlePageChange(_currentPage - 1)
                    : null,
              ),
              Text('$_currentPage / $totalPages'),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: _currentPage < totalPages
                    ? () => _handlePageChange(_currentPage + 1)
                    : null,
              ),
              IconButton(
                icon: const Icon(Icons.last_page),
                onPressed: _currentPage < totalPages
                    ? () => _handlePageChange(totalPages)
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortIcon(String columnId) {
    if (!_sortColumns.containsKey(columnId)) {
      return const Icon(Icons.sort, size: 16, color: Colors.grey);
    }
    
    return _sortColumns[columnId] == VanHubSortDirection.ascending
        ? const Icon(Icons.arrow_upward, size: 16)
        : const Icon(Icons.arrow_downward, size: 16);
  }

  Widget _buildFilterIcon(VanHubDataColumn column) {
    final isFiltered = _filterValues.containsKey(column.id);
    
    return IconButton(
      icon: Icon(
        Icons.filter_list,
        size: 16,
        color: isFiltered ? Theme.of(context).colorScheme.primary : Colors.grey,
      ),
      onPressed: () {
        _showFilterDialog(column);
      },
    );
  }

  void _showFilterDialog(VanHubDataColumn column) {
    // 收集该列的所有唯一值
    final uniqueValues = <String>{};
    for (final row in widget.rows) {
      final cell = row.cells[column.id];
      if (cell != null && cell.value != null) {
        uniqueValues.add(cell.value.toString());
      }
    }
    
    final sortedValues = uniqueValues.toList()..sort();
    final selectedValues = _filterValues[column.id] ?? [];
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('筛选 ${column.label}'),
              content: SizedBox(
                width: 300,
                height: 300,
                child: ListView(
                  children: sortedValues.map((value) {
                    return CheckboxListTile(
                      title: Text(value),
                      value: selectedValues.contains(value),
                      onChanged: (checked) {
                        setState(() {
                          if (checked == true) {
                            if (!selectedValues.contains(value)) {
                              selectedValues.add(value);
                            }
                          } else {
                            selectedValues.remove(value);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    _handleFilter(column.id, List.from(selectedValues));
                    Navigator.of(context).pop();
                  },
                  child: const Text('应用'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}