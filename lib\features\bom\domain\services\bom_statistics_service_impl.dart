import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../entities/bom_statistics.dart';
import 'bom_statistics_service.dart';

/// BOM统计计算服务实现
class BomStatisticsServiceImpl implements BomStatisticsService {
  const BomStatisticsServiceImpl();

  @override
  BomStatistics calculateStatistics(List<BomItem> bomItems) {
    // 计算总成本
    final actualCost = bomItems.fold<double>(0.0, (sum, item) => sum + item.totalCost);
    
    // 计算完成项目数量
    final completedItems = bomItems.where((item) => item.isCompleted).length;
    
    // 计算各分类成本
    final costByCategory = <String, double>{};
    for (final item in bomItems) {
      final category = item.category ?? '未分类';
      costByCategory[category] = (costByCategory[category] ?? 0.0) + item.totalCost;
    }
    
    // 计算各状态项目数量
    final itemsByStatus = <BomItemStatus, int>{};
    for (final status in BomItemStatus.values) {
      itemsByStatus[status] = bomItems.where((item) => item.status == status).length;
    }
    
    // 查找最贵的项目
    BomItem? mostExpensiveItem;
    if (bomItems.isNotEmpty) {
      mostExpensiveItem = bomItems.reduce((a, b) => a.totalCost > b.totalCost ? a : b);
    }
    
    // 计算平均成本
    final averageItemCost = bomItems.isEmpty ? 0.0 : actualCost / bomItems.length;
    
    // 查找逾期项目
    final overdueItems = findOverdueItems(bomItems);
    
    // 计算完成百分比
    final completionPercentage = calculateCompletionPercentage(bomItems);
    
    // 估计完成日期
    DateTime? estimatedCompletionDate;
    if (bomItems.isNotEmpty && completionPercentage < 100) {
      final pendingItems = bomItems
          .where((item) => !item.isCompleted && item.plannedDate != null)
          .toList();
      
      if (pendingItems.isNotEmpty) {
        // 找出最晚的计划日期作为估计完成日期
        estimatedCompletionDate = pendingItems
            .map((item) => item.plannedDate!)
            .reduce((a, b) => a.isAfter(b) ? a : b);
      }
    }
    
    // 假设总预算为实际成本的120%（如果没有提供预算）
    final totalBudget = actualCost * 1.2;
    final remainingBudget = totalBudget - actualCost;
    final budgetUtilization = totalBudget > 0 ? (actualCost / totalBudget) * 100 : 0.0;
    
    return BomStatistics(
      totalBudget: totalBudget,
      actualCost: actualCost,
      remainingBudget: remainingBudget,
      totalItems: bomItems.length,
      completedItems: completedItems,
      pendingItems: bomItems.length - completedItems,
      costByCategory: costByCategory,
      itemsByStatus: itemsByStatus,
      overdueItems: overdueItems,
      completionPercentage: completionPercentage,
      averageItemCost: averageItemCost,
      mostExpensiveItem: mostExpensiveItem,
      budgetUtilization: budgetUtilization,
      estimatedCompletionDate: estimatedCompletionDate,
    );
  }

  @override
  Map<String, double> calculateCostTrend(List<BomItem> bomItems) {
    final costTrend = <String, double>{};
    
    // 按月份分组计算成本
    for (final item in bomItems) {
      if (item.purchasedDate != null) {
        final month = '${item.purchasedDate!.year}-${item.purchasedDate!.month.toString().padLeft(2, '0')}';
        costTrend[month] = (costTrend[month] ?? 0.0) + item.totalCost;
      }
    }
    
    return costTrend;
  }

  @override
  List<BomItem> findOverdueItems(List<BomItem> bomItems) {
    return bomItems.where((item) => item.isOverdue).toList();
  }

  @override
  double calculateCompletionPercentage(List<BomItem> bomItems) {
    final completedItems = bomItems.where((item) => item.isCompleted).length;
    return bomItems.isEmpty ? 0.0 : (completedItems / bomItems.length) * 100;
  }

  @override
  Either<Failure, BomStatistics> calculateProjectBudgetAnalysis(
    List<BomItem> bomItems,
    double projectBudget,
  ) {
    try {
      // 计算总成本
      final actualCost = bomItems.fold<double>(0.0, (sum, item) => sum + item.totalCost);
      
      // 计算完成项目数量
      final completedItems = bomItems.where((item) => item.isCompleted).length;
      
      // 计算各分类成本
      final costByCategory = <String, double>{};
      for (final item in bomItems) {
        final category = item.category ?? '未分类';
        costByCategory[category] = (costByCategory[category] ?? 0.0) + item.totalCost;
      }
      
      // 计算各状态项目数量
      final itemsByStatus = <BomItemStatus, int>{};
      for (final status in BomItemStatus.values) {
        itemsByStatus[status] = bomItems.where((item) => item.status == status).length;
      }
      
      // 查找最贵的项目
      BomItem? mostExpensiveItem;
      if (bomItems.isNotEmpty) {
        mostExpensiveItem = bomItems.reduce((a, b) => a.totalCost > b.totalCost ? a : b);
      }
      
      // 计算平均成本
      final averageItemCost = bomItems.isEmpty ? 0.0 : actualCost / bomItems.length;
      
      // 查找逾期项目
      final overdueItems = findOverdueItems(bomItems);
      
      // 计算完成百分比
      final completionPercentage = calculateCompletionPercentage(bomItems);
      
      // 估计完成日期
      DateTime? estimatedCompletionDate;
      if (bomItems.isNotEmpty && completionPercentage < 100) {
        final pendingItems = bomItems
            .where((item) => !item.isCompleted && item.plannedDate != null)
            .toList();
        
        if (pendingItems.isNotEmpty) {
          // 找出最晚的计划日期作为估计完成日期
          estimatedCompletionDate = pendingItems
              .map((item) => item.plannedDate!)
              .reduce((a, b) => a.isAfter(b) ? a : b);
        }
      }
      
      final remainingBudget = projectBudget - actualCost;
      final budgetUtilization = projectBudget > 0 ? (actualCost / projectBudget) * 100 : 0.0;
      
      return Right(BomStatistics(
        totalBudget: projectBudget,
        actualCost: actualCost,
        remainingBudget: remainingBudget,
        totalItems: bomItems.length,
        completedItems: completedItems,
        pendingItems: bomItems.length - completedItems,
        costByCategory: costByCategory,
        itemsByStatus: itemsByStatus,
        overdueItems: overdueItems,
        completionPercentage: completionPercentage,
        averageItemCost: averageItemCost,
        mostExpensiveItem: mostExpensiveItem,
        budgetUtilization: budgetUtilization,
        estimatedCompletionDate: estimatedCompletionDate,
      ));
    } catch (e) {
      return Left(UnknownFailure(message: '计算预算分析失败: $e'));
    }
  }

  @override
  Either<Failure, Map<String, dynamic>> generateCostReport(List<BomItem> bomItems) {
    try {
      final report = <String, dynamic>{};
      
      // 总成本
      final totalCost = bomItems.fold<double>(0.0, (sum, item) => sum + item.totalCost);
      report['totalCost'] = totalCost;
      
      // 按分类的成本
      final costByCategory = <String, double>{};
      for (final item in bomItems) {
        final category = item.category ?? '未分类';
        costByCategory[category] = (costByCategory[category] ?? 0.0) + item.totalCost;
      }
      report['costByCategory'] = costByCategory;
      
      // 成本趋势
      report['costTrend'] = calculateCostTrend(bomItems);
      
      // 最贵的项目
      if (bomItems.isNotEmpty) {
        final topItems = bomItems.toList()
          ..sort((a, b) => b.totalCost.compareTo(a.totalCost))
          ..take(5);
        
        report['topExpensiveItems'] = topItems.map((item) => {
          'name': item.name,
          'id': item.id,
          'cost': item.totalCost,
          'category': item.category ?? '未分类',
        }).toList();
      }
      
      return Right(report);
    } catch (e) {
      return Left(UnknownFailure(message: '生成成本报告失败: $e'));
    }
  }

  @override
  Either<Failure, List<String>> generateBudgetAlerts(
    List<BomItem> bomItems,
    double projectBudget,
  ) {
    try {
      final alerts = <String>[];
      
      // 计算总成本
      final totalCost = bomItems.fold<double>(0.0, (sum, item) => sum + item.totalCost);
      
      // 检查是否超预算
      if (totalCost > projectBudget) {
        alerts.add('预算超支警告: 当前支出 ¥${totalCost.toStringAsFixed(2)} 已超出预算 ¥${projectBudget.toStringAsFixed(2)}');
      }
      
      // 检查是否接近预算
      if (totalCost >= projectBudget * 0.9 && totalCost <= projectBudget) {
        alerts.add('预算接近上限: 当前支出已达到预算的${((totalCost / projectBudget) * 100).toStringAsFixed(1)}%');
      }
      
      // 检查高成本项目
      final highCostItems = bomItems.where((item) => item.totalCost > projectBudget * 0.1).toList();
      if (highCostItems.isNotEmpty) {
        alerts.add('高成本项目警告: ${highCostItems.length}个项目单项成本超过预算的10%');
      }
      
      return Right(alerts);
    } catch (e) {
      return Left(UnknownFailure(message: '生成预算警告失败: $e'));
    }
  }
}