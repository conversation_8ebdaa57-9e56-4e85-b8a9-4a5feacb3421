import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/system_material.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../modification_log/domain/entities/enums.dart';

/// 物料状态更新对话框
class MaterialStatusDialog extends ConsumerStatefulWidget {
  const MaterialStatusDialog({
    super.key,
    required this.material,
    required this.onStatusChanged,
  });

  final SystemMaterial material;
  final Function(MaterialStatus purchaseStatus, MaterialStatus installStatus) onStatusChanged;

  @override
  ConsumerState<MaterialStatusDialog> createState() => _MaterialStatusDialogState();
}

class _MaterialStatusDialogState extends ConsumerState<MaterialStatusDialog> {
  late MaterialStatus _purchaseStatus;
  late MaterialStatus _installStatus;
  DateTime? _purchaseDate;
  DateTime? _installDate;

  @override
  void initState() {
    super.initState();
    _purchaseStatus = widget.material.purchaseStatus;
    _installStatus = widget.material.installStatus;
    _purchaseDate = widget.material.purchaseDate;
    _installDate = widget.material.installDate;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.pending,
            color: VanHubColors.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '更新物料状态',
              style: VanHubTypography.headlineSmall,
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 物料信息
            _buildMaterialInfo(),
            
            SizedBox(height: VanHubSpacing.lg),
            
            // 购买状态
            _buildStatusSection(
              title: '购买状态',
              currentStatus: _purchaseStatus,
              onStatusChanged: (status) {
                setState(() {
                  _purchaseStatus = status;
                  // 自动设置购买日期
                  if (status == MaterialStatus.received && _purchaseDate == null) {
                    _purchaseDate = DateTime.now();
                  }
                });
              },
              date: _purchaseDate,
              onDateChanged: (date) {
                setState(() {
                  _purchaseDate = date;
                });
              },
              showDatePicker: _purchaseStatus == MaterialStatus.received ||
                             _purchaseStatus == MaterialStatus.installed,
            ),
            
            SizedBox(height: VanHubSpacing.lg),
            
            // 安装状态
            _buildStatusSection(
              title: '安装状态',
              currentStatus: _installStatus,
              onStatusChanged: (status) {
                setState(() {
                  _installStatus = status;
                  // 自动设置安装日期
                  if (status == MaterialStatus.installed && _installDate == null) {
                    _installDate = DateTime.now();
                  }
                });
              },
              date: _installDate,
              onDateChanged: (date) {
                setState(() {
                  _installDate = date;
                });
              },
              showDatePicker: _installStatus == MaterialStatus.installed,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        FilledButton(
          onPressed: _hasChanges() ? _handleSave : null,
          child: const Text('保存'),
        ),
      ],
    );
  }

  /// 构建物料信息
  Widget _buildMaterialInfo() {
    return Container(
      padding: VanHubSpacing.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: VanHubColors.surfaceVariant,
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.material.name,
            style: VanHubTypography.titleMedium,
          ),
          if (widget.material.specification.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              widget.material.specification,
              style: VanHubTypography.bodyMedium.copyWith(
                color: VanHubColors.textSecondary,
              ),
            ),
          ],
          SizedBox(height: VanHubSpacing.sm),
          Row(
            children: [
              Text(
                '数量: ${widget.material.quantity}个',
                style: VanHubTypography.bodySmall,
              ),
              const Spacer(),
              Text(
                '总价: ¥${widget.material.totalPrice.toStringAsFixed(0)}',
                style: VanHubTypography.titleSmall.copyWith(
                  color: VanHubColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态选择部分
  Widget _buildStatusSection({
    required String title,
    required MaterialStatus currentStatus,
    required Function(MaterialStatus) onStatusChanged,
    DateTime? date,
    Function(DateTime?)? onDateChanged,
    bool showDatePicker = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: VanHubTypography.titleSmall,
        ),

        const SizedBox(height: 8),

        // 状态选项
        const Wrap(
          spacing: 8,
          runSpacing: 8,
          children: MaterialStatus.values.map((status) {
            final isSelected = status == currentStatus;
            final color = _getStatusColor(status);
            
            return FilterChip(
              selected: isSelected,
              onSelected: (_) => onStatusChanged(status),
              label: Text(_getStatusDisplayText(status)),
              avatar: Icon(
                _getMaterialStatusIcon(status.name),
                size: 16,
                color: isSelected ? VanHubColors.onPrimary : color,
              ),
              backgroundColor: color.withValues(alpha: 0.1),
              selectedColor: color,
              checkmarkColor: VanHubColors.onPrimary,
              labelStyle: VanHubTypography.bodySmall.copyWith(
                color: isSelected ? VanHubColors.onPrimary : color,
              ),
            );
          }).toList(),
        ),
        
        // 日期选择器
        if (showDatePicker) ...[
          SizedBox(height: VanHubSpacing.sm),
          
          InkWell(
            onTap: () => _selectDate(context, date, onDateChanged),
            child: Container(
              padding: VanHubSpacing.all(VanHubSpacing.sm),
              decoration: BoxDecoration(
                border: Border.all(color: VanHubColors.outline),
                borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: VanHubColors.textSecondary,
                  ),

                  const SizedBox(width: 8),

                  Text(
                    date != null
                        ? _formatDate(date)
                        : '选择日期',
                    style: VanHubTypography.bodyMedium,
                  ),
                  
                  const Spacer(),
                  
                  Icon(
                    Icons.arrow_drop_down,
                    color: VanHubColors.textSecondary,
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 选择日期
  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentDate,
    Function(DateTime?)? onDateChanged,
  ) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (selectedDate != null && onDateChanged != null) {
      onDateChanged(selectedDate);
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(MaterialStatus status) {
    switch (status) {
      case MaterialStatus.pending:
        return VanHubColors.textSecondary;
      case MaterialStatus.ordered:
        return VanHubColors.warning;
      case MaterialStatus.received:
        return VanHubColors.primary;
      case MaterialStatus.installed:
        return VanHubColors.success;
      case MaterialStatus.returned:
        return VanHubColors.error;
    }
  }

  /// 获取状态显示文本
  String _getStatusDisplayText(MaterialStatus status) {
    switch (status) {
      case MaterialStatus.pending:
        return '待处理';
      case MaterialStatus.ordered:
        return '已下单';
      case MaterialStatus.received:
        return '已收货';
      case MaterialStatus.installed:
        return '已安装';
      case MaterialStatus.returned:
        return '已退货';
    }
  }

  /// 检查是否有变更
  bool _hasChanges() {
    return _purchaseStatus != widget.material.purchaseStatus ||
           _installStatus != widget.material.installStatus ||
           _purchaseDate != widget.material.purchaseDate ||
           _installDate != widget.material.installDate;
  }

  /// 处理保存
  void _handleSave() {
    widget.onStatusChanged(_purchaseStatus, _installStatus);
    Navigator.of(context).pop();
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// 显示物料状态更新对话框
Future<void> showMaterialStatusDialog({
  required BuildContext context,
  required SystemMaterial material,
  required Function(MaterialStatus purchaseStatus, MaterialStatus installStatus) onStatusChanged,
}) {
  return showDialog<void>(
    context: context,
    builder: (context) => MaterialStatusDialog(
      material: material,
      onStatusChanged: onStatusChanged,
    ),
  );
}

/// 获取物料状态对应的图标
IconData _getMaterialStatusIcon(String statusName) {
  switch (statusName) {
    case 'pending':
      return Icons.pending;
    case 'purchased':
      return Icons.shopping_cart;
    case 'installed':
      return Icons.check_circle;
    case 'cancelled':
      return Icons.cancel;
    default:
      return Icons.help_outline;
  }
}