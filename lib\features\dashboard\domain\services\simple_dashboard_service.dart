import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/dashboard_data.dart';

/// 简化版仪表盘数据服务
/// 提供模拟数据，确保应用能正常运行
class SimpleDashboardService {
  
  /// 获取项目仪表盘数据
  Future<Either<Failure, DashboardData>> getProjectDashboard(String projectId) async {
    try {
      // 模拟延迟
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 生成模拟数据
      final dashboardData = DashboardData(
        projectId: projectId,
        projectOverview: _generateMockProjectOverview(),
        costAnalysis: _generateMockCostAnalysis(),
        progressTracking: _generateMockProgressTracking(),
        smartInsights: _generateMockSmartInsights(),
        recentActivities: _generateMockRecentActivities(),
        lastUpdated: DateTime.now(),
      );

      return Right(dashboardData);
    } catch (e) {
      return Left(ServerFailure(message: '获取仪表盘数据失败: $e'));
    }
  }

  ProjectOverview _generateMockProjectOverview() {
    return const ProjectOverview(
      overallProgress: 0.65, // 65%进度
      budgetUsed: 15000.0,
      totalBudget: 25000.0,
      totalTasks: 20,
      completedTasks: 13,
      totalTimeSpent: 2400, // 40小时
      estimatedTimeRemaining: 1200, // 20小时
      status: ProjectStatus.inProgress,
    );
  }

  CostAnalysis _generateMockCostAnalysis() {
    return CostAnalysis(
      totalCost: 15000.0,
      plannedCost: 12000.0,
      actualCost: 15000.0,
      categoryCosts: [
        const CategoryCost(
          categoryId: 'electrical',
          categoryName: '电力系统',
          amount: 5000.0,
          percentage: 0.33,
          colorHex: '#FF6B6B',
        ),
        const CategoryCost(
          categoryId: 'water',
          categoryName: '水路系统',
          amount: 3000.0,
          percentage: 0.20,
          colorHex: '#4ECDC4',
        ),
        const CategoryCost(
          categoryId: 'interior',
          categoryName: '内饰改装',
          amount: 4000.0,
          percentage: 0.27,
          colorHex: '#45B7D1',
        ),
        const CategoryCost(
          categoryId: 'other',
          categoryName: '其他配件',
          amount: 3000.0,
          percentage: 0.20,
          colorHex: '#96CEB4',
        ),
      ],
      monthlySpending: _generateMockMonthlySpending(),
      costTrends: _generateMockCostTrends(),
      costAlerts: [
        const CostAlert(
          id: 'budget_overrun',
          title: '预算超支警告',
          description: '当前支出已超出计划预算25%',
          severity: AlertSeverity.high,
          threshold: 12000.0,
          currentValue: 15000.0,
        ),
      ],
    );
  }

  ProgressTracking _generateMockProgressTracking() {
    return ProgressTracking(
      milestones: [
        const MilestoneProgress(
          id: 'milestone_1',
          title: '电力系统安装',
          progress: 1.0,
          status: MilestoneStatus.completed,
        ),
        const MilestoneProgress(
          id: 'milestone_2',
          title: '水路系统安装',
          progress: 0.8,
          status: MilestoneStatus.inProgress,
        ),
        const MilestoneProgress(
          id: 'milestone_3',
          title: '内饰装修',
          progress: 0.3,
          status: MilestoneStatus.inProgress,
        ),
      ],
      systemProgress: [
        const SystemProgress(
          systemId: 'electrical',
          systemName: '电力系统',
          progress: 0.9,
          totalTasks: 5,
          completedTasks: 4,
          colorHex: '#FF6B6B',
        ),
        const SystemProgress(
          systemId: 'water',
          systemName: '水路系统',
          progress: 0.6,
          totalTasks: 4,
          completedTasks: 2,
          colorHex: '#4ECDC4',
        ),
        const SystemProgress(
          systemId: 'interior',
          systemName: '内饰系统',
          progress: 0.4,
          totalTasks: 6,
          completedTasks: 2,
          colorHex: '#45B7D1',
        ),
      ],
      recentEvents: _generateMockTimelineEvents(),
      workTimeAnalysis: _generateMockWorkTimeAnalysis(),
    );
  }

  List<SmartInsight> _generateMockSmartInsights() {
    return [
      SmartInsight(
        id: 'cost_insight',
        title: '成本超支提醒',
        description: '电力系统成本超出预算，建议优化采购方案',
        type: InsightType.cost,
        priority: InsightPriority.high,
        actionText: '查看详情',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      SmartInsight(
        id: 'progress_insight',
        title: '进度良好',
        description: '项目进度符合预期，预计按时完成',
        type: InsightType.progress,
        priority: InsightPriority.medium,
        actionText: '查看时间轴',
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
      ),
    ];
  }

  List<RecentActivity> _generateMockRecentActivities() {
    return [
      RecentActivity(
        id: 'activity_1',
        title: '添加了改装日志',
        description: '完成了电池组安装',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        type: ActivityType.logCreated,
        userName: '用户',
      ),
      RecentActivity(
        id: 'activity_2',
        title: '更新了BOM',
        description: '添加了新的电线材料',
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        type: ActivityType.bomUpdated,
        userName: '用户',
      ),
      RecentActivity(
        id: 'activity_3',
        title: '完成了里程碑',
        description: '电力系统安装完成',
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        type: ActivityType.milestoneCompleted,
        userName: '用户',
      ),
    ];
  }

  List<MonthlySpending> _generateMockMonthlySpending() {
    final now = DateTime.now();
    return List.generate(6, (index) {
      final month = DateTime(now.year, now.month - (5 - index), 1);
      final monthStr = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      return MonthlySpending(
        month: monthStr,
        amount: 2000.0 + (index * 500.0),
        budgetAmount: 2500.0,
      );
    });
  }

  List<CostTrend> _generateMockCostTrends() {
    final now = DateTime.now();
    return List.generate(30, (index) {
      final date = now.subtract(Duration(days: 29 - index));
      final progress = index / 29.0;
      return CostTrend(
        date: date,
        cumulativeCost: 15000.0 * progress,
        plannedCost: 12000.0 * progress,
      );
    });
  }

  List<TimelineEvent> _generateMockTimelineEvents() {
    return [
      TimelineEvent(
        id: 'event_1',
        title: '电池组安装',
        description: '完成了主电池组的安装和连接',
        date: DateTime.now().subtract(const Duration(hours: 1)),
        type: EventType.milestone,
        systemId: 'electrical',
        systemName: '电力系统',
      ),
      TimelineEvent(
        id: 'event_2',
        title: '水泵测试',
        description: '测试了水泵的工作状态',
        date: DateTime.now().subtract(const Duration(hours: 4)),
        type: EventType.task,
        systemId: 'water',
        systemName: '水路系统',
      ),
    ];
  }

  WorkTimeAnalysis _generateMockWorkTimeAnalysis() {
    return WorkTimeAnalysis(
      totalHours: 40,
      thisWeekHours: 12,
      thisMonthHours: 35,
      averageHoursPerDay: 2.5,
      dailyWorkTime: List.generate(7, (index) {
        final date = DateTime.now().subtract(Duration(days: 6 - index));
        return DailyWorkTime(
          date: date,
          minutes: 120 + (index * 30), // 2-5小时不等
        );
      }),
    );
  }
}
