import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/bom_item.dart' as domain;
import '../../domain/entities/update_bom_item_request.dart';
import '../providers/bom_provider.dart';
import '../providers/bom_calculation_provider.dart';

/// 编辑BOM项目对话框
/// 严格遵循Clean Architecture原则，只负责UI展示和用户交互
/// 通过BomProvider调用Domain层的UseCase
class EditBomItemDialogWidget extends ConsumerStatefulWidget {
  final domain.BomItem bomItem;

  const EditBomItemDialogWidget({
    super.key,
    required this.bomItem,
  });

  @override
  ConsumerState<EditBomItemDialogWidget> createState() => _EditBomItemDialogWidgetState();
}

class _EditBomItemDialogWidgetState extends ConsumerState<EditBomItemDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _quantityController;
  late final TextEditingController _unitPriceController;
  late final TextEditingController _brandController;
  late final TextEditingController _modelController;
  late final TextEditingController _specificationsController;
  late final TextEditingController _supplierController;
  late final TextEditingController _supplierUrlController;
  late final TextEditingController _notesController;
  
  late String _selectedCategory;
  late domain.BomItemStatus _selectedStatus;
  DateTime? _plannedDate;
  bool _isLoading = false;
  
  // 房车改装专业分类
  final List<String> _categories = [
    '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装',
    '车顶改装', '底盘改装', '其他配件'
  ];

  // BOM状态选项 - 符合.kiro/specs规范
  final List<domain.BomItemStatus> _statusOptions = [
    domain.BomItemStatus.pending,
    domain.BomItemStatus.ordered,
    domain.BomItemStatus.received,
    domain.BomItemStatus.installed,
    domain.BomItemStatus.cancelled,
  ];

  @override
  void initState() {
    super.initState();
    // 初始化控制器和数据
    _nameController = TextEditingController(text: widget.bomItem.name);
    _descriptionController = TextEditingController(text: widget.bomItem.description);
    _quantityController = TextEditingController(text: widget.bomItem.quantity.toString());
    _unitPriceController = TextEditingController(text: widget.bomItem.unitPrice.toString());
    _brandController = TextEditingController(text: widget.bomItem.brand ?? '');
    _modelController = TextEditingController(text: widget.bomItem.model ?? '');
    _specificationsController = TextEditingController(text: widget.bomItem.specifications ?? '');
    _supplierController = TextEditingController(text: widget.bomItem.supplier ?? '');
    _supplierUrlController = TextEditingController(text: widget.bomItem.supplierUrl ?? '');
    _notesController = TextEditingController(text: widget.bomItem.notes ?? '');
    // 确保分类在预定义列表中，否则使用默认值
    final bomCategory = widget.bomItem.category ?? '其他配件';
    _selectedCategory = _categories.contains(bomCategory) ? bomCategory : '其他配件';
    _selectedStatus = widget.bomItem.status;
    _plannedDate = widget.bomItem.plannedDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _specificationsController.dispose();
    _supplierController.dispose();
    _supplierUrlController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Icon(
                  Icons.edit,
                  color: Colors.indigo,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  '编辑BOM项目',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildQuantityPriceSection(),
                      const SizedBox(height: 24),
                      _buildStatusPlanningSection(),
                      const SizedBox(height: 24),
                      _buildSupplierSection(),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息部分
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 物料名称
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: '物料名称 *',
            hintText: '例如：锂电池、水泵、LED灯带',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.inventory_2),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入物料名称';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // 分类选择
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: '物料分类 *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCategory = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // 描述
        TextFormField(
          controller: _descriptionController,
          maxLines: 2,
          decoration: const InputDecoration(
            labelText: '描述',
            hintText: '详细描述物料的用途和特点...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
        ),
        const SizedBox(height: 16),
        
        // 品牌和型号
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _brandController,
                decoration: const InputDecoration(
                  labelText: '品牌',
                  hintText: '例如：比亚迪、格力',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: '型号',
                  hintText: '例如：BYD-100AH',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 规格
        TextFormField(
          controller: _specificationsController,
          maxLines: 2,
          decoration: const InputDecoration(
            labelText: '规格描述',
            hintText: '详细描述物料的技术规格、尺寸、性能参数等',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.settings),
          ),
        ),
      ],
    );
  }

  /// 构建数量价格部分
  Widget _buildQuantityPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '数量和价格',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            // 数量
            Expanded(
              child: TextFormField(
                controller: _quantityController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: '数量 *',
                  hintText: '1',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.numbers),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入数量';
                  }
                  final quantity = int.tryParse(value.trim());
                  if (quantity == null || quantity <= 0) {
                    return '请输入有效的数量';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            // 单价
            Expanded(
              child: TextFormField(
                controller: _unitPriceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: '单价（元）*',
                  hintText: '299.99',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入单价';
                  }
                  final price = double.tryParse(value.trim());
                  if (price == null || price < 0) {
                    return '请输入有效的价格';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 总价显示
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.indigo.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.indigo.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.calculate, color: Colors.indigo.shade700),
              const SizedBox(width: 8),
              Text(
                '总价：',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.indigo.shade700,
                ),
              ),
              Text(
                _calculateTotalPrice(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade700,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建状态和计划部分
  Widget _buildStatusPlanningSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '状态和计划',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 状态选择
        DropdownButtonFormField<domain.BomItemStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: '状态 *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.flag),
          ),
          items: _statusOptions.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(_getStatusDisplayName(status)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedStatus = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // 计划日期
        InkWell(
          onTap: _selectPlannedDate,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: '计划日期',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.calendar_today),
            ),
            child: Text(
              _plannedDate != null
                  ? '${_plannedDate!.year}-${_plannedDate!.month.toString().padLeft(2, '0')}-${_plannedDate!.day.toString().padLeft(2, '0')}'
                  : '选择计划日期',
              style: TextStyle(
                color: _plannedDate != null ? Colors.black87 : Colors.grey[600],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建供应商部分
  Widget _buildSupplierSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '供应商信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 供应商
        TextFormField(
          controller: _supplierController,
          decoration: const InputDecoration(
            labelText: '供应商',
            hintText: '例如：京东、淘宝、线下门店',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.store),
          ),
        ),
        const SizedBox(height: 16),
        
        // 供应商链接
        TextFormField(
          controller: _supplierUrlController,
          decoration: const InputDecoration(
            labelText: '供应商链接',
            hintText: 'https://...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.link),
          ),
        ),
        const SizedBox(height: 16),
        
        // 备注
        TextFormField(
          controller: _notesController,
          maxLines: 2,
          decoration: const InputDecoration(
            labelText: '备注',
            hintText: '其他需要记录的信息...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _updateBomItem,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('保存修改'),
          ),
        ),
      ],
    );
  }

  /// 计算总价 - 通过Provider处理业务逻辑
  String _calculateTotalPrice() {
    final calculationService = ref.read(bomCalculationServiceProvider.notifier);
    final quantity = calculationService.parseQuantity(_quantityController.text);
    final unitPrice = calculationService.parsePrice(_unitPriceController.text);
    return calculationService.calculateTotalPrice(quantity, unitPrice);
  }

  /// 获取状态显示名称 - 通过Provider处理业务逻辑
  String _getStatusDisplayName(domain.BomItemStatus status) {
    final statusService = ref.read(bomStatusDisplayServiceProvider.notifier);
    return statusService.getStatusDisplayName(status);
  }

  /// 选择计划日期
  Future<void> _selectPlannedDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _plannedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _plannedDate = date;
      });
    }
  }

  /// 更新BOM项目 - 严格遵循Clean Architecture原则
  Future<void> _updateBomItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 构建UpdateBomItemRequest - Domain层实体
      final request = UpdateBomItemRequest(
        id: widget.bomItem.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        quantity: int.parse(_quantityController.text.trim()),
        unitPrice: double.parse(_unitPriceController.text.trim()),
        status: _selectedStatus,
        brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
        model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
        specifications: _specificationsController.text.trim().isEmpty ? null : _specificationsController.text.trim(),
        supplier: _supplierController.text.trim().isEmpty ? null : _supplierController.text.trim(),
        supplierUrl: _supplierUrlController.text.trim().isEmpty ? null : _supplierUrlController.text.trim(),
        plannedDate: _plannedDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      // 通过Provider调用Domain层UseCase
      final result = await ref.read(bomControllerProvider.notifier).updateBomItem(request);

      if (mounted) {
        result.fold(
          (failure) {
            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('更新BOM项目失败: ${failure.message}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: _updateBomItem,
                ),
              ),
            );
          },
          (bomItem) {
            // 更新成功
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('BOM项目 "${bomItem.name}" 更新成功！'),
                backgroundColor: Colors.green,
              ),
            );
            
            // 关闭对话框
            Navigator.of(context).pop(bomItem);
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新BOM项目时发生错误: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
