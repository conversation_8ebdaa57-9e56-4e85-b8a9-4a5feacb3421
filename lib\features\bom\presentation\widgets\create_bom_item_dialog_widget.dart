import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/bom_provider.dart';

/// VanHub手动创建BOM项对话框
class CreateBomItemDialogWidget extends ConsumerStatefulWidget {
  final String projectId;

  const CreateBomItemDialogWidget({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<CreateBomItemDialogWidget> createState() => _CreateBomItemDialogWidgetState();
}

class _CreateBomItemDialogWidgetState extends ConsumerState<CreateBomItemDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  final _unitPriceController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _specificationsController = TextEditingController();
  final _supplierController = TextEditingController();
  final _supplierUrlController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedCategory = '电力系统';
  DateTime? _plannedDate;
  bool _isLoading = false;
  
  // 房车改装专业分类
  final List<String> _categories = [
    '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', 
    '车顶改装', '底盘改装', '其他配件'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _specificationsController.dispose();
    _supplierController.dispose();
    _supplierUrlController.dispose();
    _imageUrlController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 500,
        height: 700,
        child: Column(
          children: [
            // 头部区域
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.indigo, Colors.indigo.shade700],
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.add_box, color: Colors.white, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '手动添加BOM项',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '创建自定义物料项目',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 表单区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 基本信息
                      const Text(
                        '基本信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // 物料名称
                      TextFormField(
                        controller: _nameController,
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        decoration: const InputDecoration(
                          labelText: '物料名称 *',
                          hintText: '例如：12V锂电池',
                          prefixIcon: Icon(Icons.inventory),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入物料名称';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 分类选择
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: '分类 *',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 描述
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: '描述说明',
                          hintText: '物料的详细描述...',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // 数量和价格
                      const Text(
                        '数量和价格',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      Row(
                        children: [
                          // 数量
                          Expanded(
                            child: TextFormField(
                              controller: _quantityController,
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              decoration: const InputDecoration(
                                labelText: '数量 *',
                                prefixIcon: Icon(Icons.numbers),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入数量';
                                }
                                final quantity = int.tryParse(value);
                                if (quantity == null || quantity <= 0) {
                                  return '请输入有效数量';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 单价
                          Expanded(
                            child: TextFormField(
                              controller: _unitPriceController,
                              decoration: const InputDecoration(
                                labelText: '单价 (¥)',
                                prefixIcon: Icon(Icons.attach_money),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // 产品信息
                      const Text(
                        '产品信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      Row(
                        children: [
                          // 品牌
                          Expanded(
                            child: TextFormField(
                              controller: _brandController,
                              decoration: const InputDecoration(
                                labelText: '品牌',
                                prefixIcon: Icon(Icons.branding_watermark),
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 型号
                          Expanded(
                            child: TextFormField(
                              controller: _modelController,
                              decoration: const InputDecoration(
                                labelText: '型号',
                                prefixIcon: Icon(Icons.model_training),
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 规格参数
                      TextFormField(
                        controller: _specificationsController,
                        decoration: const InputDecoration(
                          labelText: '规格参数',
                          hintText: '尺寸、功率、容量等技术参数...',
                          prefixIcon: Icon(Icons.settings),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // 供应商信息
                      const Text(
                        '供应商信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // 供应商
                      TextFormField(
                        controller: _supplierController,
                        decoration: const InputDecoration(
                          labelText: '供应商',
                          prefixIcon: Icon(Icons.store),
                          border: OutlineInputBorder(),
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 供应商链接
                      TextFormField(
                        controller: _supplierUrlController,
                        decoration: const InputDecoration(
                          labelText: '购买链接',
                          hintText: 'https://...',
                          prefixIcon: Icon(Icons.link),
                          border: OutlineInputBorder(),
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 图片链接
                      TextFormField(
                        controller: _imageUrlController,
                        decoration: const InputDecoration(
                          labelText: '图片链接',
                          hintText: 'https://...',
                          prefixIcon: Icon(Icons.image),
                          border: OutlineInputBorder(),
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // 计划和备注
                      const Text(
                        '计划和备注',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // 计划日期
                      InkWell(
                        onTap: _selectPlannedDate,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[400]!),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _plannedDate != null
                                      ? '计划日期: ${_plannedDate!.year}-${_plannedDate!.month.toString().padLeft(2, '0')}-${_plannedDate!.day.toString().padLeft(2, '0')}'
                                      : '选择计划日期 (可选)',
                                  style: TextStyle(
                                    color: _plannedDate != null ? Colors.black87 : Colors.grey[600],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 备注
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: '备注说明',
                          hintText: '安装注意事项、使用说明等...',
                          prefixIcon: Icon(Icons.note),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // 创建按钮
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : () async {
                            debugPrint('=== 按钮点击事件触发 ===');
                            debugPrint('_isLoading状态: $_isLoading');
                            debugPrint('物料名称: "${_nameController.text}"');
                            debugPrint('数量: "${_quantityController.text}"');
                            debugPrint('单价: "${_unitPriceController.text}"');

                            // 简化的创建逻辑用于测试
                            if (_nameController.text.trim().isEmpty) {
                              debugPrint('物料名称为空，停止创建');
                              return;
                            }

                            if (_quantityController.text.trim().isEmpty) {
                              debugPrint('数量为空，停止创建');
                              return;
                            }

                            debugPrint('开始调用_createBomItem方法');
                            await _createBomItem();
                            debugPrint('_createBomItem方法调用完成');
                          },
                          icon: _isLoading 
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.add),
                          label: Text(_isLoading ? '创建中...' : '创建BOM项'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.indigo,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectPlannedDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _plannedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.indigo,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      setState(() {
        _plannedDate = date;
      });
    }
  }

  Future<void> _createBomItem() async {
    // 防止重复调用
    if (_isLoading) {
      debugPrint('正在创建BOM项，忽略重复调用');
      return;
    }

    // 表单验证
    if (!_formKey.currentState!.validate()) {
      debugPrint('表单验证失败，停止创建BOM项');
      return;
    }

    // 设置UI加载状态
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // 调用BomController - 严格遵循Clean Architecture
      final result = await ref.read(bomControllerProvider.notifier).createBomItem(
        widget.projectId,
        _nameController.text.trim(),
        _descriptionController.text.trim().isEmpty
            ? ''
            : _descriptionController.text.trim(),
        int.parse(_quantityController.text),
        _unitPriceController.text.trim().isEmpty
            ? 0.0
            : double.parse(_unitPriceController.text),
        category: _selectedCategory,
        brand: _brandController.text.trim().isEmpty
            ? null
            : _brandController.text.trim(),
        model: _modelController.text.trim().isEmpty
            ? null
            : _modelController.text.trim(),
        specifications: _specificationsController.text.trim().isEmpty
            ? null
            : _specificationsController.text.trim(),
        supplier: _supplierController.text.trim().isEmpty
            ? null
            : _supplierController.text.trim(),
        supplierUrl: _supplierUrlController.text.trim().isEmpty
            ? null
            : _supplierUrlController.text.trim(),
        imageUrl: _imageUrlController.text.trim().isEmpty
            ? null
            : _imageUrlController.text.trim(),
        plannedDate: _plannedDate,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      // 处理结果 - 使用Either模式
      if (!mounted) return;

      result.fold(
        (failure) => _showError(failure.message),
        (bomItem) {
          _showSuccess('BOM项创建成功！');
          // 延迟关闭对话框
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        },
      );
    } catch (e) {
      if (mounted) {
        _showError('创建失败: $e');
      }
    } finally {
      // 重置UI加载状态
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}