import 'package:flutter/material.dart';
import '../../../foundation/colors/colors.dart';
import '../../../foundation/spacing/spacing.dart';
import '../../../foundation/typography/typography.dart';
import '../../atoms/accessibility/vanhub_semantics.dart';

/// 导航栏项目数据类
class VanHubNavigationItem {
  /// 图标
  final IconData icon;
  
  /// 选中时的图标
  final IconData? selectedIcon;
  
  /// 标签
  final String label;
  
  /// 语义标签
  final String? semanticLabel;
  
  /// 徽章文本
  final String? badge;
  
  /// 是否启用
  final bool enabled;

  const VanHubNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.semanticLabel,
    this.badge,
    this.enabled = true,
  });
}

/// VanHub无障碍导航栏组件
/// 提供符合无障碍标准的底部导航栏，支持屏幕阅读器和键盘导航
class VanHubAccessibleNavigationBar extends StatelessWidget {
  /// 导航项目列表
  final List<VanHubNavigationItem> items;
  
  /// 当前选中的索引
  final int currentIndex;
  
  /// 选中回调
  final ValueChanged<int>? onTap;
  
  /// 导航栏类型
  final BottomNavigationBarType? type;
  
  /// 背景色
  final Color? backgroundColor;
  
  /// 选中项颜色
  final Color? selectedItemColor;
  
  /// 未选中项颜色
  final Color? unselectedItemColor;
  
  /// 选中项标签样式
  final TextStyle? selectedLabelStyle;
  
  /// 未选中项标签样式
  final TextStyle? unselectedLabelStyle;
  
  /// 图标大小
  final double? iconSize;
  
  /// 是否显示标签
  final bool showLabels;
  
  /// 是否显示未选中项标签
  final bool showUnselectedLabels;
  
  /// 导航栏高度
  final double? height;
  
  /// 导航栏边距
  final EdgeInsets? margin;
  
  /// 导航栏内边距
  final EdgeInsets? padding;
  
  /// 导航栏阴影
  final List<BoxShadow>? boxShadow;
  
  /// 导航栏圆角
  final BorderRadius? borderRadius;

  const VanHubAccessibleNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    this.type,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.selectedLabelStyle,
    this.unselectedLabelStyle,
    this.iconSize,
    this.showLabels = true,
    this.showUnselectedLabels = true,
    this.height,
    this.margin,
    this.padding,
    this.boxShadow,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 80,
      margin: margin,
      padding: padding ?? VanHubSpacing.allSm,
      decoration: BoxDecoration(
        color: backgroundColor ?? VanHubColors.surface,
        borderRadius: borderRadius,
        boxShadow: boxShadow ?? [
          BoxShadow(
            color: VanHubColors.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: VanHubSemantics(
        label: '导航栏',
        hint: '使用左右箭头键在导航项之间移动，按回车键选择',
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return _buildNavigationItem(context, item, index);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildNavigationItem(
    BuildContext context,
    VanHubNavigationItem item,
    int index,
  ) {
    final isSelected = index == currentIndex;
    final isEnabled = item.enabled && onTap != null;
    
    final effectiveSelectedColor = selectedItemColor ?? VanHubColors.primary;
    final effectiveUnselectedColor = unselectedItemColor ?? VanHubColors.onSurfaceVariant;
    
    final itemColor = isSelected ? effectiveSelectedColor : effectiveUnselectedColor;
    final itemOpacity = isEnabled ? 1.0 : 0.5;

    return Expanded(
      child: VanHubSemantics.button(
        label: item.semanticLabel ?? item.label,
        hint: isSelected ? '当前选中的导航项' : '点击切换到${item.label}',
        enabled: isEnabled,
        onTap: isEnabled ? () => onTap!(index) : null,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isEnabled ? () => onTap!(index) : null,
            borderRadius: BorderRadius.circular(8),
            child: Opacity(
              opacity: itemOpacity,
              child: Container(
                padding: VanHubSpacing.allSm,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildIcon(item, isSelected, itemColor),
                    if (showLabels && (showUnselectedLabels || isSelected)) ...[
                      const SizedBox(height: 4),
                      _buildLabel(context, item, isSelected, itemColor),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(VanHubNavigationItem item, bool isSelected, Color color) {
    final effectiveIcon = isSelected && item.selectedIcon != null
        ? item.selectedIcon!
        : item.icon;

    Widget iconWidget = Icon(
      effectiveIcon,
      size: iconSize ?? 24,
      color: color,
    );

    // 添加徽章
    if (item.badge != null) {
      iconWidget = Stack(
        clipBehavior: Clip.none,
        children: [
          iconWidget,
          Positioned(
            right: -6,
            top: -6,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: VanHubColors.error,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                item.badge!,
                style: VanHubTypography.labelSmall.copyWith(
                  color: VanHubColors.onError,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return iconWidget;
  }

  Widget _buildLabel(
    BuildContext context,
    VanHubNavigationItem item,
    bool isSelected,
    Color color,
  ) {
    final effectiveStyle = isSelected
        ? (selectedLabelStyle ?? VanHubTypography.labelSmall)
        : (unselectedLabelStyle ?? VanHubTypography.labelSmall);

    return Text(
      item.label,
      style: effectiveStyle.copyWith(color: color),
      textAlign: TextAlign.center,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}

/// VanHub无障碍导航栏工厂类
class VanHubAccessibleNavigationBarFactory {
  VanHubAccessibleNavigationBarFactory._();

  /// 创建标准导航栏
  static Widget standard({
    required List<VanHubNavigationItem> items,
    required int currentIndex,
    ValueChanged<int>? onTap,
  }) {
    return VanHubAccessibleNavigationBar(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
      type: BottomNavigationBarType.fixed,
    );
  }

  /// 创建浮动导航栏
  static Widget floating({
    required List<VanHubNavigationItem> items,
    required int currentIndex,
    ValueChanged<int>? onTap,
  }) {
    return VanHubAccessibleNavigationBar(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
      margin: VanHubSpacing.allMd,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: VanHubColors.shadow.withOpacity(0.2),
          blurRadius: 16,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  /// 创建最小化导航栏（仅图标）
  static Widget minimal({
    required List<VanHubNavigationItem> items,
    required int currentIndex,
    ValueChanged<int>? onTap,
  }) {
    return VanHubAccessibleNavigationBar(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
      showLabels: false,
      height: 60,
    );
  }

  /// 创建带徽章的导航栏
  static Widget withBadges({
    required List<VanHubNavigationItem> items,
    required int currentIndex,
    ValueChanged<int>? onTap,
  }) {
    return VanHubAccessibleNavigationBar(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
    );
  }
}
