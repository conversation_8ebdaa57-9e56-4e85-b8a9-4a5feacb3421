import 'package:flutter_test/flutter_test.dart';
import 'package:fpdart/fpdart.dart';
import 'package:mockito/mockito.dart';

import 'package:vanhub/core/errors/failures.dart';
import 'package:vanhub/features/material/domain/entities/material_review.dart';
import 'package:vanhub/features/material/domain/repositories/material_review_repository.dart';
import 'package:vanhub/features/material/domain/usecases/create_material_review_usecase.dart';

import '../../../mocks/material_review_repository_mock.dart';
void main() {
  late CreateMaterialReviewUseCase useCase;
  late MockMaterialReviewRepository mockRepository;

  setUp(() {
    mockRepository = MockMaterialReviewRepository();
    useCase = CreateMaterialReviewUseCase(mockRepository);
  });

  group('CreateMaterialReviewUseCase', () {
    final testReview = MaterialReview(
      id: 'test-review-1',
      materialId: 'test-material-1',
      userId: 'test-user-1',
      userName: 'Test User',
      content: 'This is a test review with sufficient content length.',
      rating: 4.5,
      qualityRating: 4.0,
      valueRating: 4.5,
      durabilityRating: 4.0,
      installationRating: 5.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create review successfully when user has not reviewed material before', () async {
      // arrange
      when(mockRepository.hasUserReviewedMaterial(any, any))
          .thenAnswer((_) async => const Right(false));
      when(mockRepository.createReview(any))
          .thenAnswer((_) async => Right(testReview));

      // act
      final result = await useCase.call(CreateMaterialReviewParams(review: testReview));

      // assert
      expect(result, isA<Right<Failure, MaterialReview>>());
      result.fold(
        (failure) => fail('Expected success but got failure: $failure'),
        (review) => expect(review, equals(testReview)),
      );
      
      verify(mockRepository.hasUserReviewedMaterial(testReview.materialId, testReview.userId));
      verify(mockRepository.createReview(testReview));
    });

    test('should return failure when user has already reviewed the material', () async {
      // arrange
      when(mockRepository.hasUserReviewedMaterial(any, any))
          .thenAnswer((_) async => const Right(true));

      // act
      final result = await useCase.call(CreateMaterialReviewParams(review: testReview));

      // assert
      expect(result, isA<Left<Failure, MaterialReview>>());
      result.fold(
        (failure) {
          expect(failure, isA<ValidationFailure>());
          expect(failure.message, contains('已经评价过该材料'));
        },
        (review) => fail('Expected failure but got success'),
      );
      
      verify(mockRepository.hasUserReviewedMaterial(testReview.materialId, testReview.userId));
      verifyNever(mockRepository.createReview(any));
    });

    test('should return failure when repository check fails', () async {
      // arrange
      when(mockRepository.hasUserReviewedMaterial(any, any))
          .thenAnswer((_) async => Left(ServerFailure(message: 'Database error')));

      // act
      final result = await useCase.call(CreateMaterialReviewParams(review: testReview));

      // assert
      expect(result, isA<Left<Failure, MaterialReview>>());
      result.fold(
        (failure) {
          expect(failure, isA<ServerFailure>());
          expect(failure.message, equals('Database error'));
        },
        (review) => fail('Expected failure but got success'),
      );
      
      verify(mockRepository.hasUserReviewedMaterial(testReview.materialId, testReview.userId));
      verifyNever(mockRepository.createReview(any));
    });

    group('validation tests', () {
      test('should return failure when rating is out of range', () async {
        // arrange
        final invalidReview = testReview.copyWith(rating: 6.0);
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('总体评分必须在1-5星之间'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });

      test('should return failure when content is too short', () async {
        // arrange
        final invalidReview = testReview.copyWith(content: 'Too short');
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('评价内容至少需要10个字符'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });

      test('should return failure when content is too long', () async {
        // arrange
        final longContent = 'a' * 2001;
        final invalidReview = testReview.copyWith(content: longContent);
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('评价内容不能超过2000个字符'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });

      test('should return failure when content is empty', () async {
        // arrange
        final invalidReview = testReview.copyWith(content: '');
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('评价内容不能为空'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });

      test('should return failure when pros list is too long', () async {
        // arrange
        final tooManyPros = List.generate(11, (index) => 'Pro $index');
        final invalidReview = testReview.copyWith(pros: tooManyPros);
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('优点最多只能添加10个'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });

      test('should return failure when image URLs list is too long', () async {
        // arrange
        final tooManyImages = List.generate(10, (index) => 'image$index.jpg');
        final invalidReview = testReview.copyWith(imageUrls: tooManyImages);
        when(mockRepository.hasUserReviewedMaterial(any, any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call(CreateMaterialReviewParams(review: invalidReview));

        // assert
        expect(result, isA<Left<Failure, MaterialReview>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('最多只能上传9张图片'));
          },
          (review) => fail('Expected failure but got success'),
        );
      });
    });

    test('should return failure when repository create fails', () async {
      // arrange
      when(mockRepository.hasUserReviewedMaterial(any, any))
          .thenAnswer((_) async => const Right(false));
      when(mockRepository.createReview(any))
          .thenAnswer((_) async => Left(ServerFailure(message: 'Create failed')));

      // act
      final result = await useCase.call(CreateMaterialReviewParams(review: testReview));

      // assert
      expect(result, isA<Left<Failure, MaterialReview>>());
      result.fold(
        (failure) {
          expect(failure, isA<ServerFailure>());
          expect(failure.message, equals('Create failed'));
        },
        (review) => fail('Expected failure but got success'),
      );
      
      verify(mockRepository.hasUserReviewedMaterial(testReview.materialId, testReview.userId));
      verify(mockRepository.createReview(testReview));
    });
  });
}
