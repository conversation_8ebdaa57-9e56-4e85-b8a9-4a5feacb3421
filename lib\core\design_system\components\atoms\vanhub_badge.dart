import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../vanhub_design_system.dart';

/// VanHub徽章组件
/// 
/// 支持数字徽章和点状徽章，多种颜色主题和位置
/// 遵循Material Design 3规范
class VanHubBadge extends StatelessWidget {
  /// 子组件（徽章将附加到此组件上）
  final Widget child;
  
  /// 徽章内容（数字或文本）
  final String? label;
  
  /// 是否显示徽章
  final bool isVisible;
  
  /// 徽章类型
  final VanHubBadgeType type;
  
  /// 徽章颜色主题
  final VanHubBadgeColor color;
  
  /// 徽章位置
  final VanHubBadgePosition position;
  
  /// 最大显示数值（超过显示+号）
  final int maxCount;
  
  /// 是否显示动画
  final bool showAnimation;
  
  /// 自定义偏移
  final Offset? offset;
  
  /// 点击回调
  final VoidCallback? onTap;

  const VanHubBadge({
    super.key,
    required this.child,
    this.label,
    this.isVisible = true,
    this.type = VanHubBadgeType.number,
    this.color = VanHubBadgeColor.primary,
    this.position = VanHubBadgePosition.topRight,
    this.maxCount = 99,
    this.showAnimation = true,
    this.offset,
    this.onTap,
  });

  /// 创建数字徽章的便捷构造函数
  VanHubBadge.count({
    super.key,
    required this.child,
    required int count,
    this.color = VanHubBadgeColor.primary,
    this.position = VanHubBadgePosition.topRight,
    this.maxCount = 99,
    this.showAnimation = true,
    this.offset,
    this.onTap,
  }) : label = count > maxCount ? '$maxCount+' : count.toString(),
       type = VanHubBadgeType.number,
       isVisible = count > 0;

  /// 创建点状徽章的便捷构造函数
  VanHubBadge.dot({
    super.key,
    required this.child,
    this.isVisible = true,
    this.color = VanHubBadgeColor.primary,
    this.position = VanHubBadgePosition.topRight,
    this.showAnimation = true,
    this.offset,
    this.onTap,
  }) : label = null,
       type = VanHubBadgeType.dot,
       maxCount = 99;

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return child;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        _buildBadge(context),
      ],
    );
  }

  Widget _buildBadge(BuildContext context) {
    Widget badge = _buildBadgeContent(context);
    
    // 添加动画效果
    if (showAnimation) {
      badge = badge.animate().scale(
        begin: const Offset(0.0, 0.0),
        end: const Offset(1.0, 1.0),
        duration: VanHubDesignSystem.durationBase,
        curve: VanHubDesignSystem.curveBounce,
      ).fadeIn(
        duration: VanHubDesignSystem.durationFast,
      );
    }
    
    // 添加点击效果
    if (onTap != null) {
      badge = GestureDetector(
        onTap: onTap,
        child: badge,
      );
    }
    
    return Positioned(
      top: _getTopPosition(),
      right: _getRightPosition(),
      bottom: _getBottomPosition(),
      left: _getLeftPosition(),
      child: badge,
    );
  }

  Widget _buildBadgeContent(BuildContext context) {
    final badgeColors = _getBadgeColors(context);
    
    if (type == VanHubBadgeType.dot) {
      return Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: badgeColors.backgroundColor,
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).colorScheme.surface,
            width: 1,
          ),
        ),
      );
    }
    
    return Container(
      padding: _getBadgePadding(),
      decoration: BoxDecoration(
        color: badgeColors.backgroundColor,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusFull),
        border: Border.all(
          color: Theme.of(context).colorScheme.surface,
          width: 1,
        ),
      ),
      constraints: const BoxConstraints(
        minWidth: 16,
        minHeight: 16,
      ),
      child: Text(
        label ?? '',
        style: TextStyle(
          color: badgeColors.textColor,
          fontSize: 10,
          fontWeight: VanHubDesignSystem.fontWeightMedium,
          height: 1.0,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  EdgeInsets _getBadgePadding() {
    if (label != null && label!.length > 1) {
      return const EdgeInsets.symmetric(horizontal: 6, vertical: 2);
    }
    return const EdgeInsets.all(2);
  }

  _BadgeColors _getBadgeColors(BuildContext context) {
    switch (color) {
      case VanHubBadgeColor.primary:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.brandPrimary,
          textColor: Colors.white,
        );
      case VanHubBadgeColor.secondary:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.brandSecondary,
          textColor: Colors.white,
        );
      case VanHubBadgeColor.success:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.semanticSuccess,
          textColor: Colors.white,
        );
      case VanHubBadgeColor.warning:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.semanticWarning,
          textColor: Colors.white,
        );
      case VanHubBadgeColor.error:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.semanticError,
          textColor: Colors.white,
        );
      case VanHubBadgeColor.neutral:
        return _BadgeColors(
          backgroundColor: VanHubDesignSystem.neutralGray500,
          textColor: Colors.white,
        );
    }
  }

  double? _getTopPosition() {
    final baseOffset = offset?.dy ?? 0;
    switch (position) {
      case VanHubBadgePosition.topLeft:
      case VanHubBadgePosition.topRight:
        return -4 + baseOffset;
      case VanHubBadgePosition.bottomLeft:
      case VanHubBadgePosition.bottomRight:
        return null;
    }
  }

  double? _getRightPosition() {
    final baseOffset = offset?.dx ?? 0;
    switch (position) {
      case VanHubBadgePosition.topRight:
      case VanHubBadgePosition.bottomRight:
        return -4 + baseOffset;
      case VanHubBadgePosition.topLeft:
      case VanHubBadgePosition.bottomLeft:
        return null;
    }
  }

  double? _getBottomPosition() {
    final baseOffset = offset?.dy ?? 0;
    switch (position) {
      case VanHubBadgePosition.bottomLeft:
      case VanHubBadgePosition.bottomRight:
        return -4 + baseOffset;
      case VanHubBadgePosition.topLeft:
      case VanHubBadgePosition.topRight:
        return null;
    }
  }

  double? _getLeftPosition() {
    final baseOffset = offset?.dx ?? 0;
    switch (position) {
      case VanHubBadgePosition.topLeft:
      case VanHubBadgePosition.bottomLeft:
        return -4 + baseOffset;
      case VanHubBadgePosition.topRight:
      case VanHubBadgePosition.bottomRight:
        return null;
    }
  }
}

/// 徽章类型枚举
enum VanHubBadgeType {
  number, // 数字徽章
  dot,    // 点状徽章
}

/// 徽章颜色主题枚举
enum VanHubBadgeColor {
  primary,   // 主色
  secondary, // 次色
  success,   // 成功
  warning,   // 警告
  error,     // 错误
  neutral,   // 中性
}

/// 徽章位置枚举
enum VanHubBadgePosition {
  topLeft,     // 左上角
  topRight,    // 右上角
  bottomLeft,  // 左下角
  bottomRight, // 右下角
}

/// 徽章颜色配置
class _BadgeColors {
  final Color backgroundColor;
  final Color textColor;

  const _BadgeColors({
    required this.backgroundColor,
    required this.textColor,
  });
}
