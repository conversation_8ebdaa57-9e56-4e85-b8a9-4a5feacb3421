import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/bom_tree_state.dart';
import '../../domain/entities/tree_node.dart';
import '../../../../core/di/injection_container.dart' hide bomControllerProvider; // 隐藏冲突的导入

/// BOM树形结构状态管理Provider
final bomTreeProvider = StateNotifierProvider<BomTreeNotifier, BomTreeState>((ref) {
  // 我们不需要使用bomController变量，直接返回BomTreeNotifier
  return BomTreeNotifier(ref);
});

class BomTreeNotifier extends StateNotifier<BomTreeState> {
  final Ref _ref;

  BomTreeNotifier(this._ref)
      : super(const BomTreeState(
          isLoading: false,
          errorMessage: null,
          tree: [],
          searchQuery: '',
          searchResults: [],
          isSearchActive: false,
          selectedNodeId: null,
          categoryStats: {},
          lastUpdated: null,
          totalBomItemCount: 0,
          filteredStatuses: [],
          priceRange: null,
          sortMode: null,
          showStatistics: true,
          isDragEnabled: false,
          draggingNodeId: null,
          dropTargetNodeId: null,
        ));

  /// 初始化BOM树形结构
  Future<void> initializeBomTree(String projectId) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      // 使用正确的Provider获取BOM项目列表
      final result = await _ref.read(bomRepositoryProvider).getProjectBomItems(projectId);

      result.fold(
        (failure) {
          state = state.copyWith(
            isLoading: false,
            errorMessage: failure.message,
          );
        },
        (bomItems) {
          final tree = _buildTree(bomItems);
          state = state.copyWith(
            isLoading: false,
            tree: tree,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: '加载BOM树形结构失败: ${e.toString()}',
      );
    }
  }

  /// 构建树形结构
  List<TreeNode> _buildTree(List<BomItem> bomItems) {
    // 创建根节点映射
    final Map<String, TreeNode> nodeMap = {};
    final List<TreeNode> rootNodes = [];

    // 第一遍：创建所有节点
    for (final item in bomItems) {
      final node = TreeNode(
        id: item.id,
        label: item.materialName,
        type: BomTreeNodeType.bomItem,
        bomItem: item,
        depth: 0,
        isExpanded: true,
        children: [], // 确保初始化children列表
      );
      nodeMap[item.id] = node;
    }

    // 第二遍：建立父子关系
    for (final item in bomItems) {
      // 检查是否有parentId属性，如果没有，则使用metadata中的parentId
      final parentId = item.metadata?['parentId'] as String?;
      
      if (parentId != null && nodeMap.containsKey(parentId)) {
        final parentNode = nodeMap[parentId]!;
        final childNode = nodeMap[item.id]!;
        
        // 更新子节点深度
        final newChildNode = childNode.copyWith(depth: parentNode.depth + 1);
        nodeMap[item.id] = newChildNode;
        
        // 添加到父节点的子节点列表
        final updatedChildren = [...parentNode.children, newChildNode];
        final updatedParentNode = parentNode.copyWith(children: updatedChildren);
        nodeMap[parentId] = updatedParentNode;
      } else {
        // 没有父节点的作为根节点
        rootNodes.add(nodeMap[item.id]!);
      }
    }

    // 按类别分组
    final Map<String, List<TreeNode>> categorizedNodes = {};
    for (final node in rootNodes) {
      final category = node.bomItem?.category ?? '未分类';
      if (!categorizedNodes.containsKey(category)) {
        categorizedNodes[category] = [];
      }
      categorizedNodes[category]!.add(node);
    }

    // 创建分类节点
    final List<TreeNode> result = [];
    categorizedNodes.forEach((category, nodes) {
      final categoryNode = TreeNode(
        id: 'category_$category',
        label: category,
        type: BomTreeNodeType.category,
        depth: 0,
        isExpanded: true,
        children: nodes,
      );
      
      // 更新分类下所有节点的深度
      final List<TreeNode> updatedChildren = [];
      for (final node in categoryNode.children) {
        updatedChildren.add(node.copyWith(depth: categoryNode.depth + 1));
      }
      
      result.add(categoryNode.copyWith(children: updatedChildren));
    });

    return result;
  }

  /// 选择节点
  void selectNode(String nodeId) {
    state = state.copyWith(selectedNodeId: nodeId);
  }

  /// 切换节点展开状态
  void toggleNodeExpansion(String nodeId) {
    final updatedTree = _toggleNodeExpansionInList(state.tree, nodeId);
    state = state.copyWith(tree: updatedTree);
  }

  /// 递归切换节点展开状态
  List<TreeNode> _toggleNodeExpansionInList(List<TreeNode> nodes, String nodeId) {
    return nodes.map((node) {
      if (node.id == nodeId) {
        return node.copyWith(isExpanded: !node.isExpanded);
      } else if (node.children.isNotEmpty) {
        return node.copyWith(
          children: _toggleNodeExpansionInList(node.children, nodeId),
        );
      }
      return node;
    }).toList();
  }

  /// 搜索BOM树
  void searchBomTree(String query) {
    if (query.isEmpty) {
      state = state.copyWith(
        isSearchActive: false,
        searchResults: [],
        searchQuery: '',
      );
      return;
    }

    final searchResults = <String>[];
    _searchInTree(state.tree, query.toLowerCase(), searchResults);

    state = state.copyWith(
      isSearchActive: true,
      searchResults: searchResults,
      searchQuery: query,
    );
  }

  /// 递归搜索树形结构
  void _searchInTree(
    List<TreeNode> nodes,
    String query,
    List<String> searchResults,
  ) {
    for (final node in nodes) {
      // 检查节点本身是否匹配
      if (node.label.toLowerCase().contains(query)) {
        searchResults.add(node.id);
      }
      
      // 检查BOM项目的详细信息是否匹配
      if (node.bomItem != null) {
        // 安全地获取各个属性
        final materialName = node.bomItem!.materialName.toLowerCase();
        final description = node.bomItem!.description.toLowerCase();
        final supplier = node.bomItem!.supplier?.toLowerCase();
        
        if (materialName.contains(query) ||
            description.contains(query) ||
            (supplier != null && supplier.contains(query))) {
          searchResults.add(node.id);
        }
      }
      
      // 递归搜索子节点
      if (node.children.isNotEmpty) {
        _searchInTree(node.children, query, searchResults);
      }
    }
  }

  /// 切换统计面板显示
  void toggleStatistics() {
    state = state.copyWith(showStatistics: !state.showStatistics);
  }

  /// 切换拖拽模式
  void toggleDragMode() {
    state = state.copyWith(isDragEnabled: !state.isDragEnabled);
  }

  /// 开始拖拽节点
  void startDragging(String nodeId) {
    state = state.copyWith(draggingNodeId: nodeId);
  }

  /// 结束拖拽
  void endDragging() {
    state = state.copyWith(draggingNodeId: null, dropTargetNodeId: null);
  }

  /// 切换拖拽启用状态
  void toggleDragEnabled() {
    state = state.copyWith(isDragEnabled: !state.isDragEnabled);
  }

  /// 设置放置目标节点
  void setDropTarget(String? nodeId) {
    state = state.copyWith(dropTargetNodeId: nodeId);
  }

  /// 移动节点
  Future<void> moveNode(String nodeId, String? targetNodeId) async {
    if (nodeId == targetNodeId || targetNodeId == null) {
      return;
    }

    // 实现节点移动逻辑
    // TODO: 调用API更新节点父子关系
    
    // 更新本地树形结构
    final updatedTree = _moveNodeInTree(
      state.tree,
      nodeId,
      targetNodeId,
    );
    
    state = state.copyWith(
      tree: updatedTree,
      draggingNodeId: null,
      dropTargetNodeId: null,
    );
  }

  /// 递归移动节点
  List<TreeNode> _moveNodeInTree(
    List<TreeNode> nodes,
    String nodeId,
    String targetNodeId,
  ) {
    // 实现节点移动的递归逻辑
    // TODO: 完成此方法
    return nodes;
  }
}