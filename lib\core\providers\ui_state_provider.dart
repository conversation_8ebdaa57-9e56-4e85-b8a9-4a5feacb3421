import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'ui_state_provider.g.dart';

/// UI状态数据类
class UiState {
  final bool isLoading;
  final String? error;
  final Map<String, dynamic> data;

  const UiState({
    this.isLoading = false,
    this.error,
    this.data = const {},
  });

  UiState copyWith({
    bool? isLoading,
    String? error,
    Map<String, dynamic>? data,
  }) {
    return UiState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      data: data ?? this.data,
    );
  }
}

/// 通用UI状态管理Provider
@riverpod
class UiStateNotifier extends _$UiStateNotifier {
  @override
  UiState build() {
    return const UiState();
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  void setData(String key, dynamic value) {
    final newData = Map<String, dynamic>.from(state.data);
    newData[key] = value;
    state = state.copyWith(data: newData);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const UiState();
  }
}

/// 页面级UI状态Provider工厂
@riverpod
class PageUiStateNotifier extends _$PageUiStateNotifier {
  @override
  UiState build(String pageId) {
    return const UiState();
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  void setData(String key, dynamic value) {
    final newData = Map<String, dynamic>.from(state.data);
    newData[key] = value;
    state = state.copyWith(data: newData);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const UiState();
  }
}