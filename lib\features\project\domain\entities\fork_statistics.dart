import 'package:freezed_annotation/freezed_annotation.dart';

part 'fork_statistics.freezed.dart';
part 'fork_statistics.g.dart';

@freezed
class ForkStatistics with _$ForkStatistics {
  const factory ForkStatistics({
    required String projectId,
    required int totalForks,
    required int activeForks,
    required int completedForks,
    required DateTime lastForkedAt,
    @Default([]) List<String> topForkers,
    @Default(0) int thisMonthForks,
    @Default(0) int thisWeekForks,
    @Default(0.0) double averageCompletionRate,
  }) = _ForkStatistics;

  factory ForkStatistics.fromJson(Map<String, dynamic> json) =>
      _$ForkStatisticsFromJson(json);
}

extension ForkStatisticsX on ForkStatistics {
  /// 是否有复刻记录
  bool get hasForks => totalForks > 0;
  
  /// 获取完成率百分比
  double get completionPercentage => 
      totalForks > 0 ? (completedForks / totalForks) * 100 : 0.0;
  
  /// 是否为热门项目
  bool get isPopular => totalForks >= 10;
  
  /// 是否为趋势项目
  bool get isTrending => thisWeekForks >= 3;
  
  /// 获取受欢迎程度描述
  String get popularityDescription {
    if (totalForks == 0) return '暂无复刻';
    if (totalForks < 5) return '小众项目';
    if (totalForks < 20) return '受欢迎项目';
    if (totalForks < 50) return '热门项目';
    return '超级热门项目';
  }
  
  /// 获取活跃度描述
  String get activityDescription {
    if (thisWeekForks == 0) return '本周无新复刻';
    if (thisWeekForks == 1) return '本周有1个新复刻';
    return '本周有$thisWeekForks个新复刻';
  }
}
