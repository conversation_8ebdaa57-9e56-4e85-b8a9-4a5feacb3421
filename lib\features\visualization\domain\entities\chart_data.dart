/// 可视化数据基类
abstract class VisualizationData {
  final String id;
  final String title;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  const VisualizationData({
    required this.id,
    required this.title,
    required this.createdAt,
    this.metadata = const {},
  });
}

/// 完整的可视化数据集合
class CompleteVisualizationData extends VisualizationData {
  final List<TimeSeriesDataPoint> costTrend;
  final List<ChartDataPoint> materialCategories;
  final List<ChartDataPoint> projectProgress;
  final List<ChartDataPoint> bomStatus;

  const CompleteVisualizationData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.costTrend,
    required this.materialCategories,
    required this.projectProgress,
    required this.bomStatus,
    super.metadata,
  });
}

/// 图表数据点
class ChartDataPoint {
  final String label;
  final double value;
  final String? category;
  final Map<String, dynamic>? metadata;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.category,
    this.metadata,
  });

  @override
  String toString() => 'ChartDataPoint(label: $label, value: $value)';
}

/// 时间序列数据点
class TimeSeriesDataPoint {
  final DateTime timestamp;
  final double value;
  final String? label;
  final Map<String, dynamic>? metadata;

  const TimeSeriesDataPoint({
    required this.timestamp,
    required this.value,
    this.label,
    this.metadata,
  });

  @override
  String toString() => 'TimeSeriesDataPoint(timestamp: $timestamp, value: $value)';
}

/// 材料分类数据
class MaterialCategoryData extends VisualizationData {
  final List<ChartDataPoint> categories;
  final double totalCost;

  const MaterialCategoryData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.categories,
    required this.totalCost,
    super.metadata,
  });

  /// 获取成本最高的分类
  ChartDataPoint? get topCostCategory {
    if (categories.isEmpty) return null;
    return categories.reduce((a, b) => a.value > b.value ? a : b);
  }

  /// 获取指定分类的成本占比
  double getCategoryPercentage(String category) {
    if (totalCost == 0) return 0;
    final categoryData = categories.where((c) => c.category == category);
    final categoryTotal = categoryData.fold(0.0, (sum, item) => sum + item.value);
    return (categoryTotal / totalCost) * 100;
  }
}

/// 成本趋势数据
class CostTrendData extends VisualizationData {
  final List<TimeSeriesDataPoint> dataPoints;
  final double totalCost;
  final double averageMonthlyCost;

  const CostTrendData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.dataPoints,
    required this.totalCost,
    required this.averageMonthlyCost,
    super.metadata,
  });

  /// 获取指定时间范围的数据点
  List<TimeSeriesDataPoint> getDataPointsInRange(DateTime start, DateTime end) {
    return dataPoints.where((point) => 
      point.timestamp.isAfter(start) && point.timestamp.isBefore(end)
    ).toList();
  }

  /// 计算成本增长率
  double getCostGrowthRate() {
    if (dataPoints.length < 2) return 0;
    final first = dataPoints.first.value;
    final last = dataPoints.last.value;
    if (first == 0) return 0;
    return ((last - first) / first) * 100;
  }
}

/// 项目进度数据
class ProjectProgressData extends VisualizationData {
  final double completionPercentage;
  final int totalTasks;
  final int completedTasks;
  final List<ChartDataPoint> milestones;

  const ProjectProgressData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.completionPercentage,
    required this.totalTasks,
    required this.completedTasks,
    required this.milestones,
    super.metadata,
  });

  /// 获取剩余任务数
  int get remainingTasks => totalTasks - completedTasks;

  /// 检查是否已完成
  bool get isCompleted => completionPercentage >= 100;

  /// 获取下一个里程碑
  ChartDataPoint? get nextMilestone {
    final incompleteMilestones = milestones.where((m) => m.value < 100);
    if (incompleteMilestones.isEmpty) return null;
    return incompleteMilestones.reduce((a, b) => a.value < b.value ? a : b);
  }
}

/// BOM状态数据
class BomStatusData extends VisualizationData {
  final Map<String, int> statusCounts;
  final int totalItems;

  const BomStatusData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.statusCounts,
    required this.totalItems,
    super.metadata,
  });

  /// 获取状态百分比
  double getStatusPercentage(String status) {
    if (totalItems == 0) return 0;
    final count = statusCounts[status] ?? 0;
    return (count / totalItems) * 100;
  }

  /// 获取完成率
  double get completionRate {
    final completedCount = statusCounts['completed'] ?? 0;
    return getStatusPercentage('completed');
  }

  /// 转换为图表数据点
  List<ChartDataPoint> toChartDataPoints() {
    return statusCounts.entries.map((entry) => 
      ChartDataPoint(
        label: _getStatusLabel(entry.key),
        value: entry.value.toDouble(),
        category: entry.key,
      )
    ).toList();
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  }
}

/// 预算分析数据
class BudgetAnalysisData extends VisualizationData {
  final double totalBudget;
  final double actualCost;
  final double remainingBudget;
  final List<ChartDataPoint> categoryBreakdown;

  const BudgetAnalysisData({
    required super.id,
    required super.title,
    required super.createdAt,
    required this.totalBudget,
    required this.actualCost,
    required this.remainingBudget,
    required this.categoryBreakdown,
    super.metadata,
  });

  /// 检查是否超预算
  bool get isOverBudget => actualCost > totalBudget;

  /// 获取预算使用率
  double get budgetUtilization {
    if (totalBudget == 0) return 0;
    return (actualCost / totalBudget) * 100;
  }

  /// 获取超支金额
  double get overrunAmount => isOverBudget ? actualCost - totalBudget : 0;

  /// 获取预算状态描述
  String get budgetStatusDescription {
    if (isOverBudget) {
      return '超支 ¥${overrunAmount.toStringAsFixed(0)}';
    } else {
      return '剩余 ¥${remainingBudget.toStringAsFixed(0)}';
    }
  }
}