import 'package:freezed_annotation/freezed_annotation.dart';

part 'fork_settings.freezed.dart';
part 'fork_settings.g.dart';

@freezed
class ForkSettings with _$ForkSettings {
  const factory ForkSettings({
    @Default(true) bool allowFork,
    @Default(true) bool copyBomItems,
    @Default(true) bool copySystems,
    @Default(false) bool copyImages,
    @Default(false) bool requireApproval,
  }) = _ForkSettings;

  factory ForkSettings.fromJson(Map<String, dynamic> json) =>
      _$ForkSettingsFromJson(json);
}