import 'package:flutter/material.dart';
import '../core/api/supabase_config.dart';
import '../core/utils/performance_monitor.dart';
import '../core/utils/error_handler.dart';
import '../core/utils/loading_manager.dart';
import '../core/utils/database_initializer.dart';
import '../core/errors/app_error.dart';
import '../features/project/presentation/pages/project_management_page.dart';
import 'material_library_page.dart';
import 'bom_management_page.dart';
import 'data_analytics_page.dart';


// 前向声明LoginPage类
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) => const SizedBox();
}

/// 安全的HomePage实现
class SafeHomePage extends StatefulWidget {
  const SafeHomePage({super.key});

  @override
  State<SafeHomePage> createState() => _SafeHomePageState();
}

class _SafeHomePageState extends State<SafeHomePage> {
  Map<String, dynamic>? connectionInfo;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  /// 初始化应用
  Future<void> _initializeApp() async {
    await _checkConnection();
    await _initializeDatabase();
  }

  /// 初始化数据库
  Future<void> _initializeDatabase() async {
    try {
      await DatabaseInitializer.initializeTables();
    } catch (e) {
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('数据库初始化失败', details: e.toString()),
          customMessage: '数据库初始化失败，部分功能可能无法使用',
          showSnackBar: true,
        );
      }
    }
  }

  /// 检查连接状态
  Future<void> _checkConnection() async {
    const loadingKey = 'connection_check';
    
    setState(() {
      isLoading = true;
    });

    try {
      globalLoadingManager.startLoading(loadingKey, message: '检查连接状态...');
      
      // 使用性能监控检查连接
      final info = await PerformanceMonitor.monitor('connection_check', () async {
        return await SupabaseConfig.getConnectionInfo();
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: '连接检查完成');
      
      if (mounted) {
        setState(() {
          connectionInfo = info;
          isLoading = false;
        });
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '连接检查失败');
      
      if (mounted) {
        setState(() {
          connectionInfo = {
            'error': e.toString(),
            'isConnected': false,
            'timestamp': DateTime.now().toIso8601String(),
          };
          isLoading = false;
        });

        // 使用错误处理器显示友好的错误信息
        ErrorHandler.handle(
          context,
          AppError.network('无法连接到服务器', details: e.toString()),
          customMessage: '连接检查失败，请检查网络连接',
          showSnackBar: false, // 不显示SnackBar，因为页面上已经有错误信息
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildSafeScaffold();
  }

  /// 安全的Scaffold构建
  Widget _buildSafeScaffold() {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('VanHub改装宝'),
      backgroundColor: Colors.deepOrange,
      foregroundColor: Colors.white,
      actions: _buildAppBarActions(),
    );
  }

  /// 构建AppBar操作按钮
  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _checkConnection,
        tooltip: '刷新连接状态',
      ),
      IconButton(
        icon: const Icon(Icons.logout),
        onPressed: _logout,
        tooltip: '退出登录',
      ),
    ];
  }

  /// 退出登录
  void _logout() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const LoginPage(),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 20),
          _buildQuickActionsCard(),
          const SizedBox(height: 20),
          _buildFeaturesGrid(),
          const SizedBox(height: 20),
          _buildConnectionCard(),
          const SizedBox(height: 20),
          _buildStatusCard(),
        ],
      ),
    );
  }

  /// 构建欢迎卡片
  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const Icon(
              Icons.directions_car,
              size: 80,
              color: Colors.deepOrange,
            ),
            const SizedBox(height: 16),
            const Text(
              '欢迎使用VanHub改装宝！',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '您的专业房车改装项目管理平台',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速操作卡片
  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.flash_on,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  '快速操作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.add,
                    label: '新建项目',
                    color: Colors.green,
                    onTap: () => _navigateToProjectManagement(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.inventory,
                    label: '材料库',
                    color: Colors.blue,
                    onTap: () => _navigateToMaterialLibrary(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.list_alt,
                    label: 'BOM清单',
                    color: Colors.purple,
                    onTap: () => _navigateToBOMManagement(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速操作按钮
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能网格
  Widget _buildFeaturesGrid() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.apps,
                  color: Colors.deepOrange,
                ),
                const SizedBox(width: 8),
                const Text(
                  '主要功能',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                _buildFeatureCard(
                  icon: Icons.folder_open,
                  title: '项目管理',
                  description: '创建和管理改装项目',
                  color: Colors.blue,
                  onTap: () => _navigateToProjectManagement(),
                ),
                _buildFeatureCard(
                  icon: Icons.inventory_2,
                  title: '材料库',
                  description: '管理改装材料和配件',
                  color: Colors.green,
                  onTap: () => _navigateToMaterialLibrary(),
                ),
                _buildFeatureCard(
                  icon: Icons.list_alt,
                  title: 'BOM清单',
                  description: '物料清单和成本管理',
                  color: Colors.purple,
                  onTap: () => _navigateToBOMManagement(),
                ),
                _buildFeatureCard(
                  icon: Icons.analytics,
                  title: '数据分析',
                  description: '项目数据可视化分析',
                  color: Colors.orange,
                  onTap: () => _navigateToDataAnalytics(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 40,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 导航到项目管理页面
  void _navigateToProjectManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProjectManagementPage(),
      ),
    );
  }

  /// 导航到材料库页面
  void _navigateToMaterialLibrary() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MaterialLibraryPage(),
      ),
    );
  }

  /// 导航到BOM管理页面
  void _navigateToBOMManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BOMManagementPage(),
      ),
    );
  }

  /// 导航到数据分析页面
  void _navigateToDataAnalytics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DataAnalyticsPage(),
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.construction,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Text('$feature功能即将推出，敬请期待！'),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 构建连接状态卡片
  Widget _buildConnectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConnectionHeader(),
            const SizedBox(height: 16),
            _buildConnectionContent(),
          ],
        ),
      ),
    );
  }

  /// 构建连接状态头部
  Widget _buildConnectionHeader() {
    return Row(
      children: [
        const Icon(
          Icons.cloud,
          color: Colors.blue,
        ),
        const SizedBox(width: 8),
        const Text(
          'Supabase连接状态',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (isLoading)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  /// 构建连接状态内容
  Widget _buildConnectionContent() {
    if (connectionInfo != null) {
      return _buildConnectionStatus();
    } else {
      return const Text('正在检查连接状态...');
    }
  }

  /// 构建连接状态详情
  Widget _buildConnectionStatus() {
    final info = connectionInfo!;
    final isConnected = info['isConnected'] == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              color: isConnected ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              isConnected ? '连接成功' : '连接失败',
              style: TextStyle(
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (info['url'] != null) ...[
          Text('URL: ${info['url']}'),
          const SizedBox(height: 4),
        ],
        if (info['checkDuration'] != null) ...[
          Text('检查耗时: ${info['checkDuration']}ms'),
          const SizedBox(height: 4),
        ],
        if (info['timestamp'] != null) ...[
          Text('检查时间: ${_formatTimestamp(info['timestamp'])}'),
          const SizedBox(height: 4),
        ],
        if (info['hasUser'] == true) ...[
          const Text('用户状态: 已登录'),
          if (info['userEmail'] != null)
            Text('邮箱: ${info['userEmail']}'),
          const SizedBox(height: 4),
        ] else ...[
          const Text('用户状态: 未登录'),
          const SizedBox(height: 4),
        ],
        if (info['error'] != null) ...[
          Text(
            '错误信息: ${info['error']}',
            style: const TextStyle(color: Colors.red),
          ),
        ],
      ],
    );
  }

  /// 构建项目状态卡片
  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                '项目状态',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '✅ 第一阶段：核心功能架构完成\n'
            '✅ 第二阶段：用户界面和基础功能完成\n'
            '✅ 第三阶段：高级功能和数据可视化完成\n'
            '✅ 第四阶段：性能优化和错误处理完成\n'
            '🚧 后续：社区功能、智能化功能开发中...',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 格式化时间戳
  String _formatTimestamp(String? timestamp) {
    if (timestamp == null) return '未知';
    
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inSeconds < 60) {
        return '${difference.inSeconds}秒前';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else {
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return timestamp;
    }
  }
}

// LoginPage将从main.dart导入
