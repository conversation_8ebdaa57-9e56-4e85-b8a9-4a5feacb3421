{"enabled": true, "name": "Feature Architecture Validator", "description": "Validates layered architecture dependencies when saving any feature file: Domain layer can only depend on dart:core and necessary annotation packages, Data layer can depend on Domain layer and external data source packages, Presentation layer can depend on Domain layer and Flutter packages but not directly on Data layer, detects circular dependencies, and validates import statements according to layered architecture principles", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/features/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "当保存任何feature文件时，验证分层架构的依赖关系：\n1. Domain层依赖检查 - 只能依赖dart:core和必要注解包\n2. Data层依赖检查 - 可以依赖Domain层和外部数据源包\n3. Presentation层依赖检查 - 可以依赖Domain层和Flutter包，不能直接依赖Data层\n4. 循环依赖检测 - 检测并报告任何循环依赖\n5. 导入语句验证 - 确保导入语句符合分层架构原则\n如果发现依赖违规：\n- 显示违规的导入语句\n- 解释正确的依赖方向\n- 提供重构建议\n\n请分析保存的文件并验证其架构依赖关系是否符合Clean Architecture原则。"}}