import 'package:freezed_annotation/freezed_annotation.dart';

part 'material_usage_history.freezed.dart';
part 'material_usage_history.g.dart';

/// 材料使用历史记录实体
/// 记录材料在项目中的详细使用情况
@freezed
class MaterialUsageHistory with _$MaterialUsageHistory {
  const factory MaterialUsageHistory({
    /// 历史记录ID
    required String id,
    
    /// 材料ID
    required String materialId,
    
    /// 项目ID
    required String projectId,
    
    /// BOM项目ID
    required String bomItemId,
    
    /// 用户ID
    required String userId,
    
    /// 使用数量
    required int quantity,
    
    /// 单价
    required double unitPrice,
    
    /// 总价
    required double totalPrice,
    
    /// 使用类型
    required UsageType usageType,
    
    /// 使用状态
    required UsageStatus status,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 项目名称（冗余字段，便于显示）
    String? projectName,
    
    /// 材料名称（冗余字段，便于显示）
    String? materialName,
    
    /// 使用日期（实际使用日期，可能与创建时间不同）
    DateTime? usageDate,
    
    /// 备注
    String? notes,
    
    /// 供应商信息
    String? supplier,
    
    /// 采购链接
    String? purchaseUrl,
    
    /// 扩展属性
    Map<String, dynamic>? metadata,
  }) = _MaterialUsageHistory;

  factory MaterialUsageHistory.fromJson(Map<String, dynamic> json) => 
      _$MaterialUsageHistoryFromJson(json);
}

/// 使用类型枚举
enum UsageType {
  @JsonValue('planned')
  planned,    // 计划使用
  
  @JsonValue('purchased')
  purchased,  // 已采购
  
  @JsonValue('used')
  used,       // 已使用
  
  @JsonValue('cancelled')
  cancelled,  // 已取消
  
  @JsonValue('returned')
  returned,   // 已退货
}

/// 使用状态枚举
enum UsageStatus {
  @JsonValue('active')
  active,     // 活跃
  
  @JsonValue('completed')
  completed,  // 已完成
  
  @JsonValue('cancelled')
  cancelled,  // 已取消
  
  @JsonValue('archived')
  archived,   // 已归档
}

/// 使用类型扩展
extension UsageTypeX on UsageType {
  String get displayName {
    switch (this) {
      case UsageType.planned:
        return '计划使用';
      case UsageType.purchased:
        return '已采购';
      case UsageType.used:
        return '已使用';
      case UsageType.cancelled:
        return '已取消';
      case UsageType.returned:
        return '已退货';
    }
  }
  
  String get description {
    switch (this) {
      case UsageType.planned:
        return '材料已添加到BOM，计划使用';
      case UsageType.purchased:
        return '材料已采购，等待使用';
      case UsageType.used:
        return '材料已在项目中使用';
      case UsageType.cancelled:
        return '取消使用此材料';
      case UsageType.returned:
        return '材料已退货或退回库存';
    }
  }
  
  bool get isCompleted {
    return this == UsageType.used || this == UsageType.cancelled;
  }
}

/// 使用状态扩展
extension UsageStatusX on UsageStatus {
  String get displayName {
    switch (this) {
      case UsageStatus.active:
        return '活跃';
      case UsageStatus.completed:
        return '已完成';
      case UsageStatus.cancelled:
        return '已取消';
      case UsageStatus.archived:
        return '已归档';
    }
  }
  
  bool get isActive {
    return this == UsageStatus.active;
  }
  
  bool get isFinal {
    return this == UsageStatus.completed || 
           this == UsageStatus.cancelled || 
           this == UsageStatus.archived;
  }
}

/// 材料使用历史扩展方法
extension MaterialUsageHistoryX on MaterialUsageHistory {
  /// 计算总价
  double get calculatedTotalPrice => quantity * unitPrice;
  
  /// 是否已完成
  bool get isCompleted => usageType.isCompleted && status.isFinal;
  
  /// 是否可编辑
  bool get isEditable => status.isActive && !usageType.isCompleted;
  
  /// 获取状态颜色
  String get statusColor {
    switch (status) {
      case UsageStatus.active:
        return '#2196F3'; // 蓝色
      case UsageStatus.completed:
        return '#4CAF50'; // 绿色
      case UsageStatus.cancelled:
        return '#F44336'; // 红色
      case UsageStatus.archived:
        return '#9E9E9E'; // 灰色
    }
  }
  
  /// 获取使用类型图标
  String get typeIcon {
    switch (usageType) {
      case UsageType.planned:
        return '📋';
      case UsageType.purchased:
        return '🛒';
      case UsageType.used:
        return '✅';
      case UsageType.cancelled:
        return '❌';
      case UsageType.returned:
        return '↩️';
    }
  }
  
  /// 创建副本并更新状态
  MaterialUsageHistory updateStatus(UsageStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }
  
  /// 创建副本并更新使用类型
  MaterialUsageHistory updateUsageType(UsageType newType) {
    return copyWith(
      usageType: newType,
      updatedAt: DateTime.now(),
      usageDate: newType == UsageType.used ? DateTime.now() : usageDate,
    );
  }
  
  /// 创建副本并更新数量和价格
  MaterialUsageHistory updateQuantityAndPrice({
    int? newQuantity,
    double? newUnitPrice,
  }) {
    final quantity = newQuantity ?? this.quantity;
    final unitPrice = newUnitPrice ?? this.unitPrice;
    
    return copyWith(
      quantity: quantity,
      unitPrice: unitPrice,
      totalPrice: quantity * unitPrice,
      updatedAt: DateTime.now(),
    );
  }
}
