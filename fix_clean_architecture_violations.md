# Clean Architecture违规修复指南

## 1. 修复非Consumer Widget

### BomTreeNodeWidget
```dart
// 修改 lib/features/bom/presentation/widgets/bom_tree_node_widget.dart
class BomTreeNodeWidget extends ConsumerWidget {
  final TreeNode node;
  final bool isSelected;
  // ... 其他属性

  const BomTreeNodeWidget({
    super.key,
    required this.node,
    this.isSelected = false,
    // ... 其他参数
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 原有的build逻辑
    return Container(/* 实现 */);
  }
}
```

### EnhancedBomTreeNodeWidget
```dart
// 修改 lib/features/bom/presentation/widgets/enhanced_bom_tree_node_widget.dart
class EnhancedBomTreeNodeWidget extends ConsumerStatefulWidget {
  // ... 属性保持不变

  @override
  ConsumerState<EnhancedBomTreeNodeWidget> createState() => 
      _EnhancedBomTreeNodeWidgetState();
}

class _EnhancedBomTreeNodeWidgetState extends ConsumerState<EnhancedBomTreeNodeWidget> {
  // 原有的State逻辑，但build方法签名改为：
  @override
  Widget build(BuildContext context) {
    // 可以使用 ref.watch() 和 ref.read()
    return Container(/* 实现 */);
  }
}
```

## 2. 修复分层依赖违规

### 创建Domain层导出服务接口
```dart
// 新建 lib/features/bom/domain/services/bom_export_service.dart
abstract class BomExportService {
  Future<File> exportToExcel({
    required List<BomItem> bomItems,
    required String projectName,
  });
  
  Future<File> exportToPdf({
    required List<BomItem> bomItems,
    required String projectName,
  });
}
```

### 移动实现到Data层
```dart
// 移动到 lib/features/bom/data/services/bom_export_service_impl.dart
class BomExportServiceImpl implements BomExportService {
  // 原有的实现逻辑
}
```

### 更新依赖注入
```dart
// 在 lib/core/di/injection_container.dart 中添加
final bomExportServiceProvider = Provider<BomExportService>((ref) {
  return BomExportServiceImpl();
});
```

### 更新Presentation层使用
```dart
// 在 lib/features/bom/presentation/pages/bom_management_page.dart 中
// 删除：import '../../data/services/bom_export_service.dart';
// 添加：import '../../domain/services/bom_export_service.dart';

// 在方法中使用：
final exportService = ref.read(bomExportServiceProvider);
final exportedFile = await exportService.exportToExcel(
  bomItems: bomItems,
  projectName: 'Project_${widget.projectId}',
);
```

## 3. 添加freezed到可视化实体

### 修改chart_data.dart
```dart
// 在文件顶部添加
import 'package:freezed_annotation/freezed_annotation.dart';

part 'chart_data.freezed.dart';
part 'chart_data.g.dart';

// 将每个类改为freezed格式
@freezed
class ChartDataPoint with _$ChartDataPoint {
  const factory ChartDataPoint({
    required String label,
    required double value,
    String? color,
    Map<String, dynamic>? metadata,
  }) = _ChartDataPoint;

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) =>
      _$ChartDataPointFromJson(json);
}

@freezed
class TimeSeriesDataPoint with _$TimeSeriesDataPoint {
  const factory TimeSeriesDataPoint({
    required DateTime timestamp,
    required double value,
    String? label,
    Map<String, dynamic>? metadata,
  }) = _TimeSeriesDataPoint;

  factory TimeSeriesDataPoint.fromJson(Map<String, dynamic> json) =>
      _$TimeSeriesDataPointFromJson(json);
}

// ... 对其他类进行类似修改
```

## 4. 运行代码生成
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 5. 验证修复
运行以下命令验证修复效果：
```bash
flutter analyze
flutter test
```