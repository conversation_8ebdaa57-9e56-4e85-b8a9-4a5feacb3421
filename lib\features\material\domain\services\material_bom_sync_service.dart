import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material.dart';
import '../entities/material_usage_history.dart';
import '../../../bom/domain/entities/bom_item.dart';

/// 材料库与BOM双向同步服务接口
/// 负责维护材料库和BOM之间的数据一致性
abstract class MaterialBomSyncService {
  
  /// 当材料信息更新时，同步到相关的BOM项目
  /// 
  /// [materialId] 更新的材料ID
  /// [updatedFields] 更新的字段映射
  /// [syncStrategy] 同步策略：'auto', 'prompt', 'skip'
  /// 
  /// 返回受影响的BOM项目列表
  Future<Either<Failure, List<BomItem>>> syncMaterialUpdateToBom({
    required String materialId,
    required Map<String, dynamic> updatedFields,
    String syncStrategy = 'prompt',
  });

  /// 当BOM项目状态变化时，更新材料统计
  /// 
  /// [bomItemId] BOM项目ID
  /// [oldStatus] 原状态
  /// [newStatus] 新状态
  /// [quantity] 使用数量
  /// 
  /// 返回更新后的材料信息
  Future<Either<Failure, Material?>> syncBomStatusToMaterial({
    required String bomItemId,
    required String oldStatus,
    required String newStatus,
    required int quantity,
  });

  /// 处理材料价格更新的同步
  /// 
  /// [materialId] 材料ID
  /// [oldPrice] 原价格
  /// [newPrice] 新价格
  /// [updateBomPrices] 是否更新BOM中的价格
  /// 
  /// 返回受影响的BOM项目数量
  Future<Either<Failure, int>> syncMaterialPriceUpdate({
    required String materialId,
    required double oldPrice,
    required double newPrice,
    bool updateBomPrices = false,
  });

  /// 批量同步使用统计
  /// 
  /// [projectId] 项目ID（可选，为空则同步所有项目）
  /// 
  /// 返回同步的材料数量
  Future<Either<Failure, int>> batchSyncUsageStats({
    String? projectId,
  });

  /// 获取材料的使用历史
  /// 
  /// [materialId] 材料ID
  /// [limit] 返回记录数限制
  /// 
  /// 返回使用历史列表
  Future<Either<Failure, List<MaterialUsageHistory>>> getMaterialUsageHistory({
    required String materialId,
    int limit = 50,
  });

  /// 获取与材料关联的BOM项目
  /// 
  /// [materialId] 材料ID
  /// [includeCompleted] 是否包含已完成的项目
  /// 
  /// 返回关联的BOM项目列表
  Future<Either<Failure, List<BomItem>>> getLinkedBomItems({
    required String materialId,
    bool includeCompleted = true,
  });

  /// 检查材料和BOM之间的数据一致性
  /// 
  /// [materialId] 材料ID
  /// 
  /// 返回不一致的字段列表
  Future<Either<Failure, List<String>>> checkDataConsistency({
    required String materialId,
  });

  /// 解决数据冲突
  /// 
  /// [materialId] 材料ID
  /// [conflictFields] 冲突字段
  /// [resolution] 解决方案：'use_material', 'use_bom', 'merge'
  /// 
  /// 返回解决结果
  Future<Either<Failure, bool>> resolveDataConflict({
    required String materialId,
    required List<String> conflictFields,
    required String resolution,
  });

  /// 创建使用历史记录
  /// 
  /// [materialId] 材料ID
  /// [projectId] 项目ID
  /// [bomItemId] BOM项目ID
  /// [quantity] 使用数量
  /// [unitPrice] 单价
  /// [usageType] 使用类型：'planned', 'purchased', 'used'
  /// 
  /// 返回创建的历史记录
  Future<Either<Failure, MaterialUsageHistory>> createUsageHistory({
    required String materialId,
    required String projectId,
    required String bomItemId,
    required int quantity,
    required double unitPrice,
    required String usageType,
  });

  /// 更新材料的最后使用时间和使用次数
  /// 
  /// [materialId] 材料ID
  /// [incrementUsage] 是否增加使用次数
  /// 
  /// 返回更新后的材料信息
  Future<Either<Failure, Material>> updateMaterialUsageStats({
    required String materialId,
    bool incrementUsage = true,
  });

  /// 获取材料使用趋势数据
  /// 
  /// [materialId] 材料ID
  /// [days] 统计天数
  /// 
  /// 返回趋势数据（日期 -> 使用次数）
  Future<Either<Failure, Map<DateTime, int>>> getMaterialUsageTrend({
    required String materialId,
    int days = 30,
  });

  /// 获取项目中最常用的材料
  /// 
  /// [projectId] 项目ID
  /// [limit] 返回数量限制
  /// 
  /// 返回材料使用统计列表
  Future<Either<Failure, List<MaterialUsageStats>>> getProjectTopMaterials({
    required String projectId,
    int limit = 10,
  });
}

/// 材料使用统计数据
class MaterialUsageStats {
  final Material material;
  final int totalQuantity;
  final double totalCost;
  final int usageCount;
  final DateTime lastUsedAt;

  const MaterialUsageStats({
    required this.material,
    required this.totalQuantity,
    required this.totalCost,
    required this.usageCount,
    required this.lastUsedAt,
  });
}

/// 同步策略枚举
enum SyncStrategy {
  auto,     // 自动同步
  prompt,   // 提示用户选择
  skip,     // 跳过同步
}

/// 冲突解决策略枚举
enum ConflictResolution {
  useMaterial,  // 使用材料库数据
  useBom,       // 使用BOM数据
  merge,        // 合并数据
  manual,       // 手动解决
}

// UsageType枚举已在material_usage_history.dart中定义
