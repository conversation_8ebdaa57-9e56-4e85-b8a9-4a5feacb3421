import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/create_project_request.dart';
import '../entities/project.dart';
import '../repositories/project_repository.dart';

class CreateProjectUseCase implements UseCase<Project, CreateProjectRequest> {
  final ProjectRepository repository;

  CreateProjectUseCase(this.repository);

  @override
  Future<Either<Failure, Project>> call(CreateProjectRequest params) async {
    return await repository.createProject(params);
  }
}