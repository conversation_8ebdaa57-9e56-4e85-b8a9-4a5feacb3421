import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../repositories/material_review_repository.dart';

class MarkReviewAsHelpfulUseCase {
  final MaterialReviewRepository repository;

  MarkReviewAsHelpfulUseCase(this.repository);

  Future<Either<Failure, void>> call(MarkReviewAsHelpfulParams params) async {
    if (params.isHelpful) {
      return await repository.markReviewAsHelpful(
        params.reviewId,
        params.userId,
      );
    } else {
      return await repository.unmarkReviewAsHelpful(
        params.reviewId,
        params.userId,
      );
    }
  }
}

class MarkReviewAsHelpfulParams {
  final String reviewId;
  final String userId;
  final bool isHelpful;

  MarkReviewAsHelpfulParams({
    required this.reviewId,
    required this.userId,
    required this.isHelpful,
  });
}