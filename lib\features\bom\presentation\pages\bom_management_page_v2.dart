/// VanHub BOM Management Page 2.0
/// 
/// 现代化BOM管理页面，使用新设计系统
/// 
/// 特性：
/// - 层次结构可视化
/// - 智能成本分析
/// - 实时库存状态
/// - 拖拽排序功能
/// - 数据可视化图表

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/components/vanhub_button_v2.dart';
import '../../../../core/design_system/components/vanhub_input_v2.dart';
import '../../../../core/design_system/components/vanhub_chart.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../domain/entities/bom_item.dart' as domain;
import '../providers/bom_provider.dart';
import '../widgets/create_bom_item_dialog_widget.dart';

/// BOM物料状态枚举
enum BomItemStatus {
  planning,   // 计划中
  purchased,  // 已采购
  used,       // 已使用
  completed,  // 已完成
}

/// BOM视图类型枚举
enum BomViewType {
  list,       // 列表视图
  tree,       // 树形视图
  kanban,     // 看板视图
  chart,      // 图表视图
}

/// VanHub BOM管理页面 2.0
class BomManagementPageV2 extends ConsumerStatefulWidget {
  final String projectId;

  const BomManagementPageV2({
    Key? key,
    required this.projectId,
  }) : super(key: key);

  @override
  ConsumerState<BomManagementPageV2> createState() => _BomManagementPageV2State();
}

class _BomManagementPageV2State extends ConsumerState<BomManagementPageV2>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late TabController _tabController;
  
  String _searchQuery = '';
  BomItemStatus? _selectedStatus;
  BomViewType _viewType = BomViewType.list;
  bool _showStatistics = true;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _tabController = TabController(length: 4, vsync: this);
    
    // 加载BOM数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(projectBomItemsProvider(widget.projectId));
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildToolbar(),
          if (_showStatistics) _buildStatisticsSection(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
      floatingActionButton: _buildAddItemFAB(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'BOM管理',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: VanHubBrandColors.onPrimary,
        ),
      ),
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          icon: Icon(_showStatistics ? Icons.analytics : Icons.analytics_outlined),
          onPressed: () {
            setState(() {
              _showStatistics = !_showStatistics;
            });
          },
          tooltip: '统计信息',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'export':
                _exportBom();
                break;
              case 'import':
                _importBom();
                break;
              case 'template':
                _showTemplateDialog();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 20),
                  SizedBox(width: 8),
                  Text('导出BOM'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload, size: 20),
                  SizedBox(width: 8),
                  Text('导入BOM'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'template',
              child: Row(
                children: [
                  Icon(Icons.description_outlined, size: 20),
                  SizedBox(width: 8),
                  Text('使用模板'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    return Container(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      decoration: BoxDecoration(
        color: VanHubSemanticColors.getBackgroundColor(context),
        border: Border(
          bottom: BorderSide(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 搜索和筛选
          Row(
            children: [
              Expanded(
                flex: 3,
                child: VanHubInputV2(
                  controller: _searchController,
                  hint: '搜索物料名称、规格或供应商...',
                  prefixIcon: const Icon(Icons.search),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              SizedBox(width: VanHubResponsiveSpacing.md),
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<BomItemStatus?>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: '状态筛选',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: VanHubResponsiveSpacing.md,
                      vertical: VanHubResponsiveSpacing.sm,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<BomItemStatus?>(
                      value: null,
                      child: Text('全部状态'),
                    ),
                    ...BomItemStatus.values.map((status) {
                      return DropdownMenuItem<BomItemStatus?>(
                        value: status,
                        child: Text(_getStatusText(status)),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  },
                ),
              ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          // 视图切换
          Row(
            children: [
              Text(
                '视图模式：',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: VanHubResponsiveSpacing.sm),
              SegmentedButton<BomViewType>(
                segments: [
                  ButtonSegment(
                    value: BomViewType.list,
                    icon: const Icon(Icons.list, size: 18),
                    label: const Text('列表'),
                  ),
                  ButtonSegment(
                    value: BomViewType.tree,
                    icon: const Icon(Icons.account_tree, size: 18),
                    label: const Text('树形'),
                  ),
                  ButtonSegment(
                    value: BomViewType.kanban,
                    icon: const Icon(Icons.view_kanban, size: 18),
                    label: const Text('看板'),
                  ),
                  ButtonSegment(
                    value: BomViewType.chart,
                    icon: const Icon(Icons.pie_chart, size: 18),
                    label: const Text('图表'),
                  ),
                ],
                selected: {_viewType},
                onSelectionChanged: (Set<BomViewType> selection) {
                  setState(() {
                    _viewType = selection.first;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计信息区域
  Widget _buildStatisticsSection() {
    return Container(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      child: VanHubCardV2(
        variant: VanHubCardVariant.filled,
        size: VanHubCardSize.sm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: VanHubBrandColors.primary,
                  size: 20,
                ),
                SizedBox(width: VanHubResponsiveSpacing.sm),
                Text(
                  'BOM统计概览',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, size: 18),
                  onPressed: () {
                    setState(() {
                      _showStatistics = false;
                    });
                  },
                ),
              ],
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            _buildStatisticsGrid(),
          ],
        ),
      ),
    );
  }

  /// 构建统计网格
  Widget _buildStatisticsGrid() {
    return Row(
      children: [
        Expanded(child: _buildStatCard('总物料', '156', '项', VanHubSemanticColors.info)),
        SizedBox(width: VanHubResponsiveSpacing.md),
        Expanded(child: _buildStatCard('总成本', '¥32,580', '', VanHubSemanticColors.warning)),
        SizedBox(width: VanHubResponsiveSpacing.md),
        Expanded(child: _buildStatCard('已采购', '89', '项', VanHubSemanticColors.success)),
        SizedBox(width: VanHubResponsiveSpacing.md),
        Expanded(child: _buildStatCard('待采购', '67', '项', VanHubSemanticColors.error)),
      ],
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(String title, String value, String unit, Color color) {
    return Container(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.xs),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (unit.isNotEmpty) ...[
                SizedBox(width: VanHubResponsiveSpacing.xs),
                Text(
                  unit,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(widget.projectId));
    
    return bomItemsAsync.when(
      data: (bomItems) {
        final filteredItems = _filterBomItems(bomItems);
        
        if (filteredItems.isEmpty) {
          return _buildEmptyState();
        }
        
        switch (_viewType) {
          case BomViewType.list:
            return _buildListView(filteredItems);
          case BomViewType.tree:
            return _buildTreeView(filteredItems);
          case BomViewType.kanban:
            return _buildKanbanView(filteredItems);
          case BomViewType.chart:
            return _buildChartView(filteredItems);
        }
      },
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  /// 构建列表视图
  Widget _buildListView(List<domain.BomItem> items) {
    return ListView.separated(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      itemCount: items.length,
      separatorBuilder: (context, index) => SizedBox(height: VanHubResponsiveSpacing.md),
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildBomItemCard(item, index);
      },
    );
  }

  /// 构建树形视图
  Widget _buildTreeView(List<domain.BomItem> items) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      child: VanHubCardV2.outlined(
        size: VanHubCardSize.lg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'BOM层次结构',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            // TODO: 实现树形结构组件
            Text('树形视图开发中...'),
          ],
        ),
      ),
    );
  }

  /// 构建看板视图
  Widget _buildKanbanView(List<domain.BomItem> items) {
    final statusGroups = <BomItemStatus, List<domain.BomItem>>{};
    
    // 按状态分组
    for (final status in BomItemStatus.values) {
      statusGroups[status] = items.where((item) {
        return item.status == _mapToEntityStatus(status);
      }).toList();
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: BomItemStatus.values.map((status) {
          final statusItems = statusGroups[status] ?? [];
          return _buildKanbanColumn(status, statusItems);
        }).toList(),
      ),
    );
  }

  /// 构建图表视图
  Widget _buildChartView(List<domain.BomItem> items) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      child: Column(
        children: [
          // 成本分布饼图
          VanHubCardV2.outlined(
            size: VanHubCardSize.lg,
            child: VanHubChart.pie(
              title: '成本分布',
              series: [
                ChartDataSeries(
                  name: '成本分布',
                  color: VanHubBrandColors.primary,
                  data: [
                    ChartDataPoint(x: 1, y: 15000, label: '电力系统', color: VanHubBrandColors.primary),
                    ChartDataPoint(x: 2, y: 8000, label: '水路系统', color: VanHubSemanticColors.success),
                    ChartDataPoint(x: 3, y: 5000, label: '内饰改装', color: VanHubSemanticColors.warning),
                    ChartDataPoint(x: 4, y: 4580, label: '其他', color: VanHubSemanticColors.info),
                  ],
                ),
              ],
            ),
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.lg),
          
          // 采购进度柱状图
          VanHubCardV2.outlined(
            size: VanHubCardSize.lg,
            child: VanHubChart.bar(
              title: '采购进度',
              series: [
                ChartDataSeries(
                  name: '已采购',
                  color: VanHubSemanticColors.success,
                  data: [
                    ChartDataPoint(x: 1, y: 25),
                    ChartDataPoint(x: 2, y: 18),
                    ChartDataPoint(x: 3, y: 32),
                    ChartDataPoint(x: 4, y: 14),
                  ],
                ),
                ChartDataSeries(
                  name: '待采购',
                  color: VanHubSemanticColors.warning,
                  data: [
                    ChartDataPoint(x: 1, y: 15),
                    ChartDataPoint(x: 2, y: 22),
                    ChartDataPoint(x: 3, y: 8),
                    ChartDataPoint(x: 4, y: 22),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建看板列
  Widget _buildKanbanColumn(BomItemStatus status, List<domain.BomItem> items) {
    return Container(
      width: 280,
      margin: EdgeInsets.only(right: VanHubResponsiveSpacing.md),
      child: VanHubCardV2.outlined(
        size: VanHubCardSize.sm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 列标题
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                SizedBox(width: VanHubResponsiveSpacing.sm),
                Text(
                  _getStatusText(status),
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${items.length}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(status),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 物料卡片列表
            ...items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return Container(
                margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
                child: _buildKanbanItemCard(item, index),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建BOM物料卡片
  Widget _buildBomItemCard(domain.BomItem item, int index) {
    return VanHubCardV2.interactive(
      size: VanHubCardSize.md,
      onTap: () {
        // TODO: 导航到物料详情
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 物料基本信息
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (item.specification?.isNotEmpty == true) ...[
                      SizedBox(height: VanHubResponsiveSpacing.xs),
                      Text(
                        item.specification!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: VanHubSemanticColors.getTextColor(context, secondary: true),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(_mapFromEntityStatus(item.status)).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getStatusColor(_mapFromEntityStatus(item.status)).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getStatusText(_mapFromEntityStatus(item.status)),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getStatusColor(_mapFromEntityStatus(item.status)),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          // 数量和价格信息
          Row(
            children: [
              _buildInfoItem('数量', '${item.quantity} 个'),
              SizedBox(width: VanHubResponsiveSpacing.lg),
              _buildInfoItem('单价', '¥${item.unitPrice?.toStringAsFixed(2) ?? '0.00'}'),
              SizedBox(width: VanHubResponsiveSpacing.lg),
              _buildInfoItem('总价', '¥${((item.unitPrice ?? 0) * item.quantity).toStringAsFixed(2)}'),
            ],
          ),
          
          if (item.supplier?.isNotEmpty == true) ...[
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Row(
              children: [
                Icon(
                  Icons.store,
                  size: 16,
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
                SizedBox(width: VanHubResponsiveSpacing.xs),
                Text(
                  item.supplier!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: VanHubSemanticColors.getTextColor(context, secondary: true),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 构建看板物料卡片
  Widget _buildKanbanItemCard(domain.BomItem item, int index) {
    return VanHubCardV2(
      variant: VanHubCardVariant.filled,
      size: VanHubCardSize.sm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.name,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Row(
            children: [
              Text(
                '${item.quantity} 个',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const Spacer(),
              Text(
                '¥${((item.unitPrice ?? 0) * item.quantity).toStringAsFixed(0)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: VanHubSemanticColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 构建添加物料FAB
  Widget _buildAddItemFAB() {
    return FloatingActionButton.extended(
      heroTag: "bom_fab",
      onPressed: _showCreateItemDialog,
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      icon: const Icon(Icons.add),
      label: const Text('添加物料'),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80,
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '暂无BOM物料',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              '开始添加您的第一个物料吧！',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2.primary(
              text: '添加物料',
              leadingIcon: Icons.add,
              onPressed: _showCreateItemDialog,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: VanHubBrandColors.primary,
          ),
          SizedBox(height: VanHubResponsiveSpacing.lg),
          Text(
            '加载BOM数据中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: VanHubSemanticColors.error,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.error,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2(
              text: '重试',
              variant: VanHubButtonVariant.outline,
              leadingIcon: Icons.refresh,
              onPressed: () {
                ref.invalidate(projectBomItemsProvider);
              },
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  List<domain.BomItem> _filterBomItems(List<domain.BomItem> items) {
    return items.where((item) {
      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final name = item.name.toLowerCase();
        final spec = (item.specification ?? '').toLowerCase();
        final supplier = (item.supplier ?? '').toLowerCase();
        if (!name.contains(query) && !spec.contains(query) && !supplier.contains(query)) {
          return false;
        }
      }
      
      // 状态筛选
      if (_selectedStatus != null) {
        if (_mapFromEntityStatus(item.status) != _selectedStatus) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  // 将UI状态映射到实体状态
  domain.BomItemStatus _mapToEntityStatus(BomItemStatus uiStatus) {
    switch (uiStatus) {
      case BomItemStatus.planning:
        return domain.BomItemStatus.pending;
      case BomItemStatus.purchased:
        return domain.BomItemStatus.ordered;
      case BomItemStatus.used:
        return domain.BomItemStatus.received;
      case BomItemStatus.completed:
        return domain.BomItemStatus.installed;
    }
  }

  // 将实体状态映射到UI状态
  BomItemStatus _mapFromEntityStatus(domain.BomItemStatus entityStatus) {
    switch (entityStatus) {
      case domain.BomItemStatus.pending:
        return BomItemStatus.planning;
      case domain.BomItemStatus.ordered:
        return BomItemStatus.purchased;
      case domain.BomItemStatus.received:
        return BomItemStatus.used;
      case domain.BomItemStatus.installed:
        return BomItemStatus.completed;
      case domain.BomItemStatus.cancelled:
        return BomItemStatus.planning; // 默认映射
    }
  }

  String _getStatusText(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.planning:
        return '计划中';
      case BomItemStatus.purchased:
        return '已采购';
      case BomItemStatus.used:
        return '已使用';
      case BomItemStatus.completed:
        return '已完成';
    }
  }

  Color _getStatusColor(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.planning:
        return VanHubSemanticColors.info;
      case BomItemStatus.purchased:
        return VanHubSemanticColors.warning;
      case BomItemStatus.used:
        return VanHubBrandColors.primary;
      case BomItemStatus.completed:
        return VanHubSemanticColors.success;
    }
  }

  void _showCreateItemDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateBomItemDialogWidget(projectId: widget.projectId),
    );
  }

  void _exportBom() {
    // TODO: 实现BOM导出功能
  }

  void _importBom() {
    // TODO: 实现BOM导入功能
  }

  void _showTemplateDialog() {
    // TODO: 显示BOM模板选择对话框
  }
}
