import '../../domain/entities/project_timeline.dart';
import '../../domain/services/project_timeline_service.dart';

/// Implementation of project timeline service
class ProjectTimelineServiceImpl implements ProjectTimelineService {
  // Mock data storage - in real implementation, this would use a database
  final Map<String, List<ProjectTimeline>> _timelines = {};
  final Map<String, List<ProjectMilestone>> _milestones = {};
  final Map<String, List<ProjectTask>> _tasks = {};

  @override
  Future<List<ProjectTimeline>> getProjectTimeline(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return _timelines[projectId] ?? [];
  }

  @override
  Future<ProjectTimeline> createTimelineItem(ProjectTimeline timeline) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectTimelines = _timelines[timeline.projectId] ?? [];
    projectTimelines.add(timeline);
    _timelines[timeline.projectId] = projectTimelines;
    
    return timeline;
  }

  @override
  Future<ProjectTimeline> updateTimelineItem(ProjectTimeline timeline) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectTimelines = _timelines[timeline.projectId] ?? [];
    final index = projectTimelines.indexWhere((t) => t.id == timeline.id);
    
    if (index != -1) {
      projectTimelines[index] = timeline.copyWith(updatedAt: DateTime.now());
      _timelines[timeline.projectId] = projectTimelines;
      return projectTimelines[index];
    }
    
    throw Exception('Timeline item not found');
  }

  @override
  Future<void> deleteTimelineItem(String timelineId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    for (final projectId in _timelines.keys) {
      final timelines = _timelines[projectId]!;
      timelines.removeWhere((t) => t.id == timelineId);
      _timelines[projectId] = timelines;
    }
  }

  @override
  Future<void> reorderTimelineItems(String projectId, List<String> itemIds) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final timelines = _timelines[projectId] ?? [];
    final reorderedTimelines = <ProjectTimeline>[];
    
    for (int i = 0; i < itemIds.length; i++) {
      final timeline = timelines.firstWhere((t) => t.id == itemIds[i]);
      reorderedTimelines.add(timeline.copyWith(orderIndex: i));
    }
    
    _timelines[projectId] = reorderedTimelines;
  }

  @override
  Future<List<ProjectMilestone>> getProjectMilestones(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return _milestones[projectId] ?? [];
  }

  @override
  Future<ProjectMilestone> createMilestone(ProjectMilestone milestone) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectMilestones = _milestones[milestone.projectId] ?? [];
    projectMilestones.add(milestone);
    _milestones[milestone.projectId] = projectMilestones;
    
    return milestone;
  }

  @override
  Future<ProjectMilestone> updateMilestone(ProjectMilestone milestone) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectMilestones = _milestones[milestone.projectId] ?? [];
    final index = projectMilestones.indexWhere((m) => m.id == milestone.id);
    
    if (index != -1) {
      projectMilestones[index] = milestone.copyWith(updatedAt: DateTime.now());
      _milestones[milestone.projectId] = projectMilestones;
      return projectMilestones[index];
    }
    
    throw Exception('Milestone not found');
  }

  @override
  Future<void> deleteMilestone(String milestoneId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    for (final projectId in _milestones.keys) {
      final milestones = _milestones[projectId]!;
      milestones.removeWhere((m) => m.id == milestoneId);
      _milestones[projectId] = milestones;
    }
  }

  @override
  Future<void> completeMilestone(String milestoneId, {String? notes}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    for (final projectId in _milestones.keys) {
      final milestones = _milestones[projectId]!;
      final index = milestones.indexWhere((m) => m.id == milestoneId);
      
      if (index != -1) {
        milestones[index] = milestones[index].copyWith(
          status: const MilestoneStatus.completed(),
          completedDate: DateTime.now(),
          completionPercentage: 100.0,
          notes: notes ?? milestones[index].notes,
          updatedAt: DateTime.now(),
        );
        _milestones[projectId] = milestones;
        return;
      }
    }
  }

  @override
  Future<List<ProjectMilestone>> getUpcomingMilestones(String projectId, {int days = 30}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final milestones = _milestones[projectId] ?? [];
    final cutoffDate = DateTime.now().add(Duration(days: days));
    
    return milestones.where((m) => 
      m.targetDate.isBefore(cutoffDate) && 
      m.status != const MilestoneStatus.completed()
    ).toList();
  }

  @override
  Future<List<ProjectMilestone>> getOverdueMilestones(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final milestones = _milestones[projectId] ?? [];
    final now = DateTime.now();
    
    return milestones.where((m) => 
      m.targetDate.isBefore(now) && 
      m.status != const MilestoneStatus.completed()
    ).toList();
  }

  @override
  Future<List<ProjectTask>> getProjectTasks(String projectId, {String? milestoneId}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    
    if (milestoneId != null) {
      return tasks.where((t) => t.milestoneId == milestoneId).toList();
    }
    
    return tasks;
  }

  @override
  Future<ProjectTask> createTask(ProjectTask task) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectTasks = _tasks[task.projectId] ?? [];
    projectTasks.add(task);
    _tasks[task.projectId] = projectTasks;
    
    return task;
  }

  @override
  Future<ProjectTask> updateTask(ProjectTask task) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final projectTasks = _tasks[task.projectId] ?? [];
    final index = projectTasks.indexWhere((t) => t.id == task.id);
    
    if (index != -1) {
      projectTasks[index] = task.copyWith(updatedAt: DateTime.now());
      _tasks[task.projectId] = projectTasks;
      return projectTasks[index];
    }
    
    throw Exception('Task not found');
  }

  @override
  Future<void> deleteTask(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    for (final projectId in _tasks.keys) {
      final tasks = _tasks[projectId]!;
      tasks.removeWhere((t) => t.id == taskId);
      _tasks[projectId] = tasks;
    }
  }

  @override
  Future<void> completeTask(String taskId, {double? actualHours}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    for (final projectId in _tasks.keys) {
      final tasks = _tasks[projectId]!;
      final index = tasks.indexWhere((t) => t.id == taskId);
      
      if (index != -1) {
        tasks[index] = tasks[index].copyWith(
          status: const TaskStatus.completed(),
          completedDate: DateTime.now(),
          actualHours: actualHours ?? tasks[index].actualHours,
          updatedAt: DateTime.now(),
        );
        _tasks[projectId] = tasks;
        return;
      }
    }
  }

  @override
  Future<List<ProjectTask>> getTasksByStatus(String projectId, TaskStatus status) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    return tasks.where((t) => t.status == status).toList();
  }

  @override
  Future<List<ProjectTask>> getTasksByAssignee(String projectId, String assigneeId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    return tasks.where((t) => t.assignedTo == assigneeId).toList();
  }

  @override
  Future<List<ProjectTask>> getOverdueTasks(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    final now = DateTime.now();
    
    return tasks.where((t) => 
      t.dueDate.isBefore(now) && 
      t.status != const TaskStatus.completed()
    ).toList();
  }

  @override
  Future<double> calculateProjectProgress(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    if (tasks.isEmpty) return 0.0;
    
    final completedTasks = tasks.where((t) => t.status == const TaskStatus.completed()).length;
    return completedTasks / tasks.length;
  }

  @override
  Future<double> calculateMilestoneProgress(String milestoneId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Find milestone and its associated tasks
    for (final projectId in _tasks.keys) {
      final tasks = _tasks[projectId]!.where((t) => t.milestoneId == milestoneId).toList();
      if (tasks.isNotEmpty) {
        final completedTasks = tasks.where((t) => t.status == const TaskStatus.completed()).length;
        return completedTasks / tasks.length;
      }
    }
    
    return 0.0;
  }

  @override
  Future<Map<String, dynamic>> getProjectStatistics(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    final tasks = _tasks[projectId] ?? [];
    final milestones = _milestones[projectId] ?? [];
    
    final completedTasks = tasks.where((t) => t.status == const TaskStatus.completed()).length;
    final overdueTasks = tasks.where((t) => 
      t.dueDate.isBefore(DateTime.now()) && 
      t.status != const TaskStatus.completed()
    ).length;
    
    final completedMilestones = milestones.where((m) => 
      m.status == const MilestoneStatus.completed()
    ).length;
    
    final totalEstimatedHours = tasks.fold<double>(0.0, (sum, task) => 
      sum + (task.estimatedHours ?? 0.0)
    );
    
    final totalActualHours = tasks.fold<double>(0.0, (sum, task) => 
      sum + (task.actualHours ?? 0.0)
    );
    
    return {
      'totalTasks': tasks.length,
      'completedTasks': completedTasks,
      'overdueTasks': overdueTasks,
      'totalMilestones': milestones.length,
      'completedMilestones': completedMilestones,
      'progressPercentage': tasks.isEmpty ? 0.0 : (completedTasks / tasks.length) * 100,
      'totalEstimatedHours': totalEstimatedHours,
      'totalActualHours': totalActualHours,
      'efficiency': totalEstimatedHours > 0 ? (totalEstimatedHours / totalActualHours) * 100 : 0.0,
    };
  }

  @override
  Future<List<Map<String, dynamic>>> getProgressHistory(String projectId, {int days = 30}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Mock progress history data
    final history = <Map<String, dynamic>>[];
    final now = DateTime.now();
    
    for (int i = days; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final progress = (days - i) / days * 100; // Mock progressive completion
      
      history.add({
        'date': date.toIso8601String(),
        'progress': progress,
        'tasksCompleted': (progress / 10).round(),
        'milestonesCompleted': (progress / 25).round(),
      });
    }
    
    return history;
  }

  // Simplified implementations for remaining methods
  @override
  Future<void> addTaskDependency(String taskId, String dependsOnTaskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Implementation would update task dependencies
  }

  @override
  Future<void> removeTaskDependency(String taskId, String dependsOnTaskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Implementation would remove task dependencies
  }

  @override
  Future<List<ProjectTask>> getTaskDependencies(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<List<ProjectTask>> getDependentTasks(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<bool> validateTaskDependencies(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return true; // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> analyzeCriticalPath(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return {'criticalPath': [], 'duration': 0}; // Mock implementation
  }

  @override
  Future<List<Map<String, dynamic>>> getTimelineConflicts(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> getResourceAllocation(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return {'resources': [], 'allocation': {}}; // Mock implementation
  }

  @override
  Future<DateTime?> estimateProjectCompletion(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return DateTime.now().add(const Duration(days: 30)); // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> generateTimelineReport(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return await getProjectStatistics(projectId);
  }

  @override
  Future<Map<String, dynamic>> generateMilestoneReport(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final milestones = await getProjectMilestones(projectId);
    return {
      'totalMilestones': milestones.length,
      'completedMilestones': milestones.where((m) => m.status == const MilestoneStatus.completed()).length,
      'upcomingMilestones': milestones.where((m) => m.status == const MilestoneStatus.upcoming()).length,
      'overdueMilestones': milestones.where((m) => m.status == const MilestoneStatus.overdue()).length,
    };
  }

  @override
  Future<Map<String, dynamic>> generateTaskReport(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final tasks = await getProjectTasks(projectId);
    return {
      'totalTasks': tasks.length,
      'completedTasks': tasks.where((t) => t.status == const TaskStatus.completed()).length,
      'inProgressTasks': tasks.where((t) => t.status == const TaskStatus.inProgress()).length,
      'blockedTasks': tasks.where((t) => t.status == const TaskStatus.blocked()).length,
    };
  }

  @override
  Future<List<Map<String, dynamic>>> getTeamPerformanceMetrics(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<List<Map<String, dynamic>>> getUpcomingDeadlines(String projectId, {int days = 7}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final tasks = await getProjectTasks(projectId);
    final cutoffDate = DateTime.now().add(Duration(days: days));
    
    return tasks
        .where((t) => t.dueDate.isBefore(cutoffDate) && t.status != const TaskStatus.completed())
        .map((t) => {
              'id': t.id,
              'title': t.title,
              'dueDate': t.dueDate.toIso8601String(),
              'priority': t.priority.toString(),
            })
        .toList();
  }

  @override
  Future<List<Map<String, dynamic>>> getDelayedItems(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final overdueTasks = await getOverdueTasks(projectId);
    final overdueMilestones = await getOverdueMilestones(projectId);
    
    final delayedItems = <Map<String, dynamic>>[];
    
    for (final task in overdueTasks) {
      delayedItems.add({
        'id': task.id,
        'title': task.title,
        'type': 'task',
        'dueDate': task.dueDate.toIso8601String(),
        'daysOverdue': DateTime.now().difference(task.dueDate).inDays,
      });
    }
    
    for (final milestone in overdueMilestones) {
      delayedItems.add({
        'id': milestone.id,
        'title': milestone.title,
        'type': 'milestone',
        'dueDate': milestone.targetDate.toIso8601String(),
        'daysOverdue': DateTime.now().difference(milestone.targetDate).inDays,
      });
    }
    
    return delayedItems;
  }

  // Simplified implementations for remaining methods
  @override
  Future<void> sendMilestoneReminder(String milestoneId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation - would send actual notifications
  }

  @override
  Future<void> sendTaskReminder(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation - would send actual notifications
  }

  @override
  Future<void> saveAsTemplate(String projectId, String templateName) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> applyTemplate(String projectId, String templateId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<List<Map<String, dynamic>>> getTimelineTemplates() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<void> deleteTemplate(String templateId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> assignTask(String taskId, String assigneeId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> unassignTask(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<List<Map<String, dynamic>>> getTaskComments(String taskId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return []; // Mock implementation
  }

  @override
  Future<void> addTaskComment(String taskId, String comment, String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> mentionUser(String taskId, String userId, String message) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> startTimeTracking(String taskId, String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> stopTimeTracking(String taskId, String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<double> getTrackedTime(String taskId, String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return 0.0; // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> getTimeTrackingReport(String projectId, {DateTime? startDate, DateTime? endDate}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return {'totalHours': 0.0, 'breakdown': {}}; // Mock implementation
  }

  @override
  Future<void> syncWithCalendar(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> exportToGanttChart(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<void> importFromCSV(String projectId, String csvData) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // Mock implementation
  }

  @override
  Future<String> exportToCSV(String projectId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return 'CSV data'; // Mock implementation
  }
}
