// 暂时禁用路由，使用简单的Navigator导航
// 整个文件暂时禁用，等待所有页面迁移完成后重新启用

/*
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/material/presentation/pages/material_form_page.dart';
import '../widgets/main_scaffold.dart';

/// 应用路由配置
class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  /// 创建路由配置
  static GoRouter createRouter(WidgetRef ref) {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      routes: [
        // 主要应用Shell路由
        ShellRoute(
          navigatorKey: _shellNavigatorKey,
          builder: (context, state, child) {
            return MainScaffold(child: child);
          },
          routes: [
            // 首页（项目发现）
            GoRoute(
              path: '/',
              name: 'home',
              builder: (context, state) => const ProjectDiscoveryPage(),
            ),

            // 我的项目
            GoRoute(
              path: '/my-projects',
              name: 'my-projects',
              builder: (context, state) => const MyProjectsPage(),
            ),
            
            // 材料库
            GoRoute(
              path: '/materials',
              name: 'materials',
              builder: (context, state) => const MaterialsPage(),
            ),
            
            // 设置
            GoRoute(
              path: '/settings',
              name: 'settings',
              builder: (context, state) => const SettingsPage(),
            ),
          ],
        ),
        
        // 认证相关路由（全屏）
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginPage(),
        ),
        
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) => const RegisterPage(),
        ),
        
        GoRoute(
          path: '/profile',
          name: 'profile',
          builder: (context, state) => const ProfilePage(),
        ),

        // 项目相关路由
        GoRoute(
          path: '/project/create',
          name: 'create-project',
          builder: (context, state) => const CreateProjectPage(),
        ),
        GoRoute(
          path: '/project/:projectId',
          name: 'project-detail',
          builder: (context, state) => ProjectDetailPage(
            projectId: state.pathParameters['projectId']!,
          ),
        ),
        GoRoute(
          path: '/project/:projectId/edit',
          name: 'edit-project',
          builder: (context, state) {
            // TODO: 获取项目详情并传递给编辑页面
            // 这里暂时使用占位符，实际应该从provider获取项目数据
            return const Scaffold(
              body: Center(
                child: Text('项目编辑页面加载中...'),
              ),
            );
          },
        ),

        // 材料相关路由
        GoRoute(
          path: '/material/add',
          name: 'add-material',
          builder: (context, state) => const MaterialFormPage(),
        ),
        GoRoute(
          path: '/material/:materialId/edit',
          name: 'edit-material',
          builder: (context, state) {
            // TODO: 获取材料详情并传递给表单页面
            return const MaterialFormPage();
          },
        ),
      ],
      
      // 重定向逻辑
      redirect: (context, state) {
        // 这里可以添加基于认证状态的重定向逻辑
        // 例如：某些页面需要登录才能访问
        return null;
      },
      
      // 错误页面
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(title: const Text('页面未找到')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '页面未找到: ${state.location}',
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('返回首页'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 临时页面实现
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.explore,
            size: 100,
            color: Colors.blue,
          ),
          SizedBox(height: 20),
          Text(
            '项目发现',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Text(
            '浏览社区中的精彩改装项目',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}

class MyProjectsPage extends ConsumerWidget {
  const MyProjectsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 这里需要获取当前用户ID
    // 暂时使用占位符，实际应该从认证状态获取
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder,
            size: 100,
            color: Colors.blue,
          ),
          SizedBox(height: 20),
          Text(
            '我的项目',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Text(
            '管理您的改装项目',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          SizedBox(height: 20),
          Text(
            '请先登录以查看您的项目',
            style: TextStyle(fontSize: 14, color: Colors.orange),
          ),
        ],
      ),
    );
  }
}

class MaterialsPage extends StatelessWidget {
  const MaterialsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialLibraryPage();
  }
}
*/
