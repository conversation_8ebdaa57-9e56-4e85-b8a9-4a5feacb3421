# Supabase 行级安全 (RLS) 策略

本文档描述了VanHub改装宝应用的Supabase数据库行级安全(RLS)策略。这些策略确保数据安全，并实现了项目文档中要求的权限控制。

## 通用原则

1. 所有表默认启用RLS，并且默认拒绝所有操作
2. 所有写入/修改操作必须严格验证所有权
3. 读取策略必须允许任何用户读取`is_public = true`的项目
4. 匿名用户（游客模式）只能读取公开内容，不能进行写操作

## 用户表 (auth.users)

Supabase Auth已经内置了用户表和安全策略，我们不需要额外配置。

## 项目表 (projects)

```sql
-- 启用RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- 创建者可以执行所有操作
CREATE POLICY "项目创建者可以执行所有操作" ON projects
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- 任何人可以查看公开项目
CREATE POLICY "任何人可以查看公开项目" ON projects
  FOR SELECT
  USING (is_public = true);
```

## 项目点赞表 (project_likes)

```sql
-- 启用RLS
ALTER TABLE project_likes ENABLE ROW LEVEL SECURITY;

-- 用户可以添加自己的点赞
CREATE POLICY "用户可以添加自己的点赞" ON project_likes
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- 用户可以删除自己的点赞
CREATE POLICY "用户可以删除自己的点赞" ON project_likes
  FOR DELETE
  USING (auth.uid() = user_id);

-- 任何人可以查看点赞
CREATE POLICY "任何人可以查看点赞" ON project_likes
  FOR SELECT
  USING (true);
```

## 项目关注表 (project_follows)

```sql
-- 启用RLS
ALTER TABLE project_follows ENABLE ROW LEVEL SECURITY;

-- 用户可以添加自己的关注
CREATE POLICY "用户可以添加自己的关注" ON project_follows
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- 用户可以删除自己的关注
CREATE POLICY "用户可以删除自己的关注" ON project_follows
  FOR DELETE
  USING (auth.uid() = user_id);

-- 用户可以查看自己的关注
CREATE POLICY "用户可以查看自己的关注" ON project_follows
  FOR SELECT
  USING (auth.uid() = user_id);
```

## 项目复刻表 (project_forks)

```sql
-- 启用RLS
ALTER TABLE project_forks ENABLE ROW LEVEL SECURITY;

-- 用户可以添加自己的复刻
CREATE POLICY "用户可以添加自己的复刻" ON project_forks
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- 任何人可以查看复刻
CREATE POLICY "任何人可以查看复刻" ON project_forks
  FOR SELECT
  USING (true);
```

## 材料库表 (material_library)

```sql
-- 启用RLS
ALTER TABLE material_library ENABLE ROW LEVEL SECURITY;

-- 创建者可以执行所有操作
CREATE POLICY "材料创建者可以执行所有操作" ON material_library
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- 任何人可以查看公开材料（预留，目前材料默认私有）
-- CREATE POLICY "任何人可以查看公开材料" ON material_library
--   FOR SELECT
--   USING (is_public = true);
```

## BOM物料表 (bom_items)

```sql
-- 启用RLS
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;

-- 项目创建者可以执行所有BOM操作
CREATE POLICY "项目创建者可以执行所有BOM操作" ON bom_items
  USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = bom_items.project_id
      AND projects.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = bom_items.project_id
      AND projects.user_id = auth.uid()
    )
  );

-- 任何人可以查看公开项目的BOM
CREATE POLICY "任何人可以查看公开项目的BOM" ON bom_items
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = bom_items.project_id
      AND projects.is_public = true
    )
  );
```

## 存储桶策略

```sql
-- 公开存储桶（项目封面图等）
CREATE POLICY "公开内容可以被任何人查看" ON storage.objects
  FOR SELECT
  USING (bucket_id = 'public');

-- 用户可以上传到公开存储桶
CREATE POLICY "已认证用户可以上传公开内容" ON storage.objects
  FOR INSERT
  WITH CHECK (bucket_id = 'public' AND auth.role() = 'authenticated');

-- 用户可以更新自己上传的公开内容
CREATE POLICY "用户可以更新自己的公开内容" ON storage.objects
  FOR UPDATE
  USING (bucket_id = 'public' AND auth.uid() = owner)
  WITH CHECK (bucket_id = 'public' AND auth.uid() = owner);

-- 用户可以删除自己上传的公开内容
CREATE POLICY "用户可以删除自己的公开内容" ON storage.objects
  FOR DELETE
  USING (bucket_id = 'public' AND auth.uid() = owner);

-- 私有存储桶（用户私有文件）
CREATE POLICY "用户可以访问自己的私有内容" ON storage.objects
  USING (bucket_id = 'private' AND auth.uid() = owner)
  WITH CHECK (bucket_id = 'private' AND auth.uid() = owner);
```

## 数据库函数

```sql
-- 增加项目浏览量
CREATE OR REPLACE FUNCTION increment_project_views(project_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE projects
  SET views_count = views_count + 1,
      updated_at = NOW()
  WHERE id = project_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 增加材料使用次数
CREATE OR REPLACE FUNCTION increment_material_usage(material_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE material_library
  SET usage_count = usage_count + 1,
      last_used_at = NOW(),
      updated_at = NOW()
  WHERE id = material_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取项目统计信息
CREATE OR REPLACE FUNCTION get_project_stats(project_id UUID)
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  SELECT jsonb_build_object(
    'likes_count', p.likes_count,
    'forks_count', p.forks_count,
    'views_count', p.views_count,
    'comments_count', p.comments_count,
    'bom_items_count', (SELECT COUNT(*) FROM bom_items WHERE project_id = p.id),
    'total_budget', p.estimated_budget,
    'actual_spent', (SELECT COALESCE(SUM(total_price), 0) FROM bom_items WHERE project_id = p.id AND status = 'purchased')
  ) INTO result
  FROM projects p
  WHERE p.id = project_id;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取BOM统计信息
CREATE OR REPLACE FUNCTION get_bom_stats(project_id UUID)
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  SELECT jsonb_build_object(
    'total_items', COUNT(*),
    'planned_items', COUNT(*) FILTER (WHERE status = 'planned'),
    'purchased_items', COUNT(*) FILTER (WHERE status = 'purchased'),
    'used_items', COUNT(*) FILTER (WHERE status = 'used'),
    'cancelled_items', COUNT(*) FILTER (WHERE status = 'cancelled'),
    'total_budget', (SELECT estimated_budget FROM projects WHERE id = project_id),
    'spent_amount', COALESCE(SUM(total_price) FILTER (WHERE status = 'purchased' OR status = 'used'), 0),
    'remaining_budget', (SELECT estimated_budget FROM projects WHERE id = project_id) - COALESCE(SUM(total_price) FILTER (WHERE status = 'purchased' OR status = 'used'), 0)
  ) INTO result
  FROM bom_items
  WHERE project_id = project_id;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 触发器

```sql
-- 更新项目点赞计数的触发器
CREATE OR REPLACE FUNCTION update_project_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE projects
    SET likes_count = likes_count + 1
    WHERE id = NEW.project_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE projects
    SET likes_count = likes_count - 1
    WHERE id = OLD.project_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER project_likes_count_trigger
AFTER INSERT OR DELETE ON project_likes
FOR EACH ROW EXECUTE FUNCTION update_project_likes_count();

-- 更新项目复刻计数的触发器
CREATE OR REPLACE FUNCTION update_project_forks_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE projects
    SET forks_count = forks_count + 1
    WHERE id = NEW.original_project_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER project_forks_count_trigger
AFTER INSERT ON project_forks
FOR EACH ROW EXECUTE FUNCTION update_project_forks_count();
```
