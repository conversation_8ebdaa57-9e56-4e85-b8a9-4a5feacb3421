import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';

/// 日志条目数据模型
class LogEntryModel {
  final String id;
  final String project_id;
  final String system_id;
  final String title;
  final String content;
  final String log_date;
  final String author_id;
  final String? author_name;
  final String created_at;
  final String updated_at;
  final String status;
  final String difficulty;
  final int time_spent_minutes;
  final List<String>? media_ids;
  final List<String>? related_bom_item_ids;
  final double? total_cost;
  final Map<String, dynamic>? metadata;
  final bool? is_milestone;
  final String? milestone_icon_name;
  final String? milestone_color_hex;
  final String? milestone_priority;

  LogEntryModel({
    required this.id,
    required this.project_id,
    required this.system_id,
    required this.title,
    required this.content,
    required this.log_date,
    required this.author_id,
    this.author_name,
    required this.created_at,
    required this.updated_at,
    required this.status,
    required this.difficulty,
    required this.time_spent_minutes,
    this.media_ids,
    this.related_bom_item_ids,
    this.total_cost,
    this.metadata,
    this.is_milestone,
    this.milestone_icon_name,
    this.milestone_color_hex,
    this.milestone_priority,
  });

  factory LogEntryModel.fromJson(Map<String, dynamic> json) {
    return LogEntryModel(
      id: json['id'] ?? '',
      project_id: json['project_id'] ?? '',
      system_id: json['system_id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      log_date: json['log_date'] ?? '',
      author_id: json['author_id'] ?? '',
      author_name: json['author_name'], // 可以为null
      created_at: json['created_at'] ?? '',
      updated_at: json['updated_at'] ?? '',
      status: json['status'] ?? 'planned',
      difficulty: json['difficulty'] ?? 'medium',
      time_spent_minutes: json['time_spent_minutes'] ?? 0,
      media_ids: json['media_ids'] != null
          ? List<String>.from(json['media_ids'])
          : null,
      related_bom_item_ids: json['related_bom_item_ids'] != null
          ? List<String>.from(json['related_bom_item_ids'])
          : null,
      total_cost: json['total_cost']?.toDouble(),
      metadata: json['metadata'],
      is_milestone: json['is_milestone'] ?? false,
      milestone_icon_name: json['milestone_icon_name'],
      milestone_color_hex: json['milestone_color_hex'],
      milestone_priority: json['milestone_priority'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'project_id': project_id,
      'system_id': system_id,
      'title': title,
      'content': content,
      'log_date': log_date,
      'author_id': author_id,
      'created_at': created_at,
      'updated_at': updated_at,
      'status': status,
      'difficulty': difficulty,
      'time_spent_minutes': time_spent_minutes,
      'total_cost': total_cost,
      'is_milestone': is_milestone,
      'milestone_icon_name': milestone_icon_name,
      'milestone_color_hex': milestone_color_hex,
      'milestone_priority': milestone_priority,
      // 暂时不包含数据库中不存在的字段
      // 'author_name': author_name,
      // 'media_ids': media_ids,
      // 'related_bom_item_ids': related_bom_item_ids,
      // 'metadata': metadata,
    };
  }

  /// 转换为领域实体
  LogEntry toEntity() {
    return LogEntry(
      id: id,
      projectId: project_id,
      systemId: system_id,
      title: title,
      content: content,
      logDate: DateTime.parse(log_date),
      authorId: author_id,
      authorName: author_name,
      createdAt: DateTime.parse(created_at),
      updatedAt: DateTime.parse(updated_at),
      status: _parseLogStatus(status),
      difficulty: _parseDifficultyLevel(difficulty),
      timeSpentMinutes: time_spent_minutes,
      mediaIds: media_ids ?? [],
      relatedBomItemIds: related_bom_item_ids ?? [],
      totalCost: total_cost ?? 0.0,
      metadata: metadata,
      isMilestone: is_milestone ?? false,
      milestoneIconName: milestone_icon_name,
      milestoneColorHex: milestone_color_hex,
      milestonePriority: _parseMilestonePriority(milestone_priority),
    );
  }

  /// 从领域实体创建
  factory LogEntryModel.fromEntity(LogEntry entity) {
    return LogEntryModel(
      id: entity.id,
      project_id: entity.projectId,
      system_id: entity.systemId,
      title: entity.title,
      content: entity.content,
      log_date: entity.logDate.toIso8601String(),
      author_id: entity.authorId,
      author_name: entity.authorName,
      created_at: entity.createdAt.toIso8601String(),
      updated_at: entity.updatedAt.toIso8601String(),
      status: logStatusToString(entity.status),
      difficulty: _difficultyLevelToString(entity.difficulty),
      time_spent_minutes: entity.timeSpentMinutes,
      media_ids: entity.mediaIds.isEmpty ? null : entity.mediaIds,
      related_bom_item_ids: entity.relatedBomItemIds.isEmpty ? null : entity.relatedBomItemIds,
      total_cost: entity.totalCost > 0 ? entity.totalCost : null,
      metadata: entity.metadata,
      is_milestone: entity.isMilestone,
      milestone_icon_name: entity.milestoneIconName,
      milestone_color_hex: entity.milestoneColorHex,
      milestone_priority: _milestonePriorityToString(entity.milestonePriority),
    );
  }

  /// 解析日志状态
  static LogStatus _parseLogStatus(String status) {
    switch (status) {
      case 'draft':
        return LogStatus.draft;
      case 'in_progress':
        return LogStatus.inProgress;
      case 'completed':
        return LogStatus.completed;
      case 'on_hold':
        return LogStatus.onHold;
      case 'cancelled':
        return LogStatus.cancelled;
      case 'archived':
        return LogStatus.archived;
      case 'published':
      default:
        return LogStatus.published;
    }
  }

  /// 解析难度级别
  static DifficultyLevel _parseDifficultyLevel(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return DifficultyLevel.easy;
      case 'medium':
        return DifficultyLevel.medium;
      case 'hard':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      case 'beginner':
        return DifficultyLevel.beginner;
      case 'advanced':
        return DifficultyLevel.advanced;
      case 'intermediate':
      default:
        return DifficultyLevel.intermediate;
    }
  }

  /// 日志状态转字符串
  static String logStatusToString(LogStatus status) {
    switch (status) {
      case LogStatus.draft:
        return 'draft';
      case LogStatus.inProgress:
        return 'in_progress';
      case LogStatus.completed:
        return 'completed';
      case LogStatus.onHold:
        return 'on_hold';
      case LogStatus.cancelled:
        return 'cancelled';
      case LogStatus.published:
        return 'published';
      case LogStatus.archived:
        return 'archived';
    }
  }

  /// 难度级别转字符串
  static String _difficultyLevelToString(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 'easy';
      case DifficultyLevel.medium:
        return 'medium';
      case DifficultyLevel.hard:
        return 'hard';
      case DifficultyLevel.expert:
        return 'expert';
      case DifficultyLevel.beginner:
        return 'beginner';
      case DifficultyLevel.intermediate:
        return 'intermediate';
      case DifficultyLevel.advanced:
        return 'advanced';
    }
  }

  /// 解析里程碑优先级
  static MilestonePriority? _parseMilestonePriority(String? priority) {
    if (priority == null) return null;
    switch (priority) {
      case 'low':
        return MilestonePriority.low;
      case 'medium':
        return MilestonePriority.medium;
      case 'high':
        return MilestonePriority.high;
      case 'critical':
        return MilestonePriority.critical;
      default:
        return null;
    }
  }

  /// 里程碑优先级转字符串
  static String? _milestonePriorityToString(MilestonePriority? priority) {
    if (priority == null) return null;
    switch (priority) {
      case MilestonePriority.low:
        return 'low';
      case MilestonePriority.medium:
        return 'medium';
      case MilestonePriority.high:
        return 'high';
      case MilestonePriority.critical:
        return 'critical';
    }
  }
}