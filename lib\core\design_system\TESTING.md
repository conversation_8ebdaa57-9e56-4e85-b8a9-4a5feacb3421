# VanHub Design System 测试指南

> 确保设计系统质量和一致性的测试策略

## 📋 测试策略

### 测试金字塔

```
        E2E Tests (端到端测试)
       /                    \
    Integration Tests (集成测试)
   /                            \
Unit Tests (单元测试) + Widget Tests (组件测试)
```

## 🧪 单元测试

### 响应式工具测试

```dart
// test/core/design_system/utils/responsive_utils_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/utils/responsive_utils.dart';

void main() {
  group('VanHubResponsiveUtils', () {
    testWidgets('should return correct breakpoint for different screen sizes', 
        (WidgetTester tester) async {
      // 测试移动端断点
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(
                VanHubResponsiveUtils.getBreakpoint(context),
                VanHubBreakpoint.xs,
              );
              return Container();
            },
          ),
        ),
      );

      // 测试平板断点
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(
                VanHubResponsiveUtils.getBreakpoint(context),
                VanHubBreakpoint.md,
              );
              return Container();
            },
          ),
        ),
      );

      // 测试桌面断点
      await tester.binding.setSurfaceSize(const Size(1440, 900));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(
                VanHubResponsiveUtils.getBreakpoint(context),
                VanHubBreakpoint.xl,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('should return correct device type', (WidgetTester tester) async {
      // 测试移动设备
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(VanHubResponsiveUtils.isMobile(context), true);
              expect(VanHubResponsiveUtils.isTablet(context), false);
              expect(VanHubResponsiveUtils.isDesktop(context), false);
              return Container();
            },
          ),
        ),
      );

      // 测试平板设备
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(VanHubResponsiveUtils.isMobile(context), false);
              expect(VanHubResponsiveUtils.isTablet(context), true);
              expect(VanHubResponsiveUtils.isDesktop(context), false);
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('should return correct responsive values', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final value = VanHubResponsiveUtils.getValue<int>(
                context,
                xs: 1,
                sm: 2,
                md: 3,
                lg: 4,
                xl: 5,
                defaultValue: 2,
              );
              expect(value, 1);
              return Container();
            },
          ),
        ),
      );
    });
  });
}
```

### 设计令牌测试

```dart
// test/core/design_system/vanhub_design_system_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/vanhub_design_system.dart';

void main() {
  group('VanHubDesignSystem', () {
    test('should have correct brand colors', () {
      expect(VanHubDesignSystem.brandPrimary.value, 0xFF2563EB);
      expect(VanHubDesignSystem.brandSecondary.value, 0xFFEF4444);
      expect(VanHubDesignSystem.brandAccent.value, 0xFF10B981);
    });

    test('should have correct spacing values', () {
      expect(VanHubDesignSystem.spacing0, 0.0);
      expect(VanHubDesignSystem.spacing1, 4.0);
      expect(VanHubDesignSystem.spacing2, 8.0);
      expect(VanHubDesignSystem.spacing4, 16.0);
      expect(VanHubDesignSystem.spacing8, 32.0);
    });

    test('should have correct font sizes', () {
      expect(VanHubDesignSystem.fontSizeXs, 12.0);
      expect(VanHubDesignSystem.fontSizeSm, 14.0);
      expect(VanHubDesignSystem.fontSizeBase, 16.0);
      expect(VanHubDesignSystem.fontSizeLg, 18.0);
      expect(VanHubDesignSystem.fontSizeXl, 20.0);
    });

    test('should have correct border radius values', () {
      expect(VanHubDesignSystem.radiusNone, 0.0);
      expect(VanHubDesignSystem.radiusSm, 4.0);
      expect(VanHubDesignSystem.radiusBase, 8.0);
      expect(VanHubDesignSystem.radiusLg, 12.0);
      expect(VanHubDesignSystem.radiusXl, 16.0);
    });

    test('should create correct gradients', () {
      final gradient = VanHubDesignSystem.brandGradient;
      expect(gradient.colors.length, 2);
      expect(gradient.colors[0], VanHubDesignSystem.brandPrimary);
      expect(gradient.colors[1], VanHubDesignSystem.brandSecondary);
    });
  });
}
```

## 🎨 组件测试

### 材料卡片组件测试

```dart
// test/features/material/presentation/widgets/material_card_unified_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/features/material/domain/entities/material.dart' as domain;
import 'package:vanhub/features/material/presentation/widgets/material_card_unified_widget.dart';

void main() {
  group('MaterialCardUnifiedWidget', () {
    late domain.Material testMaterial;

    setUp(() {
      testMaterial = domain.Material(
        id: '1',
        name: '测试材料',
        brand: '测试品牌',
        model: '测试型号',
        category: '电力系统',
        price: 1000.0,
        description: '测试描述',
        specifications: '测试规格',
        userId: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    testWidgets('should render material information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: false,
              isListView: false,
              showActions: true,
              onTap: () {},
            ),
          ),
        ),
      );

      // 验证材料名称显示
      expect(find.text('测试材料'), findsOneWidget);
      
      // 验证品牌和型号显示
      expect(find.text('测试品牌 • 测试型号'), findsOneWidget);
      
      // 验证价格显示
      expect(find.text('¥1000'), findsOneWidget);
      
      // 验证分类标签显示
      expect(find.text('电力系统'), findsOneWidget);
    });

    testWidgets('should show action buttons when not in guest mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: false,
              isListView: false,
              showActions: true,
              onTap: () {},
              onFavorite: () {},
              onAddToBom: () {},
              onShare: () {},
            ),
          ),
        ),
      );

      // 验证操作按钮存在
      expect(find.byIcon(Icons.favorite_border), findsOneWidget);
      expect(find.byIcon(Icons.add_shopping_cart_outlined), findsOneWidget);
      expect(find.byIcon(Icons.share_outlined), findsOneWidget);
    });

    testWidgets('should hide action buttons in guest mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: true,
              isListView: false,
              showActions: true,
              onTap: () {},
            ),
          ),
        ),
      );

      // 验证操作按钮不存在
      expect(find.byIcon(Icons.favorite_border), findsNothing);
      expect(find.byIcon(Icons.add_shopping_cart_outlined), findsNothing);
      expect(find.byIcon(Icons.share_outlined), findsNothing);
    });

    testWidgets('should call onTap when card is tapped', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: false,
              isListView: false,
              showActions: true,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(MaterialCardUnifiedWidget));
      expect(tapped, true);
    });

    testWidgets('should adapt to different screen sizes', (WidgetTester tester) async {
      // 测试移动端布局
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: false,
              isListView: false,
              showActions: true,
              onTap: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 验证移动端特定的布局调整
      // 例如：字体大小、内边距等

      // 测试桌面端布局
      await tester.binding.setSurfaceSize(const Size(1440, 900));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaterialCardUnifiedWidget(
              material: testMaterial,
              isGuestMode: false,
              isListView: false,
              showActions: true,
              onTap: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 验证桌面端特定的布局调整
    });
  });
}
```

### 搜索栏组件测试

```dart
// test/features/material/presentation/widgets/material_search_bar_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vanhub/features/material/presentation/widgets/material_search_bar_widget.dart';

void main() {
  group('MaterialSearchBarWidget', () {
    testWidgets('should render search input correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MaterialSearchBarWidget(
                onSearchChanged: (query) {},
                onFilterTap: () {},
              ),
            ),
          ),
        ),
      );

      // 验证搜索框存在
      expect(find.byType(TextField), findsOneWidget);
      
      // 验证搜索图标存在
      expect(find.byIcon(Icons.search), findsOneWidget);
      
      // 验证筛选按钮存在
      expect(find.byIcon(Icons.tune), findsOneWidget);
    });

    testWidgets('should call onSearchChanged when text changes', (WidgetTester tester) async {
      String searchQuery = '';
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MaterialSearchBarWidget(
                onSearchChanged: (query) => searchQuery = query,
                onFilterTap: () {},
              ),
            ),
          ),
        ),
      );

      // 输入搜索文本
      await tester.enterText(find.byType(TextField), '测试搜索');
      await tester.pump(const Duration(milliseconds: 350)); // 等待防抖

      expect(searchQuery, '测试搜索');
    });

    testWidgets('should show search suggestions when focused', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MaterialSearchBarWidget(
                onSearchChanged: (query) {},
                onFilterTap: () {},
              ),
            ),
          ),
        ),
      );

      // 点击搜索框获得焦点
      await tester.tap(find.byType(TextField));
      await tester.pumpAndSettle();

      // 验证搜索建议显示
      expect(find.text('热门搜索'), findsOneWidget);
    });

    testWidgets('should adapt hint text based on screen size', (WidgetTester tester) async {
      // 测试移动端提示文本
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MaterialSearchBarWidget(
                onSearchChanged: (query) {},
                onFilterTap: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 验证移动端简化的提示文本
      expect(find.text('搜索材料...'), findsOneWidget);

      // 测试桌面端提示文本
      await tester.binding.setSurfaceSize(const Size(1440, 900));
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MaterialSearchBarWidget(
                onSearchChanged: (query) {},
                onFilterTap: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 验证桌面端完整的提示文本
      expect(find.text('搜索材料名称、品牌、型号...'), findsOneWidget);
    });
  });
}
```

## 🔗 集成测试

### 材料库页面集成测试

```dart
// integration_test/material_library_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:vanhub/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Material Library Integration Tests', () {
    testWidgets('should navigate to material library and perform search', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 导航到材料库
      await tester.tap(find.text('材料库'));
      await tester.pumpAndSettle();

      // 验证材料库页面加载
      expect(find.text('材料库'), findsOneWidget);

      // 执行搜索
      await tester.tap(find.byType(TextField));
      await tester.enterText(find.byType(TextField), '锂电池');
      await tester.pumpAndSettle(const Duration(milliseconds: 500));

      // 验证搜索结果
      expect(find.textContaining('锂电池'), findsAtLeastNWidgets(1));
    });

    testWidgets('should apply filters and show filtered results', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 导航到材料库
      await tester.tap(find.text('材料库'));
      await tester.pumpAndSettle();

      // 打开筛选面板
      await tester.tap(find.byIcon(Icons.tune));
      await tester.pumpAndSettle();

      // 选择分类筛选
      await tester.tap(find.text('电力系统'));
      await tester.pumpAndSettle();

      // 验证筛选结果
      expect(find.text('电力系统'), findsAtLeastNWidgets(1));
    });

    testWidgets('should work correctly in guest mode', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 确保在游客模式
      if (find.text('登录').evaluate().isNotEmpty) {
        // 导航到材料库
        await tester.tap(find.text('材料库'));
        await tester.pumpAndSettle();

        // 验证游客可以浏览材料
        expect(find.byType(MaterialCardUnifiedWidget), findsAtLeastNWidgets(1));

        // 验证游客模式下没有编辑操作
        expect(find.byIcon(Icons.edit), findsNothing);
      }
    });
  });
}
```

## 📊 性能测试

### 响应式性能测试

```dart
// test/performance/responsive_performance_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/utils/responsive_utils.dart';

void main() {
  group('Responsive Performance Tests', () {
    testWidgets('should handle rapid screen size changes efficiently', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟大量响应式值计算
              for (int i = 0; i < 1000; i++) {
                VanHubResponsiveUtils.getValue<double>(
                  context,
                  xs: 8,
                  sm: 12,
                  md: 16,
                  lg: 20,
                  xl: 24,
                  defaultValue: 16,
                );
              }
              return Container();
            },
          ),
        ),
      );

      stopwatch.stop();
      
      // 验证性能在可接受范围内（例如：< 100ms）
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
    });
  });
}
```

## 🎯 测试覆盖率

### 目标覆盖率

- **单元测试**: > 90%
- **组件测试**: > 85%
- **集成测试**: > 70%

### 生成覆盖率报告

```bash
# 运行测试并生成覆盖率报告
flutter test --coverage

# 生成HTML报告
genhtml coverage/lcov.info -o coverage/html

# 查看报告
open coverage/html/index.html
```

## 🔧 测试工具

### 测试辅助工具

```dart
// test/helpers/test_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TestHelpers {
  /// 创建测试用的MaterialApp包装器
  static Widget createTestApp({
    required Widget child,
    List<Override>? overrides,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        home: Scaffold(body: child),
      ),
    );
  }

  /// 设置特定屏幕尺寸
  static Future<void> setScreenSize(
    WidgetTester tester,
    Size size,
  ) async {
    await tester.binding.setSurfaceSize(size);
    await tester.pumpAndSettle();
  }

  /// 常用屏幕尺寸
  static const Size mobileSize = Size(375, 667);
  static const Size tabletSize = Size(768, 1024);
  static const Size desktopSize = Size(1440, 900);

  /// 验证响应式行为
  static Future<void> testResponsiveBehavior(
    WidgetTester tester,
    Widget widget,
    Map<Size, VoidCallback> sizeTests,
  ) async {
    for (final entry in sizeTests.entries) {
      await setScreenSize(tester, entry.key);
      await tester.pumpWidget(createTestApp(child: widget));
      entry.value();
    }
  }
}
```

## 📝 测试最佳实践

### 1. 测试命名规范

```dart
// 好的测试命名
testWidgets('should show error message when network fails', (tester) async {
  // 测试实现
});

// 避免的测试命名
testWidgets('test1', (tester) async {
  // 测试实现
});
```

### 2. 测试组织

```dart
group('MaterialCardUnifiedWidget', () {
  group('Rendering', () {
    testWidgets('should render material name', (tester) async {
      // 渲染测试
    });
  });

  group('Interactions', () {
    testWidgets('should call onTap when tapped', (tester) async {
      // 交互测试
    });
  });

  group('Responsive Behavior', () {
    testWidgets('should adapt to mobile screen', (tester) async {
      // 响应式测试
    });
  });
});
```

### 3. 测试数据管理

```dart
// test/fixtures/material_fixtures.dart
import 'package:vanhub/features/material/domain/entities/material.dart' as domain;

class MaterialFixtures {
  static domain.Material get sampleMaterial => domain.Material(
    id: 'test-1',
    name: '测试材料',
    brand: '测试品牌',
    model: '测试型号',
    category: '电力系统',
    price: 1000.0,
    description: '测试描述',
    specifications: '测试规格',
    userId: 'user1',
    createdAt: DateTime(2025, 1, 1),
    updatedAt: DateTime(2025, 1, 1),
  );

  static List<domain.Material> get sampleMaterials => [
    sampleMaterial,
    sampleMaterial.copyWith(id: 'test-2', name: '测试材料2'),
    sampleMaterial.copyWith(id: 'test-3', name: '测试材料3'),
  ];
}
```

---

**记住**: 测试不仅是为了发现bug，更是为了确保设计系统的一致性和可靠性。
