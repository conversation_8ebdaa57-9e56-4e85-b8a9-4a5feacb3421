import 'package:flutter/material.dart';

/// VanHub间距系统
/// 定义各种尺寸的间距常量，确保设计一致性
class VanHubSpacing {
  VanHubSpacing._();

  // ============================================================================
  // 基础间距常量 (Base Spacing Constants)
  // ============================================================================
  
  /// 基础间距单位 (4px)
  static const double unit = 4.0;

  /// 超小间距 (4px)
  static const double xs = unit * 1; // 4px

  /// 小间距 (8px)
  static const double sm = unit * 2; // 8px

  /// 中等间距 (12px)
  static const double md = unit * 3; // 12px

  /// 大间距 (16px)
  static const double lg = unit * 4; // 16px

  /// 超大间距 (20px)
  static const double xl = unit * 5; // 20px

  /// 特大间距 (24px)
  static const double xxl = unit * 6; // 24px

  /// 巨大间距 (32px)
  static const double xxxl = unit * 8; // 32px

  // ============================================================================
  // 语义化间距 (Semantic Spacing)
  // ============================================================================
  
  /// 组件内部间距
  static const double componentPadding = md; // 12px

  /// 组件外部间距
  static const double componentMargin = lg; // 16px

  /// 页面边距
  static const double pageMargin = lg; // 16px

  /// 页面内边距
  static const double pagePadding = lg; // 16px

  /// 卡片内边距
  static const double cardPadding = lg; // 16px

  /// 卡片外边距
  static const double cardMargin = md; // 12px

  /// 列表项间距
  static const double listItemSpacing = sm; // 8px

  /// 表单字段间距
  static const double formFieldSpacing = lg; // 16px

  /// 按钮间距
  static const double buttonSpacing = md; // 12px

  /// 图标间距
  static const double iconSpacing = sm; // 8px

  /// 文本间距
  static const double textSpacing = xs; // 4px

  // ============================================================================
  // 布局间距 (Layout Spacing)
  // ============================================================================
  
  /// 网格间距
  static const double gridSpacing = lg; // 16px

  /// 栏间距
  static const double columnSpacing = lg; // 16px

  /// 行间距
  static const double rowSpacing = md; // 12px

  /// 分割线间距
  static const double dividerSpacing = lg; // 16px

  /// 章节间距
  static const double sectionSpacing = xxl; // 24px

  /// 内容块间距
  static const double contentBlockSpacing = xl; // 20px

  // ============================================================================
  // 响应式间距 (Responsive Spacing)
  // ============================================================================
  
  /// 手机端页面边距
  static const double mobilePageMargin = md; // 12px

  /// 平板端页面边距
  static const double tabletPageMargin = xl; // 20px

  /// 桌面端页面边距
  static const double desktopPageMargin = xxxl; // 32px

  /// 手机端卡片间距
  static const double mobileCardSpacing = sm; // 8px

  /// 平板端卡片间距
  static const double tabletCardSpacing = md; // 12px

  /// 桌面端卡片间距
  static const double desktopCardSpacing = lg; // 16px

  // ============================================================================
  // EdgeInsets 便捷方法 (EdgeInsets Convenience Methods)
  // ============================================================================
  
  /// 全方向相同间距
  static EdgeInsets all(double value) => EdgeInsets.all(value);

  /// 水平间距
  static EdgeInsets horizontal(double value) => EdgeInsets.symmetric(horizontal: value);

  /// 垂直间距
  static EdgeInsets vertical(double value) => EdgeInsets.symmetric(vertical: value);

  /// 对称间距
  static EdgeInsets symmetric({double horizontal = 0, double vertical = 0}) =>
      EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);

  /// 仅顶部间距
  static EdgeInsets onlyTop(double value) => EdgeInsets.only(top: value);

  /// 仅底部间距
  static EdgeInsets onlyBottom(double value) => EdgeInsets.only(bottom: value);

  /// 仅左侧间距
  static EdgeInsets onlyLeft(double value) => EdgeInsets.only(left: value);

  /// 仅右侧间距
  static EdgeInsets onlyRight(double value) => EdgeInsets.only(right: value);

  /// 自定义四边间距
  static EdgeInsets fromLTRB(double left, double top, double right, double bottom) =>
      EdgeInsets.fromLTRB(left, top, right, bottom);

  // ============================================================================
  // 预定义 EdgeInsets (Predefined EdgeInsets)
  // ============================================================================
  
  /// 零间距
  static const EdgeInsets zero = EdgeInsets.zero;

  /// 超小间距 - 全方向
  static const EdgeInsets allXs = EdgeInsets.all(xs);

  /// 小间距 - 全方向
  static const EdgeInsets allSm = EdgeInsets.all(sm);

  /// 中等间距 - 全方向
  static const EdgeInsets allMd = EdgeInsets.all(md);

  /// 大间距 - 全方向
  static const EdgeInsets allLg = EdgeInsets.all(lg);

  /// 超大间距 - 全方向
  static const EdgeInsets allXl = EdgeInsets.all(xl);

  /// 特大间距 - 全方向
  static const EdgeInsets allXxl = EdgeInsets.all(xxl);

  /// 巨大间距 - 全方向
  static const EdgeInsets allXxxl = EdgeInsets.all(xxxl);

  /// 水平小间距
  static const EdgeInsets horizontalSm = EdgeInsets.symmetric(horizontal: sm);

  /// 水平中等间距
  static const EdgeInsets horizontalMd = EdgeInsets.symmetric(horizontal: md);

  /// 水平大间距
  static const EdgeInsets horizontalLg = EdgeInsets.symmetric(horizontal: lg);

  /// 水平超大间距
  static const EdgeInsets horizontalXl = EdgeInsets.symmetric(horizontal: xl);

  /// 垂直小间距
  static const EdgeInsets verticalSm = EdgeInsets.symmetric(vertical: sm);

  /// 垂直中等间距
  static const EdgeInsets verticalMd = EdgeInsets.symmetric(vertical: md);

  /// 垂直大间距
  static const EdgeInsets verticalLg = EdgeInsets.symmetric(vertical: lg);

  /// 垂直超大间距
  static const EdgeInsets verticalXl = EdgeInsets.symmetric(vertical: xl);

  /// 页面内边距
  static const EdgeInsets page = EdgeInsets.all(pageMargin);

  /// 卡片内边距
  static const EdgeInsets card = EdgeInsets.all(cardPadding);

  /// 表单内边距
  static const EdgeInsets form = EdgeInsets.all(formFieldSpacing);

  /// 按钮内边距
  static const EdgeInsets button = EdgeInsets.symmetric(
    horizontal: lg,
    vertical: md,
  );

  /// 列表项内边距
  static const EdgeInsets listItem = EdgeInsets.symmetric(
    horizontal: lg,
    vertical: md,
  );

  // ============================================================================
  // SizedBox 便捷方法 (SizedBox Convenience Methods)
  // ============================================================================
  
  /// 水平间距盒子
  static Widget horizontalSpace(double width) => SizedBox(width: width);

  /// 垂直间距盒子
  static Widget verticalSpace(double height) => SizedBox(height: height);

  /// 预定义水平间距盒子
  static const Widget hXs = SizedBox(width: xs);
  static const Widget hSm = SizedBox(width: sm);
  static const Widget hMd = SizedBox(width: md);
  static const Widget hLg = SizedBox(width: lg);
  static const Widget hXl = SizedBox(width: xl);
  static const Widget hXxl = SizedBox(width: xxl);
  static const Widget hXxxl = SizedBox(width: xxxl);

  /// 预定义垂直间距盒子
  static const Widget vXs = SizedBox(height: xs);
  static const Widget vSm = SizedBox(height: sm);
  static const Widget vMd = SizedBox(height: md);
  static const Widget vLg = SizedBox(height: lg);
  static const Widget vXl = SizedBox(height: xl);
  static const Widget vXxl = SizedBox(height: xxl);
  static const Widget vXxxl = SizedBox(height: xxxl);

  // ============================================================================
  // 响应式间距方法 (Responsive Spacing Methods)
  // ============================================================================
  
  /// 获取响应式页面边距
  static double getResponsivePageMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return mobilePageMargin;
    } else if (screenWidth < 1200) {
      return tabletPageMargin;
    } else {
      return desktopPageMargin;
    }
  }

  /// 获取响应式卡片间距
  static double getResponsiveCardSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return mobileCardSpacing;
    } else if (screenWidth < 1200) {
      return tabletCardSpacing;
    } else {
      return desktopCardSpacing;
    }
  }

  /// 获取响应式内边距
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final margin = getResponsivePageMargin(context);
    return EdgeInsets.all(margin);
  }

  // ============================================================================
  // 兼容性属性 (Compatibility Properties)
  // ============================================================================

  /// 间距getter - 兼容性属性
  static Widget get gapVerticalXl => vXl;
  static Widget get gapVerticalLg => vLg;
  static Widget get gapVerticalMd => vMd;
  static Widget get gapVerticalSm => vSm;
  static Widget get gapVerticalXs => vXs;

  static Widget get gapHorizontalXl => hXl;
  static Widget get gapHorizontalLg => hLg;
  static Widget get gapHorizontalMd => hMd;
  static Widget get gapHorizontalSm => hSm;
  static Widget get gapHorizontalXs => hXs;

  /// 内边距getter - 兼容性属性
  static EdgeInsets get paddingPageHorizontal => horizontalLg;
  static EdgeInsets get paddingSm => allSm;
  static EdgeInsets get paddingXs => allXs;
  static EdgeInsets get paddingVerticalSm => verticalSm;

  /// 获取响应式水平内边距
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    final margin = getResponsivePageMargin(context);
    return EdgeInsets.symmetric(horizontal: margin);
  }

  /// 获取响应式垂直内边距
  static EdgeInsets getResponsiveVerticalPadding(BuildContext context) {
    final margin = getResponsivePageMargin(context);
    return EdgeInsets.symmetric(vertical: margin);
  }

  // ============================================================================
  // 圆角系统兼容性 (Border Radius Compatibility)
  // ============================================================================

  /// 小圆角
  static const double radiusSmall = 4.0;

  /// 中等圆角
  static const double radiusMedium = 8.0;

  /// 大圆角
  static const double radiusLarge = 12.0;

  /// 超大圆角
  static const double radiusXLarge = 16.0;
}