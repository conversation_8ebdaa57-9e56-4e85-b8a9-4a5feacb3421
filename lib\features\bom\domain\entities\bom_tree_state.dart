import 'package:freezed_annotation/freezed_annotation.dart';
import 'tree_node.dart';
import '../services/bom_tree_service.dart';
import 'bom_item.dart';

part 'bom_tree_state.freezed.dart';

/// BOM树形结构状态
/// 严格遵循Clean Architecture原则，定义BOM树形结构的状态数据
@freezed
class BomTreeState with _$BomTreeState {
  const factory BomTreeState({
    /// 树形结构数据
    @Default([]) List<TreeNode> tree,
    
    /// 是否正在加载
    @Default(false) bool isLoading,
    
    /// 错误信息
    String? errorMessage,
    
    /// 搜索查询
    @Default('') String searchQuery,
    
    /// 搜索结果（节点ID列表）
    @Default([]) List<String> searchResults,
    
    /// 是否处于搜索状态
    @Default(false) bool isSearchActive,
    
    /// 当前选中的节点ID
    String? selectedNodeId,

    /// 分类统计信息
    @Default({}) Map<String, BomCategoryStats> categoryStats,
    
    /// 最后更新时间
    DateTime? lastUpdated,
    
    /// BOM项目总数
    @Default(0) int totalBomItemCount,
    
    /// 当前过滤的状态列表
    @Default([]) List<BomItemStatus> filteredStatuses,
    
    /// 价格过滤范围
    PriceRange? priceRange,
    
    /// 排序模式
    BomTreeSortMode? sortMode,
    
    /// 是否显示统计信息
    @Default(true) bool showStatistics,
    
    /// 是否启用拖拽功能
    @Default(false) bool isDragEnabled,
    
    /// 当前拖拽的节点ID
    String? draggingNodeId,
    
    /// 拖拽目标节点ID
    String? dropTargetNodeId,
  }) = _BomTreeState;

  const BomTreeState._();

  /// 是否有错误
  bool get hasError => errorMessage != null;

  /// 是否为空树
  bool get isEmpty => tree.isEmpty;

  /// 是否有搜索结果
  bool get hasSearchResults => searchResults.isNotEmpty;

  /// 获取展开的节点数量
  int get expandedNodeCount {
    int count = 0;
    void countExpanded(List<TreeNode> nodes) {
      for (final node in nodes) {
        if (node.isExpanded) {
          count++;
          countExpanded(node.children);
        }
      }
    }
    countExpanded(tree);
    return count;
  }

  /// 获取可见的节点数量
  int get visibleNodeCount {
    int count = 0;
    void countVisible(List<TreeNode> nodes, bool parentExpanded) {
      for (final node in nodes) {
        if (parentExpanded) {
          count++;
          countVisible(node.children, node.isExpanded);
        }
      }
    }
    countVisible(tree, true);
    return count;
  }

  /// 获取总价值
  double get totalValue {
    double total = 0.0;
    for (final stats in categoryStats.values) {
      total += stats.totalValue;
    }
    return total;
  }

  /// 获取完成率
  double get completionRate {
    if (totalBomItemCount == 0) return 0.0;
    
    int completedCount = 0;
    for (final stats in categoryStats.values) {
      completedCount += stats.completedCount;
    }
    
    return completedCount / totalBomItemCount;
  }

  /// 获取状态分布
  Map<BomItemStatus, int> get statusDistribution {
    final Map<BomItemStatus, int> distribution = {};
    
    for (final status in BomItemStatus.values) {
      distribution[status] = 0;
    }
    
    for (final stats in categoryStats.values) {
      for (final entry in stats.statusDistribution.entries) {
        distribution[entry.key] = (distribution[entry.key] ?? 0) + entry.value;
      }
    }
    
    return distribution;
  }

  /// 是否正在拖拽
  bool get isDragging => draggingNodeId != null;

  /// 是否有有效的拖拽目标
  bool get hasValidDropTarget => dropTargetNodeId != null && dropTargetNodeId != draggingNodeId;
}

/// 价格过滤范围
@freezed
class PriceRange with _$PriceRange {
  const factory PriceRange({
    required double min,
    required double max,
  }) = _PriceRange;
}


