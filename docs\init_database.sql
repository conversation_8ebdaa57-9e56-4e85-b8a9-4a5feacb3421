-- VanHub改装宝数据库初始化脚本（简化版）

-- 1. 项目表 (projects)
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'planning',
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 材料分类表 (material_categories)
CREATE TABLE IF NOT EXISTS material_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 材料表 (materials)
CREATE TABLE IF NOT EXISTS materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES material_categories(id) ON DELETE SET NULL,
    unit VARCHAR(50) DEFAULT '个',
    price DECIMAL(10,2) DEFAULT 0,
    supplier VARCHAR(255),
    model VARCHAR(255),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. BOM清单表 (bom_items)
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, material_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_materials_user_id ON materials(user_id);
CREATE INDEX IF NOT EXISTS idx_bom_items_project_id ON bom_items(project_id);

-- 启用RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;

-- 项目表的RLS策略
CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING (auth.uid() = user_id);

-- 材料表的RLS策略  
CREATE POLICY "Users can view all materials" ON materials
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own materials" ON materials
    FOR ALL USING (auth.uid() = user_id);

-- BOM清单表的RLS策略
CREATE POLICY "Users can manage BOM items for their projects" ON bom_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 材料分类表允许所有用户查看
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view material categories" ON material_categories
    FOR SELECT USING (true);

-- 插入默认材料分类
INSERT INTO material_categories (name, description) VALUES
('电气系统', '电池、逆变器、充电器等电气设备'),
('水系统', '水泵、水箱、净水器等水系统设备'),
('燃气系统', '燃气灶、热水器、燃气罐等燃气设备'),
('家具', '床、桌椅、储物柜等家具'),
('装饰材料', '地板、墙面、窗帘等装饰材料'),
('工具配件', '螺丝、胶水、线材等工具配件')
ON CONFLICT DO NOTHING;

-- 插入示例材料数据（需要在有用户登录后执行）
-- INSERT INTO materials (name, description, category_id, unit, price, supplier, model, user_id) VALUES
-- ('锂电池', '12V 100Ah磷酸铁锂电池', (SELECT id FROM material_categories WHERE name = '电气系统'), '个', 1200.00, '比亚迪', 'BYD-LFP100', auth.uid()),
-- ('逆变器', '2000W纯正弦波逆变器', (SELECT id FROM material_categories WHERE name = '电气系统'), '个', 800.00, '正弦', 'ZX-2000W', auth.uid())
-- ON CONFLICT DO NOTHING;
