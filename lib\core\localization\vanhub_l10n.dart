import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/cupertino.dart';

/// VanHub国际化支持系统
/// 提供多语言支持、RTL布局适配和文化敏感内容处理
class VanHubL10n {
  static const supportedLocales = [
    Locale('zh', 'CN'), // 简体中文
    Locale('en', 'US'), // 英文（美国）
  ];
  
  static const fallbackLocale = Locale('en', 'US');
  
  /// 本地化代理
  static const localizationsDelegates = [
    VanHubL10nDelegate(),
  ];
  
  static VanHubL10n of(BuildContext context) {
    final locale = Localizations.localeOf(context);
    return VanHubL10n(locale);
  }
  
  final Locale locale;
  Map<String, dynamic> _localizedValues = {};
  bool _isLoaded = false;
  
  VanHubL10n(this.locale);
  
  /// 加载本地化资源
  Future<bool> load() async {
    if (_isLoaded) return true;
    
    String jsonContent;
    try {
      jsonContent = await rootBundle.loadString(
        'assets/l10n/${locale.languageCode}_${locale.countryCode}.json'
      );
    } catch (e) {
      // 回退到默认语言
      jsonContent = await rootBundle.loadString(
        'assets/l10n/${fallbackLocale.languageCode}_${fallbackLocale.countryCode}.json'
      );
    }
    
    _localizedValues = json.decode(jsonContent);
    _isLoaded = true;
    return true;
  }
  
  /// 获取本地化字符串
  String get(String key, {Map<String, dynamic>? args}) {
    if (!_isLoaded) {
      return key;
    }
    
    String value = _localizedValues[key] ?? key;
    
    if (args != null) {
      args.forEach((argKey, argValue) {
        value = value.replaceAll('{$argKey}', argValue.toString());
      });
    }
    
    return value;
  }
  
  /// 应用名称
  String get appName => get('app_name');
  
  /// 首页
  String get home => get('home');
  
  /// 项目
  String get projects => get('projects');
  
  /// 材料
  String get materials => get('materials');
  
  /// 物料清单
  String get bom => get('bom');
  
  /// 个人资料
  String get profile => get('profile');
  
  /// 设置
  String get settings => get('settings');
  
  /// 登录
  String get login => get('login');
  
  /// 退出登录
  String get logout => get('logout');
  
  /// 注册
  String get register => get('register');
  
  /// 邮箱
  String get email => get('email');
  
  /// 密码
  String get password => get('password');
  
  /// 确认密码
  String get confirmPassword => get('confirm_password');
  
  /// 忘记密码
  String get forgotPassword => get('forgot_password');
  
  /// 记住我
  String get rememberMe => get('remember_me');
  
  /// 提交
  String get submit => get('submit');
  
  /// 取消
  String get cancel => get('cancel');
  
  /// 保存
  String get save => get('save');
  
  /// 删除
  String get delete => get('delete');
  
  /// 编辑
  String get edit => get('edit');
  
  /// 添加
  String get add => get('add');
  
  /// 搜索
  String get search => get('search');
  
  /// 筛选
  String get filter => get('filter');
  
  /// 排序
  String get sort => get('sort');
  
  /// 刷新
  String get refresh => get('refresh');
  
  /// 加载中...
  String get loading => get('loading');
  
  /// 错误
  String get error => get('error');
  
  /// 成功
  String get success => get('success');
  
  /// 警告
  String get warning => get('warning');
  
  /// 信息
  String get info => get('info');
  
  /// 确认
  String get confirm => get('confirm');
  
  /// 是
  String get yes => get('yes');
  
  /// 否
  String get no => get('no');
  
  /// 确定
  String get ok => get('ok');
  
  /// 重试
  String get retry => get('retry');
  
  /// 关闭
  String get close => get('close');
  
  /// 返回
  String get back => get('back');
  
  /// 下一步
  String get next => get('next');
  
  /// 上一步
  String get previous => get('previous');
  
  /// 完成
  String get finish => get('finish');
  
  /// 跳过
  String get skip => get('skip');
  
  /// 完成
  String get done => get('done');
  
  /// 名称
  String get name => get('name');
  
  /// 描述
  String get description => get('description');
  
  /// 价格
  String get price => get('price');
  
  /// 数量
  String get quantity => get('quantity');
  
  /// 总计
  String get total => get('total');
  
  /// 分类
  String get category => get('category');
  
  /// 状态
  String get status => get('status');
  
  /// 日期
  String get date => get('date');
  
  /// 时间
  String get time => get('time');
  
  /// 创建时间
  String get created => get('created');
  
  /// 更新时间
  String get updated => get('updated');
  
  /// 作者
  String get author => get('author');
  
  /// 版本
  String get version => get('version');
  
  /// 语言
  String get language => get('language');
  
  /// 主题
  String get theme => get('theme');
  
  /// 深色模式
  String get darkMode => get('dark_mode');
  
  /// 浅色模式
  String get lightMode => get('light_mode');
  
  /// 跟随系统
  String get systemMode => get('system_mode');
  
  /// 邮箱不能为空
  String get validationEmailRequired => get('validation_email_required');
  
  /// 请输入有效的邮箱地址
  String get validationEmailInvalid => get('validation_email_invalid');
  
  /// 密码不能为空
  String get validationPasswordRequired => get('validation_password_required');
  
  /// 密码长度不能少于{length}位
  String validationPasswordMinLength(int length) => get(
    'validation_password_min_length',
    args: {'length': length},
  );
  
  /// {field}不能为空
  String validationFieldRequired(String field) => get(
    'validation_field_required',
    args: {'field': field},
  );
  
  /// 请输入有效的数字
  String get validationNumberInvalid => get('validation_number_invalid');
  
  /// 数值不能小于{min}
  String validationNumberMin(num min) => get(
    'validation_number_min',
    args: {'min': min},
  );
  
  /// 数值不能大于{max}
  String validationNumberMax(num max) => get(
    'validation_number_max',
    args: {'max': max},
  );
  
  /// {count}天前
  String relativeDaysAgo(int count) => get(
    'relative_time_days_ago',
    args: {'count': count},
  );
  
  /// {count}小时前
  String relativeHoursAgo(int count) => get(
    'relative_time_hours_ago',
    args: {'count': count},
  );
  
  /// {count}分钟前
  String relativeMinutesAgo(int count) => get(
    'relative_time_minutes_ago',
    args: {'count': count},
  );
  
  /// 刚刚
  String get relativeJustNow => get('relative_time_just_now');
  
  /// {size}字节
  String fileSizeBytes(int size) => get(
    'file_size_bytes',
    args: {'size': size},
  );
  
  /// {size}KB
  String fileSizeKB(double size) => get(
    'file_size_kb',
    args: {'size': size.toStringAsFixed(1)},
  );
  
  /// {size}MB
  String fileSizeMB(double size) => get(
    'file_size_mb',
    args: {'size': size.toStringAsFixed(1)},
  );
  
  /// {size}GB
  String fileSizeGB(double size) => get(
    'file_size_gb',
    args: {'size': size.toStringAsFixed(1)},
  );
  
  /// {distance}米
  String distanceMeters(int distance) => get(
    'distance_meters',
    args: {'distance': distance},
  );
  
  /// {distance}公里
  String distanceKilometers(double distance) => get(
    'distance_kilometers',
    args: {'distance': distance.toStringAsFixed(1)},
  );
}

/// VanHub本地化代理
class VanHubL10nDelegate extends LocalizationsDelegate<VanHubL10n> {
  const VanHubL10nDelegate();

  @override
  bool isSupported(Locale locale) {
    return VanHubL10n.supportedLocales.contains(locale) ||
        VanHubL10n.supportedLocales.any((supportedLocale) =>
            supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<VanHubL10n> load(Locale locale) async {
    final l10n = VanHubL10n(locale);
    await l10n.load();
    return l10n;
  }

  @override
  bool shouldReload(VanHubL10nDelegate old) => false;
  
}

/// VanHub本地化委托
class VanHubLocalizationsDelegate extends LocalizationsDelegate<VanHubL10n> {
  const VanHubLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return VanHubL10n.supportedLocales.any(
      (supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode &&
        supportedLocale.countryCode == locale.countryCode
    );
  }

  @override
  Future<VanHubL10n> load(Locale locale) async {
    final localizations = VanHubL10n(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(VanHubLocalizationsDelegate old) => false;
}

/// VanHub国际化包装器
class VanHubLocalizationWrapper extends StatefulWidget {
  final Widget child;
  final Locale? locale;

  const VanHubLocalizationWrapper({
    super.key,
    required this.child,
    this.locale,
  });

  @override
  State<VanHubLocalizationWrapper> createState() => _VanHubLocalizationWrapperState();
}

class _VanHubLocalizationWrapperState extends State<VanHubLocalizationWrapper> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: widget.locale,
      supportedLocales: VanHubL10n.supportedLocales,
      localizationsDelegates: const [
        VanHubLocalizationsDelegate(),
        DefaultMaterialLocalizations.delegate,
        DefaultWidgetsLocalizations.delegate,
        DefaultCupertinoLocalizations.delegate,
      ],
      home: widget.child,
    );
  }
}

/// 文本方向辅助类
class VanHubTextDirection {
  /// 获取文本方向
  static TextDirection getTextDirection(Locale locale) {
    // RTL语言列表
    const rtlLanguages = ['ar', 'fa', 'he', 'ur'];
    
    return rtlLanguages.contains(locale.languageCode)
        ? TextDirection.rtl
        : TextDirection.ltr;
  }
  
  /// 是否为RTL语言
  static bool isRtl(Locale locale) {
    return getTextDirection(locale) == TextDirection.rtl;
  }
  
  /// 创建支持RTL的Padding
  static EdgeInsets getRtlAwarePadding({
    required double start,
    required double top,
    required double end,
    required double bottom,
    required TextDirection textDirection,
  }) {
    return textDirection == TextDirection.rtl
        ? EdgeInsets.fromLTRB(end, top, start, bottom)
        : EdgeInsets.fromLTRB(start, top, end, bottom);
  }
  
  /// 创建支持RTL的Border
  static Border getRtlAwareBorder({
    required Color startColor,
    required Color topColor,
    required Color endColor,
    required Color bottomColor,
    required TextDirection textDirection,
    required double width,
  }) {
    return textDirection == TextDirection.rtl
        ? Border(
            left: BorderSide(color: endColor, width: width),
            top: BorderSide(color: topColor, width: width),
            right: BorderSide(color: startColor, width: width),
            bottom: BorderSide(color: bottomColor, width: width),
          )
        : Border(
            left: BorderSide(color: startColor, width: width),
            top: BorderSide(color: topColor, width: width),
            right: BorderSide(color: endColor, width: width),
            bottom: BorderSide(color: bottomColor, width: width),
          );
  }
}