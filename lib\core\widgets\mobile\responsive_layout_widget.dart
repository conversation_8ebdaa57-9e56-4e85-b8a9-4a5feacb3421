import 'package:flutter/material.dart';

/// 设备类型枚举
enum DeviceType {
  mobile,   // 手机 (< 600px)
  tablet,   // 平板 (600px - 1200px)
  desktop,  // 桌面 (> 1200px)
}

/// 屏幕方向枚举
enum ScreenOrientation {
  portrait,   // 竖屏
  landscape,  // 横屏
}

/// 响应式布局信息
class ResponsiveInfo {
  final DeviceType deviceType;
  final ScreenOrientation orientation;
  final Size screenSize;
  final double devicePixelRatio;
  final bool isSmallScreen;
  final bool isMediumScreen;
  final bool isLargeScreen;

  const ResponsiveInfo({
    required this.deviceType,
    required this.orientation,
    required this.screenSize,
    required this.devicePixelRatio,
    required this.isSmallScreen,
    required this.isMediumScreen,
    required this.isLargeScreen,
  });

  factory ResponsiveInfo.fromContext(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final width = size.width;
    final height = size.height;
    final devicePixelRatio = mediaQuery.devicePixelRatio;

    // 确定设备类型
    DeviceType deviceType;
    if (width < 600) {
      deviceType = DeviceType.mobile;
    } else if (width < 1200) {
      deviceType = DeviceType.tablet;
    } else {
      deviceType = DeviceType.desktop;
    }

    // 确定屏幕方向
    final orientation = width > height 
        ? ScreenOrientation.landscape 
        : ScreenOrientation.portrait;

    return ResponsiveInfo(
      deviceType: deviceType,
      orientation: orientation,
      screenSize: size,
      devicePixelRatio: devicePixelRatio,
      isSmallScreen: width < 600,
      isMediumScreen: width >= 600 && width < 1200,
      isLargeScreen: width >= 1200,
    );
  }

  bool get isMobile => deviceType == DeviceType.mobile;
  bool get isTablet => deviceType == DeviceType.tablet;
  bool get isDesktop => deviceType == DeviceType.desktop;
  bool get isPortrait => orientation == ScreenOrientation.portrait;
  bool get isLandscape => orientation == ScreenOrientation.landscape;
}

/// 响应式布局构建器
class ResponsiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ResponsiveInfo info) builder;

  const ResponsiveLayoutBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final info = ResponsiveInfo.fromContext(context);
    return builder(context, info);
  }
}

/// 响应式布局组件
class ResponsiveLayout extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? fallback;

  const ResponsiveLayout({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        switch (info.deviceType) {
          case DeviceType.mobile:
            return mobile ?? tablet ?? desktop ?? fallback ?? const SizedBox();
          case DeviceType.tablet:
            return tablet ?? desktop ?? mobile ?? fallback ?? const SizedBox();
          case DeviceType.desktop:
            return desktop ?? tablet ?? mobile ?? fallback ?? const SizedBox();
        }
      },
    );
  }
}

/// 响应式值
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  T getValue(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  static T of<T>(BuildContext context, ResponsiveValue<T> value) {
    final info = ResponsiveInfo.fromContext(context);
    return value.getValue(info.deviceType);
  }
}

/// 响应式间距
class ResponsiveSpacing {
  static const ResponsiveValue<double> small = ResponsiveValue(
    mobile: 8.0,
    tablet: 12.0,
    desktop: 16.0,
  );

  static const ResponsiveValue<double> medium = ResponsiveValue(
    mobile: 16.0,
    tablet: 20.0,
    desktop: 24.0,
  );

  static const ResponsiveValue<double> large = ResponsiveValue(
    mobile: 24.0,
    tablet: 32.0,
    desktop: 40.0,
  );

  static const ResponsiveValue<double> extraLarge = ResponsiveValue(
    mobile: 32.0,
    tablet: 48.0,
    desktop: 64.0,
  );
}

/// 响应式字体大小
class ResponsiveFontSize {
  static const ResponsiveValue<double> small = ResponsiveValue(
    mobile: 12.0,
    tablet: 13.0,
    desktop: 14.0,
  );

  static const ResponsiveValue<double> medium = ResponsiveValue(
    mobile: 14.0,
    tablet: 15.0,
    desktop: 16.0,
  );

  static const ResponsiveValue<double> large = ResponsiveValue(
    mobile: 16.0,
    tablet: 18.0,
    desktop: 20.0,
  );

  static const ResponsiveValue<double> extraLarge = ResponsiveValue(
    mobile: 20.0,
    tablet: 24.0,
    desktop: 28.0,
  );

  static const ResponsiveValue<double> title = ResponsiveValue(
    mobile: 24.0,
    tablet: 28.0,
    desktop: 32.0,
  );
}

/// 响应式网格
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final ResponsiveValue<int> crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.crossAxisCount = const ResponsiveValue(
      mobile: 1,
      tablet: 2,
      desktop: 3,
    ),
    this.mainAxisSpacing = 16.0,
    this.crossAxisSpacing = 16.0,
    this.childAspectRatio = 1.0,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        final count = crossAxisCount.getValue(info.deviceType);
        
        return GridView.builder(
          physics: physics,
          shrinkWrap: shrinkWrap,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: count,
            mainAxisSpacing: mainAxisSpacing,
            crossAxisSpacing: crossAxisSpacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// 响应式容器
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final ResponsiveValue<EdgeInsets>? padding;
  final ResponsiveValue<EdgeInsets>? margin;
  final ResponsiveValue<double>? width;
  final ResponsiveValue<double>? height;
  final Color? color;
  final Decoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.color,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        return Container(
          padding: padding?.getValue(info.deviceType),
          margin: margin?.getValue(info.deviceType),
          width: width?.getValue(info.deviceType),
          height: height?.getValue(info.deviceType),
          color: color,
          decoration: decoration,
          child: child,
        );
      },
    );
  }
}

/// 响应式文本
class ResponsiveText extends StatelessWidget {
  final String text;
  final ResponsiveValue<double>? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        return Text(
          text,
          style: TextStyle(
            fontSize: fontSize?.getValue(info.deviceType),
            fontWeight: fontWeight,
            color: color,
          ),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}

/// 响应式间距组件
class ResponsiveSpacingWidget extends StatelessWidget {
  final ResponsiveValue<double> spacing;
  final bool isVertical;

  const ResponsiveSpacingWidget({
    super.key,
    required this.spacing,
    this.isVertical = true,
  });

  const ResponsiveSpacingWidget.horizontal({
    super.key,
    required this.spacing,
  }) : isVertical = false;

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, info) {
        final space = spacing.getValue(info.deviceType);
        return SizedBox(
          width: isVertical ? null : space,
          height: isVertical ? space : null,
        );
      },
    );
  }
}

/// 响应式工具类
class ResponsiveUtils {
  /// 获取响应式值
  static T getValue<T>(BuildContext context, ResponsiveValue<T> value) {
    final info = ResponsiveInfo.fromContext(context);
    return value.getValue(info.deviceType);
  }

  /// 获取设备信息
  static ResponsiveInfo getInfo(BuildContext context) {
    return ResponsiveInfo.fromContext(context);
  }

  /// 是否为移动设备
  static bool isMobile(BuildContext context) {
    return getInfo(context).isMobile;
  }

  /// 是否为平板设备
  static bool isTablet(BuildContext context) {
    return getInfo(context).isTablet;
  }

  /// 是否为桌面设备
  static bool isDesktop(BuildContext context) {
    return getInfo(context).isDesktop;
  }

  /// 是否为竖屏
  static bool isPortrait(BuildContext context) {
    return getInfo(context).isPortrait;
  }

  /// 是否为横屏
  static bool isLandscape(BuildContext context) {
    return getInfo(context).isLandscape;
  }

  /// 获取安全区域内边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全区域高度
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }
}
