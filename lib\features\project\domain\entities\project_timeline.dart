import 'package:freezed_annotation/freezed_annotation.dart';

part 'project_timeline.freezed.dart';
part 'project_timeline.g.dart';

/// JSON converters for union types
class TimelineStatusConverter implements JsonConverter<TimelineStatus, String> {
  const TimelineStatusConverter();

  @override
  TimelineStatus fromJson(String json) {
    switch (json) {
      case 'notStarted':
        return const TimelineStatus.notStarted();
      case 'inProgress':
        return const TimelineStatus.inProgress();
      case 'completed':
        return const TimelineStatus.completed();
      case 'delayed':
        return const TimelineStatus.delayed();
      case 'cancelled':
        return const TimelineStatus.cancelled();
      default:
        return const TimelineStatus.notStarted();
    }
  }

  @override
  String toJson(TimelineStatus status) {
    return status.when(
      notStarted: () => 'notStarted',
      inProgress: () => 'inProgress',
      completed: () => 'completed',
      delayed: () => 'delayed',
      cancelled: () => 'cancelled',
    );
  }
}

class TimelineTypeConverter implements JsonConverter<TimelineType, String> {
  const TimelineTypeConverter();

  @override
  TimelineType fromJson(String json) {
    switch (json) {
      case 'milestone':
        return const TimelineType.milestone();
      case 'task':
        return const TimelineType.task();
      case 'phase':
        return const TimelineType.phase();
      case 'review':
        return const TimelineType.review();
      case 'delivery':
        return const TimelineType.delivery();
      default:
        return const TimelineType.task();
    }
  }

  @override
  String toJson(TimelineType type) {
    return type.when(
      milestone: () => 'milestone',
      task: () => 'task',
      phase: () => 'phase',
      review: () => 'review',
      delivery: () => 'delivery',
    );
  }
}

class MilestoneStatusConverter implements JsonConverter<MilestoneStatus, String> {
  const MilestoneStatusConverter();

  @override
  MilestoneStatus fromJson(String json) {
    switch (json) {
      case 'upcoming':
        return const MilestoneStatus.upcoming();
      case 'active':
        return const MilestoneStatus.active();
      case 'completed':
        return const MilestoneStatus.completed();
      case 'overdue':
        return const MilestoneStatus.overdue();
      case 'cancelled':
        return const MilestoneStatus.cancelled();
      default:
        return const MilestoneStatus.upcoming();
    }
  }

  @override
  String toJson(MilestoneStatus status) {
    return status.when(
      upcoming: () => 'upcoming',
      active: () => 'active',
      completed: () => 'completed',
      overdue: () => 'overdue',
      cancelled: () => 'cancelled',
    );
  }
}

class MilestonePriorityConverter implements JsonConverter<MilestonePriority, String> {
  const MilestonePriorityConverter();

  @override
  MilestonePriority fromJson(String json) {
    switch (json) {
      case 'low':
        return const MilestonePriority.low();
      case 'medium':
        return const MilestonePriority.medium();
      case 'high':
        return const MilestonePriority.high();
      case 'critical':
        return const MilestonePriority.critical();
      default:
        return const MilestonePriority.medium();
    }
  }

  @override
  String toJson(MilestonePriority priority) {
    return priority.when(
      low: () => 'low',
      medium: () => 'medium',
      high: () => 'high',
      critical: () => 'critical',
    );
  }
}

class TaskStatusConverter implements JsonConverter<TaskStatus, String> {
  const TaskStatusConverter();

  @override
  TaskStatus fromJson(String json) {
    switch (json) {
      case 'todo':
        return const TaskStatus.todo();
      case 'inProgress':
        return const TaskStatus.inProgress();
      case 'review':
        return const TaskStatus.review();
      case 'completed':
        return const TaskStatus.completed();
      case 'blocked':
        return const TaskStatus.blocked();
      case 'cancelled':
        return const TaskStatus.cancelled();
      default:
        return const TaskStatus.todo();
    }
  }

  @override
  String toJson(TaskStatus status) {
    return status.when(
      todo: () => 'todo',
      inProgress: () => 'inProgress',
      review: () => 'review',
      completed: () => 'completed',
      blocked: () => 'blocked',
      cancelled: () => 'cancelled',
    );
  }
}

class TaskPriorityConverter implements JsonConverter<TaskPriority, String> {
  const TaskPriorityConverter();

  @override
  TaskPriority fromJson(String json) {
    switch (json) {
      case 'low':
        return const TaskPriority.low();
      case 'medium':
        return const TaskPriority.medium();
      case 'high':
        return const TaskPriority.high();
      case 'urgent':
        return const TaskPriority.urgent();
      default:
        return const TaskPriority.medium();
    }
  }

  @override
  String toJson(TaskPriority priority) {
    return priority.when(
      low: () => 'low',
      medium: () => 'medium',
      high: () => 'high',
      urgent: () => 'urgent',
    );
  }
}

/// Project timeline entity for milestone and task tracking
@freezed
class ProjectTimeline with _$ProjectTimeline {
  const factory ProjectTimeline({
    required String id,
    required String projectId,
    required String title,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    DateTime? actualStartDate,
    DateTime? actualEndDate,
    @TimelineStatusConverter() required TimelineStatus status,
    @TimelineTypeConverter() required TimelineType type,
    required int orderIndex,
    String? assignedTo,
    List<String>? dependencies,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _ProjectTimeline;

  factory ProjectTimeline.fromJson(Map<String, dynamic> json) =>
      _$ProjectTimelineFromJson(json);
}

/// Timeline status enumeration
@freezed
class TimelineStatus with _$TimelineStatus {
  const factory TimelineStatus.notStarted() = _NotStarted;
  const factory TimelineStatus.inProgress() = _InProgress;
  const factory TimelineStatus.completed() = _Completed;
  const factory TimelineStatus.delayed() = _Delayed;
  const factory TimelineStatus.cancelled() = _Cancelled;
}

/// Timeline type enumeration
@freezed
class TimelineType with _$TimelineType {
  const factory TimelineType.milestone() = _Milestone;
  const factory TimelineType.task() = _Task;
  const factory TimelineType.phase() = _Phase;
  const factory TimelineType.review() = _Review;
  const factory TimelineType.delivery() = _Delivery;
}

/// Project milestone entity
@freezed
class ProjectMilestone with _$ProjectMilestone {
  const factory ProjectMilestone({
    required String id,
    required String projectId,
    required String title,
    required String description,
    required DateTime targetDate,
    DateTime? completedDate,
    @MilestoneStatusConverter() required MilestoneStatus status,
    @MilestonePriorityConverter() required MilestonePriority priority,
    List<String>? requiredTasks,
    List<String>? deliverables,
    double? completionPercentage,
    String? notes,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _ProjectMilestone;

  factory ProjectMilestone.fromJson(Map<String, dynamic> json) =>
      _$ProjectMilestoneFromJson(json);
}

/// Milestone status enumeration
@freezed
class MilestoneStatus with _$MilestoneStatus {
  const factory MilestoneStatus.upcoming() = _MilestoneUpcoming;
  const factory MilestoneStatus.active() = _MilestoneActive;
  const factory MilestoneStatus.completed() = _MilestoneCompleted;
  const factory MilestoneStatus.overdue() = _MilestoneOverdue;
  const factory MilestoneStatus.cancelled() = _MilestoneCancelled;
}

/// Milestone priority enumeration
@freezed
class MilestonePriority with _$MilestonePriority {
  const factory MilestonePriority.low() = _MilestoneLow;
  const factory MilestonePriority.medium() = _MilestoneMedium;
  const factory MilestonePriority.high() = _MilestoneHigh;
  const factory MilestonePriority.critical() = _MilestoneCritical;
}

/// Project task entity
@freezed
class ProjectTask with _$ProjectTask {
  const factory ProjectTask({
    required String id,
    required String projectId,
    String? milestoneId,
    required String title,
    required String description,
    required DateTime dueDate,
    DateTime? completedDate,
    @TaskStatusConverter() required TaskStatus status,
    @TaskPriorityConverter() required TaskPriority priority,
    String? assignedTo,
    List<String>? dependencies,
    List<String>? subtasks,
    double? estimatedHours,
    double? actualHours,
    List<String>? tags,
    Map<String, dynamic>? customFields,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _ProjectTask;

  factory ProjectTask.fromJson(Map<String, dynamic> json) =>
      _$ProjectTaskFromJson(json);
}

/// Task status enumeration
@freezed
class TaskStatus with _$TaskStatus {
  const factory TaskStatus.todo() = _TaskTodo;
  const factory TaskStatus.inProgress() = _TaskInProgress;
  const factory TaskStatus.review() = _TaskReview;
  const factory TaskStatus.completed() = _TaskCompleted;
  const factory TaskStatus.blocked() = _TaskBlocked;
  const factory TaskStatus.cancelled() = _TaskCancelled;
}

/// Task priority enumeration
@freezed
class TaskPriority with _$TaskPriority {
  const factory TaskPriority.low() = _TaskLow;
  const factory TaskPriority.medium() = _TaskMedium;
  const factory TaskPriority.high() = _TaskHigh;
  const factory TaskPriority.urgent() = _TaskUrgent;
}
