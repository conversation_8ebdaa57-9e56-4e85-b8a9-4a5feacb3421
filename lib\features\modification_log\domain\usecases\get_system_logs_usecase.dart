import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../repositories/log_repository.dart';

/// 获取系统日志条目用例
class GetSystemLogsUseCase implements UseCase<List<LogEntry>, GetSystemLogsParams> {
  final LogRepository repository;

  GetSystemLogsUseCase(this.repository);

  @override
  Future<Either<Failure, List<LogEntry>>> call(GetSystemLogsParams params) async {
    return await repository.getSystemLogs(params.systemId);
  }
}

/// 获取系统日志条目参数
class GetSystemLogsParams {
  final String systemId;

  GetSystemLogsParams({required this.systemId});
}