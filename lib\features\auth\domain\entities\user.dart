import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    String? name,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false) bool isAnonymous,
  }) = _User;

  const User._();

  /// 创建游客用户
  factory User.guest() {
    final now = DateTime.now();
    return User(
      id: 'guest_${now.millisecondsSinceEpoch}',
      email: '<EMAIL>',
      name: '游客用户',
      avatarUrl: null,
      createdAt: now,
      updatedAt: now,
      isAnonymous: true,
    );
  }

  /// 判断是否为游客用户
  bool get isGuest => isAnonymous || id.startsWith('guest_');

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}