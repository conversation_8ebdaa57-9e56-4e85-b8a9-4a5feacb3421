import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';

/// 创建项目对话框V2
/// 
/// 5步骤向导：基本信息→车辆信息→预算设置→模板选择→确认
/// 使用VanHubModal作为基础容器
class CreateProjectDialogV2 extends ConsumerStatefulWidget {
  /// 项目创建成功回调
  final Function(Map<String, dynamic> projectData)? onProjectCreated;

  const CreateProjectDialogV2({
    super.key,
    this.onProjectCreated,
  });

  /// 显示创建项目对话框的静态方法
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    Function(Map<String, dynamic> projectData)? onProjectCreated,
  }) {
    return VanHubModal.show<Map<String, dynamic>>(
      context: context,
      title: '创建新项目',
      size: VanHubModalSize.lg,
      animation: VanHubModalAnimation.scale,
      fullscreenOnMobile: true,
      child: CreateProjectDialogV2(
        onProjectCreated: onProjectCreated,
      ),
    );
  }

  @override
  ConsumerState<CreateProjectDialogV2> createState() => _CreateProjectDialogV2State();
}

class _CreateProjectDialogV2State extends ConsumerState<CreateProjectDialogV2> {
  final _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  
  // 表单控制器
  final _projectNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _budgetController = TextEditingController();
  
  int _currentStep = 0;
  bool _isLoading = false;
  String? _errorMessage;
  
  // 项目数据
  String _projectType = 'full_conversion'; // 全改装
  String _vehicleBrand = '';
  String _vehicleModel = '';
  String _vehicleYear = '';
  double _budgetMin = 50000;
  double _budgetMax = 200000;
  String? _selectedTemplate;
  bool _isPublic = true;

  // 车辆品牌和型号数据
  final Map<String, List<String>> _vehicleData = {
    '大众': ['Crafter', 'T6', 'T5', 'Caddy'],
    '奔驰': ['Sprinter', 'Vito', 'Metris'],
    '福特': ['Transit', 'Transit Custom', 'E-Series'],
    '依维柯': ['Daily', 'Ducato'],
    '五十铃': ['NKR', 'NPR', 'ELF'],
    '江铃': ['特顺', '全顺', '凯运'],
    '其他': ['自定义'],
  };

  final List<ProjectTemplate> _templates = [
    ProjectTemplate(
      id: 'minimalist',
      name: '极简风格',
      description: '注重功能性和简洁设计，适合预算有限的改装',
      estimatedBudget: '5-10万',
      duration: '2-3个月',
      features: ['基础水电', '简单家具', '储物空间'],
    ),
    ProjectTemplate(
      id: 'comfort',
      name: '舒适居家',
      description: '平衡舒适性和实用性，适合长期旅行',
      estimatedBudget: '10-20万',
      duration: '3-4个月',
      features: ['完整水电', '舒适床铺', '厨房设备', '卫浴设施'],
    ),
    ProjectTemplate(
      id: 'luxury',
      name: '豪华定制',
      description: '高端材料和设备，追求极致体验',
      estimatedBudget: '20万以上',
      duration: '4-6个月',
      features: ['智能家居', '高端材料', '定制家具', '娱乐系统'],
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    _projectNameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 700,
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing6),
      child: Column(
        children: [
          _buildStepIndicator(),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildBasicInfoStep(),
                _buildVehicleInfoStep(),
                _buildBudgetStep(),
                _buildTemplateStep(),
                _buildConfirmationStep(),
              ],
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    final steps = ['基本信息', '车辆信息', '预算设置', '模板选择', '确认创建'];
    
    return Row(
      children: List.generate(steps.length, (index) {
        final isActive = index <= _currentStep;
        final isCompleted = index < _currentStep;
        
        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isActive 
                            ? VanHubDesignSystem.brandPrimary
                            : VanHubDesignSystem.neutralGray300,
                      ),
                      child: Center(
                        child: isCompleted
                            ? const Icon(Icons.check, color: Colors.white, size: 16)
                            : Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color: isActive ? Colors.white : VanHubDesignSystem.neutralGray600,
                                  fontWeight: VanHubDesignSystem.fontWeightMedium,
                                  fontSize: VanHubDesignSystem.fontSizeXs,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: VanHubDesignSystem.spacing2),
                    Text(
                      steps[index],
                      style: TextStyle(
                        fontSize: VanHubDesignSystem.fontSizeXs,
                        color: isActive 
                            ? VanHubDesignSystem.brandPrimary
                            : VanHubDesignSystem.neutralGray600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              if (index < steps.length - 1)
                Container(
                  width: 20,
                  height: 2,
                  margin: const EdgeInsets.only(bottom: 24),
                  color: isCompleted 
                      ? VanHubDesignSystem.brandPrimary
                      : VanHubDesignSystem.neutralGray300,
                ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildBasicInfoStep() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              '项目基本信息',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: VanHubDesignSystem.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: VanHubDesignSystem.spacing6),
            _buildErrorMessage(),
            TextFormField(
              controller: _projectNameController,
              decoration: InputDecoration(
                labelText: '项目名称',
                hintText: '为您的改装项目起个名字',
                prefixIcon: const Icon(Icons.drive_file_rename_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入项目名称';
                }
                if (value.length < 2) {
                  return '项目名称至少2个字符';
                }
                return null;
              },
            ),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: '项目描述',
                hintText: '简单描述您的改装计划和目标',
                prefixIcon: const Icon(Icons.description_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入项目描述';
                }
                return null;
              },
            ),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            Text(
              '改装类型',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: VanHubDesignSystem.fontWeightMedium,
              ),
            ),
            const SizedBox(height: VanHubDesignSystem.spacing3),
            _buildProjectTypeSelector(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            SwitchListTile(
              title: const Text('公开项目'),
              subtitle: const Text('其他用户可以查看和学习您的项目'),
              value: _isPublic,
              onChanged: (value) {
                setState(() {
                  _isPublic = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectTypeSelector() {
    final types = [
      {'id': 'full_conversion', 'name': '全改装', 'desc': '完整的房车改装'},
      {'id': 'partial_upgrade', 'name': '局部升级', 'desc': '部分功能改装'},
      {'id': 'maintenance', 'name': '维护保养', 'desc': '设备维护和保养'},
    ];
    
    return Column(
      children: types.map((type) {
        return RadioListTile<String>(
          title: Text(type['name']!),
          subtitle: Text(type['desc']!),
          value: type['id']!,
          groupValue: _projectType,
          onChanged: (value) {
            setState(() {
              _projectType = value!;
            });
          },
        );
      }).toList(),
    );
  }

  Widget _buildVehicleInfoStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            '车辆信息',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: '车辆品牌',
              prefixIcon: const Icon(Icons.directions_car),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
              ),
            ),
            value: _vehicleBrand.isEmpty ? null : _vehicleBrand,
            items: _vehicleData.keys.map((brand) {
              return DropdownMenuItem(
                value: brand,
                child: Text(brand),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _vehicleBrand = value ?? '';
                _vehicleModel = ''; // 重置型号选择
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请选择车辆品牌';
              }
              return null;
            },
          ),
          const SizedBox(height: VanHubDesignSystem.spacing4),
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: '车辆型号',
              prefixIcon: const Icon(Icons.model_training),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
              ),
            ),
            value: _vehicleModel.isEmpty ? null : _vehicleModel,
            items: _vehicleBrand.isEmpty 
                ? []
                : _vehicleData[_vehicleBrand]!.map((model) {
                    return DropdownMenuItem(
                      value: model,
                      child: Text(model),
                    );
                  }).toList(),
            onChanged: (value) {
              setState(() {
                _vehicleModel = value ?? '';
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请选择车辆型号';
              }
              return null;
            },
          ),
          const SizedBox(height: VanHubDesignSystem.spacing4),
          TextFormField(
            decoration: InputDecoration(
              labelText: '车辆年份',
              hintText: '例如：2020',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
              ),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              _vehicleYear = value;
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入车辆年份';
              }
              final year = int.tryParse(value);
              if (year == null || year < 1990 || year > DateTime.now().year + 1) {
                return '请输入有效的年份';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            '预算设置',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          Text(
            '预算范围：¥${_budgetMin.toInt()} - ¥${_budgetMax.toInt()}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: VanHubDesignSystem.fontWeightMedium,
              color: VanHubDesignSystem.brandPrimary,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing4),
          RangeSlider(
            values: RangeValues(_budgetMin, _budgetMax),
            min: 10000,
            max: 500000,
            divisions: 49,
            labels: RangeLabels(
              '¥${_budgetMin.toInt()}',
              '¥${_budgetMax.toInt()}',
            ),
            onChanged: (values) {
              setState(() {
                _budgetMin = values.start;
                _budgetMax = values.end;
              });
            },
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          _buildBudgetPresets(),
        ],
      ),
    );
  }

  Widget _buildBudgetPresets() {
            final presets = [
              {'name': '经济型', 'min': 30000.0, 'max': 80000.0},
              {'name': '舒适型', 'min': 80000.0, 'max': 150000.0},
              {'name': '豪华型', 'min': 150000.0, 'max': 300000.0},
              {'name': '定制型', 'min': 300000.0, 'max': 500000.0},
            ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速选择',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightMedium,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
            Wrap(
              spacing: VanHubDesignSystem.spacing3,
              children: presets.map((preset) {
                return FilterChip(
                  label: Text(preset['name']! as String),
                  selected: _budgetMin == (preset['min']! as double) && _budgetMax == (preset['max']! as double),
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _budgetMin = preset['min']! as double;
                        _budgetMax = preset['max']! as double;
                      });
                    }
                  },
                );
              }).toList(),
            ),
      ],
    );
  }

  Widget _buildTemplateStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            '选择项目模板',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing3),
          Text(
            '选择一个模板作为起点，您可以随时修改',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          ..._templates.map((template) => _buildTemplateCard(template)),
          const SizedBox(height: VanHubDesignSystem.spacing4),
          OutlinedButton.icon(
            onPressed: () {
              setState(() {
                _selectedTemplate = null;
              });
            },
            icon: const Icon(Icons.build),
            label: const Text('从空白项目开始'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(VanHubDesignSystem.spacing4),
              side: BorderSide(
                color: _selectedTemplate == null 
                    ? VanHubDesignSystem.brandPrimary
                    : Theme.of(context).colorScheme.outline,
                width: _selectedTemplate == null ? 2 : 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(ProjectTemplate template) {
    final isSelected = _selectedTemplate == template.id;
    
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      child: Card(
        elevation: isSelected ? 4 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
          side: BorderSide(
            color: isSelected 
                ? VanHubDesignSystem.brandPrimary
                : Colors.transparent,
            width: 2,
          ),
        ),
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedTemplate = template.id;
            });
          },
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
          child: Padding(
            padding: const EdgeInsets.all(VanHubDesignSystem.spacing4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        template.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
                        ),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: VanHubDesignSystem.brandPrimary,
                      ),
                  ],
                ),
                const SizedBox(height: VanHubDesignSystem.spacing2),
                Text(
                  template.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: VanHubDesignSystem.spacing3),
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    Text(
                      template.estimatedBudget,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(width: VanHubDesignSystem.spacing4),
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    Text(
                      template.duration,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: VanHubDesignSystem.spacing3),
                Wrap(
                  spacing: VanHubDesignSystem.spacing2,
                  children: template.features.map((feature) {
                    return Chip(
                      label: Text(
                        feature,
                        style: const TextStyle(fontSize: VanHubDesignSystem.fontSizeXs),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmationStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            '确认项目信息',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          _buildConfirmationCard('项目信息', [
            '名称：${_projectNameController.text}',
            '描述：${_descriptionController.text}',
            '类型：${_getProjectTypeName()}',
            '可见性：${_isPublic ? "公开" : "私有"}',
          ]),
          _buildConfirmationCard('车辆信息', [
            '品牌：$_vehicleBrand',
            '型号：$_vehicleModel',
            '年份：$_vehicleYear',
          ]),
          _buildConfirmationCard('预算设置', [
            '预算范围：¥${_budgetMin.toInt()} - ¥${_budgetMax.toInt()}',
          ]),
          _buildConfirmationCard('项目模板', [
            _selectedTemplate == null 
                ? '从空白项目开始'
                : _templates.firstWhere((t) => t.id == _selectedTemplate).name,
          ]),
        ],
      ),
    );
  }

  Widget _buildConfirmationCard(String title, List<String> items) {
    return Card(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      child: Padding(
        padding: const EdgeInsets.all(VanHubDesignSystem.spacing4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: VanHubDesignSystem.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: VanHubDesignSystem.spacing3),
            ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing1),
              child: Text(
                item,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : _handlePreviousStep,
              child: const Text('上一步'),
            ),
          ),
        if (_currentStep > 0)
          const SizedBox(width: VanHubDesignSystem.spacing4),
        Expanded(
          child: FilledButton(
            onPressed: _isLoading ? null : _handleNextStep,
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(_currentStep == 4 ? '创建项目' : '下一步'),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    if (_errorMessage == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing3),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.semanticError.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        border: Border.all(
          color: VanHubDesignSystem.semanticError.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: VanHubDesignSystem.semanticError,
            size: 20,
          ),
          const SizedBox(width: VanHubDesignSystem.spacing2),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: VanHubDesignSystem.semanticError,
                fontSize: VanHubDesignSystem.fontSizeSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getProjectTypeName() {
    switch (_projectType) {
      case 'full_conversion':
        return '全改装';
      case 'partial_upgrade':
        return '局部升级';
      case 'maintenance':
        return '维护保养';
      default:
        return '未知';
    }
  }

  void _handlePreviousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: VanHubDesignSystem.durationBase,
        curve: VanHubDesignSystem.curveDefault,
      );
    }
  }

  Future<void> _handleNextStep() async {
    if (_currentStep < 4) {
      // 验证当前步骤
      if (!_validateCurrentStep()) {
        return;
      }
      
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: VanHubDesignSystem.durationBase,
        curve: VanHubDesignSystem.curveDefault,
      );
    } else {
      // 创建项目
      await _handleCreateProject();
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _formKey.currentState?.validate() ?? false;
      case 1:
        if (_vehicleBrand.isEmpty || _vehicleModel.isEmpty || _vehicleYear.isEmpty) {
          setState(() {
            _errorMessage = '请完整填写车辆信息';
          });
          return false;
        }
        break;
      case 2:
        // 预算验证已在滑块中处理
        break;
      case 3:
        // 模板选择是可选的
        break;
    }
    
    setState(() {
      _errorMessage = null;
    });
    return true;
  }

  Future<void> _handleCreateProject() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 构建项目数据
      final projectData = {
        'name': _projectNameController.text,
        'description': _descriptionController.text,
        'type': _projectType,
        'vehicle_brand': _vehicleBrand,
        'vehicle_model': _vehicleModel,
        'vehicle_year': _vehicleYear,
        'budget_min': _budgetMin,
        'budget_max': _budgetMax,
        'template_id': _selectedTemplate,
        'is_public': _isPublic,
        'created_at': DateTime.now().toIso8601String(),
      };

      // TODO: 实际的项目创建逻辑
      await Future.delayed(const Duration(seconds: 2)); // 模拟网络请求
      
      if (mounted) {
        widget.onProjectCreated?.call(projectData);
        Navigator.of(context).pop(projectData);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '创建项目失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// 项目模板数据类
class ProjectTemplate {
  final String id;
  final String name;
  final String description;
  final String estimatedBudget;
  final String duration;
  final List<String> features;

  const ProjectTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedBudget,
    required this.duration,
    required this.features,
  });
}
