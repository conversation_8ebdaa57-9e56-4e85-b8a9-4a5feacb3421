import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../api/supabase_config.dart';

/// 数据库连接测试工具
class DatabaseConnectionTest {
  static const String _testTableName = 'users';
  
  /// 执行完整的数据库连接测试
  static Future<DatabaseTestResult> runFullTest() async {
    final stopwatch = Stopwatch()..start();
    final results = <String, dynamic>{};
    
    try {
      // 1. 检查初始化状态
      results['initialization'] = await _testInitialization();
      
      // 2. 检查配置
      results['configuration'] = await _testConfiguration();
      
      // 3. 检查网络连接
      results['network'] = await _testNetworkConnection();
      
      // 4. 检查数据库访问
      results['database'] = await _testDatabaseAccess();
      
      // 5. 检查认证系统
      results['auth'] = await _testAuthSystem();
      
      stopwatch.stop();
      
      return DatabaseTestResult(
        success: true,
        duration: stopwatch.elapsedMilliseconds,
        results: results,
      );
      
    } catch (e, stackTrace) {
      stopwatch.stop();
      
      if (kDebugMode) {
        print('数据库连接测试失败: $e');
        print('堆栈跟踪: $stackTrace');
      }
      
      return DatabaseTestResult(
        success: false,
        duration: stopwatch.elapsedMilliseconds,
        error: e.toString(),
        results: results,
      );
    }
  }
  
  /// 测试初始化状态
  static Future<Map<String, dynamic>> _testInitialization() async {
    return {
      'isInitialized': SupabaseConfig.isInitialized,
      'hasClient': Supabase.instance.client != null,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 测试配置
  static Future<Map<String, dynamic>> _testConfiguration() async {
    final url = SupabaseConfig.supabaseUrl;
    final key = SupabaseConfig.supabaseAnonKey;
    
    return {
      'hasUrl': url.isNotEmpty,
      'hasKey': key.isNotEmpty,
      'urlValid': url.startsWith('https://') && url.contains('.supabase.co'),
      'keyValid': key.length > 100, // JWT tokens are typically longer
      'urlLength': url.length,
      'keyLength': key.length,
      'urlPreview': url.isNotEmpty ? '${url.substring(0, 30)}...' : '',
      'keyPreview': key.isNotEmpty ? '${key.substring(0, 20)}...' : '',
    };
  }
  
  /// 测试网络连接
  static Future<Map<String, dynamic>> _testNetworkConnection() async {
    try {
      final client = SupabaseConfig.client;
      
      // 尝试获取服务器时间（不需要认证）
      final response = await client
          .rpc('now')
          .timeout(const Duration(seconds: 10));
      
      return {
        'connected': true,
        'serverTime': response?.toString(),
        'responseType': response.runtimeType.toString(),
      };
    } catch (e) {
      // 如果RPC不存在，尝试其他方法
      try {
        await SupabaseConfig.client
            .from('non_existent_table')
            .select('*')
            .limit(1)
            .timeout(const Duration(seconds: 10));
        
        return {'connected': false, 'error': 'Unexpected success'};
      } catch (tableError) {
        // 如果是表不存在错误，说明连接正常
        if (tableError.toString().contains('relation') ||
            tableError.toString().contains('does not exist')) {
          return {
            'connected': true,
            'method': 'table_query_test',
            'error': tableError.toString(),
          };
        }
        
        return {
          'connected': false,
          'error': tableError.toString(),
        };
      }
    }
  }
  
  /// 测试数据库访问
  static Future<Map<String, dynamic>> _testDatabaseAccess() async {
    try {
      final client = SupabaseConfig.client;
      
      // 尝试查询用户表（公开读取）
      final response = await client
          .from(_testTableName)
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 10));
      
      return {
        'accessible': true,
        'tableExists': true,
        'recordCount': response.length,
        'responseType': response.runtimeType.toString(),
      };
    } catch (e) {
      return {
        'accessible': false,
        'error': e.toString(),
        'tableExists': !e.toString().contains('does not exist'),
      };
    }
  }
  
  /// 测试认证系统
  static Future<Map<String, dynamic>> _testAuthSystem() async {
    try {
      final auth = SupabaseConfig.client.auth;
      
      return {
        'authAvailable': true,
        'currentUser': auth.currentUser?.id,
        'isLoggedIn': auth.currentUser != null,
        'sessionExists': auth.currentSession != null,
        'sessionExpiry': auth.currentSession?.expiresAt?.toString(),
      };
    } catch (e) {
      return {
        'authAvailable': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 快速连接检查
  static Future<bool> quickCheck() async {
    try {
      if (!SupabaseConfig.isInitialized) {
        return false;
      }
      
      await SupabaseConfig.client
          .from('test_connection')
          .select('*')
          .limit(1)
          .timeout(const Duration(seconds: 5));
      
      return true;
    } catch (e) {
      // 如果是表不存在错误，说明连接正常
      return e.toString().contains('relation') ||
             e.toString().contains('does not exist');
    }
  }
  
  /// 生成测试报告
  static String generateReport(DatabaseTestResult result) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== VanHub 数据库连接测试报告 ===');
    buffer.writeln('测试时间: ${DateTime.now()}');
    buffer.writeln('测试耗时: ${result.duration}ms');
    buffer.writeln('测试结果: ${result.success ? "✅ 成功" : "❌ 失败"}');
    
    if (!result.success && result.error != null) {
      buffer.writeln('错误信息: ${result.error}');
    }
    
    buffer.writeln('\n--- 详细结果 ---');
    
    result.results.forEach((category, data) {
      buffer.writeln('\n[$category]');
      if (data is Map<String, dynamic>) {
        data.forEach((key, value) {
          buffer.writeln('  $key: $value');
        });
      } else {
        buffer.writeln('  结果: $data');
      }
    });
    
    buffer.writeln('\n=== 报告结束 ===');
    
    return buffer.toString();
  }
}

/// 数据库测试结果
class DatabaseTestResult {
  final bool success;
  final int duration;
  final String? error;
  final Map<String, dynamic> results;
  
  const DatabaseTestResult({
    required this.success,
    required this.duration,
    this.error,
    required this.results,
  });
  
  /// 获取特定测试的结果
  T? getResult<T>(String category, String key) {
    final categoryData = results[category];
    if (categoryData is Map<String, dynamic>) {
      return categoryData[key] as T?;
    }
    return null;
  }
  
  /// 检查是否所有关键测试都通过
  bool get isHealthy {
    return success &&
           getResult<bool>('initialization', 'isInitialized') == true &&
           getResult<bool>('configuration', 'hasUrl') == true &&
           getResult<bool>('configuration', 'hasKey') == true &&
           getResult<bool>('network', 'connected') == true;
  }
  
  /// 获取健康状态描述
  String get healthStatus {
    if (!success) return '连接失败';
    if (!isHealthy) return '部分功能异常';
    return '连接正常';
  }
}