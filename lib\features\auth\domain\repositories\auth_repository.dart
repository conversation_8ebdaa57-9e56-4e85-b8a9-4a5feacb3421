import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../entities/login_request.dart';
import '../entities/register_request.dart';

abstract class AuthRepository {
  /// 用户登录
  Future<Either<Failure, User>> login(LoginRequest request);
  
  /// 用户注册
  Future<Either<Failure, User>> register(RegisterRequest request);
  
  /// 用户退出登录
  Future<Either<Failure, void>> logout();
  
  /// 获取当前用户
  Future<Either<Failure, User?>> getCurrentUser();
  
  /// 匿名登录（通过Supabase）
  Future<Either<Failure, User>> signInAnonymously();

  /// 游客模式登录（本地实现，不依赖Supabase）
  Future<Either<Failure, User>> signInAsGuest();

  /// 重置密码
  Future<Either<Failure, void>> resetPassword(String email);
  
  /// 监听认证状态变化
  Stream<User?> get authStateChanges;
}