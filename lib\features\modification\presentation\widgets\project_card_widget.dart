import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:cached_network_image/cached_network_image.dart'; // 暂时注释，需要添加到pubspec.yaml
import '../../domain/entities/modification_project.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../modification_log/domain/entities/enums.dart' as mod_log_enums;

/// 项目卡片组件
/// 用于在列表中展示项目的核心信息
class ProjectCard extends ConsumerWidget {
  const ProjectCard({
    super.key,
    required this.project,
    this.onTap,
    this.showDetailedStats = true,
    this.compact = false,
  });

  /// 项目数据
  final ModificationProject project;

  /// 点击回调
  final VoidCallback? onTap;

  /// 是否显示详细统计信息
  final bool showDetailedStats;

  /// 是否使用紧凑布局
  final bool compact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (compact) {
      return _buildCompactCard(context, ref);
    }
    return _buildFullCard(context, ref);
  }

  /// 构建完整卡片
  Widget _buildFullCard(BuildContext context, WidgetRef ref) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目主图和状态标识
            _buildImageHeader(context),
            
            // 项目信息内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 项目标题和车型信息
                  _buildTitleSection(context),
                  
                  const SizedBox(height: 8),
                  
                  // 关键统计数据
                  if (showDetailedStats) ...[
                    _buildStatsSection(context),
                    const SizedBox(height: 8),
                  ],
                  
                  // 进度条
                  _buildProgressSection(context),
                  
                  const SizedBox(height: 8),
                  
                  // 费用分布概览
                  if (showDetailedStats && project.systems.isNotEmpty)
                    _buildCostOverview(context),
                  
                  const SizedBox(height: 4),
                  
                  // 最后更新时间
                  _buildUpdateInfo(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建紧凑卡片
  Widget _buildCompactCard(BuildContext context, WidgetRef ref) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 项目缩略图
              _buildThumbnail(context),
              
              const SizedBox(width: 16),
              
              // 项目信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTitleSection(context),
                    const SizedBox(height: 4),
                    _buildCompactStats(context),
                    const SizedBox(height: 4),
                    _buildProgressSection(context),
                  ],
                ),
              ),
              
              // 状态指示器
              _buildStatusIndicator(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建图片头部
  Widget _buildImageHeader(BuildContext context) {
    return Stack(
      children: [
        // 主图片
        AspectRatio(
          aspectRatio: 16 / 9,
          child: project.mainImageUrl != null
              ? Image.network(
                  project.mainImageUrl!,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: VanHubColors.surfaceVariant,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
                )
              : _buildPlaceholderImage(),
        ),
        
        // 状态标识
        Positioned(
          top: 8,
          right: 8,
          child: _buildStatusBadge(context),
        ),
        
        // 公开标识
        if (project.isPublic)
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: VanHubColors.info.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.public,
                    size: 16,
                    color: VanHubColors.onInfo,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '公开',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: VanHubColors.onInfo,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage() {
    return Container(
      color: VanHubColors.surfaceVariant,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_outlined,
            size: 48,
            color: VanHubColors.textSecondary,
          ),
          const SizedBox(height: 4),
          Text(
            '暂无图片',
            style: VanHubTypography.bodySmall.copyWith(
              color: VanHubColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建缩略图
  Widget _buildThumbnail(BuildContext context) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: VanHubColors.surfaceVariant,
      ),
      clipBehavior: Clip.antiAlias,
      child: project.mainImageUrl != null
          ? Image.network(
              project.mainImageUrl!,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
              errorBuilder: (context, error, stackTrace) => Icon(
                Icons.folder_outlined,
                size: 48,
                color: VanHubColors.textSecondary,
              ),
            )
          : Icon(
              Icons.folder_outlined,
              size: 48,
              color: VanHubColors.textSecondary,
            ),
    );
  }

  /// 构建标题部分
  Widget _buildTitleSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 项目标题
        Text(
          project.title,
          style: VanHubTypography.headlineSmall,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        SizedBox(height: VanHubSpacing.xs),
        
        // 车型信息
        Row(
          children: [
            Icon(
              Icons.directions_car,
              size: VanHubSpacing.iconSmall,
              color: VanHubColors.textSecondary,
            ),
            SizedBox(width: VanHubSpacing.xs),
            Expanded(
              child: Text(
                '${project.vehicleBrand} ${project.vehicleModel}',
                style: VanHubTypography.bodyMedium.copyWith(
                  color: VanHubColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建统计数据部分
  Widget _buildStatsSection(BuildContext context) {
    return Row(
      children: [
        // 总费用
        _buildStatItem(
          context,
          icon: Icons.attach_money,
          label: '费用',
          value: '¥${project.totalCost.toStringAsFixed(0)}',
          color: project.isOverBudget ? VanHubColors.warning : VanHubColors.success,
        ),
        
        SizedBox(width: VanHubSpacing.md),
        
        // 改装系统数量
        _buildStatItem(
          context,
          icon: Icons.settings,
          label: '系统',
          value: '${project.systems.length}个',
          color: VanHubColors.primary,
        ),
        
        SizedBox(width: VanHubSpacing.md),
        
        // 物料总数
        _buildStatItem(
          context,
          icon: Icons.inventory,
          label: '物料',
          value: '${project.statistics.totalMaterials}个',
          color: VanHubColors.secondary,
        ),
      ],
    );
  }

  /// 构建紧凑统计数据
  Widget _buildCompactStats(BuildContext context) {
    return Row(
      children: [
        Text(
          '¥${project.totalCost.toStringAsFixed(0)}',
          style: VanHubTypography.titleMedium.copyWith(
            color: project.isOverBudget ? VanHubColors.warning : VanHubColors.success,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        SizedBox(width: VanHubSpacing.sm),
        
        Text(
          '${project.systems.length}系统',
          style: VanHubTypography.bodySmall.copyWith(
            color: VanHubColors.textSecondary,
          ),
        ),
        
        SizedBox(width: VanHubSpacing.sm),
        
        Text(
          '${project.statistics.totalMaterials}物料',
          style: VanHubTypography.bodySmall.copyWith(
            color: VanHubColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: VanHubSpacing.all(VanHubSpacing.sm),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: VanHubSpacing.iconMedium,
              color: color,
            ),
            SizedBox(height: VanHubSpacing.xs),
            Text(
              value,
              style: VanHubTypography.displaySmall.copyWith(
                color: color,
              ),
            ),
            Text(
              label,
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection(BuildContext context) {
    final percentage = project.completionPercentage;
    final percentageText = '${(percentage * 100).round()}%';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '项目进度',
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubColors.textSecondary,
              ),
            ),
            Text(
              percentageText,
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubColors.primary,
              ),
            ),
          ],
        ),
        
        SizedBox(height: VanHubSpacing.xs),
        
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: VanHubColors.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(percentage),
          ),
        ),
      ],
    );
  }

  /// 构建费用概览
  Widget _buildCostOverview(BuildContext context) {
    final costBySystem = project.costBySystemType;
    final topSystems = costBySystem.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    if (topSystems.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '费用分布',
          style: VanHubTypography.bodySmall.copyWith(
            color: VanHubColors.textSecondary,
          ),
        ),
        
        SizedBox(height: VanHubSpacing.xs),
        
        Wrap(
          spacing: VanHubSpacing.sm,
          runSpacing: VanHubSpacing.xs,
          children: topSystems.take(3).map((entry) {
            final systemType = entry.key;
            final cost = entry.value;
            final color = Colors.blue; // 临时使用固定颜色
            
            return Container(
              padding: VanHubSpacing.symmetric(
                horizontal: VanHubSpacing.sm,
                vertical: VanHubSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                border: Border.all(color: color.withValues(alpha: 0.3)),
              ),
              child: Text(
                '${_getSystemDisplayName(systemType)} ¥${cost.toStringAsFixed(0)}',
                style: VanHubTypography.bodySmall.copyWith(
                  color: color,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建更新信息
  Widget _buildUpdateInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 16, // 临时使用固定大小
          color: VanHubColors.textSecondary,
        ),
        SizedBox(width: VanHubSpacing.xs),
        Text(
          '最后更新于：${project.lastUpdateDescription}',
          style: VanHubTypography.bodySmall.copyWith(
            color: VanHubColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建状态徽章
  Widget _buildStatusBadge(BuildContext context) {
    final status = project.status;
    final color = _getStatusColor(status as mod_log_enums.ProjectStatus);
    
    return Container(
      padding: VanHubSpacing.symmetric(
        horizontal: VanHubSpacing.sm,
        vertical: VanHubSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
      ),
      child: Text(
        project.statusDisplayText,
        style: VanHubTypography.bodySmall.copyWith(
          color: Colors.white,
        ),
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(BuildContext context) {
    final status = project.status;
    final color = _getStatusColor(status as mod_log_enums.ProjectStatus);
    
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  /// 获取进度颜色
  Color _getProgressColor(double percentage) {
    if (percentage >= 1.0) {
      return VanHubColors.success;
    } else if (percentage >= 0.7) {
      return VanHubColors.primary;
    } else if (percentage >= 0.3) {
      return VanHubColors.warning;
    } else {
      return VanHubColors.error;
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(mod_log_enums.ProjectStatus status) {
    switch (status) {
      case mod_log_enums.ProjectStatus.planning:
        return VanHubColors.info;
      case mod_log_enums.ProjectStatus.inProgress:
        return VanHubColors.primary;
      case mod_log_enums.ProjectStatus.completed:
        return VanHubColors.success;
      case mod_log_enums.ProjectStatus.onHold:
        return VanHubColors.warning;
      case mod_log_enums.ProjectStatus.cancelled:
        return VanHubColors.error;
    }
  }

  /// 获取系统显示名称
  String _getSystemDisplayName(mod_log_enums.SystemType systemType) {
    switch (systemType) {
      case mod_log_enums.SystemType.electrical:
        return '电路系统';
      case mod_log_enums.SystemType.plumbing:
        return '水路系统';
      case mod_log_enums.SystemType.storage:
        return '储物系统';
      case mod_log_enums.SystemType.bedding:
        return '床铺系统';
      case mod_log_enums.SystemType.kitchen:
        return '厨房系统';
      case mod_log_enums.SystemType.bathroom:
        return '卫浴系统';
      case mod_log_enums.SystemType.exterior:
        return '外观改装';
      case mod_log_enums.SystemType.chassis:
        return '底盘改装';
      case mod_log_enums.SystemType.custom:
        return '自定义系统';
    }
  }
}