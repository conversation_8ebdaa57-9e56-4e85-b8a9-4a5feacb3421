import 'dart:io';
import 'package:excel/excel.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../../domain/entities/bom_item.dart';

/// BOM导出服务
class BomExportService {
  /// 导出为Excel格式
  static Future<File> exportToExcel({
    required List<BomItem> bomItems,
    required String projectName,
  }) async {
    final excel = Excel.createExcel();
    final sheet = excel['BOM清单'];
    
    // 设置表头
    final headers = [
      '序号', '物料名称', '分类', '规格', '数量', '单位', 
      '单价', '总价', '品牌', '型号', '供应商', '状态', '备注'
    ];
    
    for (int i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }
    
    // 填充数据
    for (int i = 0; i < bomItems.length; i++) {
      final item = bomItems[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = IntCellValue(i + 1);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(item.materialName);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(item.category ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(item.specifications ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = DoubleCellValue(item.quantity.toDouble());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue('个'); // 默认单位
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = DoubleCellValue(item.unitPrice?.toDouble() ?? 0);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = DoubleCellValue((item.unitPrice?.toDouble() ?? 0) * item.quantity.toDouble());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = TextCellValue(item.brand ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
          .value = TextCellValue(item.model ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
          .value = TextCellValue(item.supplier ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: row))
          .value = TextCellValue(_getStatusText(item.status.toString()));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 12, rowIndex: row))
          .value = TextCellValue(item.notes ?? '');
    }
    
    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${projectName}_BOM_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');
    
    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    
    return file;
  }
  
  /// 导出为PDF格式
  static Future<File> exportToPdf({
    required List<BomItem> bomItems,
    required String projectName,
  }) async {
    final pdf = pw.Document();
    
    // 计算总价
    final totalCost = bomItems.fold<double>(
      0,
      (sum, item) => sum + ((item.unitPrice?.toDouble() ?? 0) * item.quantity.toDouble()),
    );
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // 标题
            pw.Header(
              level: 0,
              child: pw.Text(
                '$projectName - BOM清单',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // 统计信息
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('统计信息', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                  pw.SizedBox(height: 8),
                  pw.Text('物料总数: ${bomItems.length}'),
                  pw.Text('预估总成本: ¥${totalCost.toStringAsFixed(2)}'),
                  pw.Text('生成时间: ${DateTime.now().toString().substring(0, 19)}'),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // BOM表格
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FixedColumnWidth(30),
                1: const pw.FlexColumnWidth(3),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(1),
                4: const pw.FlexColumnWidth(1),
                5: const pw.FlexColumnWidth(1),
                6: const pw.FlexColumnWidth(2),
              },
              children: [
                // 表头
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                  children: [
                    _buildTableCell('序号', isHeader: true),
                    _buildTableCell('物料名称', isHeader: true),
                    _buildTableCell('分类', isHeader: true),
                    _buildTableCell('数量', isHeader: true),
                    _buildTableCell('单价', isHeader: true),
                    _buildTableCell('总价', isHeader: true),
                    _buildTableCell('状态', isHeader: true),
                  ],
                ),
                
                // 数据行
                ...bomItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  final totalPrice = (item.unitPrice?.toDouble() ?? 0) * item.quantity.toDouble();
                  
                  return pw.TableRow(
                    children: [
                      _buildTableCell('${index + 1}'),
                      _buildTableCell(item.materialName),
                      _buildTableCell(item.category ?? ''),
                      _buildTableCell('${item.quantity} 个'),
                      _buildTableCell('¥${(item.unitPrice?.toDouble() ?? 0).toStringAsFixed(2)}'),
                      _buildTableCell('¥${totalPrice.toStringAsFixed(2)}'),
                      _buildTableCell(_getStatusText(item.status.toString())),
                    ],
                  );
                }).toList(),
              ],
            ),
          ];
        },
      ),
    );
    
    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${projectName}_BOM_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');
    
    await file.writeAsBytes(await pdf.save());
    
    return file;
  }
  
  /// 构建表格单元格
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }
  
  /// 获取状态文本
  static String _getStatusText(String status) {
    switch (status) {
      case 'planned':
        return '计划中';
      case 'purchased':
        return '已采购';
      case 'installed':
        return '已安装';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  }
}
