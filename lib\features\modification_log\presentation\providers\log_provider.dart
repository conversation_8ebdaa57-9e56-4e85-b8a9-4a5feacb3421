import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/di/injection_container.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/log_search_criteria.dart';
import '../../domain/usecases/create_log_entry_usecase.dart';
import '../../domain/usecases/delete_log_entry_usecase.dart';
import '../../domain/usecases/get_log_entry_usecase.dart';
import '../../domain/usecases/get_project_logs_usecase.dart';
import '../../domain/usecases/get_system_logs_usecase.dart';
import '../../domain/usecases/search_logs_usecase.dart';
import '../../domain/usecases/update_log_entry_usecase.dart';

/// 日志状态
class LogState {
  final bool isLoading;
  final List<LogEntry> logs;
  final LogEntry? selectedLog;
  final Failure? failure;

  const LogState({
    this.isLoading = false,
    this.logs = const [],
    this.selectedLog,
    this.failure,
  });

  LogState copyWith({
    bool? isLoading,
    List<LogEntry>? logs,
    LogEntry? selectedLog,
    Failure? failure,
  }) {
    return LogState(
      isLoading: isLoading ?? this.isLoading,
      logs: logs ?? this.logs,
      selectedLog: selectedLog ?? this.selectedLog,
      failure: failure,
    );
  }
}

/// 日志提供者
class LogNotifier extends StateNotifier<LogState> {
  final CreateLogEntryUseCase _createLogEntryUseCase;
  final UpdateLogEntryUseCase _updateLogEntryUseCase;
  final DeleteLogEntryUseCase _deleteLogEntryUseCase;
  final GetLogEntryUseCase _getLogEntryUseCase;
  final GetProjectLogsUseCase _getProjectLogsUseCase;
  final GetSystemLogsUseCase _getSystemLogsUseCase;
  final SearchLogsUseCase _searchLogsUseCase;

  LogNotifier({
    required CreateLogEntryUseCase createLogEntryUseCase,
    required UpdateLogEntryUseCase updateLogEntryUseCase,
    required DeleteLogEntryUseCase deleteLogEntryUseCase,
    required GetLogEntryUseCase getLogEntryUseCase,
    required GetProjectLogsUseCase getProjectLogsUseCase,
    required GetSystemLogsUseCase getSystemLogsUseCase,
    required SearchLogsUseCase searchLogsUseCase,
  })  : _createLogEntryUseCase = createLogEntryUseCase,
        _updateLogEntryUseCase = updateLogEntryUseCase,
        _deleteLogEntryUseCase = deleteLogEntryUseCase,
        _getLogEntryUseCase = getLogEntryUseCase,
        _getProjectLogsUseCase = getProjectLogsUseCase,
        _getSystemLogsUseCase = getSystemLogsUseCase,
        _searchLogsUseCase = searchLogsUseCase,
        super(const LogState());

  /// 获取项目日志
  Future<void> getProjectLogs(String projectId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _getProjectLogsUseCase(GetProjectLogsParams(projectId: projectId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (logs) => state = state.copyWith(isLoading: false, logs: logs),
    );
  }

  /// 获取系统日志
  Future<void> getSystemLogs(String systemId) async {
    state = state.copyWith(isLoading: true, failure: null);

    // 使用systemId参数获取系统日志
    final result = await _getSystemLogsUseCase(GetSystemLogsParams(systemId: systemId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (logs) => state = state.copyWith(isLoading: false, logs: logs),
    );
  }

  /// 获取日志详情
  Future<void> getLogEntry(String logId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _getLogEntryUseCase(GetLogEntryParams(logId: logId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (log) => state = state.copyWith(isLoading: false, selectedLog: log),
    );
  }

  /// 创建日志
  Future<Either<Failure, LogEntry>> createLogEntry(LogEntry log) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _createLogEntryUseCase(CreateLogEntryParams(logEntry: log));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (createdLog) {
        final updatedLogs = [...state.logs, createdLog];
        state = state.copyWith(
          isLoading: false,
          logs: updatedLogs,
          selectedLog: createdLog,
        );
      },
    );

    return result;
  }

  /// 更新日志
  Future<Either<Failure, LogEntry>> updateLogEntry(LogEntry log) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _updateLogEntryUseCase(UpdateLogEntryParams(logEntry: log));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (updatedLog) {
        final updatedLogs = state.logs.map((e) => e.id == updatedLog.id ? updatedLog : e).toList();
        state = state.copyWith(
          isLoading: false,
          logs: updatedLogs,
          selectedLog: updatedLog,
        );
      },
    );

    return result;
  }

  /// 删除日志
  Future<Either<Failure, void>> deleteLogEntry(String logId) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _deleteLogEntryUseCase(DeleteLogEntryParams(logId: logId));

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (_) {
        final updatedLogs = state.logs.where((log) => log.id != logId).toList();
        state = state.copyWith(
          isLoading: false,
          logs: updatedLogs,
          selectedLog: null,
        );
      },
    );

    return result;
  }

  /// 搜索日志
  Future<void> searchLogs(LogSearchCriteria criteria) async {
    state = state.copyWith(isLoading: true, failure: null);

    final result = await _searchLogsUseCase(criteria);

    result.fold(
      (failure) => state = state.copyWith(isLoading: false, failure: failure),
      (logs) => state = state.copyWith(isLoading: false, logs: logs),
    );
  }

  /// 清除选中的日志
  void clearSelectedLog() {
    state = state.copyWith(selectedLog: null);
  }

  /// 清除错误
  void clearFailure() {
    state = state.copyWith(failure: null);
  }
}

/// 日志提供者
final logProvider = StateNotifierProvider<LogNotifier, LogState>((ref) {
  return LogNotifier(
    createLogEntryUseCase: ref.read(createLogEntryUseCaseProvider),
    updateLogEntryUseCase: ref.read(updateLogEntryUseCaseProvider),
    deleteLogEntryUseCase: ref.read(deleteLogEntryUseCaseProvider),
    getLogEntryUseCase: ref.read(getLogEntryUseCaseProvider),
    getProjectLogsUseCase: ref.read(getProjectLogsUseCaseProvider),
    getSystemLogsUseCase: ref.read(getSystemLogsUseCaseProvider),
    searchLogsUseCase: ref.read(searchLogsUseCaseProvider),
  );
});

/// 项目日志Provider - 专门用于获取特定项目的日志列表
final projectLogsProvider = FutureProvider.family<List<LogEntry>, String>((ref, projectId) async {
  final getProjectLogsUseCase = ref.read(getProjectLogsUseCaseProvider);

  final result = await getProjectLogsUseCase(GetProjectLogsParams(projectId: projectId));

  return result.fold(
    (failure) => throw Exception(failure.message),
    (logs) => logs,
  );
});