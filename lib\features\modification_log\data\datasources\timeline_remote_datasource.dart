import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/enums.dart';
import '../models/timeline_model.dart';
import '../models/milestone_model.dart';

/// 时间轴远程数据源接口
abstract class TimelineRemoteDataSource {
  /// 获取项目时间轴
  Future<TimelineModel> getProjectTimeline(String projectId);

  /// 获取时间范围内的时间轴
  Future<TimelineModel> getTimelineRange(String projectId, DateTime start, DateTime end);

  /// 获取项目里程碑
  Future<List<MilestoneModel>> getProjectMilestones(String projectId);

  /// 获取系统里程碑
  Future<List<MilestoneModel>> getSystemMilestones(String systemId);

  /// 获取单个里程碑
  Future<MilestoneModel> getMilestone(String milestoneId);

  /// 添加里程碑
  Future<MilestoneModel> addMilestone(MilestoneModel milestone);

  /// 更新里程碑
  Future<MilestoneModel> updateMilestone(MilestoneModel milestone);

  /// 删除里程碑
  Future<void> deleteMilestone(String milestoneId);

  /// 更新里程碑状态
  Future<MilestoneModel> updateMilestoneStatus(String milestoneId, String status);

  /// 将日志链接到里程碑
  Future<MilestoneModel> linkLogToMilestone(String milestoneId, String logId);

  /// 从里程碑取消链接日志
  Future<MilestoneModel> unlinkLogFromMilestone(String milestoneId, String logId);
}

/// 时间轴远程数据源实现
class TimelineRemoteDataSourceImpl implements TimelineRemoteDataSource {
  final SupabaseClient supabaseClient;

  const TimelineRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<TimelineModel> getProjectTimeline(String projectId) async {
    try {
      // 获取项目的时间轴事件
      final response = await supabaseClient
          .from('timeline_events')
          .select('*')
          .eq('project_id', projectId)
          .order('event_date', ascending: true);

      final events = (response as List)
          .map((json) => json as Map<String, dynamic>)
          .toList();

      // 创建时间轴模型
      return TimelineModel(
        projectId: projectId,
        items: events.map((e) => TimelineItemModel(
          id: e['id'] ?? '',
          title: e['title'] ?? '',
          date: e['date'] ?? DateTime.now().toIso8601String(),
          systemId: e['system_id'],
          systemName: e['system_name'],
          description: e['description'],
          type: e['type'],
          status: e['status'],
          iconName: e['icon_name'],
          colorHex: e['color_hex'],
        )).toList(),
        startDate: DateTime.now().toIso8601String(),
        endDate: DateTime.now().add(Duration(days: 365)).toIso8601String(),
        status: TimelineStatus.inProgress,
      );
    } catch (e) {
      throw ServerException(message: '获取项目时间轴失败: $e');
    }
  }

  @override
  Future<List<MilestoneModel>> getProjectMilestones(String projectId) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .select('*')
          .eq('project_id', projectId)
          .eq('event_type', 'milestone')
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => MilestoneModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取项目里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> addMilestone(MilestoneModel milestone) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .insert(milestone.toJson())
          .select()
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '添加里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> updateMilestone(MilestoneModel milestone) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .update(milestone.toJson())
          .eq('id', milestone.id)
          .select()
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新里程碑失败: $e');
    }
  }

  @override
  Future<void> deleteMilestone(String milestoneId) async {
    try {
      await supabaseClient
          .from('timeline_events')
          .delete()
          .eq('id', milestoneId);
    } catch (e) {
      throw ServerException(message: '删除里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> updateMilestoneStatus(String milestoneId, String status) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', milestoneId)
          .select()
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新里程碑状态失败: $e');
    }
  }

  @override
  Future<TimelineModel> getTimelineRange(String projectId, DateTime start, DateTime end) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .select('*')
          .eq('project_id', projectId)
          .gte('event_date', start.toIso8601String())
          .lte('event_date', end.toIso8601String())
          .order('event_date', ascending: true);

      final events = (response as List)
          .map((json) => json as Map<String, dynamic>)
          .toList();

      return TimelineModel(
        projectId: projectId,
        items: events.map((e) => TimelineItemModel(
          id: e['id'] ?? '',
          title: e['title'] ?? '',
          date: e['date'] ?? DateTime.now().toIso8601String(),
          systemId: e['system_id'],
          systemName: e['system_name'],
          description: e['description'],
          type: e['type'],
          status: e['status'],
          iconName: e['icon_name'],
          colorHex: e['color_hex'],
        )).toList(),
        startDate: start.toIso8601String(),
        endDate: end.toIso8601String(),
        status: TimelineStatus.inProgress,
      );
    } catch (e) {
      throw ServerException(message: '获取时间范围时间轴失败: $e');
    }
  }

  @override
  Future<List<MilestoneModel>> getSystemMilestones(String systemId) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .select('*')
          .eq('system_id', systemId)
          .eq('event_type', 'milestone')
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => MilestoneModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取系统里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> getMilestone(String milestoneId) async {
    try {
      final response = await supabaseClient
          .from('timeline_events')
          .select('*')
          .eq('id', milestoneId)
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '获取里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> linkLogToMilestone(String milestoneId, String logId) async {
    try {
      // 更新里程碑，添加关联的日志ID
      final response = await supabaseClient
          .from('timeline_events')
          .update({
            'linked_log_ids': [logId], // 假设这是一个数组字段
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', milestoneId)
          .select()
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '链接日志到里程碑失败: $e');
    }
  }

  @override
  Future<MilestoneModel> unlinkLogFromMilestone(String milestoneId, String logId) async {
    try {
      // 获取当前里程碑的关联日志列表
      final currentResponse = await supabaseClient
          .from('timeline_events')
          .select('linked_log_ids')
          .eq('id', milestoneId)
          .single();

      final currentLogIds = (currentResponse['linked_log_ids'] as List<dynamic>?)
          ?.cast<String>() ?? [];

      // 移除指定的日志ID
      final updatedLogIds = currentLogIds.where((id) => id != logId).toList();

      // 更新里程碑
      final response = await supabaseClient
          .from('timeline_events')
          .update({
            'linked_log_ids': updatedLogIds,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', milestoneId)
          .select()
          .single();

      return MilestoneModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '从里程碑取消链接日志失败: $e');
    }
  }
}