import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/features/material/data/models/material_review_model.dart';
import 'package:vanhub/features/material/domain/entities/material_review.dart';

void main() {
  group('MaterialReviewModel', () {
    final testDateTime = DateTime(2024, 1, 24, 12, 0, 0);
    
    final testModel = MaterialReviewModel(
      id: 'test-review-1',
      materialId: 'test-material-1',
      userId: 'test-user-1',
      userName: 'Test User',
      userAvatarUrl: 'https://example.com/avatar.jpg',
      content: 'This is a test review content.',
      rating: 4.5,
      qualityRating: 4.0,
      valueRating: 4.5,
      durabilityRating: 4.0,
      installationRating: 5.0,
      vehicleType: 'Test Vehicle',
      systemType: 'Test System',
      usageDuration: '3 months',
      pros: ['Good quality', 'Easy to install'],
      cons: ['Expensive'],
      tips: ['Use with care'],
      isVerifiedPurchase: true,
      purchaseDate: testDateTime,
      purchaseProofUrl: 'https://example.com/proof.jpg',
      imageUrls: ['https://example.com/image1.jpg'],
      videoUrls: ['https://example.com/video1.mp4'],
      likedByUserIds: ['user1', 'user2'],
      helpfulUserIds: ['user1', 'user3'],
      helpfulCount: 2,
      createdAt: testDateTime,
      updatedAt: testDateTime,
    );

    final testEntity = MaterialReview(
      id: 'test-review-1',
      materialId: 'test-material-1',
      userId: 'test-user-1',
      userName: 'Test User',
      userAvatarUrl: 'https://example.com/avatar.jpg',
      content: 'This is a test review content.',
      rating: 4.5,
      qualityRating: 4.0,
      valueRating: 4.5,
      durabilityRating: 4.0,
      installationRating: 5.0,
      vehicleType: 'Test Vehicle',
      systemType: 'Test System',
      usageDuration: '3 months',
      pros: ['Good quality', 'Easy to install'],
      cons: ['Expensive'],
      tips: ['Use with care'],
      isVerifiedPurchase: true,
      purchaseDate: testDateTime,
      purchaseProofUrl: 'https://example.com/proof.jpg',
      imageUrls: ['https://example.com/image1.jpg'],
      videoUrls: ['https://example.com/video1.mp4'],
      likedByUserIds: ['user1', 'user2'],
      helpfulUserIds: ['user1', 'user3'],
      helpfulCount: 2,
      createdAt: testDateTime,
      updatedAt: testDateTime,
    );

    final testJson = {
      'id': 'test-review-1',
      'material_id': 'test-material-1',
      'user_id': 'test-user-1',
      'user_name': 'Test User',
      'user_avatar_url': 'https://example.com/avatar.jpg',
      'content': 'This is a test review content.',
      'rating': 4.5,
      'quality_rating': 4.0,
      'value_rating': 4.5,
      'durability_rating': 4.0,
      'installation_rating': 5.0,
      'vehicle_type': 'Test Vehicle',
      'system_type': 'Test System',
      'usage_duration': '3 months',
      'pros': ['Good quality', 'Easy to install'],
      'cons': ['Expensive'],
      'tips': ['Use with care'],
      'is_verified_purchase': true,
      'purchase_date': testDateTime.toIso8601String(),
      'purchase_proof_url': 'https://example.com/proof.jpg',
      'image_urls': ['https://example.com/image1.jpg'],
      'video_urls': ['https://example.com/video1.mp4'],
      'liked_by_user_ids': ['user1', 'user2'],
      'helpful_user_ids': ['user1', 'user3'],
      'helpful_count': 2,
      'created_at': testDateTime.toIso8601String(),
      'updated_at': testDateTime.toIso8601String(),
    };

    group('fromJson', () {
      test('should return a valid MaterialReviewModel from JSON', () {
        // act
        final result = MaterialReviewModel.fromJson(testJson);

        // assert
        expect(result, equals(testModel));
      });

      test('should handle null optional fields', () {
        // arrange
        final jsonWithNulls = Map<String, dynamic>.from(testJson);
        jsonWithNulls.removeWhere((key, value) => [
          'user_avatar_url',
          'vehicle_type',
          'system_type',
          'usage_duration',
          'purchase_date',
          'purchase_proof_url',
        ].contains(key));

        // act
        final result = MaterialReviewModel.fromJson(jsonWithNulls);

        // assert
        expect(result.userAvatarUrl, isNull);
        expect(result.vehicleType, isNull);
        expect(result.systemType, isNull);
        expect(result.usageDuration, isNull);
        expect(result.purchaseDate, isNull);
        expect(result.purchaseProofUrl, isNull);
      });

      test('should handle empty arrays', () {
        // arrange
        final jsonWithEmptyArrays = Map<String, dynamic>.from(testJson);
        jsonWithEmptyArrays['pros'] = [];
        jsonWithEmptyArrays['cons'] = [];
        jsonWithEmptyArrays['tips'] = [];
        jsonWithEmptyArrays['image_urls'] = [];
        jsonWithEmptyArrays['video_urls'] = [];
        jsonWithEmptyArrays['liked_by_user_ids'] = [];
        jsonWithEmptyArrays['helpful_user_ids'] = [];

        // act
        final result = MaterialReviewModel.fromJson(jsonWithEmptyArrays);

        // assert
        expect(result.pros, isEmpty);
        expect(result.cons, isEmpty);
        expect(result.tips, isEmpty);
        expect(result.imageUrls, isEmpty);
        expect(result.videoUrls, isEmpty);
        expect(result.likedByUserIds, isEmpty);
        expect(result.helpfulUserIds, isEmpty);
      });
    });

    group('toJson', () {
      test('should return a valid JSON map', () {
        // act
        final result = testModel.toJson();

        // assert
        expect(result, equals(testJson));
      });
    });

    group('fromEntity', () {
      test('should create MaterialReviewModel from MaterialReview entity', () {
        // act
        final result = MaterialReviewModel.fromEntity(testEntity);

        // assert
        expect(result, equals(testModel));
      });
    });

    group('toEntity', () {
      test('should convert MaterialReviewModel to MaterialReview entity', () {
        // act
        final result = testModel.toEntity();

        // assert
        expect(result, equals(testEntity));
      });
    });

    group('toInsertMap', () {
      test('should return a valid map for database insertion', () {
        // act
        final result = testModel.toInsertMap();

        // assert
        expect(result['id'], equals('test-review-1'));
        expect(result['material_id'], equals('test-material-1'));
        expect(result['user_id'], equals('test-user-1'));
        expect(result['user_name'], equals('Test User'));
        expect(result['content'], equals('This is a test review content.'));
        expect(result['rating'], equals(4.5));
        expect(result['quality_rating'], equals(4.0));
        expect(result['is_verified_purchase'], equals(true));
        expect(result['purchase_date'], equals(testDateTime.toIso8601String()));
        expect(result['created_at'], equals(testDateTime.toIso8601String()));
        expect(result['updated_at'], equals(testDateTime.toIso8601String()));
      });

      test('should handle null values in insert map', () {
        // arrange
        final modelWithNulls = testModel.copyWith(
          userAvatarUrl: null,
          vehicleType: null,
          purchaseDate: null,
        );

        // act
        final result = modelWithNulls.toInsertMap();

        // assert
        expect(result['user_avatar_url'], isNull);
        expect(result['vehicle_type'], isNull);
        expect(result['purchase_date'], isNull);
      });
    });

    group('toUpdateMap', () {
      test('should return a valid map for database update', () {
        // act
        final result = testModel.toUpdateMap();

        // assert
        expect(result['content'], equals('This is a test review content.'));
        expect(result['rating'], equals(4.5));
        expect(result['quality_rating'], equals(4.0));
        expect(result['is_verified_purchase'], equals(true));
        expect(result['updated_at'], isNotNull);
        
        // Should not include id and timestamps from original
        expect(result.containsKey('id'), isFalse);
        expect(result.containsKey('created_at'), isFalse);
      });
    });

    group('JSON serialization round trip', () {
      test('should maintain data integrity through JSON round trip', () {
        // act
        final json = testModel.toJson();
        final jsonString = jsonEncode(json);
        final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
        final reconstructedModel = MaterialReviewModel.fromJson(decodedJson);

        // assert
        expect(reconstructedModel, equals(testModel));
      });
    });

    group('Entity conversion round trip', () {
      test('should maintain data integrity through entity conversion round trip', () {
        // act
        final entity = testModel.toEntity();
        final reconstructedModel = MaterialReviewModel.fromEntity(entity);

        // assert
        expect(reconstructedModel, equals(testModel));
      });
    });
  });

  group('MaterialReviewSummaryModel', () {
    final testSummaryModel = MaterialReviewSummaryModel(
      materialId: 'test-material-1',
      totalReviews: 10,
      averageRating: 4.2,
      qualityAverage: 4.1,
      valueAverage: 4.3,
      durabilityAverage: 4.0,
      installationAverage: 4.4,
      ratingDistribution: {'5': 4, '4': 3, '3': 2, '2': 1, '1': 0},
      verifiedPurchaseCount: 7,
      topPros: ['Good quality', 'Easy to install'],
      topCons: ['Expensive'],
      commonTips: ['Use with care'],
      latestReviewDate: DateTime(2024, 1, 24),
      recommendationScore: 4.2,
    );

    final testSummaryJson = {
      'material_id': 'test-material-1',
      'total_reviews': 10,
      'average_rating': 4.2,
      'quality_average': 4.1,
      'value_average': 4.3,
      'durability_average': 4.0,
      'installation_average': 4.4,
      'rating_distribution': {'5': 4, '4': 3, '3': 2, '2': 1, '1': 0},
      'verified_purchase_count': 7,
      'top_pros': ['Good quality', 'Easy to install'],
      'top_cons': ['Expensive'],
      'common_tips': ['Use with care'],
      'latest_review_date': DateTime(2024, 1, 24).toIso8601String(),
      'recommendation_score': 4.2,
    };

    test('should convert from JSON correctly', () {
      // act
      final result = MaterialReviewSummaryModel.fromJson(testSummaryJson);

      // assert
      expect(result, equals(testSummaryModel));
    });

    test('should convert to entity correctly', () {
      // act
      final entity = testSummaryModel.toEntity();

      // assert
      expect(entity.materialId, equals('test-material-1'));
      expect(entity.totalReviews, equals(10));
      expect(entity.averageRating, equals(4.2));
      expect(entity.ratingDistribution[5], equals(4));
      expect(entity.ratingDistribution[4], equals(3));
      expect(entity.ratingDistribution[1], equals(0));
    });
  });
}
