import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/project.dart';
import '../repositories/project_repository.dart';

class GetProjectsUseCase {
  final ProjectRepository repository;

  GetProjectsUseCase(this.repository);

  Future<Either<Failure, List<Project>>> getUserProjects(String userId) async {
    return await repository.getUserProjects(userId);
  }

  Future<Either<Failure, List<Project>>> getPublicProjects({
    int limit = 20,
    int offset = 0,
  }) async {
    return await repository.getPublicProjects(limit: limit, offset: offset);
  }

  Future<Either<Failure, List<Project>>> searchProjects(String query) async {
    return await repository.searchProjects(query);
  }
}