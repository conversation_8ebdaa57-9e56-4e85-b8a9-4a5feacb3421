import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/fork_permission_service.dart';
import '../../domain/entities/fork_request.dart';
import '../../domain/entities/fork_settings.dart';
import '../../domain/entities/project_visibility.dart';
import '../../domain/repositories/project_repository.dart';

/// 复刻权限服务实现
/// 遵循Clean Architecture原则，实现项目复刻权限验证的业务逻辑
class ForkPermissionServiceImpl implements ForkPermissionService {
  final ProjectRepository projectRepository;

  const ForkPermissionServiceImpl({
    required this.projectRepository,
  });

  @override
  Future<Either<Failure, bool>> canForkProject(String projectId, String userId) async {
    try {
      // 1. 检查项目是否存在
      final projectResult = await projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(ValidationFailure(message: '项目不存在'));
      }

      final project = projectResult.getRight().getOrElse(() => throw Exception());

      // 2. 检查项目是否允许复刻
      if (!project.forkSettings.allowFork) {
        return const Right(false);
      }

      // 3. 检查用户访问权限
      final hasAccessResult = await hasUserAccess(projectId, userId);
      if (hasAccessResult.isLeft()) {
        return hasAccessResult;
      }

      final hasAccess = hasAccessResult.getRight().getOrElse(() => false);
      if (!hasAccess) {
        return const Right(false);
      }

      // 4. 检查用户复刻配额
      final quotaResult = await checkUserForkQuota(userId);
      if (quotaResult.isLeft()) {
        return quotaResult;
      }

      final hasQuota = quotaResult.getRight().getOrElse(() => false);
      return Right(hasQuota);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '检查复刻权限失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isProjectPublic(String projectId) async {
    try {
      final projectResult = await projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(ValidationFailure(message: '项目不存在'));
      }

      final project = projectResult.getRight().getOrElse(() => throw Exception());
      return Right(project.visibility == ProjectVisibility.public);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '检查项目可见性失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasUserAccess(String projectId, String userId) async {
    try {
      final projectResult = await projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(ValidationFailure(message: '项目不存在'));
      }

      final project = projectResult.getRight().getOrElse(() => throw Exception());

      // 1. 检查是否是项目所有者
      if (project.userId == userId) {
        return const Right(true);
      }

      // 2. 检查是否是公开项目
      if (project.visibility == ProjectVisibility.public) {
        return const Right(true);
      }

      // 3. 检查是否是协作者（如果有协作者功能）
      // TODO: 实现协作者权限检查
      
      return const Right(false);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '检查用户访问权限失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ForkSettings>> getProjectForkSettings(String projectId) async {
    try {
      final projectResult = await projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(ValidationFailure(message: '项目不存在'));
      }

      final project = projectResult.getRight().getOrElse(() => throw Exception());
      return Right(project.forkSettings);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取复刻设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateForkRequest(
    String projectId,
    String userId,
    ForkRequest request,
  ) async {
    try {
      // 1. 检查基本复刻权限
      final canForkResult = await canForkProject(projectId, userId);
      if (canForkResult.isLeft()) {
        return canForkResult;
      }

      final canFork = canForkResult.getRight().getOrElse(() => false);
      if (!canFork) {
        return const Right(false);
      }

      // 2. 验证复刻请求参数
      if (request.newProjectTitle.trim().isEmpty) {
        return Left(ValidationFailure(message: '项目标题不能为空'));
      }

      // 3. 检查项目复刻设置
      final settingsResult = await getProjectForkSettings(projectId);
      if (settingsResult.isLeft()) {
        return settingsResult.fold(
          (failure) => Left(failure),
          (settings) => const Right(false),
        );
      }

      final settings = settingsResult.getRight().getOrElse(() => throw Exception());

      // 4. 验证复刻选项是否符合项目设置
      if (request.copyBomItems && !settings.copyBomItems) {
        return Left(ValidationFailure(message: '该项目不允许复制BOM项目'));
      }

      if (request.copySystems && !settings.copySystems) {
        return Left(ValidationFailure(message: '该项目不允许复制系统信息'));
      }

      if (request.copyImages && !settings.copyImages) {
        return Left(ValidationFailure(message: '该项目不允许复制图片资源'));
      }

      return const Right(true);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '验证复刻请求失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkUserForkQuota(String userId) async {
    try {
      // TODO: 实现用户复刻配额检查
      // 这里可以检查用户的订阅级别、已创建的项目数量等
      // 暂时返回true，表示没有配额限制
      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure(message: '检查用户复刻配额失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logPermissionCheck(
    String projectId,
    String userId,
    String action,
    bool granted,
    String? reason,
  ) async {
    try {
      // TODO: 实现权限检查日志记录
      // 这里可以记录到审计日志表中
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '记录权限检查日志失败: $e'));
    }
  }
}
