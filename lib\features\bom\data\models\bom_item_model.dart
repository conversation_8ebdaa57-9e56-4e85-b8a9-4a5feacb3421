import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/bom_item.dart' as domain;

part 'bom_item_model.freezed.dart';

@freezed
class BomItemModel with _$BomItemModel {
  const factory BomItemModel({
    required String id,
    required String projectId,
    required String userId,
    required String materialName,
    required String description,
    required String status,
    required int quantity,
    required double unitPrice,
    required String createdAt,
    required String updatedAt,
    String? materialId,
    String? category,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    String? plannedDate,
    String? purchasedDate,
    String? usedDate,
    double? estimatedPrice,
    double? actualPrice,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _BomItemModel;

  /// 手动实现fromJson以处理数据库字段映射
  factory BomItemModel.fromJson(Map<String, dynamic> json) {
    try {
      final attributes = json['attributes'] as Map<String, dynamic>? ?? {};

      return BomItemModel(
        id: json['id'] as String,
        projectId: json['project_id'] as String,
        userId: attributes['user_id'] as String? ?? json['user_id'] as String? ?? '',
        materialName: json['item_name'] as String? ?? json['name'] as String? ?? '',
        description: json['description'] as String? ?? '',
        status: json['status'] as String? ?? 'pending',
        quantity: (json['quantity'] as num?)?.toInt() ?? 0,
        unitPrice: (json['price'] as num?)?.toDouble() ?? (json['unit_price'] as num?)?.toDouble() ?? 0.0,
        createdAt: json['created_at'] as String? ?? DateTime.now().toIso8601String(),
        updatedAt: json['updated_at'] as String? ?? DateTime.now().toIso8601String(),
        materialId: json['material_id'] as String? ?? attributes['material_id'] as String?,
        category: json['category'] as String?,
        brand: json['brand'] as String? ?? attributes['brand'] as String?,
        model: json['model'] as String? ?? attributes['model'] as String?,
        specifications: json['specifications'] as String? ?? attributes['specifications'] as String?,
        supplier: json['supplier'] as String? ?? attributes['supplier'] as String?,
        supplierUrl: json['supplier_url'] as String? ?? attributes['supplier_url'] as String?,
        imageUrl: json['image_url'] as String? ?? attributes['image_url'] as String?,
        plannedDate: json['planned_date'] as String? ?? attributes['planned_date'] as String?,
        purchasedDate: json['purchase_date'] as String?,
        usedDate: json['use_date'] as String?,
        estimatedPrice: (json['estimated_price'] as num?)?.toDouble(),
        actualPrice: (json['actual_price'] as num?)?.toDouble(),
        notes: json['notes'] as String?,
        tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? (attributes['tags'] as List<dynamic>?)?.cast<String>(),
        metadata: attributes['metadata'] as Map<String, dynamic>?,
      );
    } catch (e) {
      throw FormatException('BomItemModel.fromJson failed: $e\nJSON: $json');
    }
  }

  /// 解析状态字符串为BomItemStatus枚举 - 符合.kiro/specs规范
  static domain.BomItemStatus _parseStatusString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return domain.BomItemStatus.pending;
      case 'ordered':
        return domain.BomItemStatus.ordered;
      case 'received':
        return domain.BomItemStatus.received;
      case 'installed':
        return domain.BomItemStatus.installed;
      case 'cancelled':
        return domain.BomItemStatus.cancelled;
      // 兼容旧状态
      case 'planned':
        return domain.BomItemStatus.pending;
      case 'purchased':
        return domain.BomItemStatus.received;
      case 'used':
        return domain.BomItemStatus.installed;
      case 'completed':
        return domain.BomItemStatus.installed;
      default:
        return domain.BomItemStatus.pending;
    }
  }
}

extension BomItemModelX on BomItemModel {
  domain.BomItem toEntity() {
    return domain.BomItem(
      id: id,
      projectId: projectId,
      userId: userId,
      materialName: materialName,
      description: description,
      status: BomItemModel._parseStatusString(status),
      quantity: quantity,
      unitPrice: unitPrice,
      createdAt: DateTime.parse(createdAt),
      updatedAt: DateTime.parse(updatedAt),
      materialId: materialId,
      category: category,
      brand: brand,
      model: model,
      specifications: specifications,
      // 映射到新的material属性
      materialCategory: category,
      materialBrand: brand,
      materialModel: model,
      supplier: supplier,
      supplierUrl: supplierUrl,
      imageUrl: imageUrl,
      plannedDate: plannedDate != null ? DateTime.tryParse(plannedDate!) : null,
      purchasedDate: purchasedDate != null ? DateTime.tryParse(purchasedDate!) : null,
      usedDate: usedDate != null ? DateTime.tryParse(usedDate!) : null,
      estimatedPrice: estimatedPrice,
      actualPrice: actualPrice,
      notes: notes,
      tags: tags,
      metadata: metadata,
    );
  }


}

extension BomItemX on domain.BomItem {
  BomItemModel toModel() {
    return BomItemModel(
      id: id,
      projectId: projectId,
      userId: userId,
      materialName: materialName,
      description: description,
      status: status.code,
      quantity: quantity,
      unitPrice: unitPrice,
      createdAt: createdAt.toIso8601String(),
      updatedAt: updatedAt.toIso8601String(),
      materialId: materialId,
      category: category,
      brand: brand,
      model: model,
      specifications: specifications,
      supplier: supplier,
      supplierUrl: supplierUrl,
      imageUrl: imageUrl,
      plannedDate: plannedDate?.toIso8601String(),
      purchasedDate: purchasedDate?.toIso8601String(),
      usedDate: usedDate?.toIso8601String(),
      estimatedPrice: estimatedPrice,
      actualPrice: actualPrice,
      notes: notes,
      tags: tags,
      metadata: metadata,
    );
  }
}