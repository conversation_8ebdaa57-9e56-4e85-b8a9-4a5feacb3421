import 'dart:io';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';
import '../../domain/repositories/media_repository.dart';
import '../datasources/media_remote_datasource.dart';
import '../models/log_media_model.dart';

/// 媒体仓库实现
class MediaRepositoryImpl implements MediaRepository {
  final MediaRemoteDataSource remoteDataSource;

  MediaRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, LogMedia>> uploadMedia({
    required File file,
    required String logId,
    required MediaType type,
    required String uploadedBy,
    MediaMetadata? metadata,
  }) async {
    try {
      final mediaModel = await remoteDataSource.uploadMedia(
        file: file,
        logId: logId,
        type: LogMediaModel.mediaTypeToString(type),
        uploadedBy: uploadedBy,
        metadata: metadata?.toJson(),
      );
      return Right(mediaModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '上传媒体失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogMedia>>> getLogMedia(String logId) async {
    try {
      final mediaModels = await remoteDataSource.getLogMedia(logId);
      final media = mediaModels.map((model) => model.toEntity()).toList();
      return Right(media);
    } catch (e) {
      return Left(ServerFailure(message: '获取日志媒体失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogMedia>> getMedia(String mediaId) async {
    try {
      final mediaModel = await remoteDataSource.getMedia(mediaId);
      return Right(mediaModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '获取媒体详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMedia(String mediaId) async {
    try {
      await remoteDataSource.deleteMedia(mediaId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: '删除媒体失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogMedia>> updateMediaMetadata(String mediaId, MediaMetadata metadata) async {
    try {
      final updatedMediaModel = await remoteDataSource.updateMediaMetadata(mediaId, metadata.toJson());
      return Right(updatedMediaModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '更新媒体元数据失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogMedia>>> uploadMultipleMedia({
    required List<File> files,
    required String logId,
    required MediaType type,
    required String uploadedBy,
  }) async {
    try {
      final mediaModels = await remoteDataSource.uploadMultipleMedia(
        files: files,
        logId: logId,
        type: LogMediaModel.mediaTypeToString(type),
        uploadedBy: uploadedBy,
      );
      final media = mediaModels.map((model) => model.toEntity()).toList();
      return Right(media);
    } catch (e) {
      return Left(ServerFailure(message: '批量上传媒体失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogMedia>>> updateMediaOrder(String logId, List<String> orderedMediaIds) async {
    try {
      final mediaModels = await remoteDataSource.updateMediaOrder(logId, orderedMediaIds);
      final media = mediaModels.map((model) => model.toEntity()).toList();
      return Right(media);
    } catch (e) {
      return Left(ServerFailure(message: '更新媒体排序失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogMedia>>> getUserMedia(String userId, {int? limit, int? offset}) async {
    try {
      final mediaModels = await remoteDataSource.getUserMedia(userId, limit: limit, offset: offset);
      final media = mediaModels.map((model) => model.toEntity()).toList();
      return Right(media);
    } catch (e) {
      return Left(ServerFailure(message: '获取用户媒体失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogMedia>>> getProjectMedia(String projectId, {int? limit, int? offset}) async {
    try {
      final mediaModels = await remoteDataSource.getProjectMedia(projectId, limit: limit, offset: offset);
      final media = mediaModels.map((model) => model.toEntity()).toList();
      return Right(media);
    } catch (e) {
      return Left(ServerFailure(message: '获取项目媒体失败: $e'));
    }
  }
}