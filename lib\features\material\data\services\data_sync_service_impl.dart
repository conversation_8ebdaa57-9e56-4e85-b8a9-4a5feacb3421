import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/data_sync_service.dart';

import '../../domain/entities/create_material_request.dart';
import '../../domain/repositories/material_repository.dart';
import '../datasources/material_remote_datasource.dart';
import '../models/material_model.dart';
import '../../../bom/data/datasources/bom_remote_datasource.dart';
import '../../../bom/data/models/bom_item_model.dart';

/// 数据同步服务实现
/// 遵循Clean Architecture原则，实现材料库与BOM之间的数据同步
class DataSyncServiceImpl implements DataSyncService {
  final MaterialRemoteDataSource materialDataSource;
  final BomRemoteDataSource bomDataSource;
  final MaterialRepository materialRepository;
  final SupabaseClient supabaseClient;
  final StreamController<SyncOperation> _syncController = StreamController.broadcast();

  DataSyncServiceImpl({
    required this.materialDataSource,
    required this.bomDataSource,
    required this.materialRepository,
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, void>> syncMaterialToBom(
    String materialId,
    String bomItemId,
  ) async {
    try {
      // 获取材料信息
      final materialModel = await materialDataSource.getMaterialById(materialId);
      final material = materialModel.toEntity();

      // 获取BOM项信息
      final bomItemModel = await bomDataSource.getBomItemById(bomItemId);
      
      // 创建同步操作记录
      final syncOperation = SyncOperation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: SyncOperationType.materialToBom,
        sourceId: materialId,
        targetId: bomItemId,
        data: {
          'material_name': material.name,
          'price': material.price,
          'supplier': material.supplier,
          'specifications': material.specifications,
        },
        timestamp: DateTime.now(),
      );

      // 更新BOM项信息
      final updates = {
        'material_name': material.name,
        'unit_price': material.price,
        'supplier': material.supplier,
        'specifications': material.specifications,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await bomDataSource.updateBomItem(bomItemId, updates);

      // 更新材料使用统计
      await updateMaterialUsageStats(materialId, 1);
      await updateMaterialLastUsed(materialId);

      // 发送同步完成事件
      _syncController.add(syncOperation.copyWith(status: SyncStatus.completed));

      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步材料到BOM失败: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> syncBomToMaterial(String bomItemId) async {
    try {
      // 获取BOM项信息
      final bomItemModel = await bomDataSource.getBomItemById(bomItemId);
      final bomItem = bomItemModel.toEntity();

      // 检查是否已存在相同的材料
      final existingMaterials = await materialDataSource.searchMaterials(
        bomItem.userId, // 添加userId参数
        bomItem.materialName,
        limit: 1,
      );

      String materialId;
      if (existingMaterials.isNotEmpty) {
        // 更新现有材料
        materialId = existingMaterials.first.id;
        final updates = {
          'price': bomItem.unitPrice,
          'supplier': bomItem.supplier,
          'specifications': bomItem.specifications,
          'updated_at': DateTime.now().toIso8601String(),
        };
        await materialDataSource.updateMaterial(materialId, updates);
      } else {
        // 创建新材料
        final createRequest = CreateMaterialRequest(
          name: bomItem.materialName,
          category: bomItem.category ?? '未分类',
          price: bomItem.unitPrice,
          description: bomItem.description,
          brand: bomItem.brand,
          model: bomItem.model,
          specifications: bomItem.specifications,
          supplier: bomItem.supplier,
          supplierUrl: bomItem.supplierUrl,
          imageUrl: bomItem.imageUrl,
          notes: bomItem.notes,
          tags: bomItem.tags,
          metadata: bomItem.metadata,
        );

        // 调用材料仓库创建材料
        final createResult = await materialRepository.createMaterial(createRequest);
        materialId = createResult.fold(
          (failure) => throw ServerException(message: '创建材料失败: ${failure.message}'),
          (material) => material.id,
        );

        debugPrint('成功创建新材料: $materialId');
      }

      // 创建同步操作记录
      final syncOperation = SyncOperation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: SyncOperationType.bomToMaterial,
        sourceId: bomItemId,
        targetId: materialId,
        data: {
          'material_name': bomItem.materialName,
          'price': bomItem.unitPrice,
        },
        timestamp: DateTime.now(),
        status: SyncStatus.completed,
      );

      _syncController.add(syncOperation);

      return Right(materialId);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步BOM到材料库失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<PriceSyncNotification>>> syncPriceUpdates(
    String materialId,
  ) async {
    try {
      // 获取材料信息
      final materialModel = await materialDataSource.getMaterialById(materialId);
      final material = materialModel.toEntity();

      // 查找使用此材料的BOM项
      final response = await supabaseClient
          .from('bom_items')
          .select('id, material_name, unit_price, project_id')
          .eq('material_id', materialId);

      final notifications = <PriceSyncNotification>[];
      
      for (final bomData in response) {
        final oldPrice = (bomData['unit_price'] as num?)?.toDouble() ?? 0.0;
        if (oldPrice != material.price) {
          notifications.add(PriceSyncNotification(
            materialId: materialId,
            materialName: material.name,
            oldPrice: oldPrice,
            newPrice: material.price,
            affectedBomItemIds: [bomData['id'] as String],
            timestamp: DateTime.now(),
          ));
        }
      }

      return Right(notifications);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步价格更新失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMaterialUsageStats(
    String materialId,
    int usageIncrement,
  ) async {
    try {
      await supabaseClient.rpc('increment_material_usage', params: {
        'material_id': materialId,
        'increment': usageIncrement,
      });
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '更新材料使用统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMaterialLastUsed(String materialId) async {
    try {
      await supabaseClient
          .from('materials')
          .update({'last_used_at': DateTime.now().toIso8601String()})
          .eq('id', materialId);
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '更新材料最近使用时间失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<PriceSyncNotification>>> getPriceSyncNotifications(
    String userId,
  ) async {
    try {
      // 实现获取价格同步提醒的逻辑
      // 这里可以查询用户相关的项目和材料价格变化
      return const Right([]);
    } catch (e) {
      return Left(UnknownFailure(message: '获取价格同步提醒失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> confirmPriceSync(
    String materialId,
    List<String> bomItemIds,
    bool syncPrice,
  ) async {
    try {
      if (syncPrice) {
        final materialModel = await materialDataSource.getMaterialById(materialId);
        final material = materialModel.toEntity();

        for (final bomItemId in bomItemIds) {
          await bomDataSource.updateBomItem(bomItemId, {
            'unit_price': material.price,
            'updated_at': DateTime.now().toIso8601String(),
          });
        }
      }
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '确认价格同步失败: $e'));
    }
  }

  @override
  Stream<SyncOperation> watchSyncOperations() {
    return _syncController.stream;
  }

  @override
  Future<Either<Failure, List<SyncOperation>>> getSyncHistory({
    String? materialId,
    String? bomItemId,
    SyncOperationType? type,
    int limit = 50,
  }) async {
    try {
      // 实现同步历史查询逻辑
      // 这里可以从数据库查询同步操作记录
      return const Right([]);
    } catch (e) {
      return Left(UnknownFailure(message: '获取同步历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> retrySyncOperation(String operationId) async {
    try {
      // 实现重试同步操作的逻辑
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '重试同步操作失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelSyncOperation(String operationId) async {
    try {
      // 实现取消同步操作的逻辑
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '取消同步操作失败: $e'));
    }
  }

  void dispose() {
    _syncController.close();
  }
}
