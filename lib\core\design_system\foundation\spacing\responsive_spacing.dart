/// VanHub响应式间距系统
/// 
/// 基于8pt网格的响应式间距系统
/// 支持移动端、平板、桌面的自适应间距

import 'package:flutter/material.dart';

/// 响应式间距系统
class VanHubResponsiveSpacing {
  VanHubResponsiveSpacing._();

  // ============ 基础间距令牌 (8pt Grid) ============
  
  static const double _baseUnit = 8.0;
  
  /// 无间距
  static const double none = 0.0;
  
  /// 极小间距 (4px)
  static const double xs = _baseUnit * 0.5;
  
  /// 小间距 (8px)
  static const double sm = _baseUnit * 1;
  
  /// 中等间距 (16px)
  static const double md = _baseUnit * 2;
  
  /// 大间距 (24px)
  static const double lg = _baseUnit * 3;
  
  /// 特大间距 (32px)
  static const double xl = _baseUnit * 4;
  
  /// 超大间距 (48px)
  static const double xxl = _baseUnit * 6;
  
  /// 巨大间距 (64px)
  static const double xxxl = _baseUnit * 8;

  // ============ 响应式间距方法 ============
  
  /// 获取响应式间距值
  static double getResponsive(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
    double? large,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1440 && large != null) {
      return large;
    } else if (screenWidth >= 1024 && desktop != null) {
      return desktop;
    } else if (screenWidth >= 768 && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// 获取响应式EdgeInsets
  static EdgeInsets getResponsiveInsets(
    BuildContext context, {
    required EdgeInsets mobile,
    EdgeInsets? tablet,
    EdgeInsets? desktop,
    EdgeInsets? large,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1440 && large != null) {
      return large;
    } else if (screenWidth >= 1024 && desktop != null) {
      return desktop;
    } else if (screenWidth >= 768 && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  // ============ 预定义响应式间距 ============
  
  /// 页面内边距
  static EdgeInsets getPagePadding(BuildContext context) {
    return getResponsiveInsets(
      context,
      mobile: const EdgeInsets.all(md),
      tablet: const EdgeInsets.all(lg),
      desktop: const EdgeInsets.all(xl),
      large: const EdgeInsets.all(xxl),
    );
  }

  /// 卡片内边距
  static EdgeInsets getCardPadding(BuildContext context) {
    return getResponsiveInsets(
      context,
      mobile: const EdgeInsets.all(md),
      tablet: const EdgeInsets.all(lg),
      desktop: const EdgeInsets.all(xl),
    );
  }

  /// 组件间距
  static double getComponentSpacing(BuildContext context) {
    return getResponsive(
      context,
      mobile: md,
      tablet: lg,
      desktop: xl,
    );
  }

  /// 节间距
  static double getSectionSpacing(BuildContext context) {
    return getResponsive(
      context,
      mobile: xl,
      tablet: xxl,
      desktop: xxxl,
    );
  }

  /// 列表项间距
  static double getListItemSpacing(BuildContext context) {
    return getResponsive(
      context,
      mobile: sm,
      tablet: md,
      desktop: lg,
    );
  }

  // ============ 特殊用途间距 ============
  
  /// 安全区域间距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final basePadding = getPagePadding(context);
    
    return EdgeInsets.only(
      top: mediaQuery.padding.top + basePadding.top,
      bottom: mediaQuery.padding.bottom + basePadding.bottom,
      left: mediaQuery.padding.left + basePadding.left,
      right: mediaQuery.padding.right + basePadding.right,
    );
  }

  /// 键盘避让间距
  static EdgeInsets getKeyboardAvoidingPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final basePadding = getPagePadding(context);
    
    return EdgeInsets.only(
      top: basePadding.top,
      bottom: mediaQuery.viewInsets.bottom + basePadding.bottom,
      left: basePadding.left,
      right: basePadding.right,
    );
  }

  /// 导航栏高度
  static double getAppBarHeight(BuildContext context) {
    return getResponsive(
      context,
      mobile: 56.0,
      tablet: 64.0,
      desktop: 72.0,
    );
  }

  /// 底部导航栏高度
  static double getBottomNavHeight(BuildContext context) {
    return getResponsive(
      context,
      mobile: 60.0,
      tablet: 68.0,
      desktop: 76.0,
    );
  }

  /// 浮动按钮大小
  static double getFabSize(BuildContext context) {
    return getResponsive(
      context,
      mobile: 56.0,
      tablet: 64.0,
      desktop: 72.0,
    );
  }

  // ============ 网格系统 ============
  
  /// 获取网格列数
  static int getGridColumns(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1440) {
      return 6; // 大屏幕
    } else if (screenWidth >= 1024) {
      return 4; // 桌面
    } else if (screenWidth >= 768) {
      return 3; // 平板
    } else if (screenWidth >= 480) {
      return 2; // 大手机
    } else {
      return 1; // 小手机
    }
  }

  /// 获取网格间距
  static double getGridSpacing(BuildContext context) {
    return getResponsive(
      context,
      mobile: sm,
      tablet: md,
      desktop: lg,
    );
  }

  /// 获取网格主轴间距
  static double getGridMainAxisSpacing(BuildContext context) {
    return getGridSpacing(context);
  }

  /// 获取网格交叉轴间距
  static double getGridCrossAxisSpacing(BuildContext context) {
    return getGridSpacing(context);
  }

  // ============ 容器尺寸 ============
  
  /// 获取最大容器宽度
  static double getMaxContainerWidth(BuildContext context) {
    return getResponsive(
      context,
      mobile: double.infinity,
      tablet: 768.0,
      desktop: 1200.0,
      large: 1440.0,
    );
  }

  /// 获取侧边栏宽度
  static double getSidebarWidth(BuildContext context) {
    return getResponsive(
      context,
      mobile: 280.0,
      tablet: 320.0,
      desktop: 360.0,
    );
  }

  /// 获取抽屉宽度
  static double getDrawerWidth(BuildContext context) {
    return getResponsive(
      context,
      mobile: 280.0,
      tablet: 320.0,
      desktop: 360.0,
    );
  }

  // ============ 触摸目标 ============
  
  /// 最小触摸目标尺寸
  static double getMinTouchTarget(BuildContext context) {
    return getResponsive(
      context,
      mobile: 44.0, // iOS标准
      tablet: 48.0, // Material标准
      desktop: 40.0, // 桌面可以更小
    );
  }

  /// 按钮高度
  static double getButtonHeight(BuildContext context, {bool large = false}) {
    if (large) {
      return getResponsive(
        context,
        mobile: 48.0,
        tablet: 52.0,
        desktop: 56.0,
      );
    } else {
      return getResponsive(
        context,
        mobile: 40.0,
        tablet: 44.0,
        desktop: 48.0,
      );
    }
  }

  /// 输入框高度
  static double getInputHeight(BuildContext context) {
    return getResponsive(
      context,
      mobile: 48.0,
      tablet: 52.0,
      desktop: 56.0,
    );
  }
}

/// 响应式间距扩展
extension VanHubResponsiveSpacingExtensions on BuildContext {
  /// 获取响应式间距
  double spacing({
    required double mobile,
    double? tablet,
    double? desktop,
    double? large,
  }) {
    return VanHubResponsiveSpacing.getResponsive(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      large: large,
    );
  }

  /// 获取响应式内边距
  EdgeInsets spacingInsets({
    required EdgeInsets mobile,
    EdgeInsets? tablet,
    EdgeInsets? desktop,
    EdgeInsets? large,
  }) {
    return VanHubResponsiveSpacing.getResponsiveInsets(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      large: large,
    );
  }

  /// 快速获取页面内边距
  EdgeInsets get pagePadding => VanHubResponsiveSpacing.getPagePadding(this);

  /// 快速获取卡片内边距
  EdgeInsets get cardPadding => VanHubResponsiveSpacing.getCardPadding(this);

  /// 快速获取组件间距
  double get componentSpacing => VanHubResponsiveSpacing.getComponentSpacing(this);

  /// 快速获取节间距
  double get sectionSpacing => VanHubResponsiveSpacing.getSectionSpacing(this);
}
