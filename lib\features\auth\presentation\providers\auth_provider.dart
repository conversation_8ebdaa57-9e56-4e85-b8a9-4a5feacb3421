import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/login_request.dart';
import '../../domain/entities/register_request.dart';
import '../../../../core/di/injection_container.dart';

part 'auth_provider.g.dart';

/// 认证状态Notifier
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  FutureOr<User?> build() async {
    // 检查是否有已登录的用户
    try {
      final authRepository = ref.read(authRepositoryProvider);
      final result = await authRepository.getCurrentUser();
      return result.fold(
        (failure) => null,
        (user) => user,
      );
    } catch (e) {
      return null;
    }
  }

  /// 登录
  Future<bool> login(LoginRequest request) async {
    state = const AsyncLoading();
    
    try {
      final result = await ref.read(loginUseCaseProvider).call(request);
      
      return result.fold(
        (failure) {
          state = AsyncError(failure, StackTrace.current);
          return false;
        },
        (user) {
          state = AsyncData(user);
          return true;
        },
      );
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return false;
    }
  }

  /// 注册
  Future<bool> register(RegisterRequest request) async {
    state = const AsyncLoading();
    
    try {
      final result = await ref.read(registerUseCaseProvider).call(request);
      
      return result.fold(
        (failure) {
          state = AsyncError(failure, StackTrace.current);
          return false;
        },
        (user) {
          state = AsyncData(user);
          return true;
        },
      );
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return false;
    }
  }

  /// 登出
  Future<bool> logout() async {
    try {
      final result = await ref.read(logoutUseCaseProvider).call();
      
      return result.fold(
        (failure) => false,
        (_) {
          state = const AsyncData(null);
          return true;
        },
      );
    } catch (e) {
      return false;
    }
  }

  /// 游客登录
  Future<bool> signInAsGuest() async {
    state = const AsyncLoading();
    
    try {
      // 创建游客用户对象
      final guestUser = User(
        id: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        email: '<EMAIL>',
        name: '游客用户',
        isAnonymous: true,
        createdAt: DateTime.now(),
      );
      
      state = AsyncData(guestUser);
      return true;
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return false;
    }
  }

  /// 重置密码
  Future<bool> resetPassword(String email) async {
    try {
      final authRepository = ref.read(authRepositoryProvider);
      final result = await authRepository.resetPassword(email);
      
      return result.fold(
        (failure) => false,
        (_) => true,
      );
    } catch (e) {
      return false;
    }
  }
}