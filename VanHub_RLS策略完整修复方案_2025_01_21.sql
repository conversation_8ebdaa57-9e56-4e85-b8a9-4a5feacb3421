-- VanHub改装宝 - Supabase RLS策略完整修复方案
-- 基于Clean Architecture原则和.kiro/specs规范
-- 执行日期：2025年1月21日
-- 
-- 修复目标：
-- 1. 游客访问公开项目数据
-- 2. 认证用户管理自己的数据  
-- 3. 公共数据（如材料分类）的开放访问
-- 4. 支持Playwright测试和开发调试

-- =====================================================
-- 第一阶段：启用所有核心表的RLS
-- =====================================================

-- 根据Supabase提示文件，以下表已有策略但RLS未启用
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bom_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.timeline_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- 启用其他核心业务表的RLS
ALTER TABLE public.material_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_tags ENABLE ROW LEVEL SECURITY;

-- 启用项目扩展功能表的RLS
ALTER TABLE public.project_discovery ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_nodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_node_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_versions ENABLE ROW LEVEL SECURITY;

-- 启用社交功能表的RLS
ALTER TABLE public.project_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_forks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- 启用BOM相关表的RLS
ALTER TABLE public.bom_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bom_template_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bom_version_history ENABLE ROW LEVEL SECURITY;

-- 启用日志和媒体表的RLS
ALTER TABLE public.log_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media ENABLE ROW LEVEL SECURITY;

-- 启用协作功能表的RLS
ALTER TABLE public.collaboration_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_notifications ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 第二阶段：核心业务表策略配置
-- =====================================================

-- 1. PROJECTS表策略 - 支持游客访问公开项目
-- 删除可能存在的旧策略
DROP POLICY IF EXISTS "Users can view public projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view own projects" ON public.projects;
DROP POLICY IF EXISTS "Users can insert own projects" ON public.projects;
DROP POLICY IF EXISTS "Users can update own projects" ON public.projects;
DROP POLICY IF EXISTS "Users can delete own projects" ON public.projects;

-- 创建新的策略
CREATE POLICY "Allow public read for public projects" ON public.projects
    FOR SELECT USING (is_public = true);

CREATE POLICY "Allow authenticated users full access to own projects" ON public.projects
    FOR ALL USING (auth.uid() = user_id);

-- 2. BOM_ITEMS表策略 - 基于项目公开性
DROP POLICY IF EXISTS "Users can view bom_items of public projects" ON public.bom_items;
DROP POLICY IF EXISTS "Users can view bom_items of own projects" ON public.bom_items;
DROP POLICY IF EXISTS "Users can insert bom_items to own projects" ON public.bom_items;
DROP POLICY IF EXISTS "Users can update bom_items of own projects" ON public.bom_items;
DROP POLICY IF EXISTS "Users can delete bom_items of own projects" ON public.bom_items;

CREATE POLICY "Allow read bom_items for public projects" ON public.bom_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow authenticated users manage own project bom_items" ON public.bom_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 3. COMMITS表策略 - 基于项目公开性
DROP POLICY IF EXISTS "Users can view commits of public projects" ON public.commits;
DROP POLICY IF EXISTS "Users can view commits of own projects" ON public.commits;
DROP POLICY IF EXISTS "Users can insert commits to own projects" ON public.commits;
DROP POLICY IF EXISTS "Users can update commits of own projects" ON public.commits;
DROP POLICY IF EXISTS "Users can delete commits of own projects" ON public.commits;

CREATE POLICY "Allow read commits for public projects" ON public.commits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = commits.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow authenticated users manage own project commits" ON public.commits
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = commits.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- =====================================================
-- 第三阶段：材料库系统策略配置
-- =====================================================

-- 4. MATERIAL_CATEGORIES表策略 - 公共数据，所有人可读
DROP POLICY IF EXISTS "Allow public read for material categories" ON public.material_categories;
CREATE POLICY "Allow public read for material categories" ON public.material_categories
    FOR SELECT USING (true);

-- 只允许管理员修改分类（可选）
CREATE POLICY "Allow admin manage material categories" ON public.material_categories
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid() 
            AND users.email LIKE '%@vanhub.admin'
        )
    );

-- 5. MATERIAL_LIBRARY表策略 - 公开读取，用户管理自己的材料
DROP POLICY IF EXISTS "Allow public read for materials" ON public.material_library;
CREATE POLICY "Allow public read for materials" ON public.material_library
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow users manage own materials" ON public.material_library;
CREATE POLICY "Allow users manage own materials" ON public.material_library
    FOR ALL USING (auth.uid() = user_id);

-- 6. MATERIAL_BRANDS表策略 - 公共数据
CREATE POLICY "Allow public read for material brands" ON public.material_brands
    FOR SELECT USING (true);

-- 7. MATERIAL_TAGS表策略 - 公共数据
CREATE POLICY "Allow public read for material tags" ON public.material_tags
    FOR SELECT USING (true);

-- =====================================================
-- 第四阶段：用户相关表策略配置
-- =====================================================

-- 8. USERS表策略 - 用户管理自己的资料
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;

CREATE POLICY "Allow users view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Allow users insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Allow users update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- 9. MATERIAL_FAVORITES表策略 - 用户管理自己的收藏
CREATE POLICY "Allow users manage own favorites" ON public.material_favorites
    FOR ALL USING (auth.uid() = user_id);

-- 10. MATERIAL_REVIEWS表策略 - 公开读取，用户管理自己的评价
CREATE POLICY "Allow public read for reviews" ON public.material_reviews
    FOR SELECT USING (true);

CREATE POLICY "Allow users create reviews" ON public.material_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users update own reviews" ON public.material_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- 第五阶段：项目扩展功能策略配置
-- =====================================================

-- 11. PROJECT_DISCOVERY表策略 - 公开项目的发现数据
CREATE POLICY "Allow public read for project discovery" ON public.project_discovery
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = project_discovery.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow users manage own project discovery" ON public.project_discovery
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = project_discovery.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 12. PROJECT_STATS表策略 - 公开项目的统计数据
CREATE POLICY "Allow public read for project stats" ON public.project_stats
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = project_stats.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow system update project stats" ON public.project_stats
    FOR ALL USING (true); -- 系统自动更新统计数据

-- =====================================================
-- 第六阶段：社交功能策略配置
-- =====================================================

-- 13. PROJECT_LIKES表策略 - 公开读取，用户管理自己的点赞
CREATE POLICY "Allow public read for project likes" ON public.project_likes
    FOR SELECT USING (true);

CREATE POLICY "Allow users manage own likes" ON public.project_likes
    FOR ALL USING (auth.uid() = user_id);

-- 14. PROJECT_FOLLOWS表策略 - 用户管理自己的关注
CREATE POLICY "Allow users manage own follows" ON public.project_follows
    FOR ALL USING (auth.uid() = user_id);

-- 15. PROJECT_FORKS表策略 - 公开读取分叉信息
CREATE POLICY "Allow public read for project forks" ON public.project_forks
    FOR SELECT USING (true);

CREATE POLICY "Allow users create forks" ON public.project_forks
    FOR INSERT WITH CHECK (auth.uid() = forked_by_user_id);

-- 16. COMMENTS表策略 - 公开读取，用户管理自己的评论
CREATE POLICY "Allow public read for comments" ON public.comments
    FOR SELECT USING (true);

CREATE POLICY "Allow users create comments" ON public.comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users update own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- 第七阶段：时间轴和日志策略配置
-- =====================================================

-- 17. TIMELINE_EVENTS表策略 - 基于项目公开性
DROP POLICY IF EXISTS "Users can view timeline events" ON public.timeline_events;
DROP POLICY IF EXISTS "Users can insert timeline events" ON public.timeline_events;
DROP POLICY IF EXISTS "Users can update timeline events" ON public.timeline_events;
DROP POLICY IF EXISTS "Users can delete timeline events" ON public.timeline_events;

CREATE POLICY "Allow read timeline for public projects" ON public.timeline_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = timeline_events.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow users manage own project timeline" ON public.timeline_events
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = timeline_events.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 18. LOG_ENTRIES表策略 - 基于项目公开性
CREATE POLICY "Allow read logs for public projects" ON public.log_entries
    FOR SELECT USING (
        project_id IS NULL OR
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = log_entries.project_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow users manage own project logs" ON public.log_entries
    FOR ALL USING (
        project_id IS NULL OR
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = log_entries.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 19. MEDIA表策略 - 基于关联的commit和项目
CREATE POLICY "Allow read media for public projects" ON public.media
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.commits 
            JOIN public.projects ON commits.project_id = projects.id
            WHERE commits.id = media.commit_id 
            AND projects.is_public = true
        )
    );

CREATE POLICY "Allow users manage own project media" ON public.media
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.commits 
            JOIN public.projects ON commits.project_id = projects.id
            WHERE commits.id = media.commit_id 
            AND projects.user_id = auth.uid()
        )
    );

-- =====================================================
-- 第八阶段：验证RLS配置状态
-- =====================================================

-- 检查所有核心表的RLS启用状态
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN (
    'projects', 'bom_items', 'commits', 'timeline_events', 'users',
    'material_library', 'material_categories', 'material_favorites', 'material_reviews',
    'project_discovery', 'project_stats', 'project_likes', 'project_follows', 'project_forks',
    'log_entries', 'media', 'comments'
)
ORDER BY tablename;

-- 检查策略创建状态
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('projects', 'bom_items', 'material_library', 'material_categories')
ORDER BY tablename, policyname;

-- =====================================================
-- 第九阶段：测试查询验证
-- =====================================================

-- 以下查询应该能够成功执行（游客模式测试）
-- 测试公开项目访问
-- SELECT id, title, description, is_public FROM public.projects WHERE is_public = true LIMIT 3;

-- 测试材料分类访问
-- SELECT id, name, icon, color FROM public.material_categories ORDER BY sort_order LIMIT 5;

-- 测试材料库访问
-- SELECT id, item_name, category, reference_price FROM public.material_library LIMIT 5;

-- 测试BOM项目访问（公开项目的）
-- SELECT bi.id, bi.item_name, bi.price, p.title as project_title 
-- FROM public.bom_items bi 
-- JOIN public.projects p ON bi.project_id = p.id 
-- WHERE p.is_public = true LIMIT 3;

-- =====================================================
-- 完成提示和后续步骤
-- =====================================================

-- 🎉 RLS策略修复完成！
-- 
-- ✅ 已完成的配置：
-- 1. 启用了所有核心表的RLS
-- 2. 配置了游客访问公开项目数据的策略
-- 3. 配置了认证用户管理自己数据的策略
-- 4. 配置了公共数据（材料分类等）的开放访问
-- 5. 支持Playwright测试和开发调试
-- 
-- 🔄 下一步操作：
-- 1. 在VanHub应用中测试游客模式功能
-- 2. 测试认证用户的完整功能
-- 3. 运行Playwright测试验证修复效果
-- 4. 检查编译错误是否解决
-- 
-- 📊 预期结果：
-- - API 404错误应该消失
-- - 游客可以浏览公开项目和材料库
-- - 认证用户可以管理自己的数据
-- - Playwright测试应该能够正常运行
-- - 编译错误应该大幅减少
