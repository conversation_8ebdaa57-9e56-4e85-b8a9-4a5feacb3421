import 'package:freezed_annotation/freezed_annotation.dart';

part 'project_stats.freezed.dart';
part 'project_stats.g.dart';

/// 项目统计数据实体
/// 包含项目的进度、预算、BOM项目等统计信息
@freezed
class ProjectStats with _$ProjectStats {
  const factory ProjectStats({
    /// 项目ID
    required String projectId,
    
    /// 项目进度百分比 (0-100)
    required double progressPercentage,
    
    /// 总预算
    required double totalBudget,
    
    /// 实际花费
    required double actualCost,
    
    /// 剩余预算
    required double remainingBudget,
    
    /// 预算使用率 (0-100)
    required double budgetUtilization,
    
    /// BOM项目总数
    required int totalBomItems,
    
    /// 已完成的BOM项目数
    required int completedBomItems,
    
    /// 计划中的BOM项目数
    required int plannedBomItems,
    
    /// 已采购的BOM项目数
    required int purchasedBomItems,
    
    /// 使用中的BOM项目数
    required int inUseBomItems,
    
    /// 已安装的BOM项目数
    required int installedBomItems,
    
    /// 最后更新时间
    required DateTime lastUpdated,
    
    /// 项目创建时间
    required DateTime projectCreatedAt,
    
    /// 预计完成时间
    DateTime? estimatedCompletionDate,
    
    /// 项目状态分布
    required Map<String, int> statusDistribution,
    
    /// 分类成本分布
    required Map<String, double> categoryCostDistribution,
  }) = _ProjectStats;
  
  factory ProjectStats.fromJson(Map<String, dynamic> json) => _$ProjectStatsFromJson(json);
}

/// 项目统计扩展方法
extension ProjectStatsX on ProjectStats {
  /// 是否超出预算
  bool get isOverBudget => actualCost > totalBudget;
  
  /// 预算超支金额
  double get budgetOverrun => isOverBudget ? actualCost - totalBudget : 0.0;
  
  /// 项目完成率 (0-1)
  double get completionRate => totalBomItems > 0 ? completedBomItems / totalBomItems : 0.0;
  
  /// 是否接近完成 (完成率 >= 80%)
  bool get isNearCompletion => completionRate >= 0.8;
  
  /// 预算健康状态
  BudgetHealthStatus get budgetHealthStatus {
    if (budgetUtilization <= 70) return BudgetHealthStatus.healthy;
    if (budgetUtilization <= 90) return BudgetHealthStatus.warning;
    if (budgetUtilization <= 100) return BudgetHealthStatus.critical;
    return BudgetHealthStatus.overBudget;
  }
  
  /// 项目进度状态
  ProjectProgressStatus get progressStatus {
    if (progressPercentage == 0) return ProjectProgressStatus.notStarted;
    if (progressPercentage < 25) return ProjectProgressStatus.justStarted;
    if (progressPercentage < 50) return ProjectProgressStatus.inProgress;
    if (progressPercentage < 75) return ProjectProgressStatus.halfWay;
    if (progressPercentage < 100) return ProjectProgressStatus.nearCompletion;
    return ProjectProgressStatus.completed;
  }
  
  /// 获取最大成本分类
  String? get topCostCategory {
    if (categoryCostDistribution.isEmpty) return null;
    return categoryCostDistribution.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
  
  /// 获取最大成本分类的金额
  double get topCategoryCost {
    if (categoryCostDistribution.isEmpty) return 0.0;
    return categoryCostDistribution.values
        .reduce((a, b) => a > b ? a : b);
  }
}

/// 预算健康状态枚举
enum BudgetHealthStatus {
  healthy,    // 健康 (≤70%)
  warning,    // 警告 (70-90%)
  critical,   // 危险 (90-100%)
  overBudget, // 超支 (>100%)
}

/// 项目进度状态枚举
enum ProjectProgressStatus {
  notStarted,     // 未开始 (0%)
  justStarted,    // 刚开始 (0-25%)
  inProgress,     // 进行中 (25-50%)
  halfWay,        // 过半 (50-75%)
  nearCompletion, // 接近完成 (75-100%)
  completed,      // 已完成 (100%)
}

/// 预算健康状态扩展
extension BudgetHealthStatusX on BudgetHealthStatus {
  /// 获取状态颜色
  String get colorHex {
    switch (this) {
      case BudgetHealthStatus.healthy:
        return '#4CAF50'; // 绿色
      case BudgetHealthStatus.warning:
        return '#FF9800'; // 橙色
      case BudgetHealthStatus.critical:
        return '#F44336'; // 红色
      case BudgetHealthStatus.overBudget:
        return '#9C27B0'; // 紫色
    }
  }
  
  /// 获取状态描述
  String get description {
    switch (this) {
      case BudgetHealthStatus.healthy:
        return '预算健康';
      case BudgetHealthStatus.warning:
        return '预算紧张';
      case BudgetHealthStatus.critical:
        return '预算危险';
      case BudgetHealthStatus.overBudget:
        return '预算超支';
    }
  }
}

/// 项目进度状态扩展
extension ProjectProgressStatusX on ProjectProgressStatus {
  /// 获取状态描述
  String get description {
    switch (this) {
      case ProjectProgressStatus.notStarted:
        return '未开始';
      case ProjectProgressStatus.justStarted:
        return '刚开始';
      case ProjectProgressStatus.inProgress:
        return '进行中';
      case ProjectProgressStatus.halfWay:
        return '过半';
      case ProjectProgressStatus.nearCompletion:
        return '接近完成';
      case ProjectProgressStatus.completed:
        return '已完成';
    }
  }
  
  /// 获取状态图标
  String get iconName {
    switch (this) {
      case ProjectProgressStatus.notStarted:
        return 'play_circle_outline';
      case ProjectProgressStatus.justStarted:
        return 'play_arrow';
      case ProjectProgressStatus.inProgress:
        return 'trending_up';
      case ProjectProgressStatus.halfWay:
        return 'timeline';
      case ProjectProgressStatus.nearCompletion:
        return 'near_me';
      case ProjectProgressStatus.completed:
        return 'check_circle';
    }
  }
}
