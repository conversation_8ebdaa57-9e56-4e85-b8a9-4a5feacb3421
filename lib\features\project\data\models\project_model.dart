import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/project.dart' as domain;
import '../../domain/entities/project_visibility.dart';

part 'project_model.freezed.dart';

@freezed
class ProjectModel with _$ProjectModel {
  const factory ProjectModel({
    required String id,
    required String authorId,
    required String title,
    required String description,
    required String status,
    required String createdAt,
    required String updatedAt,
    @Default('private') String visibility,
    @Default(0.0) double budget,
    @Default(0.0) double spentAmount,
    @Default(0) int progress,
    @Default(0) int likesCount,
    @Default(0) int forksCount,
    @Default(0) int viewsCount,
    String? authorName,
    String? authorAvatarUrl,
    List<String>? tags,
    String? imageUrl,
    String? coverImageUrl,
    String? vehicleBrand,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleInfo,
    String? startDate,
    String? endDate,
    // 复刻相关字段
    String? sourceProjectId,
    String? sourceProjectTitle,
    String? sourceAuthorId,
    String? sourceAuthorName,
    Map<String, dynamic>? forkSettings,
    // 兼容性字段
    String? userId,
    bool? isPublic,
    String? vehicleType,
  }) = _ProjectModel;

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'] as String,
      authorId: json['user_id'] as String,  // 修复字段映射
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      status: 'planning',  // 数据库中没有status字段，使用默认值
      createdAt: json['created_at'] as String? ?? DateTime.now().toIso8601String(),
      updatedAt: json['updated_at'] as String? ?? DateTime.now().toIso8601String(),
      visibility: (json['is_public'] as bool? ?? false) ? 'public' : 'private',  // 修复字段映射
      budget: (json['total_budget'] as num?)?.toDouble() ?? 0.0,  // 修复字段映射
      vehicleModel: json['vehicle_model'] as String?,  // 修复字段映射
      // 其他字段使用默认值
      spentAmount: 0.0,
      progress: 0,
      likesCount: 0,
      forksCount: 0,
      viewsCount: 0,
      isPublic: json['is_public'] as bool?,
      userId: json['user_id'] as String?,
    );
  }
}

extension ProjectModelX on ProjectModel {
  domain.Project toEntity() {
    return domain.Project(
      id: id,
      authorId: authorId,
      title: title,
      description: description,
      status: _parseStatus(status),
      createdAt: DateTime.parse(createdAt),
      updatedAt: DateTime.parse(updatedAt),
      visibility: ProjectModelParsingX._parseVisibility(visibility),
      budget: budget,
      spentAmount: spentAmount,
      progress: progress,
      likesCount: likesCount,
      forksCount: forksCount,
      viewsCount: viewsCount,
      tags: tags,
      imageUrl: imageUrl,
      vehicleType: vehicleType,
      vehicleModel: vehicleModel,
      startDate: startDate != null ? DateTime.tryParse(startDate!) : null,
      endDate: endDate != null ? DateTime.tryParse(endDate!) : null,
    );
  }

  domain.ProjectStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return domain.ProjectStatus.planning;
      case 'in_progress':
        return domain.ProjectStatus.inProgress;
      case 'completed':
        return domain.ProjectStatus.completed;
      case 'paused':
        return domain.ProjectStatus.paused;
      default:
        return domain.ProjectStatus.planning;
    }
  }
}

extension ProjectX on domain.Project {
  ProjectModel toModel() {
    return ProjectModel(
      id: id,
      authorId: authorId,
      title: title,
      description: description,
      status: _statusToString(status),
      createdAt: createdAt.toIso8601String(),
      updatedAt: updatedAt.toIso8601String(),
      visibility: _visibilityToString(visibility),
      budget: budget,
      spentAmount: spentAmount,
      progress: progress,
      likesCount: likesCount,
      forksCount: forksCount,
      viewsCount: viewsCount,
      tags: tags,
      imageUrl: imageUrl,
      vehicleType: vehicleType,
      vehicleModel: vehicleModel,
      startDate: startDate?.toIso8601String(),
      endDate: endDate?.toIso8601String(),
    );
  }

  String _statusToString(domain.ProjectStatus status) {
    switch (status) {
      case domain.ProjectStatus.planning:
        return 'planning';
      case domain.ProjectStatus.inProgress:
        return 'in_progress';
      case domain.ProjectStatus.completed:
        return 'completed';
      case domain.ProjectStatus.paused:
        return 'paused';
    }
  }

  String _visibilityToString(ProjectVisibility visibility) {
    switch (visibility) {
      case ProjectVisibility.private:
        return 'private';
      case ProjectVisibility.unlisted:
        return 'unlisted';
      case ProjectVisibility.public:
        return 'public';
    }
  }
}

extension ProjectModelParsingX on ProjectModel {
  /// 解析项目可见性
  static ProjectVisibility _parseVisibility(String visibility) {
    switch (visibility) {
      case 'public':
        return ProjectVisibility.public;
      case 'unlisted':
        return ProjectVisibility.unlisted;
      case 'private':
      default:
        return ProjectVisibility.private;
    }
  }
}