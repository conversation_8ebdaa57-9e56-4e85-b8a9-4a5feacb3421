import 'package:flutter/material.dart';
import '../../../foundation/typography/typography.dart';

/// VanHub本地化文本组件
/// 提供统一的本地化文本显示，支持多语言和样式定制
class VanHubLocalizedText extends StatelessWidget {
  /// 文本键值
  final String textKey;
  
  /// 默认文本（当本地化失败时显示）
  final String? defaultText;
  
  /// 文本样式
  final TextStyle? style;
  
  /// 文本对齐方式
  final TextAlign? textAlign;
  
  /// 文本方向
  final TextDirection? textDirection;
  
  /// 软换行
  final bool? softWrap;
  
  /// 文本溢出处理
  final TextOverflow? overflow;
  
  /// 文本缩放因子
  final double? textScaleFactor;
  
  /// 最大行数
  final int? maxLines;
  
  /// 语义标签
  final String? semanticsLabel;
  
  /// 文本宽度基础
  final TextWidthBasis? textWidthBasis;
  
  /// 文本高度行为
  final TextHeightBehavior? textHeightBehavior;
  
  /// 插值参数
  final Map<String, dynamic>? args;

  const VanHubLocalizedText(
    this.textKey, {
    super.key,
    this.defaultText,
    this.style,
    this.textAlign,
    this.textDirection,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.args,
  });

  /// 创建标题文本
  const VanHubLocalizedText.headline(
    this.textKey, {
    super.key,
    this.defaultText,
    this.textAlign,
    this.textDirection,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.args,
  }) : style = null;

  /// 创建正文文本
  const VanHubLocalizedText.body(
    this.textKey, {
    super.key,
    this.defaultText,
    this.textAlign,
    this.textDirection,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.args,
  }) : style = null;

  /// 创建标签文本
  const VanHubLocalizedText.label(
    this.textKey, {
    super.key,
    this.defaultText,
    this.textAlign,
    this.textDirection,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.args,
  }) : style = null;

  @override
  Widget build(BuildContext context) {
    // 获取本地化文本
    final localizedText = _getLocalizedText(context);
    
    // 应用样式
    final effectiveStyle = _getEffectiveStyle(context);

    return Text(
      localizedText,
      style: effectiveStyle,
      textAlign: textAlign,
      textDirection: textDirection,
      softWrap: softWrap,
      overflow: overflow,
      textScaleFactor: textScaleFactor,
      maxLines: maxLines,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
    );
  }

  String _getLocalizedText(BuildContext context) {
    try {
      // 尝试获取本地化文本
      // 这里应该使用实际的本地化系统，如 flutter_localizations
      // 目前使用简单的映射作为示例
      final localizations = _getLocalizations(context);
      
      String text = localizations[textKey] ?? defaultText ?? textKey;
      
      // 处理插值参数
      if (args != null) {
        text = _interpolateText(text, args!);
      }
      
      return text;
    } catch (e) {
      // 本地化失败时返回默认文本或键值
      return defaultText ?? textKey;
    }
  }

  TextStyle? _getEffectiveStyle(BuildContext context) {
    if (style != null) return style;

    // 根据构造函数类型返回默认样式
    if (runtimeType.toString().contains('headline')) {
      return VanHubTypography.headlineMedium;
    } else if (runtimeType.toString().contains('body')) {
      return VanHubTypography.bodyMedium;
    } else if (runtimeType.toString().contains('label')) {
      return VanHubTypography.labelMedium;
    }

    return VanHubTypography.bodyMedium;
  }

  Map<String, String> _getLocalizations(BuildContext context) {
    // 这里应该从实际的本地化系统获取翻译
    // 目前使用硬编码的映射作为示例
    return {
      // 通用文本
      'app.title': 'VanHub改装宝',
      'app.loading': '加载中...',
      'app.error': '出现错误',
      'app.retry': '重试',
      'app.cancel': '取消',
      'app.confirm': '确认',
      'app.save': '保存',
      'app.delete': '删除',
      'app.edit': '编辑',
      'app.add': '添加',
      'app.search': '搜索',
      'app.filter': '筛选',
      'app.sort': '排序',
      'app.refresh': '刷新',
      
      // 项目相关
      'project.title': '项目',
      'project.create': '创建项目',
      'project.edit': '编辑项目',
      'project.delete': '删除项目',
      'project.name': '项目名称',
      'project.description': '项目描述',
      'project.status': '项目状态',
      'project.progress': '项目进度',
      
      // BOM相关
      'bom.title': 'BOM清单',
      'bom.add_item': '添加物料',
      'bom.edit_item': '编辑物料',
      'bom.delete_item': '删除物料',
      'bom.item_name': '物料名称',
      'bom.item_quantity': '数量',
      'bom.item_price': '价格',
      'bom.item_status': '状态',
      
      // 材料相关
      'material.title': '材料库',
      'material.add': '添加材料',
      'material.edit': '编辑材料',
      'material.delete': '删除材料',
      'material.name': '材料名称',
      'material.category': '材料分类',
      'material.brand': '品牌',
      'material.price': '价格',
      
      // 用户相关
      'user.login': '登录',
      'user.logout': '退出登录',
      'user.register': '注册',
      'user.profile': '个人资料',
      'user.settings': '设置',
      
      // 错误消息
      'error.network': '网络连接失败',
      'error.server': '服务器错误',
      'error.auth': '认证失败',
      'error.validation': '输入验证失败',
      'error.not_found': '内容未找到',
      'error.permission': '权限不足',
      
      // 成功消息
      'success.saved': '保存成功',
      'success.deleted': '删除成功',
      'success.created': '创建成功',
      'success.updated': '更新成功',
    };
  }

  String _interpolateText(String text, Map<String, dynamic> args) {
    String result = text;
    
    args.forEach((key, value) {
      // 支持 {key} 格式的插值
      result = result.replaceAll('{$key}', value.toString());
      // 支持 {{key}} 格式的插值
      result = result.replaceAll('{{$key}}', value.toString());
    });
    
    return result;
  }
}

/// VanHub本地化文本工厂类
class VanHubLocalizedTextFactory {
  VanHubLocalizedTextFactory._();

  /// 创建应用标题
  static Widget appTitle() {
    return const VanHubLocalizedText.headline('app.title');
  }

  /// 创建加载文本
  static Widget loading() {
    return const VanHubLocalizedText.body('app.loading');
  }

  /// 创建错误文本
  static Widget error([String? errorKey]) {
    return VanHubLocalizedText.body(errorKey ?? 'app.error');
  }

  /// 创建按钮文本
  static Widget button(String key) {
    return VanHubLocalizedText.label(key);
  }

  /// 创建带参数的文本
  static Widget withArgs(String key, Map<String, dynamic> args) {
    return VanHubLocalizedText(key, args: args);
  }
}
