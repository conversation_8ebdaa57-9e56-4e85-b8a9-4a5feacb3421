import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/material_favorite.dart';

part 'material_favorite_model.freezed.dart';
part 'material_favorite_model.g.dart';

/// 材料收藏数据模型
/// 用于与Supabase数据库交互的数据传输对象
@freezed
class MaterialFavoriteModel with _$MaterialFavoriteModel {
  const factory MaterialFavoriteModel({
    required String id,
    @JsonKey(name: 'user_id') required String userId,
    @Json<PERSON>ey(name: 'material_id') required String materialId,
    @<PERSON>son<PERSON>ey(name: 'created_at') required String createdAt,
    @<PERSON>son<PERSON><PERSON>(name: 'updated_at') required String updatedAt,
    List<String>? tags,
    String? notes,
    String? category,
    @Default(3) int priority,
    @Json<PERSON>ey(name: 'is_pinned') @Default(false) bool isPinned,
    Map<String, dynamic>? metadata,
  }) = _MaterialFavoriteModel;

  factory MaterialFavoriteModel.fromJson(Map<String, dynamic> json) => 
      _$MaterialFavoriteModelFromJson(json);
}

/// MaterialFavoriteModel扩展方法
extension MaterialFavoriteModelX on MaterialFavoriteModel {
  /// 转换为Domain实体
  MaterialFavorite toDomain() {
    return MaterialFavorite(
      id: id,
      userId: userId,
      materialId: materialId,
      createdAt: DateTime.parse(createdAt),
      updatedAt: DateTime.parse(updatedAt),
      tags: tags,
      notes: notes,
      category: category,
      priority: priority,
      isPinned: isPinned,
      metadata: metadata,
    );
  }

  /// 从Domain实体创建Model
  static MaterialFavoriteModel fromDomain(MaterialFavorite favorite) {
    return MaterialFavoriteModel(
      id: favorite.id,
      userId: favorite.userId,
      materialId: favorite.materialId,
      createdAt: favorite.createdAt.toIso8601String(),
      updatedAt: favorite.updatedAt.toIso8601String(),
      tags: favorite.tags,
      notes: favorite.notes,
      category: favorite.category,
      priority: favorite.priority,
      isPinned: favorite.isPinned,
      metadata: favorite.metadata,
    );
  }

  /// 转换为数据库插入格式
  Map<String, dynamic> toInsertMap() {
    return {
      'user_id': userId,
      'material_id': materialId,
      'tags': tags,
      'notes': notes,
      'category': category,
      'priority': priority,
      'is_pinned': isPinned,
      'metadata': metadata,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  /// 转换为数据库更新格式
  Map<String, dynamic> toUpdateMap() {
    return {
      'tags': tags,
      'notes': notes,
      'category': category,
      'priority': priority,
      'is_pinned': isPinned,
      'metadata': metadata,
      'updated_at': DateTime.now().toIso8601String(),
    };
  }
}
