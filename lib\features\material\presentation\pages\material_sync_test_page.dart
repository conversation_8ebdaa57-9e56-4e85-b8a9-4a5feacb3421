import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/material_sync_info_widget.dart';
import '../providers/material_provider.dart';
import '../providers/material_sync_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 材料同步功能测试页面
/// 用于展示和测试材料库与BOM的双向同步功能
class MaterialSyncTestPage extends ConsumerStatefulWidget {
  const MaterialSyncTestPage({super.key});

  @override
  ConsumerState<MaterialSyncTestPage> createState() => _MaterialSyncTestPageState();
}

class _MaterialSyncTestPageState extends ConsumerState<MaterialSyncTestPage> {
  String? _selectedMaterialId;

  @override
  Widget build(BuildContext context) {
    final materialsAsync = ref.watch(userMaterialsProvider());

    return Scaffold(
      appBar: AppBar(
        title: const Text('材料同步测试'),
        backgroundColor: VanHubColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(userMaterialsProvider);
              if (_selectedMaterialId != null) {
                ref.invalidate(linkedBomItemsProvider(_selectedMaterialId!));
              }
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 材料选择区域
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '选择材料',
                  style: VanHubTypography.titleMedium,
                ),
                const SizedBox(height: 8),
                materialsAsync.when(
                  data: (materials) => _buildMaterialSelector(materials),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Text('加载材料失败: $error'),
                ),
              ],
            ),
          ),
          
          // 同步信息展示区域
          Expanded(
            child: _selectedMaterialId == null
                ? _buildEmptyState()
                : _buildSyncInfoSection(),
          ),
        ],
      ),
      floatingActionButton: _selectedMaterialId != null
          ? FloatingActionButton.extended(
              onPressed: _showSyncActionsDialog,
              icon: const Icon(Icons.sync),
              label: const Text('同步操作'),
              backgroundColor: VanHubColors.primary,
            )
          : null,
    );
  }

  Widget _buildMaterialSelector(List<dynamic> materials) {
    if (materials.isEmpty) {
      return const Text('暂无材料，请先添加一些材料到材料库');
    }

    return DropdownButtonFormField<String>(
      value: _selectedMaterialId,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        hintText: '请选择一个材料',
      ),
      items: materials.map((material) {
        return DropdownMenuItem<String>(
          value: material.id,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                material.name,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                '${material.category} - ¥${material.price.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedMaterialId = value;
        });
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sync_disabled,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '请选择一个材料',
            style: VanHubTypography.headlineSmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '选择材料后可以查看其同步信息',
            style: VanHubTypography.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncInfoSection() {
    final materialAsync = ref.watch(materialDetailProvider(_selectedMaterialId!));

    return materialAsync.when(
      data: (material) => SingleChildScrollView(
        child: Column(
          children: [
            // 材料基本信息
            _buildMaterialInfo(material),
            
            // 同步信息组件
            MaterialSyncInfoWidget(
              material: material,
              showSyncActions: true,
            ),
            
            // 同步测试按钮
            _buildTestActions(),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('加载材料详情失败: $error'),
      ),
    );
  }

  Widget _buildMaterialInfo(dynamic material) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory,
                  color: VanHubColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '材料信息',
                  style: VanHubTypography.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('名称', material.name),
            _buildInfoRow('分类', material.category),
            _buildInfoRow('价格', '¥${material.price.toStringAsFixed(2)}'),
            if (material.brand != null) _buildInfoRow('品牌', material.brand!),
            if (material.model != null) _buildInfoRow('型号', material.model!),
            _buildInfoRow('使用次数', '${material.usageCount}'),
            if (material.lastUsedAt != null)
              _buildInfoRow('最后使用', _formatDate(material.lastUsedAt!)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: VanHubTypography.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: VanHubTypography.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestActions() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '同步测试操作',
              style: VanHubTypography.titleMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _testMaterialUpdate,
                  icon: const Icon(Icons.edit),
                  label: const Text('测试材料更新'),
                ),
                ElevatedButton.icon(
                  onPressed: _testPriceSync,
                  icon: const Icon(Icons.attach_money),
                  label: const Text('测试价格同步'),
                ),
                ElevatedButton.icon(
                  onPressed: _testUsageStats,
                  icon: const Icon(Icons.bar_chart),
                  label: const Text('测试使用统计'),
                ),
                ElevatedButton.icon(
                  onPressed: _testBatchSync,
                  icon: const Icon(Icons.sync_alt),
                  label: const Text('测试批量同步'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showSyncActionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('同步操作'),
        content: const Text('选择要执行的同步操作：'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _testMaterialUpdate();
            },
            child: const Text('同步到BOM'),
          ),
        ],
      ),
    );
  }

  void _testMaterialUpdate() async {
    if (_selectedMaterialId == null) return;

    final result = await ref.read(materialSyncControllerProvider.notifier)
        .syncMaterialUpdateToBom(
      materialId: _selectedMaterialId!,
      updatedFields: {
        'name': '测试更新材料',
        'price': 999.99,
      },
      syncStrategy: 'prompt',
    );

    if (mounted) {
      result.fold(
        (failure) => _showMessage('同步失败: ${failure.message}', Colors.red),
        (bomItems) => _showMessage('发现 ${bomItems.length} 个关联BOM项目', Colors.green),
      );
    }
  }

  void _testPriceSync() async {
    if (_selectedMaterialId == null) return;

    final result = await ref.read(materialSyncControllerProvider.notifier)
        .syncMaterialPriceUpdate(
      materialId: _selectedMaterialId!,
      oldPrice: 100.0,
      newPrice: 150.0,
      updateBomPrices: true,
    );

    if (mounted) {
      result.fold(
        (failure) => _showMessage('价格同步失败: ${failure.message}', Colors.red),
        (count) => _showMessage('成功同步 $count 个BOM项目的价格', Colors.green),
      );
    }
  }

  void _testUsageStats() async {
    if (_selectedMaterialId == null) return;

    final result = await ref.read(materialSyncControllerProvider.notifier)
        .getMaterialUsageHistory(materialId: _selectedMaterialId!);

    if (mounted) {
      result.fold(
        (failure) => _showMessage('获取使用历史失败: ${failure.message}', Colors.red),
        (history) => _showMessage('找到 ${history.length} 条使用历史', Colors.blue),
      );
    }
  }

  void _testBatchSync() async {
    final result = await ref.read(materialSyncControllerProvider.notifier)
        .batchSyncUsageStats();

    if (mounted) {
      result.fold(
        (failure) => _showMessage('批量同步失败: ${failure.message}', Colors.red),
        (count) => _showMessage('成功同步 $count 个材料的使用统计', Colors.green),
      );
    }
  }

  void _showMessage(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
