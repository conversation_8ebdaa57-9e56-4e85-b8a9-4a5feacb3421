# 🔧 Gradle下载超时问题解决方案

## 🚨 **当前问题**

### **错误信息**
```
Exception in thread "main" java.lang.RuntimeException: 
Timeout of 120000 reached waiting for exclusive access to file: 
C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12-all.zip
```

### **问题分析**
- **原因**：Gradle下载超时（120秒）
- **位置**：Gradle Wrapper下载gradle-8.12-all.zip
- **影响**：无法完成APK构建
- **类型**：网络连接问题

## 🚀 **解决方案**

### **方案1：清理Gradle缓存（推荐）**

#### **步骤1：清理Gradle缓存**
```powershell
# 删除Gradle缓存目录
Remove-Item -Path "$env:USERPROFILE\.gradle" -Recurse -Force -ErrorAction SilentlyContinue

# 或者只删除wrapper缓存
Remove-Item -Path "$env:USERPROFILE\.gradle\wrapper" -Recurse -Force -ErrorAction SilentlyContinue
```

#### **步骤2：清理项目缓存**
```powershell
# 进入项目目录
cd D:\AIAPP\VanHub

# 清理Flutter缓存
flutter clean

# 清理Android缓存
cd android
./gradlew clean
cd ..
```

#### **步骤3：重新构建**
```powershell
# 重新获取依赖
flutter pub get

# 重新构建APK
flutter build apk --release
```

### **方案2：手动下载Gradle**

#### **步骤1：手动下载Gradle**
```powershell
# 创建目录
$gradleDir = "$env:USERPROFILE\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb"
New-Item -ItemType Directory -Path $gradleDir -Force

# 下载Gradle（使用浏览器或下载工具）
# URL: https://services.gradle.org/distributions/gradle-8.12-all.zip
# 保存到: $gradleDir\gradle-8.12-all.zip
```

#### **步骤2：验证下载**
```powershell
# 检查文件是否存在
Test-Path "$env:USERPROFILE\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12-all.zip"
```

### **方案3：使用国内镜像**

#### **配置Gradle镜像**
编辑 `android/build.gradle`：
```gradle
allprojects {
    repositories {
        // 添加阿里云镜像
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        
        // 原有配置
        google()
        mavenCentral()
    }
}
```

#### **配置Gradle Wrapper镜像**
编辑 `android/gradle/wrapper/gradle-wrapper.properties`：
```properties
# 使用阿里云镜像
distributionUrl=https\://mirrors.aliyun.com/gradle/gradle-8.12-all.zip
```

### **方案4：增加超时时间**

#### **配置Gradle超时**
在 `android/gradle.properties` 中添加：
```properties
# 增加网络超时时间
org.gradle.daemon.idletimeout=300000
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
```

## 🔍 **立即执行方案**

### **快速修复步骤**

#### **步骤1：清理缓存**
```powershell
# 删除Gradle wrapper缓存
Remove-Item -Path "$env:USERPROFILE\.gradle\wrapper" -Recurse -Force -ErrorAction SilentlyContinue

# 清理项目
flutter clean
```

#### **步骤2：配置镜像**
```powershell
# 备份原文件
Copy-Item "android\gradle\wrapper\gradle-wrapper.properties" "android\gradle\wrapper\gradle-wrapper.properties.bak"
```

#### **步骤3：重新构建**
```powershell
# 重新获取依赖
flutter pub get

# 重新构建（使用debug版本测试）
flutter build apk --debug
```

## 📊 **问题诊断**

### **网络连接测试**
```powershell
# 测试Gradle官方下载
Test-NetConnection services.gradle.org -Port 443

# 测试阿里云镜像
Test-NetConnection mirrors.aliyun.com -Port 443
```

### **Gradle状态检查**
```powershell
# 检查Gradle进程
Get-Process | Where-Object {$_.ProcessName -like "*gradle*"}

# 检查Gradle缓存大小
Get-ChildItem -Path "$env:USERPROFILE\.gradle" -Recurse | Measure-Object -Property Length -Sum
```

## 🎯 **预防措施**

### **长期解决方案**
1. **使用国内镜像**：配置阿里云Gradle镜像
2. **增加超时时间**：设置更长的网络超时
3. **预下载依赖**：提前下载常用Gradle版本
4. **网络优化**：使用稳定的网络连接

### **监控建议**
1. **定期清理缓存**：避免缓存损坏
2. **检查网络状态**：确保网络连接稳定
3. **更新Gradle版本**：使用最新稳定版本

## 🚀 **立即行动**

### **现在执行**
```powershell
# 1. 清理Gradle缓存
Remove-Item -Path "$env:USERPROFILE\.gradle\wrapper" -Recurse -Force

# 2. 清理项目
flutter clean

# 3. 重新获取依赖
flutter pub get

# 4. 构建debug版本测试
flutter build apk --debug
```

### **如果仍然失败**
1. **检查网络连接**
2. **尝试使用VPN或更换网络**
3. **手动下载Gradle文件**
4. **配置国内镜像源**

---

**总结**：这是一个常见的网络下载超时问题，通过清理缓存和重新下载通常可以解决。如果问题持续，建议配置国内镜像源。
