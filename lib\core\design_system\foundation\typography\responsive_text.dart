/// VanHub响应式字体系统
/// 
/// 6层级响应式字体系统，支持多语言和动态缩放
/// 国际化字体适配（中英日韩）

import 'package:flutter/material.dart';
import 'package:auto_size_text/auto_size_text.dart';
import '../colors/semantic_colors.dart';

/// 字体权重系统
class VanHubFontWeights {
  VanHubFontWeights._();

  static const FontWeight thin = FontWeight.w100;
  static const FontWeight extraLight = FontWeight.w200;
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;
}

/// 字体家族系统
class VanHubFontFamilies {
  VanHubFontFamilies._();

  /// 主字体 - 用于界面文本
  static const String primary = 'Inter';
  
  /// 等宽字体 - 用于代码和数据
  static const String monospace = 'JetBrains Mono';
  
  /// 中文字体
  static const String chinese = 'PingFang SC';
  
  /// 日文字体
  static const String japanese = 'Hiragino Sans';
  
  /// 韩文字体
  static const String korean = 'Apple SD Gothic Neo';
  
  /// 获取本地化字体
  static String getLocalizedFont(Locale locale) {
    switch (locale.languageCode) {
      case 'zh':
        return chinese;
      case 'ja':
        return japanese;
      case 'ko':
        return korean;
      default:
        return primary;
    }
  }
}

/// 响应式字体系统
class VanHubResponsiveText {
  VanHubResponsiveText._();

  /// 获取响应式字体大小
  static double _getResponsiveSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
    double? large,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1440 && large != null) {
      return large;
    } else if (screenWidth >= 1024 && desktop != null) {
      return desktop;
    } else if (screenWidth >= 768 && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// 获取响应式行高
  static double _getResponsiveLineHeight(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1024 && desktop != null) {
      return desktop;
    } else if (screenWidth >= 768 && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  // ============ 6层级字体系统 ============

  /// Display 1 - 特大标题 (48px/40px/32px)
  static TextStyle display1(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 32,
        tablet: 40,
        desktop: 48,
        large: 56,
      ),
      fontWeight: VanHubFontWeights.extraBold,
      letterSpacing: -0.02,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.2,
        tablet: 1.15,
        desktop: 1.1,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Display 2 - 大标题 (40px/32px/28px)
  static TextStyle display2(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 28,
        tablet: 32,
        desktop: 40,
        large: 48,
      ),
      fontWeight: VanHubFontWeights.bold,
      letterSpacing: -0.015,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.25,
        tablet: 1.2,
        desktop: 1.15,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Headline 1 - 主标题 (32px/28px/24px)
  static TextStyle headline1(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 24,
        tablet: 28,
        desktop: 32,
        large: 36,
      ),
      fontWeight: VanHubFontWeights.bold,
      letterSpacing: -0.01,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.3,
        tablet: 1.25,
        desktop: 1.2,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Headline 2 - 副标题 (24px/22px/20px)
  static TextStyle headline2(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 20,
        tablet: 22,
        desktop: 24,
        large: 28,
      ),
      fontWeight: VanHubFontWeights.semiBold,
      letterSpacing: -0.005,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.35,
        tablet: 1.3,
        desktop: 1.25,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Body 1 - 正文大 (18px/17px/16px)
  static TextStyle body1(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 16,
        tablet: 17,
        desktop: 18,
        large: 20,
      ),
      fontWeight: VanHubFontWeights.regular,
      letterSpacing: 0,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.5,
        tablet: 1.45,
        desktop: 1.4,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Body 2 - 正文 (16px/15px/14px)
  static TextStyle body2(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 14,
        tablet: 15,
        desktop: 16,
        large: 18,
      ),
      fontWeight: VanHubFontWeights.regular,
      letterSpacing: 0,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.5,
        tablet: 1.45,
        desktop: 1.4,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context),
    );
  }

  /// Caption - 辅助文本 (14px/13px/12px)
  static TextStyle caption(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 12,
        tablet: 13,
        desktop: 14,
        large: 16,
      ),
      fontWeight: VanHubFontWeights.regular,
      letterSpacing: 0.01,
      height: _getResponsiveLineHeight(
        context,
        mobile: 1.4,
        tablet: 1.35,
        desktop: 1.3,
      ),
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context, secondary: true),
    );
  }

  // ============ 特殊用途字体 ============

  /// 按钮文本
  static TextStyle button(BuildContext context, {bool large = false}) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: large ? 16 : 14,
        tablet: large ? 17 : 15,
        desktop: large ? 18 : 16,
      ),
      fontWeight: VanHubFontWeights.semiBold,
      letterSpacing: 0.01,
      height: 1.2,
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
    );
  }

  /// 标签文本
  static TextStyle label(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(
        context,
        mobile: 12,
        tablet: 13,
        desktop: 14,
      ),
      fontWeight: VanHubFontWeights.medium,
      letterSpacing: 0.02,
      height: 1.2,
      fontFamily: VanHubFontFamilies.getLocalizedFont(
        Localizations.localeOf(context),
      ),
      color: VanHubSemanticColors.getTextColor(context, secondary: true),
    );
  }

  /// 等宽字体 - 用于代码和数据
  static TextStyle monospace(BuildContext context, {double? fontSize}) {
    return TextStyle(
      fontSize: fontSize ?? _getResponsiveSize(
        context,
        mobile: 14,
        tablet: 15,
        desktop: 16,
      ),
      fontWeight: VanHubFontWeights.regular,
      letterSpacing: 0,
      height: 1.4,
      fontFamily: VanHubFontFamilies.monospace,
      color: VanHubSemanticColors.getTextColor(context),
    );
  }
}

/// 自适应文本组件
class VanHubAutoSizeText extends StatelessWidget {
  final String text;
  final TextStyle Function(BuildContext) styleBuilder;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final double? minFontSize;
  final double? maxFontSize;

  const VanHubAutoSizeText(
    this.text, {
    Key? key,
    required this.styleBuilder,
    this.maxLines,
    this.textAlign,
    this.overflow,
    this.minFontSize,
    this.maxFontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final style = styleBuilder(context);
    
    return AutoSizeText(
      text,
      style: style,
      maxLines: maxLines,
      textAlign: textAlign,
      overflow: overflow ?? TextOverflow.ellipsis,
      minFontSize: minFontSize ?? (style.fontSize ?? 12) * 0.8,
      maxFontSize: maxFontSize ?? (style.fontSize ?? 16) * 1.2,
    );
  }
}
