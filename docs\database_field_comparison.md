# VanHub数据库字段对比分析

## 问题分析

根据错误信息"Bad state: Future already completed"和代码分析，发现数据库字段映射存在不匹配问题。

## 数据库表结构 vs 代码实体字段对比

### 1. BOM Items 表 (bom_items)

#### 数据库表字段 (docs/database_schema.sql)
```sql
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 实际Supabase表字段 (从代码推断)
```sql
-- 基于BomRemoteDataSourceImpl的INSERT语句推断
CREATE TABLE bom_items (
    id UUID PRIMARY KEY,
    project_id UUID,
    commit_id UUID,           -- 新增字段，用于RLS策略
    item_name VARCHAR(255),   -- 对应 materialName
    description TEXT,
    quantity INTEGER,
    price DECIMAL(10,2),      -- 对应 unitPrice
    category VARCHAR(100),
    notes TEXT,
    status VARCHAR(50),
    attributes JSONB,         -- 存储扩展字段
    purchase_date TIMESTAMP,
    use_date TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 代码实体字段 (BomItem)
```dart
@freezed
class BomItem with _$BomItem {
  const factory BomItem({
    required String id,
    required String projectId,
    required String userId,
    required String materialName,  // ❌ 映射到 item_name
    required String description,
    required BomItemStatus status,
    required int quantity,
    required double unitPrice,     // ❌ 映射到 price
    required DateTime createdAt,
    required DateTime updatedAt,
    String? materialId,
    String? category,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,
    DateTime? purchasedDate,       // ❌ 映射到 purchase_date
    DateTime? usedDate,            // ❌ 映射到 use_date
    // ... 其他字段
  }) = _BomItem;
}
```

### 2. Materials 表 (materials vs material_library)

#### 原始设计 (materials)
```sql
CREATE TABLE materials (
    id UUID PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    category_id UUID,
    unit VARCHAR(50),
    price DECIMAL(10,2),
    supplier VARCHAR(255),
    model VARCHAR(255),
    specifications TEXT,
    image_url TEXT,
    user_id UUID,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 实际使用 (material_library)
```sql
-- 基于代码推断的实际表结构
CREATE TABLE material_library (
    id UUID PRIMARY KEY,
    item_name VARCHAR(255),      -- ❌ 不是 name
    category VARCHAR(100),
    brand VARCHAR(100),
    model VARCHAR(100),
    specification TEXT,          -- ❌ 不是 specifications
    reference_price DECIMAL(10,2), -- ❌ 不是 price
    description TEXT,
    attributes JSONB,
    purchase_link TEXT,          -- ❌ 不是 supplier_url
    weight DECIMAL(8,2),
    user_id UUID,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 字段映射问题汇总

### 🚨 关键字段不匹配

1. **BOM Items 表**
   - `materialName` → `item_name` (不是 `name`)
   - `unitPrice` → `price` (不是 `unit_price`)
   - `purchasedDate` → `purchase_date`
   - `usedDate` → `use_date`
   - 缺少 `commit_id` 字段 (RLS策略要求)

2. **Material Library 表**
   - `name` → `item_name`
   - `price` → `reference_price`
   - `specifications` → `specification` (单数形式)
   - `supplierUrl` → `purchase_link`

3. **新增字段**
   - `commit_id` - BOM项必须关联到commit才能通过RLS策略
   - `attributes` - JSONB字段存储扩展属性
   - `weight` - 材料重量字段

## 修复方案

### 1. 立即修复 - 更新字段映射

需要修复以下文件中的字段映射：

1. `BomItemModel.fromJson()` - 修复字段名映射
2. `BomRemoteDataSourceImpl` - 修复INSERT/UPDATE语句
3. `MaterialModel.fromJson()` - 修复材料库字段映射

### 2. 数据库结构同步

选择以下方案之一：

**方案A: 更新代码适配现有数据库**
- 修改所有Model的fromJson/toJson方法
- 更新DataSource中的SQL语句
- 保持现有数据库结构不变

**方案B: 更新数据库适配代码结构**
- 执行数据库迁移脚本
- 重命名字段以匹配代码期望
- 可能影响现有数据

**推荐方案A** - 风险更小，不影响现有数据。

## 错误根因分析

"Bad state: Future already completed" 错误可能由以下原因引起：

1. **字段映射错误** - fromJson解析失败导致异常
2. **RLS策略限制** - 缺少commit_id导致权限验证失败
3. **异步操作重复** - 同一个Future被多次await
4. **数据类型不匹配** - 数据库返回的类型与代码期望不符

## 修复状态

### ✅ 已完成的修复

1. **字段映射修复**
   - ✅ BomItemModel.fromJson() - 修复了字段名映射
   - ✅ BomRemoteDataSourceImpl.createBomItem() - 修复了INSERT语句
   - ✅ 添加了详细的调试日志和错误处理
   - ✅ 修复了数据类型转换问题

2. **错误处理改进**
   - ✅ 添加了PostgrestException详细错误信息
   - ✅ 改进了异常捕获和错误消息
   - ✅ 添加了认证状态检查

3. **调试功能增强**
   - ✅ 添加了详细的控制台日志
   - ✅ 改进了错误追踪能力
   - ✅ 添加了数据验证步骤

### 🔄 待测试验证

1. **功能测试**
   - [ ] 手动创建BOM项功能
   - [ ] 从材料库添加到BOM功能
   - [ ] BOM项状态更新功能
   - [ ] BOM统计功能

2. **错误场景测试**
   - [ ] 网络错误处理
   - [ ] 权限错误处理
   - [ ] 数据验证错误处理

### 📋 后续优化

1. **性能优化**
   - [ ] 移除调试日志（生产环境）
   - [ ] 优化数据库查询
   - [ ] 改进状态管理

2. **用户体验**
   - [ ] 简化错误消息
   - [ ] 添加加载指示器
   - [ ] 改进表单验证

## 测试指南

参考 `test_bom_creation.md` 文件进行功能测试。