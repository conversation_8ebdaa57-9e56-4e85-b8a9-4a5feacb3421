/// VanHub Collaboration Component 2.0
/// 
/// 实时协作组件，支持多人协作和实时同步
/// 
/// 特性：
/// - 实时协作状态显示
/// - 多人光标跟踪
/// - 协作者头像展示
/// - 实时评论系统
/// - 冲突解决机制
/// - 权限管理

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import 'vanhub_card_v2.dart';

/// 协作者状态枚举
enum CollaboratorStatus {
  online,     // 在线
  away,       // 离开
  busy,       // 忙碌
  offline,    // 离线
}

/// 协作权限枚举
enum CollaborationPermission {
  owner,      // 所有者
  editor,     // 编辑者
  viewer,     // 查看者
  commenter,  // 评论者
}

/// 协作操作类型枚举
enum CollaborationAction {
  join,       // 加入
  leave,      // 离开
  edit,       // 编辑
  comment,    // 评论
  share,      // 分享
  invite,     // 邀请
}

/// 协作者数据模型
class Collaborator {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final CollaboratorStatus status;
  final CollaborationPermission permission;
  final DateTime lastActiveAt;
  final Color cursorColor;

  const Collaborator({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    required this.status,
    required this.permission,
    required this.lastActiveAt,
    required this.cursorColor,
  });

  /// 获取状态颜色
  Color get statusColor {
    switch (status) {
      case CollaboratorStatus.online:
        return VanHubSemanticColors.success;
      case CollaboratorStatus.away:
        return VanHubSemanticColors.warning;
      case CollaboratorStatus.busy:
        return VanHubSemanticColors.error;
      case CollaboratorStatus.offline:
        return VanHubSemanticColors.textSecondary;
    }
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case CollaboratorStatus.online:
        return '在线';
      case CollaboratorStatus.away:
        return '离开';
      case CollaboratorStatus.busy:
        return '忙碌';
      case CollaboratorStatus.offline:
        return '离线';
    }
  }

  /// 获取权限文本
  String get permissionText {
    switch (permission) {
      case CollaborationPermission.owner:
        return '所有者';
      case CollaborationPermission.editor:
        return '编辑者';
      case CollaborationPermission.viewer:
        return '查看者';
      case CollaborationPermission.commenter:
        return '评论者';
    }
  }

  /// 获取权限图标
  IconData get permissionIcon {
    switch (permission) {
      case CollaborationPermission.owner:
        return Icons.admin_panel_settings;
      case CollaborationPermission.editor:
        return Icons.edit;
      case CollaborationPermission.viewer:
        return Icons.visibility;
      case CollaborationPermission.commenter:
        return Icons.comment;
    }
  }
}

/// 协作活动数据模型
class CollaborationActivity {
  final String id;
  final String collaboratorId;
  final String collaboratorName;
  final CollaborationAction action;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const CollaborationActivity({
    required this.id,
    required this.collaboratorId,
    required this.collaboratorName,
    required this.action,
    required this.description,
    required this.timestamp,
    this.metadata,
  });

  /// 获取操作图标
  IconData get actionIcon {
    switch (action) {
      case CollaborationAction.join:
        return Icons.person_add;
      case CollaborationAction.leave:
        return Icons.person_remove;
      case CollaborationAction.edit:
        return Icons.edit;
      case CollaborationAction.comment:
        return Icons.comment;
      case CollaborationAction.share:
        return Icons.share;
      case CollaborationAction.invite:
        return Icons.mail;
    }
  }

  /// 获取操作颜色
  Color get actionColor {
    switch (action) {
      case CollaborationAction.join:
        return VanHubSemanticColors.success;
      case CollaborationAction.leave:
        return VanHubSemanticColors.warning;
      case CollaborationAction.edit:
        return VanHubBrandColors.primary;
      case CollaborationAction.comment:
        return VanHubSemanticColors.info;
      case CollaborationAction.share:
        return VanHubBrandColors.accent;
      case CollaborationAction.invite:
        return VanHubSemanticColors.success;
    }
  }
}

/// VanHub协作组件
class VanHubCollaboration extends StatefulWidget {
  final List<Collaborator> collaborators;
  final List<CollaborationActivity> activities;
  final String? currentUserId;
  final bool showOnlineIndicator;
  final bool showActivityFeed;
  final bool enableInvite;
  final bool enablePermissionManagement;
  final int maxDisplayedCollaborators;
  final Function(String)? onInvite;
  final Function(String, CollaborationPermission)? onPermissionChange;
  final Function(String)? onRemoveCollaborator;
  final VoidCallback? onShowAllCollaborators;
  
  // 动画配置
  final Duration animationDuration;
  final Duration pulseInterval;

  const VanHubCollaboration({
    Key? key,
    required this.collaborators,
    required this.activities,
    this.currentUserId,
    this.showOnlineIndicator = true,
    this.showActivityFeed = true,
    this.enableInvite = true,
    this.enablePermissionManagement = false,
    this.maxDisplayedCollaborators = 5,
    this.onInvite,
    this.onPermissionChange,
    this.onRemoveCollaborator,
    this.onShowAllCollaborators,
    this.animationDuration = VanHubAnimationDurations.fast,
    this.pulseInterval = const Duration(seconds: 2),
  }) : super(key: key);

  @override
  State<VanHubCollaboration> createState() => _VanHubCollaborationState();
}

class _VanHubCollaborationState extends State<VanHubCollaboration>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: widget.pulseInterval,
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCollaboratorsSection(),
        if (widget.showActivityFeed) ...[
          SizedBox(height: VanHubResponsiveSpacing.lg),
          _buildActivityFeed(),
        ],
      ],
    );
  }

  /// 构建协作者部分
  Widget _buildCollaboratorsSection() {
    final onlineCollaborators = widget.collaborators
        .where((c) => c.status == CollaboratorStatus.online)
        .toList();
    
    final displayedCollaborators = widget.collaborators
        .take(widget.maxDisplayedCollaborators)
        .toList();

    return VanHubCardV2.outlined(
      size: VanHubCardSize.sm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部
          Row(
            children: [
              Icon(
                Icons.people,
                color: VanHubBrandColors.primary,
                size: 20,
              ),
              SizedBox(width: VanHubResponsiveSpacing.sm),
              Text(
                '协作者',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: VanHubSemanticColors.getTextColor(context),
                ),
              ),
              const Spacer(),
              if (widget.showOnlineIndicator) ...[
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: VanHubSemanticColors.success,
                          shape: BoxShape.circle,
                        ),
                      ),
                    );
                  },
                ),
                SizedBox(width: VanHubResponsiveSpacing.xs),
                Text(
                  '${onlineCollaborators.length} 在线',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: VanHubSemanticColors.success,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (widget.enableInvite)
                IconButton(
                  icon: const Icon(Icons.person_add, size: 18),
                  onPressed: () => _showInviteDialog(),
                  color: VanHubBrandColors.primary,
                  tooltip: '邀请协作者',
                ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          // 协作者列表
          ...displayedCollaborators.asMap().entries.map((entry) {
            final index = entry.key;
            final collaborator = entry.value;
            return _buildCollaboratorItem(collaborator, index);
          }).toList(),
          
          // 显示更多按钮
          if (widget.collaborators.length > widget.maxDisplayedCollaborators) ...[
            SizedBox(height: VanHubResponsiveSpacing.sm),
            InkWell(
              onTap: widget.onShowAllCollaborators,
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: EdgeInsets.all(VanHubResponsiveSpacing.sm),
                child: Row(
                  children: [
                    Icon(
                      Icons.expand_more,
                      size: 16,
                      color: VanHubBrandColors.primary,
                    ),
                    SizedBox(width: VanHubResponsiveSpacing.xs),
                    Text(
                      '查看全部 ${widget.collaborators.length} 位协作者',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: VanHubBrandColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建协作者项
  Widget _buildCollaboratorItem(Collaborator collaborator, int index) {
    final isCurrentUser = collaborator.id == widget.currentUserId;
    
    return Container(
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Row(
        children: [
          // 头像
          Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: collaborator.cursorColor.withOpacity(0.2),
                backgroundImage: collaborator.avatarUrl != null
                    ? NetworkImage(collaborator.avatarUrl!)
                    : null,
                child: collaborator.avatarUrl == null
                    ? Text(
                        collaborator.name.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          color: collaborator.cursorColor,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              // 状态指示器
              if (widget.showOnlineIndicator)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: collaborator.statusColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: VanHubSemanticColors.getBackgroundColor(context),
                        width: 2,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          
          SizedBox(width: VanHubResponsiveSpacing.sm),
          
          // 信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      collaborator.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: VanHubSemanticColors.getTextColor(context),
                      ),
                    ),
                    if (isCurrentUser) ...[
                      SizedBox(width: VanHubResponsiveSpacing.xs),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: VanHubBrandColors.primaryContainer,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '我',
                          style: TextStyle(
                            fontSize: 10,
                            color: VanHubBrandColors.onPrimaryContainer,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Row(
                  children: [
                    Icon(
                      collaborator.permissionIcon,
                      size: 12,
                      color: VanHubSemanticColors.getTextColor(context, secondary: true),
                    ),
                    SizedBox(width: VanHubResponsiveSpacing.xs),
                    Text(
                      collaborator.permissionText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: VanHubSemanticColors.getTextColor(context, secondary: true),
                      ),
                    ),
                    SizedBox(width: VanHubResponsiveSpacing.sm),
                    Text(
                      collaborator.statusText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: collaborator.statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // 操作按钮
          if (widget.enablePermissionManagement && !isCurrentUser)
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                size: 16,
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              onSelected: (value) {
                if (value == 'remove') {
                  widget.onRemoveCollaborator?.call(collaborator.id);
                } else if (value.startsWith('permission_')) {
                  final permission = CollaborationPermission.values
                      .firstWhere((p) => p.name == value.split('_')[1]);
                  widget.onPermissionChange?.call(collaborator.id, permission);
                }
              },
              itemBuilder: (context) => [
                ...CollaborationPermission.values.map((permission) {
                  return PopupMenuItem(
                    value: 'permission_${permission.name}',
                    child: Row(
                      children: [
                        Icon(
                          permission == collaborator.permission
                              ? Icons.check
                              : Icons.circle_outlined,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(_getPermissionText(permission)),
                      ],
                    ),
                  );
                }).toList(),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      Icon(Icons.person_remove, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('移除', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
    ).animate()
      .fadeIn(
        duration: widget.animationDuration,
        delay: Duration(milliseconds: index * 100),
      )
      .slideX(
        begin: -0.2,
        end: 0,
        duration: widget.animationDuration,
        delay: Duration(milliseconds: index * 100),
      );
  }

  /// 构建活动动态
  Widget _buildActivityFeed() {
    if (widget.activities.isEmpty) {
      return Container();
    }

    return VanHubCardV2.outlined(
      size: VanHubCardSize.sm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: VanHubBrandColors.primary,
                size: 20,
              ),
              SizedBox(width: VanHubResponsiveSpacing.sm),
              Text(
                '最近活动',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: VanHubSemanticColors.getTextColor(context),
                ),
              ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          ...widget.activities.take(5).toList().asMap().entries.map((entry) {
            final index = entry.key;
            final activity = entry.value;
            return _buildActivityItem(activity, index);
          }).toList(),
        ],
      ),
    );
  }

  /// 构建活动项
  Widget _buildActivityItem(CollaborationActivity activity, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: activity.actionColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity.actionIcon,
              size: 14,
              color: activity.actionColor,
            ),
          ),
          
          SizedBox(width: VanHubResponsiveSpacing.sm),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: VanHubSemanticColors.getTextColor(context),
                    ),
                    children: [
                      TextSpan(
                        text: activity.collaboratorName,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(text: ' ${activity.description}'),
                    ],
                  ),
                ),
                Text(
                  _formatTimestamp(activity.timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: VanHubSemanticColors.getTextColor(context, secondary: true),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate()
      .fadeIn(
        duration: widget.animationDuration,
        delay: Duration(milliseconds: index * 50),
      )
      .slideX(
        begin: 0.2,
        end: 0,
        duration: widget.animationDuration,
        delay: Duration(milliseconds: index * 50),
      );
  }

  /// 显示邀请对话框
  void _showInviteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('邀请协作者'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: '邮箱地址',
                hintText: '输入要邀请的用户邮箱',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现邀请逻辑
              Navigator.of(context).pop();
              widget.onInvite?.call('<EMAIL>');
            },
            child: const Text('发送邀请'),
          ),
        ],
      ),
    );
  }

  /// 获取权限文本
  String _getPermissionText(CollaborationPermission permission) {
    switch (permission) {
      case CollaborationPermission.owner:
        return '所有者';
      case CollaborationPermission.editor:
        return '编辑者';
      case CollaborationPermission.viewer:
        return '查看者';
      case CollaborationPermission.commenter:
        return '评论者';
    }
  }

  /// 格式化时间戳
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }
}
