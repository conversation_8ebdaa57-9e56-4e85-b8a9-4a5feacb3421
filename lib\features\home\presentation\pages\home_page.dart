import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../../../core/design_system/components/vanhub_button_v2.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../../project/presentation/widgets/create_project_dialog_widget.dart';
import '../../../material/presentation/widgets/create_material_dialog_widget.dart';
import '../../../project/presentation/pages/project_management_page.dart';
import '../../../project/presentation/pages/project_management_page_v2.dart';
import '../../../material/presentation/pages/material_library_page.dart';
import '../../../material/presentation/pages/material_library_page_v2.dart';
import '../../../material/presentation/pages/material_library_page_unified.dart';
import '../../../material/presentation/pages/material_smart_search_page.dart';
import '../../../bom/presentation/pages/bom_management_page_v2.dart';
import '../../../search/presentation/pages/search_page_v2.dart';
import '../../../settings/presentation/pages/settings_page_v2.dart';
import '../widgets/home_dashboard_widget_v2.dart';
import '../widgets/enhanced_home_dashboard.dart';
import '../widgets/guest_welcome_widget.dart';

/// VanHub主页 - 房车改装项目管理平台
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  int _currentIndex = 0;
  String? _selectedProjectId; // 当前选中的项目ID

  @override
  Widget build(BuildContext context) {
    try {
      final currentUserId = ref.watch(currentUserIdProvider);
      final authState = ref.watch(authNotifierProvider);

      // 检查是否为游客模式或未登录状态
      return authState.when(
        data: (user) {
          if (user == null) {
            // 未登录状态
            return _buildGuestMode();
          } else if (user.isGuest) {
            // 游客模式
            return _buildGuestMode();
          } else {
            // 已登录用户
            return _buildAuthenticatedUserInterface(user);
          }
        },
        loading: () => const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载...'),
              ],
            ),
          ),
        ),
        error: (error, stack) {
          debugPrint('认证状态错误: $error');
          debugPrint('错误堆栈: $stack');
          return _buildGuestMode();
        },
      );
    } catch (e, stack) {
      debugPrint('HomePage构建错误: $e');
      debugPrint('错误堆栈: $stack');
      // 如果出现任何错误，回退到游客模式
      return _buildGuestMode();
    }
  }

  /// 构建已登录用户界面
  Widget _buildAuthenticatedUserInterface(user) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VanHub改装宝'),
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: VanHubBrandColors.onPrimary,
        actions: [
          // 搜索按钮
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              setState(() {
                _currentIndex = 4; // 切换到搜索页面
              });
            },
          ),
          // 设置按钮
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              setState(() {
                _currentIndex = 5; // 切换到设置页面
              });
            },
          ),
          // 显示当前用户邮箱
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Center(
              child: Text(
                user.email,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
          // 退出登录按钮
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout),
            tooltip: '退出登录',
          ),
        ],
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.1, 0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut,
              )),
              child: child,
            ),
          );
        },
        child: IndexedStack(
          key: ValueKey(_currentIndex),
          index: _currentIndex,
          children: [
            // 首页仪表盘 - 增强版数据可视化
            const EnhancedHomeDashboard(),

            // 项目管理 2.0
            const ProjectManagementPageV2(),

            // 材料库统一版本
            MaterialLibraryPageUnified(userId: user.id),

            // BOM管理 2.0 (需要选择项目)
            _selectedProjectId != null
                ? BomManagementPageV2(projectId: _selectedProjectId!)
                : _buildSelectProjectPrompt(),

            // 搜索页面 2.0
            const SearchPageV2(),

            // 设置页面 2.0
            const SettingsPageV2(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildGuestMode() {
    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.1, 0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut,
              )),
              child: child,
            ),
          );
        },
        child: IndexedStack(
          key: ValueKey(_currentIndex),
          index: _currentIndex,
          children: [
            // 0: 欢迎页面
            const GuestWelcomeWidget(),

            // 1: 发现项目 (游客可浏览公开项目)
            const ProjectManagementPage(),

            // 2: 材料库 (游客可浏览) - 使用统一版本
            const MaterialLibraryPageUnified(userId: null), // 传入null表示游客模式
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        shape: const CircularNotchedRectangle(),
        notchMargin: 8,
        child: Container(
          height: 70, // 增加高度以容纳内容
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                icon: Icons.home,
                label: '首页',
                index: 0,
                isActive: _currentIndex == 0,
              ),
              _buildNavItem(
                icon: Icons.explore,
                label: '发现项目',
                index: 1,
                isActive: _currentIndex == 1,
              ),
              _buildNavItem(
                icon: Icons.inventory,
                label: '材料库',
                index: 2,
                isActive: _currentIndex == 2,
              ),
              _buildNavItem(
                icon: Icons.login,
                label: '登录',
                index: -1, // 特殊索引表示登录
                isActive: false,
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 游客模式下点击FAB显示登录提示
          _showLoginPrompt();
        },
        backgroundColor: VanHubBrandColors.primary,
        child: const Icon(Icons.add, color: VanHubBrandColors.onPrimary),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomAppBar(
      shape: const CircularNotchedRectangle(),
      notchMargin: 8,
      child: Container(
        height: 70, // 增加高度以容纳内容
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              icon: Icons.dashboard,
              label: '首页',
              index: 0,
              isActive: _currentIndex == 0,
            ),
            _buildNavItem(
              icon: Icons.folder,
              label: '项目',
              index: 1,
              isActive: _currentIndex == 1,
            ),
            _buildNavItem(
              icon: Icons.inventory,
              label: '材料库',
              index: 2,
              isActive: _currentIndex == 2,
            ),
            _buildNavItem(
              icon: Icons.list_alt,
              label: 'BOM',
              index: 3,
              isActive: _currentIndex == 3,
            ),
            _buildNavItem(
              icon: Icons.search,
              label: '搜索',
              index: 4,
              isActive: _currentIndex == 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isActive,
  }) {
    return InkWell(
      onTap: () {
        debugPrint('导航点击: index=$index, label=$label');
        if (index == -1) {
          // 登录按钮
          _showLoginDialog();
        } else {
          setState(() {
            debugPrint('切换导航: $_currentIndex -> $index');
            _currentIndex = index;
          });
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6), // 减少padding
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? VanHubBrandColors.primary : VanHubSemanticColors.textSecondary,
              size: 22, // 稍微减小图标尺寸
            ),
            const SizedBox(height: 2), // 减少间距
            Text(
              label,
              style: TextStyle(
                color: isActive ? VanHubBrandColors.primary : VanHubSemanticColors.textSecondary,
                fontSize: 11, // 稍微减小字体
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    IconData icon;
    String tooltip;
    VoidCallback onPressed;

    switch (_currentIndex) {
      case 0: // 首页
        icon = Icons.add;
        tooltip = '快速添加';
        onPressed = _showQuickAddMenu;
        break;
      case 1: // 项目
        icon = Icons.add_road;
        tooltip = '创建项目';
        onPressed = _showCreateProjectDialog;
        break;
      case 2: // 材料库
        icon = Icons.add_shopping_cart;
        tooltip = '添加材料';
        onPressed = _showCreateMaterialDialog;
        break;
      case 3: // BOM
        icon = Icons.playlist_add;
        tooltip = '添加物料';
        onPressed = _showAddToBomMenu;
        break;
      default:
        icon = Icons.add;
        tooltip = '添加';
        onPressed = _showQuickAddMenu;
    }

    return FloatingActionButton(
      heroTag: "home_fab_${_currentIndex}",  // 修复Hero Widget冲突
      onPressed: onPressed,
      tooltip: tooltip,
      backgroundColor: VanHubBrandColors.primary,
      child: Icon(icon, color: VanHubBrandColors.onPrimary),
    );
  }

  Widget _buildSelectProjectPrompt() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 20),
          Text(
            '请先选择一个项目',
            style: TextStyle(
              fontSize: 20,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'BOM管理需要在特定项目下进行\n请先创建或选择一个改装项目',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24.0), // 使用固定间距
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _currentIndex = 1; // 跳转到项目页面
              });
            },
            icon: const Icon(Icons.folder),
            label: const Text('选择项目'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfilePage() {
    final authState = ref.watch(authNotifierProvider);
    
    return authState.when(
      data: (user) => Scaffold(
        appBar: AppBar(
          title: const Text('个人中心'),
          backgroundColor: VanHubBrandColors.primary,
          foregroundColor: VanHubBrandColors.onPrimary,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // 用户信息卡片
              VanHubCardV2.interactive(
                size: VanHubCardSize.lg,
                enable3DEffect: true,
                child: Row(
                  children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: VanHubBrandColors.primary,
                        child: Text(
                          user?.name?.substring(0, 1).toUpperCase() ?? 'U',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user?.name ?? '用户',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              user?.email ?? '',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // 功能菜单
              _buildMenuCard('我的项目', Icons.folder, () {}),
              _buildMenuCard('我的材料库', Icons.inventory, () {}),
              _buildMenuCard('智能搜索与推荐', Icons.search, () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MaterialSmartSearchPage(),
                  ),
                );
              }),
              _buildMenuCard('数据统计', Icons.analytics, () {}),
              _buildMenuCard('核心功能测试', Icons.bug_report, () {
                Navigator.pushNamed(context, '/test');
              }),
              _buildMenuCard('改装日志系统测试', Icons.note_alt, () {
                Navigator.pushNamed(context, '/test_log_system');
              }),
              _buildMenuCard('设置', Icons.settings, () {}),
              
              const SizedBox(height: 20),
              
              // 退出登录按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _logout,
                  icon: const Icon(Icons.logout, color: Colors.red),
                  label: const Text(
                    '退出登录',
                    style: TextStyle(color: Colors.red),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('加载用户信息失败: $error'),
      ),
    );
  }

  Widget _buildMenuCard(String title, IconData icon, VoidCallback onTap) {
    return VanHubCardV2.outlined(
      size: VanHubCardSize.sm,
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      onTap: onTap,
      child: ListTile(
        leading: Icon(icon, color: VanHubBrandColors.primary),
        title: Text(title),
        trailing: const Icon(Icons.chevron_right),
      ),
    );
  }

  void _showQuickAddMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '快速添加',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickAddItem(
                  icon: Icons.add_road,
                  label: '新项目',
                  onTap: _showCreateProjectDialog,
                ),
                _buildQuickAddItem(
                  icon: Icons.add_shopping_cart,
                  label: '新材料',
                  onTap: _showCreateMaterialDialog,
                ),
                _buildQuickAddItem(
                  icon: Icons.playlist_add,
                  label: '添加到BOM',
                  onTap: _showAddToBomMenu,
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAddItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: VanHubBrandColors.primaryContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: VanHubBrandColors.primary, size: 32),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLoginPrompt() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.info_outline, color: Colors.white),
            SizedBox(width: 8),
            Text('请先登录以使用完整功能'),
          ],
        ),
        backgroundColor: VanHubBrandColors.primary,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: '登录',
          textColor: Colors.white,
          onPressed: _showLoginDialog,
        ),
      ),
    );
  }

  void _showLoginDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LoginPage(),
      ),
    );
  }

  void _showCreateProjectDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // 防止意外关闭
      builder: (context) => const CreateProjectDialogWidget(),
    ).then((result) {
      // 如果创建成功，显示成功消息
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('项目 "${result.title}" 创建成功！'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                // 导航到项目管理页面
                // TODO: 导航到项目详情页面
              },
            ),
          ),
        );
      }
    });
  }

  void _showCreateMaterialDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // 防止意外关闭
      builder: (context) => const CreateMaterialDialogWidget(),
    ).then((result) {
      // 如果创建成功，显示成功消息
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('材料 "${result.name}" 添加成功！'),
            backgroundColor: Colors.teal,
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                // 导航到材料库页面
                // TODO: 导航到材料详情页面
              },
            ),
          ),
        );
      }
    });
  }

  void _showAddToBomMenu() {
    if (_selectedProjectId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先选择一个项目'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // TODO: 实现添加到BOM功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('添加到BOM功能开发中...'),
        backgroundColor: Colors.indigo,
      ),
    );
  }

  void _logout() async {
    final result = await ref.read(authNotifierProvider.notifier).logout();

    if (mounted) {
      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已退出登录'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('退出登录失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}