import 'package:fpdart/fpdart.dart';
import 'dart:io';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';

/// BOM导出服务接口 - Domain层
/// 遵循Clean Architecture原则，定义导出功能的抽象接口
abstract class BomExportService {
  /// 导出BOM到Excel格式
  Future<Either<Failure, File>> exportToExcel({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  });

  /// 导出BOM到PDF格式
  Future<Either<Failure, File>> exportToPdf({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  });

  /// 导出BOM到CSV格式
  Future<Either<Failure, File>> exportToCsv({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  });

  /// 获取支持的导出格式列表
  List<String> getSupportedFormats();

  /// 获取默认导出路径
  Future<String> getDefaultExportPath();
}