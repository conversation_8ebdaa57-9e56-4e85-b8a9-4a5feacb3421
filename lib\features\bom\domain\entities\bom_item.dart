import 'package:freezed_annotation/freezed_annotation.dart';

part 'bom_item.freezed.dart';
part 'bom_item.g.dart';

/// BOM项目状态枚举 - 符合.kiro/specs设计规范
enum BomItemStatus {
  pending,    // 待采购
  ordered,    // 已下单
  received,   // 已收货
  installed,  // 已安装
  cancelled,  // 已取消
}

extension BomItemStatusX on BomItemStatus {
  String get displayName {
    switch (this) {
      case BomItemStatus.pending:
        return '待采购';
      case BomItemStatus.ordered:
        return '已下单';
      case BomItemStatus.received:
        return '已收货';
      case BomItemStatus.installed:
        return '已安装';
      case BomItemStatus.cancelled:
        return '已取消';
    }
  }

  String get code {
    switch (this) {
      case BomItemStatus.pending:
        return 'pending';
      case BomItemStatus.ordered:
        return 'ordered';
      case BomItemStatus.received:
        return 'received';
      case BomItemStatus.installed:
        return 'installed';
      case BomItemStatus.cancelled:
        return 'cancelled';
    }
  }

  /// 获取状态颜色
  String get color {
    switch (this) {
      case BomItemStatus.pending:
        return '#FFA726'; // 橙色
      case BomItemStatus.ordered:
        return '#42A5F5'; // 蓝色
      case BomItemStatus.received:
        return '#66BB6A'; // 绿色
      case BomItemStatus.installed:
        return '#4CAF50'; // 深绿色
      case BomItemStatus.cancelled:
        return '#F44336'; // 红色
    }
  }

  /// 获取状态颜色（与color相同，用于兼容性）
  String get colorHex => color;
}

@freezed
class BomItem with _$BomItem {
  const factory BomItem({
    required String id,
    required String projectId,
    required String userId,
    required String materialName,  // 材料名称
    required String description,
    required BomItemStatus status,
    required int quantity,
    required double unitPrice,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? materialId,        // 关联的材料库ID
    String? category,
    String? brand,
    String? model,
    String? specifications,
    // 新增属性 - 用于BomTreeService
    String? materialCategory,  // 材料分类
    String? materialBrand,     // 材料品牌
    String? materialModel,     // 材料型号
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,     // 计划日期
    DateTime? purchasedDate,   // 采购日期
    DateTime? usedDate,        // 使用日期
    double? estimatedPrice,    // 预估价格
    double? actualPrice,       // 实际价格
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _BomItem;

  factory BomItem.fromJson(Map<String, dynamic> json) => _$BomItemFromJson(json);
}

/// BomItem业务方法扩展 - 符合.kiro/specs设计规范
extension BomItemX on BomItem {
  /// 计算总成本 - 符合规范要求
  double get totalCost => quantity * unitPrice;

  /// 计算总价（与totalCost同义，用于兼容性）
  double get totalPrice => totalCost;

  /// 是否已完成 - 符合规范要求
  bool get isCompleted => status == BomItemStatus.installed;

  /// 是否逾期 - 符合规范要求
  bool get isOverdue {
    if (plannedDate == null) return false;
    return DateTime.now().isAfter(plannedDate!) && !isCompleted;
  }

  /// 是否来自材料库
  bool get isFromMaterialLibrary => materialId != null;

  /// 获取状态颜色
  String get statusColor => status.color;

  /// 获取名称（与materialName同义，用于兼容性）
  String get name => materialName;

  /// 获取规格（与specifications同义，用于兼容性）
  String? get specification => specifications;

  /// 获取材料分类（优先使用materialCategory，回退到category）
  String? get effectiveMaterialCategory => materialCategory ?? category;

  /// 获取材料品牌（优先使用materialBrand，回退到brand）
  String? get effectiveMaterialBrand => materialBrand ?? brand;

  /// 获取材料型号（优先使用materialModel，回退到model）
  String? get effectiveMaterialModel => materialModel ?? model;

  /// 是否可以进入下一状态
  bool get canAdvanceStatus {
    switch (status) {
      case BomItemStatus.pending:
      case BomItemStatus.ordered:
      case BomItemStatus.received:
        return true;
      case BomItemStatus.installed:
      case BomItemStatus.cancelled:
        return false;
    }
  }

  /// 获取下一个状态
  BomItemStatus? get nextStatus {
    switch (status) {
      case BomItemStatus.pending:
        return BomItemStatus.ordered;
      case BomItemStatus.ordered:
        return BomItemStatus.received;
      case BomItemStatus.received:
        return BomItemStatus.installed;
      case BomItemStatus.installed:
      case BomItemStatus.cancelled:
        return null;
    }
  }

  /// 状态流转验证
  bool canTransitionTo(BomItemStatus newStatus) {
    switch (status) {
      case BomItemStatus.pending:
        return [BomItemStatus.ordered, BomItemStatus.cancelled].contains(newStatus);
      case BomItemStatus.ordered:
        return [BomItemStatus.received, BomItemStatus.cancelled].contains(newStatus);
      case BomItemStatus.received:
        return [BomItemStatus.installed, BomItemStatus.cancelled].contains(newStatus);
      case BomItemStatus.installed:
        return false; // 已安装状态不能转换
      case BomItemStatus.cancelled:
        return newStatus == BomItemStatus.pending; // 取消状态只能回到待采购
    }
  }
}