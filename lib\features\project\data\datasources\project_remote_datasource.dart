import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../../../../core/errors/exceptions.dart';
import '../models/project_model.dart';
import '../../domain/entities/create_project_request.dart';

abstract class ProjectRemoteDataSource {
  Future<ProjectModel> createProject(CreateProjectRequest request, String userId);
  Future<List<ProjectModel>> getUserProjects(String userId);
  Future<List<ProjectModel>> getPublicProjects({int limit = 20, int offset = 0});
  Future<ProjectModel> getProjectById(String projectId);
  Future<ProjectModel> updateProject(String projectId, Map<String, dynamic> updates);
  Future<void> deleteProject(String projectId);
  Future<void> likeProject(String projectId, String userId);
  Future<void> unlikeProject(String projectId, String userId);
  Future<ProjectModel> forkProject(String projectId, String userId);
  Future<bool> getLikeStatus(String projectId, String userId);
  Future<List<ProjectModel>> searchProjects(String query, {int limit = 20, int offset = 0});
}

class ProjectRemoteDataSourceImpl implements ProjectRemoteDataSource {
  final supabase.SupabaseClient supabaseClient;

  const ProjectRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<ProjectModel> createProject(CreateProjectRequest request, String userId) async {
    try {
      debugPrint('🔍 调试：准备插入数据库，用户ID = $userId');
      debugPrint('🔍 调试：插入数据 = ${request.toString()}');

      final insertData = {
        'user_id': userId,
        'title': request.title,
        'description': request.description,
        'vehicle_type': request.vehicleType,
        'vehicle_model': request.vehicleModel,
        'vehicle_brand': request.vehicleBrand,
        'total_budget': request.budget,
        'is_public': request.isPublic,
        'tags': request.tags,
      };

      debugPrint('🔍 调试：数据库插入数据 = $insertData');

      final response = await supabaseClient
          .from('projects')
          .insert(insertData)
          .select()
          .single();

      debugPrint('🔍 调试：数据库插入成功，返回数据 = $response');
      return ProjectModel.fromJson(response);
    } catch (e) {
      debugPrint('🔍 调试：数据库插入失败，错误 = $e');
      throw ServerException(message: '创建项目失败: $e');
    }
  }

  @override
  Future<List<ProjectModel>> getUserProjects(String userId) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => ProjectModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取用户项目失败: $e');
    }
  }

  @override
  Future<List<ProjectModel>> getPublicProjects({int limit = 20, int offset = 0}) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .select()
          .eq('is_public', true)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => ProjectModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取公开项目失败: $e');
    }
  }

  @override
  Future<ProjectModel> getProjectById(String projectId) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .select()
          .eq('id', projectId)
          .single();

      return ProjectModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '获取项目详情失败: $e');
    }
  }

  @override
  Future<ProjectModel> updateProject(String projectId, Map<String, dynamic> updates) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .update(updates)
          .eq('id', projectId)
          .select()
          .single();

      return ProjectModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新项目失败: $e');
    }
  }

  @override
  Future<void> deleteProject(String projectId) async {
    try {
      await supabaseClient
          .from('projects')
          .delete()
          .eq('id', projectId);
    } catch (e) {
      throw ServerException(message: '删除项目失败: $e');
    }
  }

  @override
  Future<void> likeProject(String projectId, String userId) async {
    try {
      // 插入点赞记录
      await supabaseClient
          .from('project_likes')
          .insert({
            'project_id': projectId,
            'user_id': userId,
          });

      // 更新项目点赞数
      await supabaseClient.rpc('increment_project_likes', params: {
        'project_id': projectId,
      });
    } catch (e) {
      throw ServerException(message: '点赞项目失败: $e');
    }
  }

  @override
  Future<void> unlikeProject(String projectId, String userId) async {
    try {
      // 删除点赞记录
      await supabaseClient
          .from('project_likes')
          .delete()
          .eq('project_id', projectId)
          .eq('user_id', userId);

      // 更新项目点赞数
      await supabaseClient.rpc('decrement_project_likes', params: {
        'project_id': projectId,
      });
    } catch (e) {
      throw ServerException(message: '取消点赞失败: $e');
    }
  }

  @override
  Future<ProjectModel> forkProject(String projectId, String userId) async {
    try {
      final response = await supabaseClient.rpc('fork_project', params: {
        'original_project_id': projectId,
        'new_user_id': userId,
      });

      return ProjectModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '复刻项目失败: $e');
    }
  }

  @override
  Future<bool> getLikeStatus(String projectId, String userId) async {
    try {
      final response = await supabaseClient
          .from('project_likes')
          .select('id')
          .eq('project_id', projectId)
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      throw ServerException(message: '获取点赞状态失败: $e');
    }
  }

  @override
  Future<List<ProjectModel>> searchProjects(String query, {int limit = 20, int offset = 0}) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .select()
          .eq('is_public', true)
          .or('title.ilike.%$query%,description.ilike.%$query%')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => ProjectModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '搜索项目失败: $e');
    }
  }
}