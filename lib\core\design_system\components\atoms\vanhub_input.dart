import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../foundation/colors/colors.dart';
import '../../foundation/typography/typography.dart';
import '../../foundation/spacing/spacing.dart';
import '../../foundation/animations/animations.dart';

/// VanHub输入框组件，支持验证、提示、错误状态
class VanHubInput extends StatefulWidget {
  const VanHubInput({
    super.key,
    this.controller,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.validator,
    this.inputFormatters,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.variant = VanHubInputVariant.outlined,
    this.size = VanHubInputSize.medium,
  });

  final TextEditingController? controller;
  final String? label;
  final String? placeholder;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final int maxLines;
  final int? minLines;
  final int? maxLength;
  final VanHubInputVariant variant;
  final VanHubInputSize size;

  @override
  State<VanHubInput> createState() => _VanHubInputState();
}

class _VanHubInputState extends State<VanHubInput> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  void _onChanged(String value) {
    if (widget.validator != null) {
      setState(() {
        _validationError = widget.validator!(value);
      });
    }
    widget.onChanged?.call(value);
  }

  @override
  Widget build(BuildContext context) {
    final hasError = widget.errorText != null || _validationError != null;
    final errorText = widget.errorText ?? _validationError;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: _getLabelStyle(hasError),
          ),
          SizedBox(height: VanHubSpacing.xs),
        ],
        AnimatedContainer(
          duration: VanHubAnimations.fast,
          curve: VanHubAnimations.easeInOut,
          child: _buildTextField(hasError),
        ),
        if (widget.helperText != null || errorText != null) ...[
          SizedBox(height: VanHubSpacing.xs),
          AnimatedSwitcher(
            duration: VanHubAnimations.fast,
            child: Text(
              errorText ?? widget.helperText!,
              key: ValueKey(errorText ?? widget.helperText),
              style: _getHelperTextStyle(hasError),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTextField(bool hasError) {
    switch (widget.variant) {
      case VanHubInputVariant.outlined:
        return _buildOutlinedTextField(hasError);
      case VanHubInputVariant.filled:
        return _buildFilledTextField(hasError);
      case VanHubInputVariant.underlined:
        return _buildUnderlinedTextField(hasError);
    }
  }

  Widget _buildOutlinedTextField(bool hasError) {
    return TextField(
      controller: widget.controller,
      focusNode: _focusNode,
      onChanged: _onChanged,
      onSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: widget.obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.placeholder,
        hintStyle: _getPlaceholderStyle(),
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: _getIconColor(hasError),
                size: _getIconSize(),
              )
            : null,
        suffixIcon: widget.suffixIcon,
        border: _getOutlinedBorder(false, hasError),
        enabledBorder: _getOutlinedBorder(false, hasError),
        focusedBorder: _getOutlinedBorder(true, hasError),
        errorBorder: _getOutlinedBorder(false, true),
        focusedErrorBorder: _getOutlinedBorder(true, true),
        disabledBorder: _getOutlinedBorder(false, hasError),
        filled: false,
        contentPadding: _getContentPadding(),
        counterText: '',
      ),
    );
  }

  Widget _buildFilledTextField(bool hasError) {
    return TextField(
      controller: widget.controller,
      focusNode: _focusNode,
      onChanged: _onChanged,
      onSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: widget.obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.placeholder,
        hintStyle: _getPlaceholderStyle(),
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: _getIconColor(hasError),
                size: _getIconSize(),
              )
            : null,
        suffixIcon: widget.suffixIcon,
        border: _getFilledBorder(),
        enabledBorder: _getFilledBorder(),
        focusedBorder: _getFilledBorder(),
        errorBorder: _getFilledBorder(),
        focusedErrorBorder: _getFilledBorder(),
        disabledBorder: _getFilledBorder(),
        filled: true,
        fillColor: _getFillColor(hasError),
        contentPadding: _getContentPadding(),
        counterText: '',
      ),
    );
  }

  Widget _buildUnderlinedTextField(bool hasError) {
    return TextField(
      controller: widget.controller,
      focusNode: _focusNode,
      onChanged: _onChanged,
      onSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: widget.obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.placeholder,
        hintStyle: _getPlaceholderStyle(),
        prefixIcon: widget.prefixIcon != null
            ? Icon(
                widget.prefixIcon,
                color: _getIconColor(hasError),
                size: _getIconSize(),
              )
            : null,
        suffixIcon: widget.suffixIcon,
        border: _getUnderlineBorder(false, hasError),
        enabledBorder: _getUnderlineBorder(false, hasError),
        focusedBorder: _getUnderlineBorder(true, hasError),
        errorBorder: _getUnderlineBorder(false, true),
        focusedErrorBorder: _getUnderlineBorder(true, true),
        disabledBorder: _getUnderlineBorder(false, hasError),
        filled: false,
        contentPadding: _getContentPadding(),
        counterText: '',
      ),
    );
  }

  OutlineInputBorder _getOutlinedBorder(bool isFocused, bool hasError) {
    Color borderColor;
    double borderWidth;

    if (hasError) {
      borderColor = VanHubColors.error;
      borderWidth = isFocused ? 2.0 : 1.5;
    } else if (isFocused) {
      borderColor = VanHubColors.primary;
      borderWidth = 2.0;
    } else if (!widget.enabled) {
      borderColor = VanHubColors.disabled;
      borderWidth = 1.0;
    } else {
      borderColor = VanHubColors.outline;
      borderWidth = 1.0;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(VanHubSpacing.xs),
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  InputBorder _getFilledBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(VanHubSpacing.xs),
      borderSide: BorderSide.none,
    );
  }

  UnderlineInputBorder _getUnderlineBorder(bool isFocused, bool hasError) {
    Color borderColor;
    double borderWidth;

    if (hasError) {
      borderColor = VanHubColors.error;
      borderWidth = isFocused ? 2.0 : 1.5;
    } else if (isFocused) {
      borderColor = VanHubColors.primary;
      borderWidth = 2.0;
    } else if (!widget.enabled) {
      borderColor = VanHubColors.disabled;
      borderWidth = 1.0;
    } else {
      borderColor = VanHubColors.outline;
      borderWidth = 1.0;
    }

    return UnderlineInputBorder(
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  Color _getFillColor(bool hasError) {
    if (!widget.enabled) {
      return VanHubColors.surfaceVariant.withOpacity(0.3);
    }
    if (hasError) {
      return VanHubColors.errorContainer.withOpacity(0.1);
    }
    if (_isFocused) {
      return VanHubColors.primaryContainer.withOpacity(0.1);
    }
    return VanHubColors.surfaceVariant.withOpacity(0.5);
  }

  Color _getIconColor(bool hasError) {
    if (!widget.enabled) {
      return VanHubColors.disabled;
    }
    if (hasError) {
      return VanHubColors.error;
    }
    if (_isFocused) {
      return VanHubColors.primary;
    }
    return VanHubColors.onSurfaceVariant;
  }

  double _getIconSize() {
    switch (widget.size) {
      case VanHubInputSize.small:
        return 18;
      case VanHubInputSize.medium:
        return 20;
      case VanHubInputSize.large:
        return 22;
    }
  }

  EdgeInsets _getContentPadding() {
    switch (widget.size) {
      case VanHubInputSize.small:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.sm,
          vertical: VanHubSpacing.xs,
        );
      case VanHubInputSize.medium:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.md,
          vertical: VanHubSpacing.sm,
        );
      case VanHubInputSize.large:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.lg,
          vertical: VanHubSpacing.md,
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case VanHubInputSize.small:
        return VanHubTypography.bodySmall;
      case VanHubInputSize.medium:
        return VanHubTypography.bodyMedium;
      case VanHubInputSize.large:
        return VanHubTypography.bodyLarge;
    }
  }

  TextStyle _getLabelStyle(bool hasError) {
    final baseStyle = VanHubTypography.labelMedium;
    return baseStyle.copyWith(
      color: hasError
          ? VanHubColors.error
          : widget.enabled
              ? VanHubColors.onSurface
              : VanHubColors.disabled,
    );
  }

  TextStyle _getPlaceholderStyle() {
    return _getTextStyle().copyWith(
      color: VanHubColors.onSurfaceVariant,
    );
  }

  TextStyle _getHelperTextStyle(bool hasError) {
    return VanHubTypography.bodySmall.copyWith(
      color: hasError ? VanHubColors.error : VanHubColors.onSurfaceVariant,
    );
  }
}

/// 输入框变体枚举
enum VanHubInputVariant {
  outlined,
  filled,
  underlined,
}

/// 输入框尺寸枚举
enum VanHubInputSize {
  small,
  medium,
  large,
}