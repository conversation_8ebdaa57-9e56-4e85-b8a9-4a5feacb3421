import 'package:flutter/material.dart';

import '../../domain/entities/enums.dart';

/// 里程碑设置组件
/// 集成在改装日志编辑器中，简化里程碑创建流程
class MilestoneSettingsWidget extends StatefulWidget {
  final bool isMilestone;
  final String? iconName;
  final String? colorHex;
  final MilestonePriority? priority;
  final ValueChanged<bool> onMilestoneChanged;
  final ValueChanged<String?> onIconChanged;
  final ValueChanged<String?> onColorChanged;
  final ValueChanged<MilestonePriority?> onPriorityChanged;

  const MilestoneSettingsWidget({
    super.key,
    required this.isMilestone,
    this.iconName,
    this.colorHex,
    this.priority,
    required this.onMilestoneChanged,
    required this.onIconChanged,
    required this.onColorChanged,
    required this.onPriorityChanged,
  });

  @override
  State<MilestoneSettingsWidget> createState() => _MilestoneSettingsWidgetState();
}

class _MilestoneSettingsWidgetState extends State<MilestoneSettingsWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  // 预设的里程碑图标
  final List<Map<String, dynamic>> _milestoneIcons = [
    {'name': 'flag', 'icon': Icons.flag, 'label': '里程碑'},
    {'name': 'rocket_launch', 'icon': Icons.rocket_launch, 'label': '开始'},
    {'name': 'electrical_services', 'icon': Icons.electrical_services, 'label': '电气'},
    {'name': 'water_drop', 'icon': Icons.water_drop, 'label': '水路'},
    {'name': 'local_fire_department', 'icon': Icons.local_fire_department, 'label': '燃气'},
    {'name': 'build', 'icon': Icons.build, 'label': '施工'},
    {'name': 'check_circle', 'icon': Icons.check_circle, 'label': '完成'},
    {'name': 'star', 'icon': Icons.star, 'label': '重要'},
  ];

  // 预设的里程碑颜色
  final List<Map<String, dynamic>> _milestoneColors = [
    {'name': '蓝色', 'hex': '#2196F3', 'color': Colors.blue},
    {'name': '绿色', 'hex': '#4CAF50', 'color': Colors.green},
    {'name': '橙色', 'hex': '#FF9800', 'color': Colors.orange},
    {'name': '红色', 'hex': '#F44336', 'color': Colors.red},
    {'name': '紫色', 'hex': '#9C27B0', 'color': Colors.purple},
    {'name': '青色', 'hex': '#009688', 'color': Colors.teal},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.isMilestone) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(MilestoneSettingsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isMilestone != oldWidget.isMilestone) {
      if (widget.isMilestone) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMilestoneToggle(),
        AnimatedBuilder(
          animation: _expandAnimation,
          builder: (context, child) {
            return SizeTransition(
              sizeFactor: _expandAnimation,
              child: child,
            );
          },
          child: widget.isMilestone ? _buildMilestoneSettings() : null,
        ),
      ],
    );
  }

  /// 构建里程碑开关
  Widget _buildMilestoneToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isMilestone ? Colors.orange.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.isMilestone ? Colors.orange.shade200 : Colors.grey.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.flag,
            color: widget.isMilestone ? Colors.orange : Colors.grey,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '标记为里程碑',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: widget.isMilestone ? Colors.orange.shade700 : Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.isMilestone ? '这个日志将作为项目的重要里程碑' : '将此日志标记为项目的重要节点',
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.isMilestone ? Colors.orange.shade600 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: widget.isMilestone,
            onChanged: (value) {
              widget.onMilestoneChanged(value);
              if (value) {
                // 设置默认值
                if (widget.iconName == null) {
                  widget.onIconChanged('flag');
                }
                if (widget.colorHex == null) {
                  widget.onColorChanged('#FF9800');
                }
                if (widget.priority == null) {
                  widget.onPriorityChanged(MilestonePriority.medium);
                }
              }
            },
            activeColor: Colors.orange,
          ),
        ],
      ),
    );
  }

  /// 构建里程碑设置
  Widget _buildMilestoneSettings() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '里程碑设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange.shade700,
            ),
          ),
          const SizedBox(height: 16),
          
          // 图标选择
          _buildIconSelection(),
          
          const SizedBox(height: 16),
          
          // 颜色选择
          _buildColorSelection(),
          
          const SizedBox(height: 16),
          
          // 优先级选择
          _buildPrioritySelection(),
        ],
      ),
    );
  }

  /// 构建图标选择
  Widget _buildIconSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择图标',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _milestoneIcons.map((iconData) {
            final isSelected = widget.iconName == iconData['name'];
            return InkWell(
              onTap: () => widget.onIconChanged(iconData['name']),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.orange : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.orange : Colors.grey.shade300,
                    width: 2,
                  ),
                ),
                child: Icon(
                  iconData['icon'],
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                  size: 24,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建颜色选择
  Widget _buildColorSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择颜色',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _milestoneColors.map((colorData) {
            final isSelected = widget.colorHex == colorData['hex'];
            return InkWell(
              onTap: () => widget.onColorChanged(colorData['hex']),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: colorData['color'],
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建优先级选择
  Widget _buildPrioritySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '优先级',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<MilestonePriority>(
          value: widget.priority,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: MilestonePriority.values.map((priority) {
            return DropdownMenuItem(
              value: priority,
              child: Row(
                children: [
                  Icon(
                    _getPriorityIcon(priority),
                    color: _getPriorityColor(priority),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(_getPriorityText(priority)),
                ],
              ),
            );
          }).toList(),
          onChanged: widget.onPriorityChanged,
        ),
      ],
    );
  }

  // 辅助方法
  IconData _getPriorityIcon(MilestonePriority priority) {
    switch (priority) {
      case MilestonePriority.low:
        return Icons.keyboard_arrow_down;
      case MilestonePriority.medium:
        return Icons.remove;
      case MilestonePriority.high:
        return Icons.keyboard_arrow_up;
      case MilestonePriority.critical:
        return Icons.priority_high;
    }
  }

  Color _getPriorityColor(MilestonePriority priority) {
    switch (priority) {
      case MilestonePriority.low:
        return Colors.green;
      case MilestonePriority.medium:
        return Colors.orange;
      case MilestonePriority.high:
        return Colors.red;
      case MilestonePriority.critical:
        return Colors.purple;
    }
  }

  String _getPriorityText(MilestonePriority priority) {
    switch (priority) {
      case MilestonePriority.low:
        return '低优先级';
      case MilestonePriority.medium:
        return '中等优先级';
      case MilestonePriority.high:
        return '高优先级';
      case MilestonePriority.critical:
        return '紧急';
    }
  }
}
