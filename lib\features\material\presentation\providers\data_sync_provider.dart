import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/services/data_sync_service.dart';
import '../../../../core/di/injection_container.dart';

part 'data_sync_provider.g.dart';

/// 价格同步通知提供者
@riverpod
Future<List<PriceSyncNotification>> priceSyncNotifications(
  PriceSyncNotificationsRef ref,
) async {
  final userId = ref.read(currentUserIdProvider);
  if (userId == null) return [];
  
  final result = await ref.read(dataSyncServiceProvider).getPriceSyncNotifications(userId);
  
  return result.fold(
    (failure) => [],
    (notifications) => notifications,
  );
}

/// 同步历史提供者
@riverpod
Future<List<SyncOperation>> syncHistory(
  SyncHistoryRef ref, {
  String? materialId,
  String? bomItemId,
  SyncOperationType? type,
  int limit = 50,
}) async {
  final result = await ref.read(dataSyncServiceProvider).getSyncHistory(
    materialId: materialId,
    bomItemId: bomItemId,
    type: type,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (history) => history,
  );
}

/// 数据同步状态管理
@riverpod
class DataSyncState extends _$DataSyncState {
  @override
  Future<void> build() async {
    // 初始状态
  }

  /// 将材料同步到BOM项目
  Future<Either<Failure, void>> syncMaterialToBom(
    String materialId,
    String bomItemId,
  ) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).syncMaterialToBom(
      materialId,
      bomItemId,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
      },
    );
    
    return result;
  }

  /// 将BOM项目保存到材料库
  Future<Either<Failure, String>> syncBomToMaterial(String bomItemId) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).syncBomToMaterial(bomItemId);
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
      },
    );
    
    return result;
  }

  /// 同步材料价格更新
  Future<Either<Failure, List<PriceSyncNotification>>> syncPriceUpdates(
    String materialId,
  ) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).syncPriceUpdates(materialId);
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
      },
    );
    
    return result;
  }

  /// 确认价格同步
  Future<Either<Failure, void>> confirmPriceSync(
    String materialId,
    List<String> bomItemIds,
    bool syncPrice,
  ) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).confirmPriceSync(
      materialId,
      bomItemIds,
      syncPrice,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
        // 刷新价格同步通知
        ref.invalidate(priceSyncNotificationsProvider);
      },
    );
    
    return result;
  }

  /// 更新材料使用统计
  Future<Either<Failure, void>> updateMaterialUsageStats(
    String materialId,
    int usageIncrement,
  ) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).updateMaterialUsageStats(
      materialId,
      usageIncrement,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
      },
    );
    
    return result;
  }

  /// 重试同步操作
  Future<Either<Failure, void>> retrySyncOperation(String operationId) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).retrySyncOperation(operationId);
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
        // 刷新同步历史
        ref.invalidate(syncHistoryProvider);
      },
    );
    
    return result;
  }

  /// 取消同步操作
  Future<Either<Failure, void>> cancelSyncOperation(String operationId) async {
    state = const AsyncLoading();
    
    final result = await ref.read(dataSyncServiceProvider).cancelSyncOperation(operationId);
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (_) {
        state = const AsyncData(null);
        // 刷新同步历史
        ref.invalidate(syncHistoryProvider);
      },
    );
    
    return result;
  }
}