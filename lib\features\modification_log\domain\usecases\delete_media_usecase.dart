import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/media_repository.dart';

/// 删除媒体用例
class DeleteMediaUseCase implements UseCase<void, DeleteMediaParams> {
  final MediaRepository repository;

  DeleteMediaUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteMediaParams params) async {
    return await repository.deleteMedia(params.mediaId);
  }
}

/// 删除媒体参数
class DeleteMediaParams {
  final String mediaId;

  DeleteMediaParams({required this.mediaId});
}