# Design Document

## Overview

物料树形结构功能将现有的平面物料列表转换为层级化的树形视图，基于物料的category字段构建分类层级。该功能将与现有的Clean Architecture保持一致，通过新的Domain实体、UI组件和状态管理来实现树形展示和交互功能。

## Architecture

### 核心设计原则
- 保持Clean Architecture三层分离
- 复用现有的Material实体和Repository
- 通过新的TreeNode实体包装Material数据
- 使用Riverpod管理树形结构状态
- 支持与现有列表视图的无缝切换

### 数据流架构
```
UI Layer (TreeView) 
    ↓ 
Presentation Layer (TreeProvider) 
    ↓ 
Domain Layer (TreeService + Material UseCase) 
    ↓ 
Data Layer (MaterialRepository - 复用现有)
```

## Components and Interfaces

### 1. Domain Layer 新增组件

#### TreeNode Entity
```dart
@freezed
class TreeNode with _$TreeNode {
  const factory TreeNode({
    required String id,
    required String label,
    required TreeNodeType type,
    @Default([]) List<TreeNode> children,
    @Default(false) bool isExpanded,
    @Default(0) int materialCount,
    domain.Material? material, // 叶子节点包含Material数据
    Map<String, dynamic>? metadata,
  }) = _TreeNode;
}

enum TreeNodeType {
  category,    // 分类节点
  subcategory, // 子分类节点  
  material,    // 物料节点
}
```

#### MaterialTreeService
```dart
abstract class MaterialTreeService {
  List<TreeNode> buildTreeFromMaterials(List<domain.Material> materials);
  List<TreeNode> searchInTree(List<TreeNode> tree, String query);
  TreeNode toggleNodeExpansion(TreeNode node, String nodeId);
  List<TreeNode> expandPathToMaterial(List<TreeNode> tree, String materialId);
  Map<String, int> calculateCategoryStatistics(List<domain.Material> materials);
}
```

### 2. Presentation Layer 新增组件

#### MaterialTreeProvider
```dart
@riverpod
class MaterialTreeNotifier extends _$MaterialTreeNotifier {
  @override
  Future<List<TreeNode>> build() async {
    final materials = await ref.watch(materialsNotifierProvider.future);
    return ref.read(materialTreeServiceProvider).buildTreeFromMaterials(materials);
  }

  void toggleNode(String nodeId) { /* 切换节点展开状态 */ }
  void searchTree(String query) { /* 搜索树节点 */ }
  void expandToMaterial(String materialId) { /* 展开到指定物料 */ }
}
```

#### MaterialTreeWidget
```dart
class MaterialTreeWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final treeState = ref.watch(materialTreeNotifierProvider);
    
    return treeState.when(
      data: (tree) => TreeView(nodes: tree),
      loading: () => TreeSkeletonWidget(),
      error: (error, stack) => TreeErrorWidget(error: error),
    );
  }
}
```

#### TreeNodeWidget
```dart
class TreeNodeWidget extends ConsumerWidget {
  final TreeNode node;
  final int depth;
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        _buildNodeHeader(),
        if (node.isExpanded) 
          ...node.children.map((child) => 
            TreeNodeWidget(node: child, depth: depth + 1)
          ),
      ],
    );
  }
  
  Widget _buildNodeHeader() {
    switch (node.type) {
      case TreeNodeType.category:
        return CategoryNodeHeader(node: node);
      case TreeNodeType.material:
        return MaterialNodeHeader(node: node);
      default:
        return DefaultNodeHeader(node: node);
    }
  }
}
```

### 3. UI Components

#### TreeViewControls
```dart
class TreeViewControls extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        // 搜索框
        Expanded(child: TreeSearchField()),
        // 展开级别控制
        ExpandLevelDropdown(),
        // 排序方式
        SortModeDropdown(),
        // 视图切换按钮
        ViewModeToggleButton(),
      ],
    );
  }
}
```

#### MaterialContextMenu
```dart
class MaterialContextMenu extends StatelessWidget {
  final domain.Material material;
  
  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      itemBuilder: (context) => [
        PopupMenuItem(value: 'edit', child: Text('编辑物料')),
        PopupMenuItem(value: 'delete', child: Text('删除物料')),
        PopupMenuItem(value: 'addToBom', child: Text('添加到BOM')),
        PopupMenuItem(value: 'viewDetails', child: Text('查看详情')),
      ],
      onSelected: (value) => _handleMenuAction(context, value),
    );
  }
}
```

## Data Models

### TreeViewSettings
```dart
@freezed
class TreeViewSettings with _$TreeViewSettings {
  const factory TreeViewSettings({
    @Default(2) int defaultExpandLevel,
    @Default(TreeSortMode.byName) TreeSortMode sortMode,
    @Default(true) bool showStatistics,
    @Default(false) bool compactMode,
    @Default({}) Map<String, bool> expandedNodes,
  }) = _TreeViewSettings;
}

enum TreeSortMode {
  byName,
  byUsageCount,
  byCreatedDate,
  byPrice,
}
```

### MaterialTreeState
```dart
@freezed
class MaterialTreeState with _$MaterialTreeState {
  const factory MaterialTreeState({
    @Default([]) List<TreeNode> tree,
    @Default('') String searchQuery,
    @Default([]) List<String> searchResults,
    TreeViewSettings? settings,
    @Default({}) Map<String, int> categoryStats,
  }) = _MaterialTreeState;
}
```

## Error Handling

### TreeFailure Types
```dart
abstract class TreeFailure extends Failure {
  const TreeFailure({required String message}) : super(message: message);
}

class TreeBuildFailure extends TreeFailure {
  const TreeBuildFailure({required String message}) : super(message: message);
}

class TreeSearchFailure extends TreeFailure {
  const TreeSearchFailure({required String message}) : super(message: message);
}

class TreeNodeNotFoundFailure extends TreeFailure {
  const TreeNodeNotFoundFailure({required String message}) : super(message: message);
}
```

### Error Recovery Strategies
- 树构建失败时回退到列表视图
- 搜索失败时显示原始树结构
- 节点操作失败时恢复之前状态
- 提供重试机制和错误提示

## Testing Strategy

### Unit Tests
```dart
// Domain Layer Tests
group('MaterialTreeService', () {
  test('should build correct tree structure from materials', () {});
  test('should handle empty materials list', () {});
  test('should search materials correctly', () {});
  test('should toggle node expansion', () {});
});

// Presentation Layer Tests  
group('MaterialTreeNotifier', () {
  test('should load tree data correctly', () {});
  test('should handle search queries', () {});
  test('should manage expansion state', () {});
});
```

### Widget Tests
```dart
group('MaterialTreeWidget', () {
  testWidgets('should display tree structure', (tester) async {});
  testWidgets('should handle node expansion', (tester) async {});
  testWidgets('should show context menu on right click', (tester) async {});
});

group('TreeNodeWidget', () {
  testWidgets('should render category nodes correctly', (tester) async {});
  testWidgets('should render material nodes correctly', (tester) async {});
  testWidgets('should handle tap gestures', (tester) async {});
});
```

### Integration Tests
```dart
group('Material Tree Integration', () {
  testWidgets('should integrate with existing material management', (tester) async {});
  testWidgets('should sync with BOM functionality', (tester) async {});
  testWidgets('should persist user preferences', (tester) async {});
});
```

## Performance Considerations

### 优化策略
1. **虚拟化渲染**: 对于大型树结构使用虚拟滚动
2. **懒加载**: 按需加载子节点数据
3. **状态缓存**: 缓存展开状态和搜索结果
4. **防抖搜索**: 搜索输入使用防抖机制
5. **内存管理**: 及时释放不可见节点的资源

### 性能监控
```dart
class TreePerformanceMonitor {
  static void trackTreeBuildTime(Duration duration) {}
  static void trackSearchTime(Duration duration) {}
  static void trackRenderTime(Duration duration) {}
}
```

## Integration Points

### 与现有功能的集成

#### 1. Material Management Integration
- 复用现有的MaterialRepository和UseCase
- 树形视图的CRUD操作通过现有的MaterialController
- 保持数据一致性和状态同步

#### 2. BOM Integration  
- 从树形视图拖拽物料到BOM
- 支持批量添加分类下的所有物料
- BOM中的物料在树形视图中高亮显示

#### 3. Search Integration
- 集成现有的MaterialSearchService
- 搜索结果在树形结构中定位和高亮
- 支持高级搜索条件

#### 4. Settings Integration
- 用户偏好设置持久化
- 与应用主题系统集成
- 响应式设计适配

## Migration Strategy

### 渐进式实现
1. **Phase 1**: 实现基础树形结构和展示
2. **Phase 2**: 添加搜索和过滤功能  
3. **Phase 3**: 实现拖拽和上下文菜单
4. **Phase 4**: 添加统计信息和用户偏好
5. **Phase 5**: 性能优化和高级功能

### 向后兼容
- 保持现有列表视图功能完整
- 提供视图模式切换
- 用户可以选择默认视图模式
- 现有API和数据结构不变

## Security Considerations

### 权限控制
- 树形视图遵循现有的用户权限系统
- 只显示用户有权访问的物料
- 操作权限检查与现有系统一致

### 数据保护
- 搜索查询不包含敏感信息
- 用户偏好设置本地存储加密
- 防止通过树结构推断其他用户数据