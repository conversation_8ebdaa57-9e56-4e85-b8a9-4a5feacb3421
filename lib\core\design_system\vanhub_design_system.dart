/// VanHub Design System 2.0
///
/// 国际化高端UI/UX设计系统
/// 专为房车改装项目管理平台设计
///
/// 设计原则：
/// 1. 专业而亲和 - 工业美学与人性化设计的完美结合
/// 2. 简约而丰富 - 极简界面承载复杂功能
/// 3. 智能而可控 - AI辅助但用户主导
/// 4. 全球而本土 - 国际化设计适配本地需求

library vanhub_design_system;

import 'package:flutter/material.dart';

// 导出分层组件
export 'components/atoms/atoms.dart';
export 'components/molecules/molecules.dart';
export 'components/organisms/organisms.dart';

// 导出基础系统
export 'foundation/colors/colors.dart';
export 'foundation/typography/typography.dart';
export 'foundation/spacing/spacing.dart';
export 'foundation/animations/animations.dart';

// 导出工具类
export 'utils/responsive_utils.dart';

// 导出现有组件
export 'vanhub_colors.dart';
export 'vanhub_typography.dart';
export 'vanhub_spacing.dart';
export 'vanhub_icons.dart';

/// VanHub Design System 2.0 核心类
class VanHubDesignSystem {
  VanHubDesignSystem._();

  /// 设计系统版本
  static const String version = '2.0.0';
  
  /// 设计系统名称
  static const String name = 'VanHub Design System';
  
  /// 设计理念
  static const String philosophy = '让房车改装变成一种享受的生活方式';

  // ============ 设计令牌 (Design Tokens) ============
  
  /// 品牌核心色彩
  static const Color brandPrimary = Color(0xFF2563EB); // 专业蓝
  static const Color brandSecondary = Color(0xFFEF4444); // 活力红
  static const Color brandAccent = Color(0xFF10B981); // 成功绿
  
  /// 功能色彩语义化
  static const Color semanticSuccess = Color(0xFF10B981);
  static const Color semanticWarning = Color(0xFFF59E0B);
  static const Color semanticError = Color(0xFFEF4444);
  static const Color semanticInfo = Color(0xFF3B82F6);
  
  /// 中性色彩系统
  static const Color neutralWhite = Color(0xFFFFFFFF);
  static const Color neutralGray50 = Color(0xFFF9FAFB);
  static const Color neutralGray100 = Color(0xFFF3F4F6);
  static const Color neutralGray200 = Color(0xFFE5E7EB);
  static const Color neutralGray300 = Color(0xFFD1D5DB);
  static const Color neutralGray400 = Color(0xFF9CA3AF);
  static const Color neutralGray500 = Color(0xFF6B7280);
  static const Color neutralGray600 = Color(0xFF4B5563);
  static const Color neutralGray700 = Color(0xFF374151);
  static const Color neutralGray800 = Color(0xFF1F2937);
  static const Color neutralGray900 = Color(0xFF111827);
  static const Color neutralBlack = Color(0xFF000000);

  // ============ 字体系统 ============
  
  /// 字体家族
  static const String fontFamilyPrimary = 'Inter';
  static const String fontFamilySecondary = 'JetBrains Mono';
  static const String fontFamilyChinese = 'PingFang SC';
  
  /// 字体权重
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;

  /// 字体大小系统 (基于8pt网格)
  static const double fontSizeXs = 12.0;   // 辅助文字
  static const double fontSizeSm = 14.0;   // 正文小
  static const double fontSizeBase = 16.0; // 正文
  static const double fontSizeLg = 18.0;   // 正文大
  static const double fontSizeXl = 20.0;   // 小标题
  static const double fontSize2xl = 24.0;  // 标题
  static const double fontSize3xl = 30.0;  // 大标题
  static const double fontSize4xl = 36.0;  // 特大标题

  /// 行高系统
  static const double lineHeightTight = 1.25;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.75;

  // ============ 间距系统 (8pt Grid) ============
  
  static const double spacing0 = 0.0;
  static const double spacing1 = 4.0;   // 0.5 * 8
  static const double spacing2 = 8.0;   // 1 * 8
  static const double spacing3 = 12.0;  // 1.5 * 8
  static const double spacing4 = 16.0;  // 2 * 8
  static const double spacing5 = 20.0;  // 2.5 * 8
  static const double spacing6 = 24.0;  // 3 * 8
  static const double spacing8 = 32.0;  // 4 * 8
  static const double spacing10 = 40.0; // 5 * 8
  static const double spacing12 = 48.0; // 6 * 8
  static const double spacing16 = 64.0; // 8 * 8
  static const double spacing20 = 80.0; // 10 * 8
  static const double spacing24 = 96.0; // 12 * 8

  // ============ 圆角系统 ============
  
  static const double radiusNone = 0.0;
  static const double radiusSm = 4.0;
  static const double radiusBase = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radius2xl = 24.0;
  static const double radiusFull = 9999.0;

  // ============ 阴影系统 ============
  
  /// 轻微阴影
  static const BoxShadow shadowSm = BoxShadow(
    color: Color(0x0D000000), // 5% 黑色
    offset: Offset(0, 1),
    blurRadius: 2,
    spreadRadius: 0,
  );

  /// 基础阴影
  static const BoxShadow shadowBase = BoxShadow(
    color: Color(0x1A000000), // 10% 黑色
    offset: Offset(0, 4),
    blurRadius: 6,
    spreadRadius: -1,
  );

  /// 中等阴影
  static const BoxShadow shadowMd = BoxShadow(
    color: Color(0x1A000000), // 10% 黑色
    offset: Offset(0, 10),
    blurRadius: 15,
    spreadRadius: -3,
  );

  /// 大阴影
  static const BoxShadow shadowLg = BoxShadow(
    color: Color(0x1A000000), // 10% 黑色
    offset: Offset(0, 20),
    blurRadius: 25,
    spreadRadius: -5,
  );

  /// 特大阴影
  static const BoxShadow shadowXl = BoxShadow(
    color: Color(0x19000000), // 10% 黑色
    offset: Offset(0, 25),
    blurRadius: 50,
    spreadRadius: -12,
  );

  // ============ 动画系统 ============
  
  /// 动画时长
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationBase = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);
  
  /// 动画曲线
  static const Curve curveDefault = Curves.easeInOut;
  static const Curve curveEaseIn = Curves.easeIn;
  static const Curve curveEaseOut = Curves.easeOut;
  static const Curve curveBounce = Curves.bounceOut;

  // ============ 断点系统 (响应式设计) ============
  
  static const double breakpointSm = 640.0;   // 手机
  static const double breakpointMd = 768.0;   // 平板
  static const double breakpointLg = 1024.0;  // 笔记本
  static const double breakpointXl = 1280.0;  // 桌面
  static const double breakpoint2xl = 1536.0; // 大屏

  // ============ Z-Index 系统 ============
  
  static const int zIndexBase = 0;
  static const int zIndexDropdown = 1000;
  static const int zIndexSticky = 1020;
  static const int zIndexFixed = 1030;
  static const int zIndexModalBackdrop = 1040;
  static const int zIndexModal = 1050;
  static const int zIndexPopover = 1060;
  static const int zIndexTooltip = 1070;
  static const int zIndexToast = 1080;

  // ============ 工具方法 ============
  
  /// 获取响应式值
  static T getResponsiveValue<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= breakpointLg && desktop != null) {
      return desktop;
    } else if (width >= breakpointMd && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// 获取安全区域内边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  /// 创建渐变色
  static LinearGradient createGradient({
    required Color startColor,
    required Color endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
    );
  }

  /// 创建品牌渐变
  static LinearGradient get brandGradient => createGradient(
    startColor: brandPrimary,
    endColor: brandSecondary,
  );

  /// 创建成功渐变
  static LinearGradient get successGradient => createGradient(
    startColor: semanticSuccess,
    endColor: Color(0xFF059669),
  );

  /// 创建警告渐变
  static LinearGradient get warningGradient => createGradient(
    startColor: semanticWarning,
    endColor: Color(0xFFD97706),
  );

  /// 创建错误渐变
  static LinearGradient get errorGradient => createGradient(
    startColor: semanticError,
    endColor: Color(0xFFDC2626),
  );
}

/// 设计系统扩展方法
extension VanHubDesignSystemExtensions on BuildContext {
  /// 获取设计系统
  VanHubDesignSystem get designSystem => VanHubDesignSystem._();
  
  /// 获取响应式值的便捷方法
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    return VanHubDesignSystem.getResponsiveValue(
      context: this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}
