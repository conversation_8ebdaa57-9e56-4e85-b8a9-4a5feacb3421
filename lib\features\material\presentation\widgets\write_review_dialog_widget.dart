import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/material_review.dart';
import '../providers/material_review_provider.dart';

/// 写评价对话框组件
/// 提供完整的材料评价创建界面，帮助用户分享真实的使用体验
class WriteReviewDialogWidget extends ConsumerStatefulWidget {
  final String materialId;
  final String materialName;
  final MaterialReview? existingReview; // 如果是编辑现有评价

  const WriteReviewDialogWidget({
    super.key,
    required this.materialId,
    required this.materialName,
    this.existingReview,
  });

  @override
  ConsumerState<WriteReviewDialogWidget> createState() => _WriteReviewDialogWidgetState();
}

class _WriteReviewDialogWidgetState extends ConsumerState<WriteReviewDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();
  final _vehicleTypeController = TextEditingController();
  final _systemTypeController = TextEditingController();
  final _usageDurationController = TextEditingController();
  
  // 评分
  double _overallRating = 5.0;
  double _qualityRating = 5.0;
  double _valueRating = 5.0;
  double _durabilityRating = 5.0;
  double _installationRating = 5.0;
  
  // 优缺点和技巧
  final List<String> _pros = [];
  final List<String> _cons = [];
  final List<String> _tips = [];
  
  // 验证购买
  bool _isVerifiedPurchase = false;
  DateTime? _purchaseDate;
  
  // 控制器
  final _proController = TextEditingController();
  final _conController = TextEditingController();
  final _tipController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeFromExistingReview();
  }

  void _initializeFromExistingReview() {
    if (widget.existingReview != null) {
      final review = widget.existingReview!;
      _contentController.text = review.content;
      _vehicleTypeController.text = review.vehicleType ?? '';
      _systemTypeController.text = review.systemType ?? '';
      _usageDurationController.text = review.usageDuration ?? '';
      
      _overallRating = review.rating;
      _qualityRating = review.qualityRating;
      _valueRating = review.valueRating;
      _durabilityRating = review.durabilityRating;
      _installationRating = review.installationRating;
      
      _pros.addAll(review.pros);
      _cons.addAll(review.cons);
      _tips.addAll(review.tips);
      
      _isVerifiedPurchase = review.isVerifiedPurchase;
      _purchaseDate = review.purchaseDate;
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    _vehicleTypeController.dispose();
    _systemTypeController.dispose();
    _usageDurationController.dispose();
    _proController.dispose();
    _conController.dispose();
    _tipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final createReviewState = ref.watch(createReviewNotifierProvider);
    final updateReviewState = ref.watch(updateReviewNotifierProvider);
    
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            _buildHeader(context),
            
            const SizedBox(height: 16),
            
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 总体评分
                      _buildOverallRating(context),
                      
                      const SizedBox(height: 24),
                      
                      // 详细评分
                      _buildDetailedRatings(context),
                      
                      const SizedBox(height: 24),
                      
                      // 评价内容
                      _buildReviewContent(context),
                      
                      const SizedBox(height: 24),
                      
                      // 使用场景
                      _buildUsageContext(context),
                      
                      const SizedBox(height: 24),
                      
                      // 优缺点和技巧
                      _buildProsCons(context),
                      
                      const SizedBox(height: 24),
                      
                      // 验证购买
                      _buildVerificationSection(context),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            _buildActionButtons(context, createReviewState, updateReviewState),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.existingReview != null ? '编辑评价' : '写评价',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                widget.materialName,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildOverallRating(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '总体评分',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Row(
          children: [
            _buildInteractiveStarRating(
              _overallRating,
              (rating) => setState(() => _overallRating = rating),
            ),
            
            const SizedBox(width: 16),
            
            Text(
              _overallRating.toStringAsFixed(1),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailedRatings(BuildContext context) {
    final ratings = [
      ('质量', _qualityRating, (rating) => setState(() => _qualityRating = rating)),
      ('性价比', _valueRating, (rating) => setState(() => _valueRating = rating)),
      ('耐用性', _durabilityRating, (rating) => setState(() => _durabilityRating = rating)),
      ('安装难度', _installationRating, (rating) => setState(() => _installationRating = rating)),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '详细评分',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        ...ratings.map((rating) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  rating.$1,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: _buildInteractiveStarRating(rating.$2, rating.$3),
              ),
              
              const SizedBox(width: 16),
              
              SizedBox(
                width: 40,
                child: Text(
                  rating.$2.toStringAsFixed(1),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildReviewContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '评价内容',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        TextFormField(
          controller: _contentController,
          maxLines: 5,
          decoration: const InputDecoration(
            hintText: '请分享您的使用体验，包括材料的表现、安装过程、使用感受等...',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入评价内容';
            }
            if (value.trim().length < 10) {
              return '评价内容至少需要10个字符';
            }
            if (value.length > 2000) {
              return '评价内容不能超过2000个字符';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildUsageContext(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '使用场景',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _vehicleTypeController,
                decoration: const InputDecoration(
                  labelText: '车型',
                  hintText: '如：依维柯Daily',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: TextFormField(
                controller: _systemTypeController,
                decoration: const InputDecoration(
                  labelText: '改装系统',
                  hintText: '如：电气系统',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        TextFormField(
          controller: _usageDurationController,
          decoration: const InputDecoration(
            labelText: '使用时长',
            hintText: '如：使用3个月',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildProsCons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '优缺点和使用技巧',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 优点
        _buildListSection(
          context,
          '优点',
          _pros,
          _proController,
          Colors.green,
          Icons.add_circle,
          '添加优点',
        ),
        
        const SizedBox(height: 16),
        
        // 缺点
        _buildListSection(
          context,
          '缺点',
          _cons,
          _conController,
          Colors.orange,
          Icons.remove_circle,
          '添加缺点',
        ),
        
        const SizedBox(height: 16),
        
        // 使用技巧
        _buildListSection(
          context,
          '使用技巧',
          _tips,
          _tipController,
          Colors.blue,
          Icons.lightbulb,
          '添加技巧',
        ),
      ],
    );
  }

  Widget _buildListSection(
    BuildContext context,
    String title,
    List<String> items,
    TextEditingController controller,
    Color color,
    IconData icon,
    String hintText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 已添加的项目
        ...items.asMap().entries.map((entry) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  entry.value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              IconButton(
                onPressed: () => setState(() => items.removeAt(entry.key)),
                icon: const Icon(Icons.close, size: 16),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ),
        )),
        
        // 添加新项目
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: const OutlineInputBorder(),
                  isDense: true,
                ),
                onSubmitted: (value) => _addItem(items, controller),
              ),
            ),
            
            const SizedBox(width: 8),
            
            IconButton(
              onPressed: () => _addItem(items, controller),
              icon: Icon(Icons.add, color: color),
            ),
          ],
        ),
      ],
    );
  }

  void _addItem(List<String> items, TextEditingController controller) {
    final text = controller.text.trim();
    if (text.isNotEmpty && !items.contains(text) && items.length < 10) {
      setState(() {
        items.add(text);
        controller.clear();
      });
    }
  }

  Widget _buildVerificationSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '购买验证',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        CheckboxListTile(
          title: const Text('我已购买并使用过此材料'),
          subtitle: const Text('验证购买可以提高评价的可信度'),
          value: _isVerifiedPurchase,
          onChanged: (value) => setState(() => _isVerifiedPurchase = value ?? false),
          controlAffinity: ListTileControlAffinity.leading,
        ),
        
        if (_isVerifiedPurchase) ...[
          const SizedBox(height: 8),
          
          ListTile(
            title: const Text('购买日期'),
            subtitle: Text(_purchaseDate?.toString().split(' ')[0] ?? '请选择购买日期'),
            trailing: const Icon(Icons.calendar_today),
            onTap: _selectPurchaseDate,
          ),
        ],
      ],
    );
  }

  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _purchaseDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() => _purchaseDate = date);
    }
  }

  Widget _buildInteractiveStarRating(double rating, Function(double) onRatingChanged) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1.0;
        return GestureDetector(
          onTap: () => onRatingChanged(starValue),
          child: Icon(
            starValue <= rating ? Icons.star : Icons.star_border,
            color: Colors.amber,
            size: 24,
          ),
        );
      }),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    AsyncValue<void> createState,
    AsyncValue<void> updateState,
  ) {
    final isLoading = createState.isLoading || updateState.isLoading;
    
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitReview,
            child: isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(widget.existingReview != null ? '更新评价' : '发布评价'),
          ),
        ),
      ],
    );
  }

  Future<void> _submitReview() async {
    if (!_formKey.currentState!.validate()) return;

    final review = MaterialReview(
      id: widget.existingReview?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      materialId: widget.materialId,
      userId: 'current-user-id', // TODO: 从认证状态获取
      userName: '当前用户', // TODO: 从认证状态获取
      content: _contentController.text.trim(),
      rating: _overallRating,
      qualityRating: _qualityRating,
      valueRating: _valueRating,
      durabilityRating: _durabilityRating,
      installationRating: _installationRating,
      vehicleType: _vehicleTypeController.text.trim().isEmpty ? null : _vehicleTypeController.text.trim(),
      systemType: _systemTypeController.text.trim().isEmpty ? null : _systemTypeController.text.trim(),
      usageDuration: _usageDurationController.text.trim().isEmpty ? null : _usageDurationController.text.trim(),
      pros: _pros,
      cons: _cons,
      tips: _tips,
      isVerifiedPurchase: _isVerifiedPurchase,
      purchaseDate: _purchaseDate,
      createdAt: widget.existingReview?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      if (widget.existingReview != null) {
        await ref.read(updateReviewNotifierProvider.notifier).updateReview(review);
      } else {
        await ref.read(createReviewNotifierProvider.notifier).createReview(review);
      }
      
      if (mounted) {
        Navigator.of(context).pop(true); // 返回true表示成功
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.existingReview != null ? '评价更新成功' : '评价发布成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
