import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../../core/design_system/components/molecules/vanhub_modal.dart';

/// 创建材料对话框V2
/// 
/// 完善的材料信息管理：分类选择→规格参数→价格信息→供应商→图片上传
/// 使用VanHubModal作为基础容器
class CreateMaterialDialogV2 extends ConsumerStatefulWidget {
  /// 材料创建成功回调
  final Function(Map<String, dynamic> materialData)? onMaterialCreated;
  
  /// 预选分类ID
  final String? preselectedCategoryId;

  const CreateMaterialDialogV2({
    super.key,
    this.onMaterialCreated,
    this.preselectedCategoryId,
  });

  /// 显示创建材料对话框的静态方法
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    Function(Map<String, dynamic> materialData)? onMaterialCreated,
    String? preselectedCategoryId,
  }) {
    return VanHubModal.show<Map<String, dynamic>>(
      context: context,
      title: '添加新材料',
      size: VanHubModalSize.lg,
      animation: VanHubModalAnimation.scale,
      fullscreenOnMobile: true,
      child: CreateMaterialDialogV2(
        onMaterialCreated: onMaterialCreated,
        preselectedCategoryId: preselectedCategoryId,
      ),
    );
  }

  @override
  ConsumerState<CreateMaterialDialogV2> createState() => _CreateMaterialDialogV2State();
}

class _CreateMaterialDialogV2State extends ConsumerState<CreateMaterialDialogV2> {
  final _formKey = GlobalKey<FormState>();
  
  // 表单控制器
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _supplierController = TextEditingController();
  final _supplierContactController = TextEditingController();
  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  String? _errorMessage;
  
  // 材料数据
  String _selectedCategoryId = '';
  String _selectedSubcategoryId = '';
  String _currency = 'CNY';
  String _priceType = 'unit'; // unit, meter, square_meter, cubic_meter
  List<String> _selectedImages = [];
  Map<String, String> _specifications = {};
  
  // 材料分类数据
  final Map<String, MaterialCategory> _categories = {
    'electrical': MaterialCategory(
      id: 'electrical',
      name: '电气系统',
      icon: Icons.electrical_services,
      subcategories: {
        'battery': '电池系统',
        'inverter': '逆变器',
        'solar': '太阳能板',
        'wiring': '线缆配件',
        'lighting': '照明设备',
      },
      specifications: ['电压', '功率', '容量', '认证标准'],
    ),
    'plumbing': MaterialCategory(
      id: 'plumbing',
      name: '水路系统',
      icon: Icons.water_drop,
      subcategories: {
        'tank': '水箱',
        'pump': '水泵',
        'pipe': '管道',
        'fittings': '接头配件',
        'filter': '净水设备',
      },
      specifications: ['材质', '压力等级', '接口规格', '容量'],
    ),
    'furniture': MaterialCategory(
      id: 'furniture',
      name: '家具设备',
      icon: Icons.chair,
      subcategories: {
        'bed': '床铺',
        'table': '桌椅',
        'storage': '储物',
        'kitchen': '厨房设备',
        'bathroom': '卫浴设备',
      },
      specifications: ['尺寸', '材质', '重量', '承重'],
    ),
    'insulation': MaterialCategory(
      id: 'insulation',
      name: '保温隔热',
      icon: Icons.ac_unit,
      subcategories: {
        'foam': '泡沫材料',
        'wool': '保温棉',
        'reflective': '反射材料',
        'vapor_barrier': '防潮层',
      },
      specifications: ['厚度', '导热系数', '密度', '阻燃等级'],
    ),
    'exterior': MaterialCategory(
      id: 'exterior',
      name: '外观装饰',
      icon: Icons.palette,
      subcategories: {
        'paint': '涂料',
        'decal': '贴纸',
        'trim': '装饰条',
        'window': '窗户',
      },
      specifications: ['颜色', '材质', '尺寸', '耐候性'],
    ),
  };

  @override
  void initState() {
    super.initState();
    if (widget.preselectedCategoryId != null) {
      _selectedCategoryId = widget.preselectedCategoryId!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _supplierController.dispose();
    _supplierContactController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 700,
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing6),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildErrorMessage(),
              _buildCategorySection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildBasicInfoSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildSpecificationsSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildPriceSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildSupplierSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildImageSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildNotesSection(),
              const SizedBox(height: VanHubDesignSystem.spacing6),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    if (_errorMessage == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing3),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.semanticError.withOpacity(0.1),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        border: Border.all(
          color: VanHubDesignSystem.semanticError.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: VanHubDesignSystem.semanticError,
            size: 20,
          ),
          const SizedBox(width: VanHubDesignSystem.spacing2),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: VanHubDesignSystem.semanticError,
                fontSize: VanHubDesignSystem.fontSizeSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '材料分类',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        _buildCategoryGrid(),
        if (_selectedCategoryId.isNotEmpty) ...[
          const SizedBox(height: VanHubDesignSystem.spacing4),
          _buildSubcategoryDropdown(),
        ],
      ],
    );
  }

  Widget _buildCategoryGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.2,
        crossAxisSpacing: VanHubDesignSystem.spacing3,
        mainAxisSpacing: VanHubDesignSystem.spacing3,
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories.values.elementAt(index);
        final isSelected = _selectedCategoryId == category.id;
        
        return Card(
          elevation: isSelected ? 4 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            side: BorderSide(
              color: isSelected 
                  ? VanHubDesignSystem.brandPrimary
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: InkWell(
            onTap: () {
              setState(() {
                _selectedCategoryId = category.id;
                _selectedSubcategoryId = '';
                _specifications.clear();
              });
            },
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  category.icon,
                  size: 32,
                  color: isSelected 
                      ? VanHubDesignSystem.brandPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(height: VanHubDesignSystem.spacing2),
                Text(
                  category.name,
                  style: TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeSm,
                    fontWeight: isSelected 
                        ? VanHubDesignSystem.fontWeightMedium
                        : VanHubDesignSystem.fontWeightRegular,
                    color: isSelected 
                        ? VanHubDesignSystem.brandPrimary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubcategoryDropdown() {
    final category = _categories[_selectedCategoryId];
    if (category == null) return const SizedBox.shrink();
    
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: '子分类',
        prefixIcon: const Icon(Icons.category),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      value: _selectedSubcategoryId.isEmpty ? null : _selectedSubcategoryId,
      items: category.subcategories.entries.map((entry) {
        return DropdownMenuItem(
          value: entry.key,
          child: Text(entry.value),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedSubcategoryId = value ?? '';
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请选择子分类';
        }
        return null;
      },
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '基本信息',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: '材料名称',
            hintText: '请输入材料名称',
            prefixIcon: const Icon(Icons.inventory),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入材料名称';
            }
            return null;
          },
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _brandController,
                decoration: InputDecoration(
                  labelText: '品牌',
                  hintText: '材料品牌',
                  prefixIcon: const Icon(Icons.business),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                  ),
                ),
              ),
            ),
            const SizedBox(width: VanHubDesignSystem.spacing4),
            Expanded(
              child: TextFormField(
                controller: _modelController,
                decoration: InputDecoration(
                  labelText: '型号',
                  hintText: '产品型号',
                  prefixIcon: const Icon(Icons.model_training),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: '材料描述',
            hintText: '详细描述材料的特性和用途',
            prefixIcon: const Icon(Icons.description),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecificationsSection() {
    final category = _categories[_selectedCategoryId];
    if (category == null || category.specifications.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '规格参数',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        ...category.specifications.map((spec) => Padding(
          padding: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing3),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: spec,
              hintText: '请输入$spec',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
              ),
            ),
            onChanged: (value) {
              _specifications[spec] = value;
            },
          ),
        )),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '价格信息',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: '价格',
                  hintText: '0.00',
                  prefixIcon: const Icon(Icons.attach_money),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入价格';
                  }
                  if (double.tryParse(value) == null) {
                    return '请输入有效的价格';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: VanHubDesignSystem.spacing3),
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: '货币',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                  ),
                ),
                value: _currency,
                items: const [
                  DropdownMenuItem(value: 'CNY', child: Text('人民币')),
                  DropdownMenuItem(value: 'USD', child: Text('美元')),
                  DropdownMenuItem(value: 'EUR', child: Text('欧元')),
                ],
                onChanged: (value) {
                  setState(() {
                    _currency = value ?? 'CNY';
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: '计价单位',
            prefixIcon: const Icon(Icons.straighten),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
          value: _priceType,
          items: const [
            DropdownMenuItem(value: 'unit', child: Text('按件')),
            DropdownMenuItem(value: 'meter', child: Text('按米')),
            DropdownMenuItem(value: 'square_meter', child: Text('按平方米')),
            DropdownMenuItem(value: 'cubic_meter', child: Text('按立方米')),
            DropdownMenuItem(value: 'kilogram', child: Text('按公斤')),
          ],
          onChanged: (value) {
            setState(() {
              _priceType = value ?? 'unit';
            });
          },
        ),
      ],
    );
  }

  Widget _buildSupplierSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '供应商信息',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        TextFormField(
          controller: _supplierController,
          decoration: InputDecoration(
            labelText: '供应商名称',
            hintText: '请输入供应商名称',
            prefixIcon: const Icon(Icons.store),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        TextFormField(
          controller: _supplierContactController,
          decoration: InputDecoration(
            labelText: '联系方式',
            hintText: '电话、微信或其他联系方式',
            prefixIcon: const Icon(Icons.contact_phone),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '产品图片',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        if (_selectedImages.isNotEmpty) ...[
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: VanHubDesignSystem.spacing3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                        child: Container(
                          width: 100,
                          height: 100,
                          color: VanHubDesignSystem.neutralGray100,
                          child: const Icon(
                            Icons.image,
                            size: 40,
                            color: VanHubDesignSystem.neutralGray400,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: const BoxDecoration(
                              color: VanHubDesignSystem.semanticError,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing3),
        ],
        OutlinedButton.icon(
          onPressed: _handleImagePicker,
          icon: const Icon(Icons.add_photo_alternate),
          label: Text(_selectedImages.isEmpty ? '添加图片' : '添加更多图片'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.all(VanHubDesignSystem.spacing4),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '备注信息',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: '备注',
            hintText: '其他需要记录的信息',
            prefixIcon: const Icon(Icons.note),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: VanHubDesignSystem.spacing4),
        Expanded(
          child: FilledButton(
            onPressed: _isLoading ? null : _handleCreateMaterial,
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('创建材料'),
          ),
        ),
      ],
    );
  }

  Future<void> _handleImagePicker() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
      );
      
      if (result != null) {
        setState(() {
          _selectedImages.addAll(
            result.files.map((file) => file.path ?? '').where((path) => path.isNotEmpty),
          );
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败：${e.toString()}')),
        );
      }
    }
  }

  Future<void> _handleCreateMaterial() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    if (_selectedCategoryId.isEmpty) {
      setState(() {
        _errorMessage = '请选择材料分类';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 构建材料数据
      final materialData = {
        'name': _nameController.text,
        'brand': _brandController.text,
        'model': _modelController.text,
        'description': _descriptionController.text,
        'category_id': _selectedCategoryId,
        'subcategory_id': _selectedSubcategoryId,
        'price': double.tryParse(_priceController.text) ?? 0.0,
        'currency': _currency,
        'price_type': _priceType,
        'supplier': _supplierController.text,
        'supplier_contact': _supplierContactController.text,
        'notes': _notesController.text,
        'specifications': _specifications,
        'images': _selectedImages,
        'created_at': DateTime.now().toIso8601String(),
      };

      // TODO: 实际的材料创建逻辑
      await Future.delayed(const Duration(seconds: 2)); // 模拟网络请求
      
      if (mounted) {
        widget.onMaterialCreated?.call(materialData);
        Navigator.of(context).pop(materialData);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '创建材料失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// 材料分类数据类
class MaterialCategory {
  final String id;
  final String name;
  final IconData icon;
  final Map<String, String> subcategories;
  final List<String> specifications;

  const MaterialCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.subcategories,
    required this.specifications,
  });
}
