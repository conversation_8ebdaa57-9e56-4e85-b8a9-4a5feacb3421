import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 智能筛选建议组件
/// 
/// 特性：
/// - 基于用户行为的智能推荐
/// - 热门筛选组合
/// - 快速筛选预设
/// - 筛选历史记录
class SmartFilterSuggestionsWidget extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onApplyFilter;
  final Map<String, dynamic>? currentFilters;

  const SmartFilterSuggestionsWidget({
    super.key,
    required this.onApplyFilter,
    this.currentFilters,
  });

  @override
  ConsumerState<SmartFilterSuggestionsWidget> createState() => _SmartFilterSuggestionsWidgetState();
}

class _SmartFilterSuggestionsWidgetState extends ConsumerState<SmartFilterSuggestionsWidget> {
  
  // 预设筛选组合
  final List<Map<String, dynamic>> _presetFilters = [
    {
      'name': '电力系统套装',
      'icon': Icons.electrical_services,
      'color': Colors.orange,
      'filters': {
        'category': '电力系统',
        'brands': ['Victron Energy', 'Renogy'],
        'priceRange': [1000, 5000],
      },
    },
    {
      'name': '新手入门',
      'icon': Icons.school,
      'color': Colors.green,
      'filters': {
        'priceRange': [0, 1000],
        'statuses': ['可用', '新品'],
      },
    },
    {
      'name': '高端配置',
      'icon': Icons.star,
      'color': Colors.purple,
      'filters': {
        'priceRange': [5000, 20000],
        'brands': ['Victron Energy', 'Dometic'],
      },
    },
    {
      'name': '促销商品',
      'icon': Icons.local_offer,
      'color': Colors.red,
      'filters': {
        'statuses': ['促销'],
      },
    },
    {
      'name': '水路系统',
      'icon': Icons.water_drop,
      'color': Colors.blue,
      'filters': {
        'category': '水系统',
        'priceRange': [500, 3000],
      },
    },
    {
      'name': '照明方案',
      'icon': Icons.lightbulb,
      'color': Colors.amber,
      'filters': {
        'category': '照明系统',
        'priceRange': [100, 1000],
      },
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          _buildQuickFilters(context),
          const SizedBox(height: 16),
          _buildPresetFilters(context),
        ],
      ),
    );
  }
  
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.auto_awesome,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          '智能筛选建议',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        TextButton.icon(
          onPressed: () {
            // TODO: 显示筛选历史
          },
          icon: const Icon(Icons.history, size: 16),
          label: const Text('历史'),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
        ),
      ],
    );
  }
  
  Widget _buildQuickFilters(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速筛选',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickFilterChip(context, '价格 < ¥500', {'priceRange': [0, 500]}),
            _buildQuickFilterChip(context, '新品上市', {'statuses': ['新品']}),
            _buildQuickFilterChip(context, '热门品牌', {'brands': ['Victron Energy', 'Dometic']}),
            _buildQuickFilterChip(context, '现货供应', {'statuses': ['可用']}),
          ],
        ),
      ],
    );
  }
  
  Widget _buildQuickFilterChip(BuildContext context, String label, Map<String, dynamic> filters) {
    return ActionChip(
      label: Text(
        label,
        style: Theme.of(context).textTheme.labelSmall,
      ),
      onPressed: () => widget.onApplyFilter(filters),
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
      side: BorderSide(
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
      ),
    );
  }
  
  Widget _buildPresetFilters(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '推荐组合',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _presetFilters.length,
          itemBuilder: (context, index) {
            final preset = _presetFilters[index];
            return _buildPresetCard(context, preset);
          },
        ),
      ],
    );
  }
  
  Widget _buildPresetCard(BuildContext context, Map<String, dynamic> preset) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => widget.onApplyFilter(preset['filters']),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: preset['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  preset['icon'],
                  color: preset['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      preset['name'],
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getFilterDescription(preset['filters']),
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _getFilterDescription(Map<String, dynamic> filters) {
    final descriptions = <String>[];
    
    if (filters.containsKey('category')) {
      descriptions.add(filters['category']);
    }
    
    if (filters.containsKey('priceRange')) {
      final range = filters['priceRange'] as List<int>;
      descriptions.add('¥${range[0]}-${range[1]}');
    }
    
    if (filters.containsKey('brands')) {
      final brands = filters['brands'] as List<String>;
      descriptions.add('${brands.length}个品牌');
    }
    
    if (filters.containsKey('statuses')) {
      final statuses = filters['statuses'] as List<String>;
      descriptions.add(statuses.join('、'));
    }
    
    return descriptions.take(2).join(' • ');
  }
}
