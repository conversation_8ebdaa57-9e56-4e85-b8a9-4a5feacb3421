import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_favorite_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 增强版材料卡片组件
/// 现代化设计，优秀的用户体验和视觉效果
class EnhancedMaterialCardWidget extends ConsumerStatefulWidget {
  final domain.Material material;
  final VoidCallback? onTap;
  final VoidCallback? onAddToBom;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onFavorite;
  final bool showActions;
  final bool isCompact;
  final bool isFavorite;

  const EnhancedMaterialCardWidget({
    super.key,
    required this.material,
    this.onTap,
    this.onAddToBom,
    this.onEdit,
    this.onDelete,
    this.onFavorite,
    this.showActions = true,
    this.isCompact = false,
    this.isFavorite = false,
  });

  @override
  ConsumerState<EnhancedMaterialCardWidget> createState() => _EnhancedMaterialCardWidgetState();
}

class _EnhancedMaterialCardWidgetState extends ConsumerState<EnhancedMaterialCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late Animation<double> _hoverAnimation;
  late Animation<double> _pressAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _hoverAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOutCubic),
    );
    _pressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeOutCubic),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeOutCubic),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverAnimation, _pressAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _handleHover(true),
            onExit: (_) => _handleHover(false),
            child: GestureDetector(
              onTapDown: (_) => _handlePress(true),
              onTapUp: (_) => _handlePress(false),
              onTapCancel: () => _handlePress(false),
              onTap: widget.onTap,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08 + (_hoverAnimation.value * 0.12)),
                      blurRadius: 8 + (_hoverAnimation.value * 16),
                      offset: Offset(0, 4 + (_hoverAnimation.value * 8)),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 0,
                  margin: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: _isHovered 
                          ? VanHubColors.primary.withValues(alpha: 0.3)
                          : Colors.transparent,
                      width: 1.5,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: widget.isCompact 
                        ? _buildCompactContent()
                        : _buildFullContent(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  void _handlePress(bool isPressed) {
    setState(() {
      _isPressed = isPressed;
    });
    if (isPressed) {
      _pressController.forward();
    } else {
      _pressController.reverse();
    }
  }

  Widget _buildFullContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部区域
          _buildHeader(),
          
          const SizedBox(height: 16),
          
          // 材料信息
          _buildMaterialInfo(),
          
          const SizedBox(height: 16),
          
          // 价格和统计
          _buildPriceAndStats(),
          
          if (widget.showActions) ...[
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 材料图标
          _buildMaterialIcon(),
          
          const SizedBox(width: 12),
          
          // 材料信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.material.name,
                  style: VanHubTypography.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  widget.material.category,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: _getCategoryColor(),
                  ),
                ),
              ],
            ),
          ),
          
          // 价格
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '¥${widget.material.price.toStringAsFixed(2)}',
                style: VanHubTypography.titleSmall.copyWith(
                  color: VanHubColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.material.usageCount > 0)
                Text(
                  '已用${widget.material.usageCount}次',
                  style: VanHubTypography.bodySmall,
                ),
            ],
          ),
          
          if (widget.showActions) ...[
            const SizedBox(width: 8),
            _buildCompactActions(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // 材料图标
        _buildMaterialIcon(),
        
        const SizedBox(width: 12),
        
        // 材料名称和分类
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.material.name,
                style: VanHubTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.material.category,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: _getCategoryColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 收藏按钮
        if (widget.onFavorite != null)
          Consumer(
            builder: (context, ref, child) {
              final favoriteStatusAsync = ref.watch(materialFavoriteStatusProvider(widget.material.id));

              return favoriteStatusAsync.when(
                data: (isFavorited) => IconButton(
                  onPressed: () async {
                    final result = await ref.read(materialFavoriteControllerProvider.notifier)
                        .toggleFavorite(materialId: widget.material.id);

                    result.fold(
                      (failure) => ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('操作失败: ${failure.message}'),
                          backgroundColor: Colors.red,
                        ),
                      ),
                      (newStatus) => ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(newStatus ? '已添加到收藏' : '已从收藏中移除'),
                          backgroundColor: newStatus ? Colors.green : Colors.orange,
                        ),
                      ),
                    );

                    widget.onFavorite?.call();
                  },
                  icon: Icon(
                    isFavorited ? Icons.favorite : Icons.favorite_border,
                    color: isFavorited ? Colors.red : Colors.grey,
                  ),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                loading: () => const SizedBox(
                  width: 32,
                  height: 32,
                  child: Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                ),
                error: (error, stack) => IconButton(
                  onPressed: widget.onFavorite,
                  icon: const Icon(Icons.favorite_border, color: Colors.grey),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildMaterialIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor().withValues(alpha: 0.8),
            _getCategoryColor(),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _getCategoryColor().withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        _getCategoryIcon(),
        color: Colors.white,
        size: 24,
      ),
    );
  }

  Widget _buildMaterialInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.material.description.isNotEmpty) ...[
          Text(
            widget.material.description,
            style: VanHubTypography.bodyMedium,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),
        ],
        
        // 规格信息
        if (widget.material.specifications != null) ...[
          _buildInfoRow(
            icon: Icons.info_outline,
            label: '规格',
            value: widget.material.specifications!,
          ),
          const SizedBox(height: 8),
        ],
        
        // 品牌型号
        if (widget.material.brand != null || widget.material.model != null) ...[
          _buildInfoRow(
            icon: Icons.business,
            label: '品牌型号',
            value: '${widget.material.brand ?? ''} ${widget.material.model ?? ''}'.trim(),
          ),
          const SizedBox(height: 8),
        ],
        
        // 供应商
        if (widget.material.supplier != null) ...[
          _buildInfoRow(
            icon: Icons.store,
            label: '供应商',
            value: widget.material.supplier!,
          ),
        ],
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: VanHubTypography.bodySmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceAndStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // 价格信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '参考价格',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '¥${widget.material.price.toStringAsFixed(2)}',
                  style: VanHubTypography.headlineSmall.copyWith(
                    color: VanHubColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // 使用统计
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '使用次数',
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: widget.material.usageCount > 0 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${widget.material.usageCount}次',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: widget.material.usageCount > 0
                        ? Colors.green[700]
                        : Colors.grey[600],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // 添加到BOM按钮
        if (widget.onAddToBom != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onAddToBom,
              icon: const Icon(Icons.add_shopping_cart, size: 18),
              label: const Text('添加到BOM'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        
        if (widget.onAddToBom != null && (widget.onEdit != null || widget.onDelete != null))
          const SizedBox(width: 8),
        
        // 编辑按钮
        if (widget.onEdit != null)
          IconButton(
            onPressed: widget.onEdit,
            icon: const Icon(Icons.edit_outlined),
            style: IconButton.styleFrom(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              foregroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        
        if (widget.onEdit != null && widget.onDelete != null)
          const SizedBox(width: 8),
        
        // 删除按钮
        if (widget.onDelete != null)
          IconButton(
            onPressed: widget.onDelete,
            icon: const Icon(Icons.delete_outline),
            style: IconButton.styleFrom(
              backgroundColor: Colors.red.withValues(alpha: 0.1),
              foregroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCompactActions() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        switch (value) {
          case 'add_to_bom':
            widget.onAddToBom?.call();
            break;
          case 'edit':
            widget.onEdit?.call();
            break;
          case 'delete':
            widget.onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        if (widget.onAddToBom != null)
          const PopupMenuItem(
            value: 'add_to_bom',
            child: Row(
              children: [
                Icon(Icons.add_shopping_cart, size: 18),
                SizedBox(width: 8),
                Text('添加到BOM'),
              ],
            ),
          ),
        if (widget.onEdit != null)
          const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit_outlined, size: 18),
                SizedBox(width: 8),
                Text('编辑'),
              ],
            ),
          ),
        if (widget.onDelete != null)
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete_outline, size: 18, color: Colors.red),
                SizedBox(width: 8),
                Text('删除', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  Color _getCategoryColor() {
    switch (widget.material.category.toLowerCase()) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.purple;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.indigo;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '安全设备':
        return Colors.red;
      case '通讯设备':
        return Colors.teal;
      case '娱乐设备':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon() {
    switch (widget.material.category.toLowerCase()) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.directions_car;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathroom;
      case '安全设备':
        return Icons.security;
      case '通讯设备':
        return Icons.wifi;
      case '娱乐设备':
        return Icons.tv;
      default:
        return Icons.category;
    }
  }
}
