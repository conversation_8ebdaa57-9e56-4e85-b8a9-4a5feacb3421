# VanHub Clean Architecture 实施指南

## 项目架构原则

### 核心原则
1. **严禁在Widget中包含任何业务逻辑或直接的数据调用**
2. **所有状态管理必须通过Riverpod的Notifier**
3. **数据获取必须通过Data层的Repository实现**
4. **Domain层必须保持纯净，不能依赖Flutter或任何外部包**
5. **所有可能失败的操作必须返回`Either<Failure, Success>`类型**

## 立即需要修复的问题

### 1. main.dart 重构
当前main.dart包含大量业务逻辑，需要重构：

```dart
// ❌ 当前问题：main.dart中包含LoginPage、RegisterPage等UI组件
// ✅ 应该移动到：lib/features/auth/presentation/pages/

// ❌ 当前问题：直接在Widget中处理登录逻辑
// ✅ 应该通过：AuthNotifier处理所有认证逻辑
```

### 2. 缺失的Either类型
需要添加fpdart依赖并实现Either类型：

```yaml
dependencies:
  fpdart: ^1.1.0
```

```dart
// 所有Repository方法必须返回Either
Future<Either<Failure, User>> login(LoginRequest request);
Future<Either<Failure, void>> logout();
```

### 3. 缺失的Freezed实体
Domain层实体必须使用freezed：

```dart
@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    String? name,
  }) = _User;
}
```

## 重构步骤

### 第一步：创建Auth模块结构
```
lib/features/auth/
├── domain/
│   ├── entities/
│   │   └── user.dart              # freezed实体
│   ├── repositories/
│   │   └── auth_repository.dart   # Repository接口
│   └── usecases/
│       ├── login_usecase.dart
│       └── register_usecase.dart
├── data/
│   ├── models/
│   │   └── user_model.dart        # DTO
│   ├── datasources/
│   │   └── auth_remote_datasource.dart
│   └── repositories/
│       └── auth_repository_impl.dart
└── presentation/
    ├── pages/
    │   ├── login_page.dart
    │   └── register_page.dart
    └── providers/
        └── auth_provider.dart     # Riverpod Notifier
```

### 第二步：实现Either类型
```dart
// Repository接口
abstract class AuthRepository {
  Future<Either<Failure, User>> login(LoginRequest request);
  Future<Either<Failure, User>> register(RegisterRequest request);
  Future<Either<Failure, void>> logout();
}

// Repository实现
class AuthRepositoryImpl implements AuthRepository {
  @override
  Future<Either<Failure, User>> login(LoginRequest request) async {
    try {
      final userModel = await remoteDataSource.login(request);
      return Right(userModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
```

### 第三步：实现Riverpod Notifier
```dart
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  Future<User?> build() async {
    // 初始化时检查用户状态
    final result = await ref.read(authRepositoryProvider).getCurrentUser();
    return result.fold(
      (failure) => null,
      (user) => user,
    );
  }
  
  Future<Either<Failure, void>> login(LoginRequest request) async {
    state = const AsyncLoading();
    final result = await ref.read(authRepositoryProvider).login(request);
    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
        return Left(failure);
      },
      (user) {
        state = AsyncData(user);
        return const Right(null);
      },
    );
  }
}
```

### 第四步：重构UI层
```dart
class LoginPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    
    return Scaffold(
      body: authState.when(
        data: (user) => user != null ? HomePage() : LoginForm(),
        loading: () => LoadingWidget(),
        error: (error, stack) => ErrorWidget(error: error),
      ),
    );
  }
}

class LoginForm extends ConsumerStatefulWidget {
  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  // 只处理UI状态，不处理业务逻辑
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    
    final request = LoginRequest(
      email: _emailController.text,
      password: _passwordController.text,
    );
    
    // 通过Notifier处理业务逻辑
    final result = await ref.read(authNotifierProvider.notifier).login(request);
    
    result.fold(
      (failure) => _showError(failure.message),
      (_) => _navigateToHome(),
    );
  }
}
```

## 代码生成命令

```bash
# 生成freezed代码
flutter packages pub run build_runner build

# 生成riverpod代码
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 验证清单

- [ ] 所有Widget都使用ConsumerWidget
- [ ] 没有业务逻辑在Widget中
- [ ] 所有Repository方法返回Either类型
- [ ] Domain层实体使用freezed
- [ ] 状态管理通过Riverpod Notifier
- [ ] 依赖注入通过Riverpod Provider
- [ ] 分层依赖关系正确