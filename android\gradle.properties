# JVM内存优化
org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Android配置
android.useAndroidX=true
android.enableJetifier=true

# Gradle性能优化
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.configuration-cache=true

# 构建优化
android.enableR8.fullMode=true

# 网络超时配置
org.gradle.daemon.idletimeout=300000
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
