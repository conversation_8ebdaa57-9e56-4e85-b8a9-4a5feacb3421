| create_statement                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| CREATE TABLE public.bom_items (purchase_date TIMESTAMP WITH TIME ZONE, notes TEXT DEFAULT ''::text, use_date TIMESTAMP WITH TIME ZONE, purchase_link TEXT, attributes JSONB, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), project_id UUID NOT NULL, status TEXT DEFAULT 'planned'::text, description TEXT, image_urls ARRAY, purchase_location TEXT, id UUID NOT NULL DEFAULT uuid_generate_v4(), commit_id UUID, item_name TEXT NOT NULL, category TEXT, price DECIMAL(10,2) NOT NULL DEFAULT 0, quantity INTEGER NOT NULL DEFAULT 1, weight DECIMAL(8,2), node_id UUID, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                          |
| CREATE TABLE public.bom_template_items (id UUID NOT NULL DEFAULT uuid_generate_v4(), template_id UUID NOT NULL, item_name TEXT NOT NULL, category TEXT, price DECIMAL(10,2) NOT NULL DEFAULT 0, quantity INTEGER NOT NULL DEFAULT 1, weight DECIMAL(8,2), purchase_link TEXT, attributes JSONB, created_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| CREATE TABLE public.bom_templates (user_id UUID NOT NULL, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), usage_count INTEGER DEFAULT 0, is_public BOOLEAN DEFAULT false, description TEXT, category TEXT, template_name TEXT NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| CREATE TABLE public.bom_version_history (id UUID NOT NULL DEFAULT uuid_generate_v4(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), change_reason TEXT, changed_by UUID NOT NULL, new_data JSONB, old_data JSONB, action_type TEXT NOT NULL, bom_item_id UUID NOT NULL, project_version_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| CREATE TABLE public.collaboration_notifications (notification_type TEXT NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), created_by UUID, is_read BOOLEAN DEFAULT false, data JSONB, message TEXT NOT NULL, title TEXT NOT NULL, project_id UUID NOT NULL, user_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| CREATE TABLE public.collaboration_operations (id UUID NOT NULL DEFAULT uuid_generate_v4(), session_id UUID NOT NULL, operation_data JSONB NOT NULL, target_id UUID, target_type TEXT NOT NULL, operation_type TEXT NOT NULL, conflict_resolved BOOLEAN DEFAULT false, timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(), applied BOOLEAN DEFAULT false);                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| CREATE TABLE public.collaboration_sessions (user_id UUID NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), is_active BOOLEAN DEFAULT true, cursor_position JSONB, id UUID NOT NULL DEFAULT uuid_generate_v4(), current_page TEXT, project_id UUID NOT NULL, last_activity TIMESTAMP WITH TIME ZONE DEFAULT now(), session_token TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| CREATE TABLE public.comments (content TEXT NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), target_id UUID NOT NULL, target_type TEXT NOT NULL, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), parent_id UUID, user_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| CREATE TABLE public.commits (updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), total_cost DECIMAL(10,2) DEFAULT 0, labor_hours DECIMAL(6,2) DEFAULT 0, status TEXT DEFAULT 'planned'::text, tags ARRAY, metadata JSONB, commit_date DATE DEFAULT CURRENT_DATE, description_md TEXT, message TEXT NOT NULL, project_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), title TEXT, description TEXT);                                                                                                                                                                                                                                                                                                                         |
| CREATE TABLE public.conflict_resolutions (id UUID NOT NULL DEFAULT uuid_generate_v4(), resolved_by UUID, resolution_data JSONB, resolved_at TIMESTAMP WITH TIME ZONE DEFAULT now(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), project_id UUID NOT NULL, conflict_type TEXT NOT NULL, conflicted_operations ARRAY NOT NULL, resolution_strategy TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                      |
| CREATE TABLE public.forks (forked_project_id UUID NOT NULL, forked_by_user_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), source_project_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| CREATE TABLE public.log_entries (total_cost DECIMAL(10,2) DEFAULT 0.00, time_spent_minutes INTEGER DEFAULT 0, difficulty TEXT, status TEXT, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), author_id UUID, log_date TIMESTAMP WITH TIME ZONE NOT NULL, content TEXT, title TEXT NOT NULL, system_id TEXT, project_id UUID, id UUID NOT NULL DEFAULT gen_random_uuid());                                                                                                                                                                                                                                                                                                                                                               |
| CREATE TABLE public.material_brands (created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), description TEXT, website TEXT, logo_url TEXT, id UUID NOT NULL DEFAULT uuid_generate_v4(), name TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| CREATE TABLE public.material_categories (name TEXT NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), parent_id UUID, icon TEXT, color TEXT, sort_order INTEGER DEFAULT 0, created_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| CREATE TABLE public.material_comparisons (id UUID NOT NULL DEFAULT uuid_generate_v4(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), notes TEXT, material_ids ARRAY NOT NULL, comparison_name TEXT NOT NULL, user_id UUID NOT NULL, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| CREATE TABLE public.material_favorites (created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), notes TEXT, material_price DECIMAL(10,2), material_category TEXT, material_name TEXT NOT NULL, user_id UUID NOT NULL, material_id TEXT NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| CREATE TABLE public.material_library (specification TEXT, id UUID NOT NULL DEFAULT uuid_generate_v4(), user_id UUID NOT NULL, item_name TEXT NOT NULL, category TEXT, brand TEXT, model TEXT, reference_price DECIMAL(10,2), weight DECIMAL(8,2), purchase_link TEXT, description TEXT, attributes JSONB, usage_count INTEGER DEFAULT 0, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), rating DECIMAL(3,2) DEFAULT 0, review_count INTEGER DEFAULT 0);                                                                                                                                                                                                                                                                               |
| CREATE TABLE public.material_reviews (id UUID NOT NULL DEFAULT uuid_generate_v4(), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), material_id TEXT NOT NULL, user_id UUID NOT NULL, user_name TEXT NOT NULL, user_avatar TEXT, rating DECIMAL(2,1) NOT NULL, review_text TEXT, images ARRAY, tags ARRAY, project_id UUID, project_title TEXT, detailed_ratings JSONB, helpful_count INTEGER DEFAULT 0, created_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                               |
| CREATE TABLE public.material_tags (icon TEXT, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4(), name TEXT NOT NULL, category TEXT NOT NULL, usage_count INTEGER DEFAULT 0, color TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| CREATE TABLE public.material_usages (id UUID NOT NULL DEFAULT uuid_generate_v4(), usage_note TEXT, user_name TEXT NOT NULL, user_id UUID NOT NULL, project_title TEXT NOT NULL, project_id UUID NOT NULL, material_id TEXT NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), user_rating DECIMAL(2,1), images ARRAY);                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| CREATE TABLE public.media (file_path TEXT NOT NULL, commit_id UUID NOT NULL, user_id UUID NOT NULL, file_type TEXT NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| CREATE TABLE public.project_activity_log (project_id UUID NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), metadata JSONB, description TEXT NOT NULL, target_id UUID, target_type TEXT, activity_type TEXT NOT NULL, user_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| CREATE TABLE public.project_collaborators (user_id UUID NOT NULL, status TEXT NOT NULL DEFAULT 'pending'::text, permissions JSONB DEFAULT '{}'::jsonb, invited_by UUID, invited_at TIMESTAMP WITH TIME ZONE DEFAULT now(), accepted_at TIMESTAMP WITH TIME ZONE, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), role TEXT NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                           |
| CREATE TABLE public.project_discovery (view_count INTEGER DEFAULT 0, like_count INTEGER DEFAULT 0, fork_count INTEGER DEFAULT 0, tags ARRAY, difficulty_level TEXT, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), estimated_cost DECIMAL(10,2), estimated_time_days INTEGER, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL, featured BOOLEAN DEFAULT false);                                                                                                                                                                                                                                                                                                                                                 |
| CREATE TABLE public.project_follows (user_id UUID NOT NULL, followed_at TIMESTAMP WITH TIME ZONE DEFAULT now(), notification_enabled BOOLEAN DEFAULT true, id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| CREATE TABLE public.project_forks (created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), fork_date TIMESTAMP WITH TIME ZONE DEFAULT now(), fork_reason TEXT, is_active BOOLEAN DEFAULT true, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4(), original_project_id UUID NOT NULL, forked_project_id UUID NOT NULL, forked_by_user_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                              |
| CREATE TABLE public.project_likes (user_id UUID NOT NULL, liked_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| CREATE TABLE public.project_node_dependencies (id UUID NOT NULL DEFAULT uuid_generate_v4(), dependency_type TEXT NOT NULL DEFAULT 'blocks'::text, target_node_id UUID NOT NULL, source_node_id UUID NOT NULL, project_id UUID NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| CREATE TABLE public.project_nodes (progress INTEGER DEFAULT 0, id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL, parent_id UUID, title TEXT NOT NULL, description TEXT DEFAULT ''::text, node_type TEXT NOT NULL, status TEXT NOT NULL DEFAULT 'planned'::text, estimated_cost DECIMAL(10,2) DEFAULT 0, estimated_hours INTEGER DEFAULT 0, actual_cost DECIMAL(10,2) DEFAULT 0, actual_hours INTEGER DEFAULT 0, priority INTEGER DEFAULT 0, sort_order INTEGER DEFAULT 0, data JSONB DEFAULT '{}'::jsonb, metadata JSONB DEFAULT '{}'::jsonb, tags ARRAY DEFAULT '{}'::text[], created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), dependencies ARRAY DEFAULT '{}'::text[], children_ids ARRAY DEFAULT '{}'::text[]); |
| CREATE TABLE public.project_stats (project_id UUID NOT NULL, follow_count INTEGER DEFAULT 0, view_count INTEGER DEFAULT 0, commit_count INTEGER DEFAULT 0, last_activity_at TIMESTAMP WITH TIME ZONE, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), like_count INTEGER DEFAULT 0, fork_count INTEGER DEFAULT 0);                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| CREATE TABLE public.project_tag_relations (project_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), tag_id UUID NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| CREATE TABLE public.project_tags (created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), id UUID NOT NULL DEFAULT uuid_generate_v4(), name TEXT NOT NULL, description TEXT, color TEXT DEFAULT '#2196F3'::text);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| CREATE TABLE public.project_versions (version_name TEXT, id UUID NOT NULL DEFAULT uuid_generate_v4(), project_id UUID NOT NULL, parent_version_id UUID, snapshot_data JSONB NOT NULL, created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), version_number TEXT NOT NULL, description TEXT, is_current BOOLEAN DEFAULT false, created_by UUID NOT NULL, is_major BOOLEAN DEFAULT false);                                                                                                                                                                                                                                                                                                                                                                                                        |
| CREATE TABLE public.projects (total_budget DECIMAL(10,2), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), user_id UUID NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), is_public BOOLEAN DEFAULT false, description TEXT, vehicle_model TEXT NOT NULL, title TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| CREATE TABLE public.review_helpfulness (created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), is_helpful BOOLEAN NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), user_id UUID NOT NULL, review_id UUID NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| CREATE TABLE public.timeline_events (completed_date TIMESTAMP WITH TIME ZONE, attachments ARRAY, id UUID NOT NULL DEFAULT uuid_generate_v4(), actual_hours DECIMAL(6,2), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), title TEXT NOT NULL, description TEXT, event_type TEXT NOT NULL, event_date DATE NOT NULL, status TEXT NOT NULL DEFAULT 'planned'::text, priority TEXT DEFAULT 'medium'::text, progress INTEGER DEFAULT 0, estimated_hours DECIMAL(6,2), project_id UUID NOT NULL, tags ARRAY, metadata JSONB, updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), assignee TEXT);                                                                                                                                                                                                |
| CREATE TABLE public.users (avatar_url TEXT, username TEXT NOT NULL, id UUID NOT NULL DEFAULT uuid_generate_v4(), updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), email TEXT NOT NULL);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |