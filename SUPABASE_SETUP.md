# Supabase连接配置指南

## 1. 获取Supabase项目信息

1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 登录您的账户
3. 选择您的项目或创建新项目
4. 在项目设置中找到以下信息：
   - **Project URL**: 类似 `https://your-project-ref.supabase.co`
   - **API Key (anon public)**: 匿名公钥

## 2. 配置项目

### 方法1: 直接修改配置文件

编辑 `lib/core/api/supabase_config.dart` 文件：

```dart
class SupabaseConfig {
  // 替换为您的实际信息
  static const String supabaseUrl = 'https://your-project-ref.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
  
  // ... 其他代码保持不变
}
```

### 方法2: 使用环境变量（推荐）

1. 复制 `.env.example` 为 `.env`
2. 在 `.env` 文件中填入您的信息：

```
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
```

## 3. 测试连接

1. 运行应用：`flutter run`
2. 导航到设置页面
3. 查看"工具连接"部分的Supabase连接状态
4. 点击"刷新"按钮测试连接

## 4. 常见问题

### 连接失败
- 检查URL和API密钥是否正确
- 确保网络连接正常
- 检查Supabase项目是否处于活跃状态

### 权限错误
- 确保使用的是anon public密钥，不是service role密钥
- 检查Supabase项目的RLS（行级安全）设置

## 5. 下一步

连接成功后，您可以：
- 实现用户认证功能
- 创建数据库表和RLS策略
- 开发具体的业务功能模块

## 安全提醒

- 不要将真实的API密钥提交到版本控制系统
- 在生产环境中使用环境变量管理敏感信息
- 定期轮换API密钥
