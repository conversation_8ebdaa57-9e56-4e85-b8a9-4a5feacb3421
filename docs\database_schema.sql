-- VanHub改装宝数据库表结构

-- 1. 项目表 (projects)
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'planning' CHECK (status IN ('planning', 'in_progress', 'completed', 'cancelled')),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 材料分类表 (material_categories)
CREATE TABLE IF NOT EXISTS material_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES material_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 材料表 (materials)
CREATE TABLE IF NOT EXISTS materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES material_categories(id) ON DELETE SET NULL,
    unit VARCHAR(50) DEFAULT '个',
    price DECIMAL(10,2) DEFAULT 0,
    supplier VARCHAR(255),
    model VARCHAR(255),
    specifications TEXT,
    image_url TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. BOM清单表 (bom_items)
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'ordered', 'received', 'installed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, material_id)
);

-- 5. 项目进度表 (project_progress)
CREATE TABLE IF NOT EXISTS project_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    status VARCHAR(50) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 项目文件表 (project_files)
CREATE TABLE IF NOT EXISTS project_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_url TEXT NOT NULL,
    file_type VARCHAR(100),
    file_size BIGINT,
    description TEXT,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);

CREATE INDEX IF NOT EXISTS idx_materials_category_id ON materials(category_id);
CREATE INDEX IF NOT EXISTS idx_materials_user_id ON materials(user_id);
CREATE INDEX IF NOT EXISTS idx_materials_name ON materials(name);

CREATE INDEX IF NOT EXISTS idx_bom_items_project_id ON bom_items(project_id);
CREATE INDEX IF NOT EXISTS idx_bom_items_material_id ON bom_items(material_id);
CREATE INDEX IF NOT EXISTS idx_bom_items_status ON bom_items(status);

CREATE INDEX IF NOT EXISTS idx_project_progress_project_id ON project_progress(project_id);
CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_materials_updated_at BEFORE UPDATE ON materials
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bom_items_updated_at BEFORE UPDATE ON bom_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_progress_updated_at BEFORE UPDATE ON project_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认材料分类
INSERT INTO material_categories (name, description) VALUES
('电气系统', '电池、逆变器、充电器等电气设备'),
('水系统', '水泵、水箱、净水器等水系统设备'),
('燃气系统', '燃气灶、热水器、燃气罐等燃气设备'),
('家具', '床、桌椅、储物柜等家具'),
('装饰材料', '地板、墙面、窗帘等装饰材料'),
('工具配件', '螺丝、胶水、线材等工具配件')
ON CONFLICT DO NOTHING;

-- 插入示例材料数据
INSERT INTO materials (name, description, category_id, unit, price, supplier, model) VALUES
('锂电池', '12V 100Ah磷酸铁锂电池', (SELECT id FROM material_categories WHERE name = '电气系统'), '个', 1200.00, '比亚迪', 'BYD-LFP100'),
('逆变器', '2000W纯正弦波逆变器', (SELECT id FROM material_categories WHERE name = '电气系统'), '个', 800.00, '正弦', 'ZX-2000W'),
('水泵', '12V直流水泵', (SELECT id FROM material_categories WHERE name = '水系统'), '个', 150.00, '威乐', 'WILO-DC12'),
('燃气灶', '双头燃气灶', (SELECT id FROM material_categories WHERE name = '燃气系统'), '个', 300.00, '方太', 'FT-2B'),
('折叠床', '1.2米折叠床', (SELECT id FROM material_categories WHERE name = '家具'), '个', 500.00, '宜家', 'IKEA-FB120')
ON CONFLICT DO NOTHING;

-- RLS (Row Level Security) 策略
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_files ENABLE ROW LEVEL SECURITY;

-- 项目表的RLS策略
CREATE POLICY "Users can view their own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own projects" ON projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own projects" ON projects
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own projects" ON projects
    FOR DELETE USING (auth.uid() = user_id);

-- 材料表的RLS策略
CREATE POLICY "Users can view all materials" ON materials
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own materials" ON materials
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own materials" ON materials
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own materials" ON materials
    FOR DELETE USING (auth.uid() = user_id);

-- BOM清单表的RLS策略
CREATE POLICY "Users can view BOM items for their projects" ON bom_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert BOM items for their projects" ON bom_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update BOM items for their projects" ON bom_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete BOM items for their projects" ON bom_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = bom_items.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- 材料分类表允许所有用户查看
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view material categories" ON material_categories
    FOR SELECT USING (true);
