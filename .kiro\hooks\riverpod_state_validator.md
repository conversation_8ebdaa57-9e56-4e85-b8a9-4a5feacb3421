# Riverpod State Validator Hook

## Hook Configuration
```yaml
name: "Riverpod State Validator"
description: "验证Riverpod状态管理的正确使用"
trigger: "on_file_save"
file_patterns: ["lib/features/**/presentation/**/*.dart"]
auto_execute: true
```

## Riverpod Rules

### Widget State Management
Widget中的状态管理规则：
```dart
// ✅ 正确 - 使用Riverpod
class LoginPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    return authState.when(
      data: (user) => HomePage(),
      loading: () => LoadingWidget(),
      error: (error, stack) => ErrorWidget(),
    );
  }
}

// ❌ 错误 - 在Widget中使用setState处理业务状态
class LoginPage extends StatefulWidget {
  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  User? user;
  
  void login() async {
    final result = await authRepository.login();
    setState(() {
      user = result;
    });
  }
}
```

### Notifier Implementation
Notifier必须正确实现：
```dart
// ✅ 正确
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  Future<User?> build() async {
    return await ref.read(authRepositoryProvider).getCurrentUser();
  }
  
  Future<Either<Failure, void>> login(LoginParams params) async {
    state = const AsyncLoading();
    final result = await ref.read(loginUseCaseProvider).call(params);
    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
        return Left(failure);
      },
      (user) {
        state = AsyncData(user);
        return const Right(null);
      },
    );
  }
}

// ❌ 错误 - 直接在Notifier中处理UI逻辑
```

### Provider Definition
Provider必须正确定义：
```dart
// ✅ 正确
@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  return AuthRepositoryImpl(
    remoteDataSource: ref.read(authRemoteDataSourceProvider),
  );
}

// ❌ 错误 - 在Widget中创建Repository实例
```

## 执行动作
1. 检查Widget是否正确使用ConsumerWidget
2. 验证Notifier实现是否符合规范
3. 检查是否有业务逻辑泄露到UI层
4. 提供重构建议