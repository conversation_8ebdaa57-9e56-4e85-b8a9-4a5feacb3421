import 'package:flutter/material.dart';
import 'vanhub_data_models.dart';
import 'vanhub_data_table_controller.dart';
import 'vanhub_data_table_style.dart';

/// 虚拟滚动数据表格
/// 适用于大量数据的高性能表格
class VanHubVirtualDataTable extends StatefulWidget {
  final List<VanHubDataColumn> columns;
  final List<VanHubDataRow> rows;
  final bool showCheckboxColumn;
  final bool sortable;
  final ValueChanged<List<VanHubDataRow>>? onSelectChanged;
  final VanHubDataTableController? controller;
  final double rowHeight;
  final double headerHeight;
  final bool showBorders;
  final bool zebra;
  final bool hoverable;
  final VanHubDataTableStyle? style;

  const VanHubVirtualDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.showCheckboxColumn = false,
    this.sortable = true,
    this.onSelectChanged,
    this.controller,
    this.rowHeight = 48.0,
    this.headerHeight = 56.0,
    this.showBorders = true,
    this.zebra = true,
    this.hoverable = true,
    this.style,
  });

  @override
  State<VanHubVirtualDataTable> createState() => _VanHubVirtualDataTableState();
}

class _VanHubVirtualDataTableState extends State<VanHubVirtualDataTable> {
  late VanHubDataTableController _controller;
  late List<VanHubDataRow> _displayedRows;
  late List<VanHubDataRow> _selectedRows;
  late Map<String, VanHubSortDirection> _sortColumns;
  final ScrollController _horizontalScrollController = ScrollController();
  final ScrollController _verticalScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? VanHubDataTableController();
    _displayedRows = List.from(widget.rows);
    _selectedRows = [];
    _sortColumns = {};
    
    _applyInitialSorting();
  }

  @override
  void didUpdateWidget(VanHubVirtualDataTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.rows != oldWidget.rows) {
      _displayedRows = List.from(widget.rows);
      _applySorting();
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    super.dispose();
  }

  void _applyInitialSorting() {
    // 应用初始排序
    for (final column in widget.columns) {
      if (column.initialSortDirection != null) {
        _sortColumns[column.id] = column.initialSortDirection!;
      }
    }
    
    if (_sortColumns.isNotEmpty) {
      _applySorting();
    }
  }

  void _applySorting() {
    if (_sortColumns.isEmpty) {
      return;
    }
    
    _displayedRows.sort((a, b) {
      int result = 0;
      
      for (final entry in _sortColumns.entries) {
        final columnId = entry.key;
        final direction = entry.value;
        final column = widget.columns.firstWhere((col) => col.id == columnId);
        
        if (column.comparator != null) {
          final aCell = a.cells[columnId];
          final bCell = b.cells[columnId];
          if (aCell != null && bCell != null) {
            result = column.comparator!(aCell, bCell);
          } else if (aCell == null && bCell == null) {
            result = 0;
          } else if (aCell == null) {
            result = -1;
          } else {
            result = 1;
          }
        } else {
          final aValue = a.cells[columnId]?.value;
          final bValue = b.cells[columnId]?.value;
          
          if (aValue == null && bValue == null) {
            result = 0;
          } else if (aValue == null) {
            result = -1;
          } else if (bValue == null) {
            result = 1;
          } else if (aValue is Comparable && bValue is Comparable) {
            result = Comparable.compare(aValue, bValue);
          } else {
            result = aValue.toString().compareTo(bValue.toString());
          }
        }
        
        if (result != 0) {
          return direction == VanHubSortDirection.ascending ? result : -result;
        }
      }
      
      return result;
    });
    
    setState(() {});
  }

  void _handleSort(String columnId) {
    if (!widget.sortable) return;
    
    setState(() {
      if (_sortColumns.containsKey(columnId)) {
        if (_sortColumns[columnId] == VanHubSortDirection.ascending) {
          _sortColumns[columnId] = VanHubSortDirection.descending;
        } else {
          _sortColumns.remove(columnId);
        }
      } else {
        _sortColumns[columnId] = VanHubSortDirection.ascending;
      }
      
      _applySorting();
    });
  }

  void _handleSelectAll(bool? selected) {
    if (selected == null) return;
    
    setState(() {
      if (selected) {
        _selectedRows = List.from(_displayedRows);
      } else {
        _selectedRows = [];
      }
      
      if (widget.onSelectChanged != null) {
        widget.onSelectChanged!(_selectedRows);
      }
    });
  }

  void _handleSelectRow(VanHubDataRow row, bool? selected) {
    if (selected == null) return;
    
    setState(() {
      if (selected) {
        if (!_selectedRows.contains(row)) {
          _selectedRows.add(row);
        }
      } else {
        _selectedRows.remove(row);
      }
      
      if (widget.onSelectChanged != null) {
        widget.onSelectChanged!(_selectedRows);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final style = widget.style ?? const VanHubDataTableStyle();
    
    // 计算表格总宽度
    double tableWidth = 0;
    for (final column in widget.columns) {
      tableWidth += style.columnSpacing;
    }
    
    // 计算表格总高度
    final tableHeight = widget.headerHeight + (widget.rowHeight * _displayedRows.length);
    
    return Column(
      children: [
        // 表头
        SizedBox(
          height: widget.headerHeight,
          child: SingleChildScrollView(
            controller: _horizontalScrollController,
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                if (widget.showCheckboxColumn)
                  SizedBox(
                    width: style.columnSpacing,
                    child: Checkbox(
                      value: _selectedRows.length == _displayedRows.length && _displayedRows.isNotEmpty,
                      onChanged: _handleSelectAll,
                    ),
                  ),
                ...widget.columns.map((column) {
                  return SizedBox(
                    width: style.columnSpacing,
                    child: InkWell(
                      onTap: widget.sortable && column.sortable
                          ? () => _handleSort(column.id)
                          : null,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                column.label,
                                style: style.headerTextStyle ?? 
                                    TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (widget.sortable && column.sortable)
                              _buildSortIcon(column.id),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
        
        // 分隔线
        if (widget.showBorders)
          Divider(
            height: style.dividerThickness,
            thickness: style.dividerThickness,
            color: theme.dividerColor,
          ),
        
        // 表格内容
        Expanded(
          child: Scrollbar(
            controller: _verticalScrollController,
            thumbVisibility: true,
            child: SingleChildScrollView(
              controller: _verticalScrollController,
              child: SingleChildScrollView(
                controller: _horizontalScrollController,
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  width: tableWidth,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _displayedRows.length,
                    itemBuilder: (context, index) {
                      final row = _displayedRows[index];
                      final isSelected = _selectedRows.contains(row);
                      final isEven = index % 2 == 0;
                      
                      Color? rowColor;
                      if (isSelected) {
                        rowColor = theme.colorScheme.primary.withOpacity(0.08);
                      } else if (widget.zebra && !isEven) {
                        rowColor = theme.colorScheme.surfaceVariant.withOpacity(0.1);
                      }
                      
                      return Container(
                        height: widget.rowHeight,
                        color: rowColor,
                        child: Row(
                          children: [
                            if (widget.showCheckboxColumn)
                              SizedBox(
                                width: style.columnSpacing,
                                child: Checkbox(
                                  value: isSelected,
                                  onChanged: (selected) => _handleSelectRow(row, selected),
                                ),
                              ),
                            ...widget.columns.map((column) {
                              final cell = row.cells[column.id];
                              
                              return SizedBox(
                                width: style.columnSpacing,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                  child: cell != null && cell.customBuilder != null
                                      ? cell.customBuilder!(context, cell.value)
                                      : Text(
                                          cell?.value?.toString() ?? '-',
                                          style: style.cellTextStyle,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                ),
                              );
                            }),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSortIcon(String columnId) {
    if (!_sortColumns.containsKey(columnId)) {
      return const Icon(Icons.sort, size: 16, color: Colors.grey);
    }
    
    return _sortColumns[columnId] == VanHubSortDirection.ascending
        ? const Icon(Icons.arrow_upward, size: 16)
        : const Icon(Icons.arrow_downward, size: 16);
  }
}