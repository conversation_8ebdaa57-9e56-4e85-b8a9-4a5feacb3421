import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/bom_statistics.dart';
import '../../domain/entities/bom_item.dart';
import '../providers/bom_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 增强版BOM统计组件 - 支持实时数据更新和交互功能
class EnhancedBomStatisticsWidget extends ConsumerStatefulWidget {
  final String projectId;
  final bool showCharts;
  final bool showOverdueItems;

  const EnhancedBomStatisticsWidget({
    super.key,
    required this.projectId,
    this.showCharts = true,
    this.showOverdueItems = true,
  });

  @override
  ConsumerState<EnhancedBomStatisticsWidget> createState() => _EnhancedBomStatisticsWidgetState();
}

class _EnhancedBomStatisticsWidgetState extends ConsumerState<EnhancedBomStatisticsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedChartType = 'pie';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statisticsAsync = ref.watch(projectBomStatisticsProvider(widget.projectId));
    final costTrendAsync = ref.watch(bomCostTrendProvider(widget.projectId));
    final overdueItemsAsync = ref.watch(bomOverdueItemsProvider(widget.projectId));

    return Card(
      elevation: 4,
      margin: EdgeInsets.all(VanHubSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const Divider(),
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(icon: Icon(Icons.dashboard), text: '概览'),
              Tab(icon: Icon(Icons.bar_chart), text: '图表'),
              Tab(icon: Icon(Icons.warning), text: '逾期'),
            ],
          ),
          Expanded(
            child: statisticsAsync.when(
              data: (statistics) => _buildContent(
                context,
                statistics,
                costTrendAsync,
                overdueItemsAsync,
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 48, color: VanHubColors.error),
                    SizedBox(height: VanHubSpacing.md),
                    const Text('加载统计数据失败'),
                    SizedBox(height: VanHubSpacing.md),
                    ElevatedButton(
                      onPressed: () => ref.invalidate(projectBomStatisticsProvider(widget.projectId)),
                      child: const Text('重试'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(VanHubSpacing.md),
      child: Row(
        children: [
          Icon(Icons.analytics, color: VanHubColors.primary),
          SizedBox(width: VanHubSpacing.sm),
          Text(
            'BOM统计分析',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: VanHubColors.textPrimary,
            ),
          ),
          const Spacer(),
          _buildRefreshButton(),
          SizedBox(width: VanHubSpacing.sm),
          _buildChartTypeSelector(),
        ],
      ),
    );
  }

  Widget _buildRefreshButton() {
    return IconButton(
      icon: const Icon(Icons.refresh),
      onPressed: () {
        ref.invalidate(projectBomStatisticsProvider(widget.projectId));
        ref.invalidate(bomCostTrendProvider(widget.projectId));
        ref.invalidate(bomOverdueItemsProvider(widget.projectId));
      },
      tooltip: '刷新数据',
    );
  }

  Widget _buildChartTypeSelector() {
    return DropdownButton<String>(
      value: _selectedChartType,
      items: const [
        DropdownMenuItem(value: 'pie', child: Text('饼图')),
        DropdownMenuItem(value: 'bar', child: Text('柱状图')),
        DropdownMenuItem(value: 'line', child: Text('趋势图')),
      ],
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedChartType = value;
          });
        }
      },
    );
  }

  Widget _buildContent(
    BuildContext context,
    BomStatistics statistics,
    AsyncValue<Map<String, double>> costTrendAsync,
    AsyncValue<List<BomItem>> overdueItemsAsync,
  ) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(context, statistics),
        _buildChartsTab(context, statistics, costTrendAsync),
        _buildOverdueTab(context, overdueItemsAsync),
      ],
    );
  }

  Widget _buildOverviewTab(BuildContext context, BomStatistics statistics) {
    return Padding(
      padding: EdgeInsets.all(VanHubSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(context, statistics),
          SizedBox(height: VanHubSpacing.md),
          _buildProgressIndicator(context, statistics),
          SizedBox(height: VanHubSpacing.md),
          _buildBudgetAnalysis(context, statistics),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards(BuildContext context, BomStatistics statistics) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            '总项目',
            statistics.totalItems.toString(),
            Icons.list_alt,
            VanHubColors.primary,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: _buildStatCard(
            context,
            '已完成',
            statistics.completedItems.toString(),
            Icons.check_circle,
            VanHubColors.success,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: _buildStatCard(
            context,
            '总成本',
            '¥${_calculateTotalCost(statistics).toStringAsFixed(2)}',
            Icons.attach_money,
            VanHubColors.secondary,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: _buildStatCard(
            context,
            '完成率',
            '${_calculateCompletionRate(statistics).toStringAsFixed(1)}%',
            Icons.trending_up,
            _calculateCompletionRate(statistics) >= 75 ? VanHubColors.success : VanHubColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(VanHubSpacing.sm),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            SizedBox(height: VanHubSpacing.sm),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: VanHubColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context, BomStatistics statistics) {
    final completionRate = _calculateCompletionRate(statistics);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '项目进度',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: VanHubColors.textPrimary,
          ),
        ),
        SizedBox(height: VanHubSpacing.sm),
        LinearProgressIndicator(
          value: completionRate / 100,
          backgroundColor: VanHubColors.outlineVariant,
          valueColor: AlwaysStoppedAnimation<Color>(
            completionRate >= 75 ? VanHubColors.success : VanHubColors.warning,
          ),
        ),
        SizedBox(height: VanHubSpacing.xs),
        Text(
          '${statistics.completedItems}/${statistics.totalItems} 项目已完成',
          style: TextStyle(
            fontSize: 12,
            color: VanHubColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetAnalysis(BuildContext context, BomStatistics statistics) {
    final totalCost = _calculateTotalCost(statistics);
    final budget = statistics.totalBudget;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '预算分析',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: VanHubColors.textPrimary,
          ),
        ),
        SizedBox(height: VanHubSpacing.sm),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('总预算: ¥${budget.toStringAsFixed(2)}'),
                  Text('已花费: ¥${totalCost.toStringAsFixed(2)}'),
                  Text('剩余: ¥${statistics.remainingBudget.toStringAsFixed(2)}'),
                ],
              ),
            ),
            CircularProgressIndicator(
              value: budget > 0 ? totalCost / budget : 0,
              backgroundColor: VanHubColors.outlineVariant,
              valueColor: AlwaysStoppedAnimation<Color>(
                totalCost > budget ? VanHubColors.error : VanHubColors.success,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsTab(
    BuildContext context,
    BomStatistics statistics,
    AsyncValue<Map<String, double>> costTrendAsync,
  ) {
    if (!widget.showCharts) {
      return const Center(child: Text('图表功能已禁用'));
    }

    return Padding(
      padding: EdgeInsets.all(VanHubSpacing.md),
      child: Column(
        children: [
          Expanded(
            child: _buildChart(context, statistics),
          ),
          SizedBox(height: VanHubSpacing.md),
          costTrendAsync.when(
            data: (costTrend) => _buildCostTrendChart(context, costTrend),
            loading: () => const CircularProgressIndicator(),
            error: (_, __) => const Text('成本趋势加载失败'),
          ),
        ],
      ),
    );
  }

  Widget _buildChart(BuildContext context, BomStatistics statistics) {
    switch (_selectedChartType) {
      case 'pie':
        return _buildPieChart(context, statistics);
      case 'bar':
        return _buildBarChart(context, statistics);
      case 'line':
        return _buildLineChart(context, statistics);
      default:
        return _buildPieChart(context, statistics);
    }
  }

  Widget _buildPieChart(BuildContext context, BomStatistics statistics) {
    final categoryDistribution = _getCategoryDistribution(statistics);
    
    final sections = categoryDistribution.entries.map((entry) {
      final percentage = (entry.value / statistics.totalItems) * 100;
      return PieChartSectionData(
        color: _getCategoryColor(entry.key),
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 40,
        sectionsSpace: 2,
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            // 处理点击事件
            if (pieTouchResponse?.touchedSection != null) {
              final index = pieTouchResponse!.touchedSection!.touchedSectionIndex;
              if (index >= 0 && index < categoryDistribution.length) {
                final category = categoryDistribution.keys.elementAt(index);
                _showCategoryDetails(context, category, categoryDistribution[category]!.toDouble());
              }
            }
          },
        ),
      ),
    );
  }

  Widget _buildBarChart(BuildContext context, BomStatistics statistics) {
    // 实现柱状图
    return const Center(child: Text('柱状图开发中...'));
  }

  Widget _buildLineChart(BuildContext context, BomStatistics statistics) {
    // 实现折线图
    return const Center(child: Text('折线图开发中...'));
  }

  Widget _buildCostTrendChart(BuildContext context, Map<String, double> costTrend) {
    return SizedBox(
      height: 100,
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(VanHubSpacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '成本趋势',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: VanHubColors.textPrimary,
                ),
              ),
              Expanded(
                child: costTrend.isEmpty
                    ? const Center(child: Text('暂无趋势数据'))
                    : const Center(child: Text('趋势图表开发中...')),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverdueTab(BuildContext context, AsyncValue<List<BomItem>> overdueItemsAsync) {
    if (!widget.showOverdueItems) {
      return const Center(child: Text('逾期项目功能已禁用'));
    }

    return overdueItemsAsync.when(
      data: (overdueItems) => overdueItems.isEmpty
          ? const Center(child: Text('没有逾期项目'))
          : ListView.builder(
              padding: EdgeInsets.all(VanHubSpacing.md),
              itemCount: overdueItems.length,
              itemBuilder: (context, index) {
                final item = overdueItems[index];
                final totalCost = (item.estimatedPrice ?? 0) * item.quantity;
                return ListTile(
                  leading: Icon(Icons.warning, color: VanHubColors.error),
                  title: Text(item.materialName),
                  subtitle: Text('状态: ${_getStatusDisplayName(item.status)}'),
                  trailing: Text('¥${totalCost.toStringAsFixed(2)}'),
                );
              },
            ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Center(child: Text('加载逾期项目失败')),
    );
  }

  Color _getCategoryColor(String category) {
    // 为不同分类返回不同颜色
    final colors = [
      VanHubColors.primary,
      VanHubColors.secondary,
      VanHubColors.success,
      VanHubColors.warning,
      VanHubColors.info,
      VanHubColors.error,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
      Colors.cyan,
      Colors.lime,
    ];
    
    final index = category.hashCode % colors.length;
    return colors[index.abs()];
  }

  void _showCategoryDetails(BuildContext context, String category, double value) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(category),
        content: Text('数量: ${value.toInt()}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  // 辅助方法，计算完成率
  double _calculateCompletionRate(BomStatistics statistics) {
    if (statistics.totalItems == 0) return 0.0;
    return (statistics.completedItems / statistics.totalItems) * 100;
  }
  
  // 辅助方法，计算总成本
  double _calculateTotalCost(BomStatistics statistics) {
    return statistics.actualCost;
  }
  
  // 辅助方法，获取分类分布
  Map<String, int> _getCategoryDistribution(BomStatistics statistics) {
    // 将costByCategory转换为分类分布
    final Map<String, int> result = {};
    statistics.costByCategory.forEach((key, value) {
      // 将每个分类的成本转换为项目数量（简单估计）
      result[key] = (value / (statistics.averageItemCost > 0 ? statistics.averageItemCost : 1)).round();
    });
    
    // 如果没有分类数据，返回默认分布
    if (result.isEmpty) {
      return {'未分类': statistics.totalItems};
    }
    return result;
  }
  
  // 辅助方法，获取状态显示名称
  String _getStatusDisplayName(dynamic status) {
    if (status is BomItemStatus) {
      return status.displayName;
    }

    // 处理其他可能的状态类型
    try {
      final statusStr = status.toString().toLowerCase();
      if (statusStr.contains('planned') || statusStr.contains('pending')) return '待采购';
      if (statusStr.contains('purchased') || statusStr.contains('ordered')) return '已下单';
      if (statusStr.contains('received')) return '已收货';
      if (statusStr.contains('used') || statusStr.contains('installed')) return '已安装';
      if (statusStr.contains('cancelled')) return '已取消';
    } catch (e) {
      // 忽略错误，返回未知状态
    }
    
    return '未知';
  }
}