import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_material_request.freezed.dart';
part 'create_material_request.g.dart';

@freezed
class CreateMaterialRequest with _$CreateMaterialRequest {
  const factory CreateMaterialRequest({
    required String name,
    required String category,
    required double price,
    String? description,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? purchaseDate,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _CreateMaterialRequest;

  factory CreateMaterialRequest.fromJson(Map<String, dynamic> json) => _$CreateMaterialRequestFromJson(json);
}