import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 测试配置和工具类
class TestConfig {
  /// 初始化测试环境
  static Future<void> initializeTestEnvironment() async {
    // 设置SharedPreferences的测试值
    SharedPreferences.setMockInitialValues({});
    
    // 设置测试用的默认值
    TestWidgetsFlutterBinding.ensureInitialized();
  }

  /// 清理测试环境
  static Future<void> cleanupTestEnvironment() async {
    // 清理SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// 创建测试用的Material应用包装器
  static Widget createTestApp({required Widget child}) {
    return MaterialApp(
      home: Scaffold(body: child),
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
    );
  }

  /// 模拟网络延迟
  static Future<void> simulateNetworkDelay([Duration? delay]) async {
    await Future.delayed(delay ?? const Duration(milliseconds: 100));
  }

  /// 创建测试数据
  static Map<String, dynamic> createTestProjectData() {
    return {
      'id': 'test_project_1',
      'name': '测试项目',
      'description': '这是一个测试项目',
      'budget': 10000.0,
      'status': 'planning',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  static Map<String, dynamic> createTestMaterialData() {
    return {
      'id': 'test_material_1',
      'name': '测试材料',
      'description': '这是一个测试材料',
      'price': 100.0,
      'category': 'electrical',
      'brand': '测试品牌',
      'model': 'TEST-001',
      'specification': '测试规格',
      'unit': '个',
      'supplier': '测试供应商',
    };
  }

  static Map<String, dynamic> createTestBomItemData() {
    return {
      'id': 'test_bom_item_1',
      'materialId': 'test_material_1',
      'name': '测试BOM项',
      'quantity': 2,
      'unitPrice': 100.0,
      'totalPrice': 200.0,
      'supplier': '测试供应商',
      'status': 'pending',
      'notes': '测试备注',
    };
  }

  /// 测试用户数据
  static Map<String, dynamic> createTestUserData() {
    return {
      'id': 'test_user_1',
      'email': '<EMAIL>',
      'name': '测试用户',
      'avatar': 'https://example.com/avatar.jpg',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
}

/// 测试工具类
class TestUtils {
  /// 等待动画完成
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// 输入文本到指定字段
  static Future<void> enterTextInField(
    WidgetTester tester,
    String text,
    Finder finder,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// 点击并等待
  static Future<void> tapAndWait(
    WidgetTester tester,
    Finder finder,
  ) async {
    await tester.tap(finder);
    await waitForAnimations(tester);
  }

  /// 滚动到指定组件
  static Future<void> scrollToWidget(
    WidgetTester tester,
    Finder finder,
  ) async {
    await tester.scrollUntilVisible(
      finder,
      500.0,
      scrollable: find.byType(Scrollable).first,
    );
  }

  /// 验证文本存在
  static void expectTextExists(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// 验证组件存在
  static void expectWidgetExists<T>() {
    expect(find.byType(T), findsOneWidget);
  }

  /// 验证组件数量
  static void expectWidgetCount<T>(int count) {
    expect(find.byType(T), findsNWidgets(count));
  }
}

/// 模拟数据提供者
class MockDataProvider {
  static List<Map<String, dynamic>> getProjects() {
    return [
      TestConfig.createTestProjectData(),
      {
        ...TestConfig.createTestProjectData(),
        'id': 'test_project_2',
        'name': '测试项目2',
        'status': 'in_progress',
      },
      {
        ...TestConfig.createTestProjectData(),
        'id': 'test_project_3',
        'name': '测试项目3',
        'status': 'completed',
      },
    ];
  }

  static List<Map<String, dynamic>> getMaterials() {
    return [
      TestConfig.createTestMaterialData(),
      {
        ...TestConfig.createTestMaterialData(),
        'id': 'test_material_2',
        'name': '测试材料2',
        'category': 'water',
      },
      {
        ...TestConfig.createTestMaterialData(),
        'id': 'test_material_3',
        'name': '测试材料3',
        'category': 'interior',
      },
    ];
  }

  static List<Map<String, dynamic>> getBomItems() {
    return [
      TestConfig.createTestBomItemData(),
      {
        ...TestConfig.createTestBomItemData(),
        'id': 'test_bom_item_2',
        'name': '测试BOM项2',
        'status': 'ordered',
      },
      {
        ...TestConfig.createTestBomItemData(),
        'id': 'test_bom_item_3',
        'name': '测试BOM项3',
        'status': 'received',
      },
    ];
  }
}

/// 性能测试工具
class PerformanceTestUtils {
  /// 测量组件渲染时间
  static Future<Duration> measureRenderTime(
    WidgetTester tester,
    Widget widget,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
    
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// 测量滚动性能
  static Future<Duration> measureScrollPerformance(
    WidgetTester tester,
    Finder scrollable,
    double scrollDistance,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    await tester.drag(scrollable, Offset(0, -scrollDistance));
    await tester.pumpAndSettle();
    
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// 验证帧率
  static void verifyFrameRate(Duration renderTime, int expectedFps) {
    final actualFps = 1000 / renderTime.inMilliseconds;
    expect(actualFps, greaterThanOrEqualTo(expectedFps));
  }
}

/// 无障碍测试工具
class AccessibilityTestUtils {
  /// 验证语义化标签
  static void verifySemanticLabel(WidgetTester tester, Finder finder, String expectedLabel) {
    final semantics = tester.getSemantics(finder);
    expect(semantics.label, equals(expectedLabel));
  }

  /// 验证按钮语义
  static void verifyButtonSemantics(WidgetTester tester, Finder finder) {
    final semantics = tester.getSemantics(finder);
    expect(semantics.hasFlag(SemanticsFlag.isButton), isTrue);
  }

  /// 验证文本字段语义
  static void verifyTextFieldSemantics(WidgetTester tester, Finder finder) {
    final semantics = tester.getSemantics(finder);
    expect(semantics.hasFlag(SemanticsFlag.isTextField), isTrue);
  }

  /// 验证焦点顺序
  static Future<void> verifyFocusOrder(
    WidgetTester tester,
    List<Finder> finders,
  ) async {
    for (int i = 0; i < finders.length; i++) {
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();
      
      final focusedWidget = FocusManager.instance.primaryFocus;
      expect(focusedWidget, isNotNull);
    }
  }
}

/// 国际化测试工具
class L10nTestUtils {
  /// 测试语言切换
  static Future<void> testLanguageSwitch(
    WidgetTester tester,
    Widget app,
    Locale newLocale,
  ) async {
    await tester.pumpWidget(app);
    
    // 模拟语言切换
    await tester.binding.setLocale(newLocale.languageCode, newLocale.countryCode ?? '');
    await tester.pump();
  }

  /// 验证本地化文本
  static void verifyLocalizedText(String key, String expectedText) {
    expect(find.text(expectedText), findsOneWidget);
  }

  /// 验证RTL布局
  static void verifyRTLLayout(WidgetTester tester, Widget widget) {
    // 设置RTL语言环境
    tester.binding.window.localeTestValue = const Locale('ar', 'SA');
    
    // 验证文本方向
    final directionality = tester.widget<Directionality>(
      find.byType(Directionality).first,
    );
    expect(directionality.textDirection, equals(TextDirection.rtl));
  }
}