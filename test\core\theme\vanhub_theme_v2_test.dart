import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/theme/vanhub_theme_v2.dart';

void main() {
  group('VanHubThemeV2', () {
    test('应该创建浅色主题', () {
      final theme = VanHubThemeV2.getLightTheme();
      
      expect(theme.brightness, equals(Brightness.light));
      expect(theme.useMaterial3, isTrue);
      expect(theme.colorScheme.brightness, equals(Brightness.light));
    });

    test('应该创建深色主题', () {
      final theme = VanHubThemeV2.getDarkTheme();
      
      expect(theme.brightness, equals(Brightness.dark));
      expect(theme.useMaterial3, isTrue);
      expect(theme.colorScheme.brightness, equals(Brightness.dark));
    });

    test('应该支持字体缩放', () {
      final normalTheme = VanHubThemeV2.getLightTheme(textScale: 'normal');
      final largeTheme = VanHubThemeV2.getLightTheme(textScale: 'large');
      
      expect(normalTheme.textTheme.bodyLarge!.fontSize, equals(16.0));
      expect(largeTheme.textTheme.bodyLarge!.fontSize, equals(18.4)); // 16 * 1.15
    });

    test('应该支持动画减少选项', () {
      final normalTheme = VanHubThemeV2.getLightTheme(reduceAnimations: false);
      final reducedTheme = VanHubThemeV2.getLightTheme(reduceAnimations: true);
      
      expect(normalTheme.pageTransitionsTheme, isNotNull);
      expect(reducedTheme.pageTransitionsTheme, isNotNull);
    });

    test('应该包含完整的组件主题', () {
      final theme = VanHubThemeV2.getLightTheme();
      
      expect(theme.appBarTheme, isNotNull);
      expect(theme.navigationBarTheme, isNotNull);
      expect(theme.cardTheme, isNotNull);
      expect(theme.filledButtonTheme, isNotNull);
      expect(theme.outlinedButtonTheme, isNotNull);
      expect(theme.textButtonTheme, isNotNull);
      expect(theme.inputDecorationTheme, isNotNull);
      expect(theme.dialogTheme, isNotNull);
      expect(theme.bottomSheetTheme, isNotNull);
    });

    test('应该支持动态颜色方案', () {
      final customColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.purple,
        brightness: Brightness.light,
      );
      
      final theme = VanHubThemeV2.getLightTheme(
        dynamicColorScheme: customColorScheme,
      );
      
      expect(theme.colorScheme.primary, equals(customColorScheme.primary));
    });

    testWidgets('应该正确应用主题到应用', (WidgetTester tester) async {
      final theme = VanHubThemeV2.getLightTheme();
      
      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: const Scaffold(
            body: Text('测试'),
          ),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme, equals(theme));
    });
  });
}