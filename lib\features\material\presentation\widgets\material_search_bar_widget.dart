import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';

/// 材料搜索栏组件
/// 
/// 特性：
/// - 实时搜索防抖
/// - 搜索历史记录
/// - 智能建议
/// - 筛选器快速访问
class MaterialSearchBarWidget extends ConsumerStatefulWidget {
  final Function(String) onSearchChanged;
  final VoidCallback? onFilterTap;
  final String? initialQuery;
  final String? hintText;

  const MaterialSearchBarWidget({
    super.key,
    required this.onSearchChanged,
    this.onFilterTap,
    this.initialQuery,
    this.hintText,
  });

  @override
  ConsumerState<MaterialSearchBarWidget> createState() => _MaterialSearchBarWidgetState();
}

class _MaterialSearchBarWidgetState extends ConsumerState<MaterialSearchBarWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  
  // 防抖定时器
  Timer? _debounceTimer;
  
  // 搜索状态
  bool _isSearching = false;
  List<String> _searchSuggestions = [];
  List<String> _searchHistory = [];

  // 搜索历史存储键
  static const String _searchHistoryKey = 'material_search_history';
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _focusNode = FocusNode();

    // 监听焦点变化
    _focusNode.addListener(_onFocusChanged);

    // 加载搜索历史
    _loadSearchHistory();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }
  
  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _loadSearchSuggestions();
    }
  }
  
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _debounceTimer?.cancel();

    // 设置新的防抖定时器
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      widget.onSearchChanged(query);
      _updateSearchSuggestions(query);

      // 如果查询不为空，保存到搜索历史
      if (query.trim().isNotEmpty) {
        _saveToSearchHistory(query.trim());
      }
    });
  }

  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_searchHistoryKey);
      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        setState(() {
          _searchHistory = historyList.cast<String>();
        });
      }
    } catch (e) {
      // 忽略加载错误
      debugPrint('加载搜索历史失败: $e');
    }
  }

  /// 保存搜索历史
  Future<void> _saveToSearchHistory(String query) async {
    try {
      // 移除重复项
      _searchHistory.remove(query);
      // 添加到开头
      _searchHistory.insert(0, query);
      // 限制历史记录数量
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.take(10).toList();
      }

      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_searchHistoryKey, jsonEncode(_searchHistory));
    } catch (e) {
      debugPrint('保存搜索历史失败: $e');
    }
  }

  /// 清除搜索历史
  Future<void> _clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
      setState(() {
        _searchHistory.clear();
      });
    } catch (e) {
      debugPrint('清除搜索历史失败: $e');
    }
  }
  
  void _loadSearchSuggestions() {
    // 整合搜索历史和热门搜索
    final hotSearches = [
      '锂电池',
      '太阳能板',
      '逆变器',
      '水泵',
      'LED灯条',
      '房车冰箱',
      '驻车空调',
      '房车马桶',
      '储水箱',
      '燃气灶',
    ];

    setState(() {
      _searchSuggestions = [
        // 优先显示搜索历史
        ..._searchHistory.take(5),
        // 然后显示热门搜索（排除已在历史中的）
        ...hotSearches.where((item) => !_searchHistory.contains(item)).take(5),
      ];
    });
  }

  void _updateSearchSuggestions(String query) {
    if (query.isEmpty) {
      _loadSearchSuggestions();
      return;
    }

    // 智能搜索建议：匹配名称、品牌、分类
    final queryLower = query.toLowerCase();
    final allSuggestions = [
      // 材料名称建议
      '锂电池', '磷酸铁锂电池', '三元锂电池',
      '太阳能板', '单晶硅太阳能板', '多晶硅太阳能板',
      '逆变器', '正弦波逆变器', '修正波逆变器',
      '水泵', '增压水泵', '潜水泵', '自吸泵',
      'LED灯', 'LED灯条', 'LED射灯', 'LED吸顶灯',

      // 品牌建议
      'Victron Energy', 'Renogy', 'Goal Zero', 'Dometic',
      'Thule', 'Yakima', 'Fiamma', 'Truma', 'Webasto',

      // 分类建议
      '电力系统', '水系统', '照明系统', '储物系统',
      '床铺系统', '厨房系统', '卫浴系统', '通风系统',
    ];

    setState(() {
      _searchSuggestions = allSuggestions
          .where((suggestion) => suggestion.toLowerCase().contains(queryLower))
          .take(8) // 限制建议数量
          .toList();
    });
  }
  
  void _clearSearch() {
    _controller.clear();
    widget.onSearchChanged('');
    _loadSearchSuggestions();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        if (_focusNode.hasFocus && _searchSuggestions.isNotEmpty)
          _buildSuggestionsList(),
      ],
    );
  }
  
  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(context.responsiveValue(
          xs: 8,
          sm: 10,
          md: 12,
          lg: 14,
          xl: 16,
          defaultValue: 12,
        )),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: context.responsiveValue(
              xs: 4,
              sm: 6,
              md: 8,
              lg: 10,
              xl: 12,
              defaultValue: 8,
            ),
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: widget.hintText ?? (context.isMobile
              ? '搜索材料...'
              : '搜索材料名称、品牌、型号...'),
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: context.responsiveValue(
              xs: 14,
              sm: 15,
              md: 16,
              lg: 16,
              xl: 17,
              defaultValue: 16,
            ),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: context.responsiveValue(
              xs: 20,
              sm: 22,
              md: 24,
              lg: 24,
              xl: 26,
              defaultValue: 24,
            ),
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_controller.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                  tooltip: '清除搜索',
                ),
              if (widget.onFilterTap != null)
                IconButton(
                  icon: const Icon(Icons.tune),
                  onPressed: widget.onFilterTap,
                  tooltip: '筛选选项',
                ),
            ],
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: context.responsiveValue(
              xs: 12,
              sm: 14,
              md: 16,
              lg: 18,
              xl: 20,
              defaultValue: 16,
            ),
            vertical: context.responsiveValue(
              xs: 10,
              sm: 11,
              md: 12,
              lg: 13,
              xl: 14,
              defaultValue: 12,
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSuggestionsList() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 搜索历史部分
          if (_searchHistory.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
              child: Row(
                children: [
                  Icon(
                    Icons.history,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '搜索历史',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: _clearSearchHistory,
                    style: TextButton.styleFrom(
                      minimumSize: Size.zero,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    child: Text(
                      '清除',
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                  ),
                ],
              ),
            ),
            ...(_searchHistory.take(3).map((suggestion) => _buildSuggestionItem(
              suggestion,
              Icons.history,
              isHistory: true,
            ))),
          ],

          // 热门搜索部分
          if (_searchSuggestions.where((item) => !_searchHistory.contains(item)).isNotEmpty) ...[
            if (_searchHistory.isNotEmpty) const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '热门搜索',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            ...(_searchSuggestions.where((item) => !_searchHistory.contains(item)).take(5).map((suggestion) => _buildSuggestionItem(
              suggestion,
              Icons.trending_up,
              isHistory: false,
            ))),
          ],
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion, IconData icon, {required bool isHistory}) {
    return ListTile(
      dense: true,
      leading: Icon(
        icon,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        size: 18,
      ),
      title: Text(
        suggestion,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      onTap: () {
        _controller.text = suggestion;
        widget.onSearchChanged(suggestion);
        _focusNode.unfocus();
      },
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.north_west, size: 16),
            onPressed: () {
              _controller.text = suggestion;
              widget.onSearchChanged(suggestion);
            },
            tooltip: '填入搜索框',
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
          if (isHistory)
            IconButton(
              icon: const Icon(Icons.close, size: 16),
              onPressed: () => _removeFromHistory(suggestion),
              tooltip: '删除历史',
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
        ],
      ),
    );
  }

  /// 从搜索历史中移除指定项
  Future<void> _removeFromHistory(String query) async {
    setState(() {
      _searchHistory.remove(query);
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_searchHistoryKey, jsonEncode(_searchHistory));
    } catch (e) {
      debugPrint('移除搜索历史失败: $e');
    }

    // 重新加载建议
    _loadSearchSuggestions();
  }
}


