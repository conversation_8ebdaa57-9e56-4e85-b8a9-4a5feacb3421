import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/dashboard_data.dart';
import '../../../project/domain/repositories/project_repository.dart';
import '../../../bom/domain/repositories/bom_repository.dart';
import '../../../modification_log/domain/repositories/log_repository.dart';
import '../../../modification_log/domain/repositories/timeline_repository.dart';
import '../../../modification_log/domain/entities/timeline.dart';

/// 仪表盘数据服务
/// 负责聚合各个模块的数据，生成仪表盘展示内容
class DashboardService {
  final ProjectRepository projectRepository;
  final BomRepository bomRepository;
  final LogRepository logRepository;
  final TimelineRepository timelineRepository;

  DashboardService({
    required this.projectRepository,
    required this.bomRepository,
    required this.logRepository,
    required this.timelineRepository,
  });

  /// 获取项目仪表盘数据
  Future<Either<Failure, DashboardData>> getProjectDashboard(String projectId) async {
    try {
      // 并行获取各模块数据
      final results = await Future.wait([
        _getProjectOverview(projectId),
        _getCostAnalysis(projectId),
        _getProgressTracking(projectId),
        _getSmartInsights(projectId),
        _getRecentActivities(projectId),
      ]);

      // 检查是否有失败的结果
      for (final result in results) {
        if (result.isLeft()) {
          return result.cast<DashboardData>();
        }
      }

      // 组装仪表盘数据
      final dashboardData = DashboardData(
        projectId: projectId,
        projectOverview: results[0].getRight().getOrElse(() => throw Exception('Project overview failed')),
        costAnalysis: results[1].getRight().getOrElse(() => throw Exception('Cost analysis failed')),
        progressTracking: results[2].getRight().getOrElse(() => throw Exception('Progress tracking failed')),
        smartInsights: results[3].getRight().getOrElse(() => throw Exception('Smart insights failed')),
        recentActivities: results[4].getRight().getOrElse(() => throw Exception('Recent activities failed')),
        lastUpdated: DateTime.now(),
      );

      return Right(dashboardData);
    } catch (e) {
      return Left(ServerFailure(message: '获取仪表盘数据失败: $e'));
    }
  }

  /// 获取项目概览数据
  Future<Either<Failure, ProjectOverview>> _getProjectOverview(String projectId) async {
    try {
      // 获取项目基本信息
      final projectResult = await projectRepository.getProject(projectId);
      if (projectResult.isLeft()) {
        return projectResult.cast<ProjectOverview>();
      }
      
      final project = projectResult.getRight().getOrElse(() => throw Exception('Project not found'));

      // 获取BOM数据计算成本
      final bomResult = await bomRepository.getProjectBomItems(projectId);
      final bomItems = bomResult.getRight().getOrElse(() => []);
      
      final totalBudget = project.budget ?? 0.0;
      final budgetUsed = bomItems.fold(0.0, (sum, item) => sum + (item.totalPrice ?? 0.0));

      // 获取时间轴数据计算进度
      final timelineResult = await timelineRepository.getProjectTimeline(projectId);
      final timeline = timelineResult.getRight().getOrElse(() => throw Exception('Timeline not found'));
      
      final totalTasks = timeline.items.length;
      final completedTasks = timeline.items.where((item) => 
        item.map(
          logEntry: (entry) => entry.status == 'completed',
          milestone: (milestone) => milestone.status == 'completed',
        )
      ).length;
      
      final overallProgress = totalTasks > 0 ? completedTasks / totalTasks : 0.0;

      // 计算工时
      final logsResult = await logRepository.getProjectLogs(projectId);
      final logs = logsResult.getRight().getOrElse(() => []);
      final totalTimeSpent = logs.fold(0, (sum, log) => sum + log.timeSpentMinutes);

      return Right(ProjectOverview(
        overallProgress: overallProgress,
        budgetUsed: budgetUsed,
        totalBudget: totalBudget,
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        totalTimeSpent: totalTimeSpent,
        estimatedTimeRemaining: _calculateEstimatedTime(overallProgress, totalTimeSpent),
        status: _getProjectStatus(overallProgress, budgetUsed, totalBudget),
        startDate: project.createdAt,
        expectedEndDate: project.expectedCompletionDate,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '获取项目概览失败: $e'));
    }
  }

  /// 获取成本分析数据
  Future<Either<Failure, CostAnalysis>> _getCostAnalysis(String projectId) async {
    try {
      final bomResult = await bomRepository.getProjectBomItems(projectId);
      final bomItems = bomResult.getRight().getOrElse(() => []);

      final totalCost = bomItems.fold(0.0, (sum, item) => sum + (item.totalPrice ?? 0.0));
      final plannedCost = bomItems.fold(0.0, (sum, item) => sum + (item.unitPrice * item.quantity));

      // 按分类统计成本
      final categoryMap = <String, double>{};
      for (final item in bomItems) {
        final category = item.category;
        categoryMap[category] = (categoryMap[category] ?? 0.0) + (item.totalPrice ?? 0.0);
      }

      final categoryCosts = categoryMap.entries.map((entry) => CategoryCost(
        categoryId: entry.key,
        categoryName: _getCategoryDisplayName(entry.key),
        amount: entry.value,
        percentage: totalCost > 0 ? entry.value / totalCost : 0.0,
        colorHex: _getCategoryColor(entry.key),
      )).toList();

      // 生成模拟的月度支出数据
      final monthlySpending = _generateMonthlySpending(totalCost);
      
      // 生成成本趋势数据
      final costTrends = _generateCostTrends(totalCost, plannedCost);

      // 生成成本预警
      final costAlerts = _generateCostAlerts(totalCost, plannedCost);

      return Right(CostAnalysis(
        totalCost: totalCost,
        plannedCost: plannedCost,
        actualCost: totalCost,
        categoryCosts: categoryCosts,
        monthlySpending: monthlySpending,
        costTrends: costTrends,
        costAlerts: costAlerts,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '获取成本分析失败: $e'));
    }
  }

  /// 获取进度跟踪数据
  Future<Either<Failure, ProgressTracking>> _getProgressTracking(String projectId) async {
    try {
      final timelineResult = await timelineRepository.getProjectTimeline(projectId);
      final timeline = timelineResult.getRight().getOrElse(() => throw Exception('Timeline not found'));

      // 提取里程碑进度
      final milestones = timeline.items
          .where((item) => item.map(
            logEntry: (_) => false,
            milestone: (_) => true,
          ))
          .map((item) => item.map(
            logEntry: (_) => throw Exception('Not a milestone'),
            milestone: (milestone) => MilestoneProgress(
              id: milestone.id,
              title: milestone.title,
              progress: milestone.status == 'completed' ? 1.0 : 0.5,
              status: _parseMilestoneStatus(milestone.status),
              dueDate: DateTime.tryParse(milestone.date),
              completedDate: milestone.status == 'completed' ? DateTime.tryParse(milestone.date) : null,
            ),
          ))
          .toList();

      // 生成系统进度数据
      final systemProgress = _generateSystemProgress(timeline.items);

      // 获取最近事件
      final recentEvents = timeline.items.take(10).map((item) => TimelineEvent(
        id: item.itemId,
        title: item.itemTitle,
        description: item.itemDescription ?? '',
        date: item.itemDate,
        type: EventType.milestone,
        systemId: item.itemSystemId,
        systemName: _getSystemDisplayName(item.itemSystemId ?? 'unknown'),
      )).toList();

      // 获取工时分析
      final logsResult = await logRepository.getProjectLogs(projectId);
      final logs = logsResult.getRight().getOrElse(() => []);
      final workTimeAnalysis = _generateWorkTimeAnalysis(logs);

      return Right(ProgressTracking(
        milestones: milestones,
        systemProgress: systemProgress,
        recentEvents: recentEvents,
        workTimeAnalysis: workTimeAnalysis,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '获取进度跟踪失败: $e'));
    }
  }

  /// 获取智能洞察
  Future<Either<Failure, List<SmartInsight>>> _getSmartInsights(String projectId) async {
    try {
      final insights = <SmartInsight>[];

      // 基于成本分析生成洞察
      final costAnalysisResult = await _getCostAnalysis(projectId);
      if (costAnalysisResult.isRight()) {
        final costAnalysis = costAnalysisResult.getRight().getOrElse(() => throw Exception('Cost analysis failed'));
        
        if (costAnalysis.actualCost > costAnalysis.plannedCost * 1.1) {
          insights.add(SmartInsight(
            id: 'cost_overrun',
            title: '成本超支预警',
            description: '当前实际成本已超出计划成本${((costAnalysis.actualCost / costAnalysis.plannedCost - 1) * 100).toStringAsFixed(1)}%',
            type: InsightType.cost,
            priority: InsightPriority.high,
            actionText: '查看详细分析',
            actionRoute: '/cost-analysis',
            createdAt: DateTime.now(),
          ));
        }
      }

      // 基于进度分析生成洞察
      final progressResult = await _getProgressTracking(projectId);
      if (progressResult.isRight()) {
        final progress = progressResult.getRight().getOrElse(() => throw Exception('Progress tracking failed'));
        
        final overdueCount = progress.milestones.where((m) => 
          m.dueDate != null && 
          m.dueDate!.isBefore(DateTime.now()) && 
          m.status != MilestoneStatus.completed
        ).length;
        
        if (overdueCount > 0) {
          insights.add(SmartInsight(
            id: 'overdue_milestones',
            title: '里程碑延期提醒',
            description: '有${overdueCount}个里程碑已延期，建议调整计划',
            type: InsightType.progress,
            priority: InsightPriority.medium,
            actionText: '查看时间轴',
            actionRoute: '/timeline',
            createdAt: DateTime.now(),
          ));
        }
      }

      return Right(insights);
    } catch (e) {
      return Left(ServerFailure(message: '获取智能洞察失败: $e'));
    }
  }

  /// 获取最近活动
  Future<Either<Failure, List<RecentActivity>>> _getRecentActivities(String projectId) async {
    try {
      final logsResult = await logRepository.getProjectLogs(projectId);
      final logs = logsResult.getRight().getOrElse(() => []);

      final activities = logs.take(10).map((log) => RecentActivity(
        id: log.id,
        title: '创建了改装日志',
        description: log.title,
        timestamp: log.createdAt,
        type: ActivityType.logCreated,
        userId: log.authorId,
        userName: log.authorName,
      )).toList();

      return Right(activities);
    } catch (e) {
      return Left(ServerFailure(message: '获取最近活动失败: $e'));
    }
  }

  // 辅助方法
  int _calculateEstimatedTime(double progress, int totalTimeSpent) {
    if (progress <= 0) return 0;
    final estimatedTotal = totalTimeSpent / progress;
    return (estimatedTotal - totalTimeSpent).round();
  }

  ProjectStatus _getProjectStatus(double progress, double budgetUsed, double totalBudget) {
    if (progress >= 1.0) return ProjectStatus.completed;
    if (budgetUsed > totalBudget * 1.2) return ProjectStatus.onHold;
    if (progress > 0) return ProjectStatus.inProgress;
    return ProjectStatus.planning;
  }

  String _getCategoryDisplayName(String category) {
    const categoryNames = {
      'electrical': '电力系统',
      'water': '水路系统',
      'interior': '内饰改装',
      'exterior': '外观改装',
      'storage': '储物方案',
      'bed': '床铺设计',
      'kitchen': '厨房改装',
      'bathroom': '卫浴改装',
      'roof': '车顶改装',
      'chassis': '底盘改装',
      'other': '其他配件',
    };
    return categoryNames[category] ?? category;
  }

  String _getCategoryColor(String category) {
    const categoryColors = {
      'electrical': '#FF6B6B',
      'water': '#4ECDC4',
      'interior': '#45B7D1',
      'exterior': '#96CEB4',
      'storage': '#FFEAA7',
      'bed': '#DDA0DD',
      'kitchen': '#98D8C8',
      'bathroom': '#F7DC6F',
      'roof': '#BB8FCE',
      'chassis': '#85C1E9',
      'other': '#D5DBDB',
    };
    return categoryColors[category] ?? '#D5DBDB';
  }

  List<MonthlySpending> _generateMonthlySpending(double totalCost) {
    // 生成过去6个月的模拟数据
    final now = DateTime.now();
    final months = <MonthlySpending>[];
    
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthStr = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      final amount = totalCost * (0.1 + (5 - i) * 0.15); // 递增支出模式
      
      months.add(MonthlySpending(
        month: monthStr,
        amount: amount,
        budgetAmount: totalCost * 0.2, // 假设每月预算为总预算的20%
      ));
    }
    
    return months;
  }

  List<CostTrend> _generateCostTrends(double totalCost, double plannedCost) {
    // 生成过去30天的成本趋势
    final now = DateTime.now();
    final trends = <CostTrend>[];
    
    for (int i = 29; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final progress = (29 - i) / 29.0;
      final cumulativeCost = totalCost * progress;
      final plannedCostAtDate = plannedCost * progress;
      
      trends.add(CostTrend(
        date: date,
        cumulativeCost: cumulativeCost,
        plannedCost: plannedCostAtDate,
      ));
    }
    
    return trends;
  }

  List<CostAlert> _generateCostAlerts(double totalCost, double plannedCost) {
    final alerts = <CostAlert>[];
    
    if (totalCost > plannedCost * 1.1) {
      alerts.add(CostAlert(
        id: 'budget_overrun',
        title: '预算超支',
        description: '当前支出已超出计划预算',
        severity: AlertSeverity.high,
        threshold: plannedCost,
        currentValue: totalCost,
        createdAt: DateTime.now(),
      ));
    }
    
    return alerts;
  }

  List<SystemProgress> _generateSystemProgress(List<dynamic> timelineItems) {
    // 根据时间轴项目生成系统进度
    final systemMap = <String, List<dynamic>>{};
    
    for (final item in timelineItems) {
      final systemId = item.itemSystemId ?? 'unknown';
      systemMap[systemId] = (systemMap[systemId] ?? [])..add(item);
    }
    
    return systemMap.entries.map((entry) {
      final systemId = entry.key;
      final items = entry.value;
      final completedItems = items.where((item) => 
        item.map(
          logEntry: (entry) => entry.status == 'completed',
          milestone: (milestone) => milestone.status == 'completed',
        )
      ).length;
      
      return SystemProgress(
        systemId: systemId,
        systemName: _getSystemDisplayName(systemId),
        progress: items.isNotEmpty ? completedItems / items.length : 0.0,
        totalTasks: items.length,
        completedTasks: completedItems,
        colorHex: _getCategoryColor(systemId),
      );
    }).toList();
  }

  String _getSystemDisplayName(String systemId) {
    const systemNames = {
      'electrical': '电力系统',
      'water': '水路系统',
      'interior': '内饰系统',
      'exterior': '外观系统',
      'storage': '储物系统',
      'bed': '床铺系统',
      'kitchen': '厨房系统',
      'bathroom': '卫浴系统',
      'unknown': '其他系统',
    };
    return systemNames[systemId] ?? systemId;
  }

  WorkTimeAnalysis _generateWorkTimeAnalysis(List<dynamic> logs) {
    final totalMinutes = logs.fold<int>(0, (sum, log) => sum + ((log.timeSpentMinutes ?? 0) as int));
    final totalHours = (totalMinutes / 60).round();

    // 计算本周和本月工时
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final monthStart = DateTime(now.year, now.month, 1);

    final thisWeekLogs = logs.where((log) => log.logDate.isAfter(weekStart)).toList();
    final thisMonthLogs = logs.where((log) => log.logDate.isAfter(monthStart)).toList();

    final thisWeekMinutes = thisWeekLogs.fold<int>(0, (sum, log) => sum + ((log.timeSpentMinutes ?? 0) as int));
    final thisMonthMinutes = thisMonthLogs.fold<int>(0, (sum, log) => sum + ((log.timeSpentMinutes ?? 0) as int));
    
    // 生成每日工时数据
    final dailyWorkTime = <DailyWorkTime>[];
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayLogs = logs.where((log) => 
        log.logDate.year == date.year &&
        log.logDate.month == date.month &&
        log.logDate.day == date.day
      ).toList();
      
      final dayMinutes = dayLogs.fold<int>(0, (sum, log) => sum + ((log.timeSpentMinutes ?? 0) as int));
      
      dailyWorkTime.add(DailyWorkTime(
        date: date,
        minutes: dayMinutes,
      ));
    }
    
    return WorkTimeAnalysis(
      totalHours: totalHours,
      thisWeekHours: (thisWeekMinutes / 60).round(),
      thisMonthHours: (thisMonthMinutes / 60).round(),
      averageHoursPerDay: logs.isNotEmpty ? totalHours / logs.length : 0.0,
      dailyWorkTime: dailyWorkTime,
    );
  }

  MilestoneStatus _parseMilestoneStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return MilestoneStatus.completed;
      case 'in_progress':
        return MilestoneStatus.inProgress;
      case 'overdue':
        return MilestoneStatus.overdue;
      default:
        return MilestoneStatus.planned;
    }
  }
}
