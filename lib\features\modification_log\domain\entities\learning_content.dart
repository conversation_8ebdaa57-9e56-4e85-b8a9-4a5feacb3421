import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'learning_content.freezed.dart';
part 'learning_content.g.dart';

/// 学习内容实体
/// 代表从改装日志中提炼出的知识和经验
@freezed
class LearningContent with _$LearningContent {
  const factory LearningContent({
    /// 学习内容唯一标识
    required String id,
    
    /// 关联的日志条目ID
    required String logEntryId,
    
    /// 关联的项目ID
    required String projectId,
    
    /// 关联的系统ID
    String? systemId,
    
    /// 内容标题
    required String title,
    
    /// 内容描述
    String? description,
    
    /// 知识类型
    required LearningContentType contentType,
    
    /// 难度级别
    @Default(DifficultyLevel.intermediate) DifficultyLevel difficulty,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 创建者ID
    required String creatorId,
    
    /// 创建者名称
    String? creatorName,
    
    /// 核心要点列表
    @Default([]) List<String> keyPoints,
    
    /// 常见问题列表
    @Default([]) List<String> commonProblems,
    
    /// 解决方案列表
    @Default([]) List<String> solutions,
    
    /// 实用技巧列表
    @Default([]) List<String> tips,
    
    /// 工具清单
    @Default([]) List<String> toolsList,
    
    /// 材料清单
    @Default([]) List<String> materialsList,
    
    /// 成本范围
    String? costRange,
    
    /// 时间范围
    String? timeRange,
    
    /// 前置条件
    @Default([]) List<String> prerequisites,
    
    /// 后续步骤
    @Default([]) List<String> nextSteps,
    
    /// 相关案例ID列表
    @Default([]) List<String> relatedCaseIds,
    
    /// 标签列表
    @Default([]) List<String> tags,
    
    /// 学习价值评分 (1-5)
    @Default(3.0) double learningValue,
    
    /// 实用性评分 (1-5)
    @Default(3.0) double practicalValue,
    
    /// 创新性评分 (1-5)
    @Default(3.0) double innovationValue,
    
    /// 被收藏次数
    @Default(0) int bookmarkCount,
    
    /// 被引用次数
    @Default(0) int referenceCount,
    
    /// 浏览次数
    @Default(0) int viewCount,
    
    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,
    
    /// 收藏用户ID列表
    @Default([]) List<String> bookmarkedByUserIds,
    
    /// 是否为精华内容
    @Default(false) bool isFeatured,
    
    /// 是否为官方认证
    @Default(false) bool isVerified,
    
    /// 状态
    @Default(LogStatus.published) LogStatus status,
    
    /// 元数据
    Map<String, dynamic>? metadata,
  }) = _LearningContent;

  factory LearningContent.fromJson(Map<String, dynamic> json) =>
      _$LearningContentFromJson(json);
}

/// 学习内容类型枚举
@JsonEnum()
enum LearningContentType {
  /// 核心经验
  @JsonValue('core_experience')
  coreExperience,
  
  /// 避坑指南
  @JsonValue('pitfall_guide')
  pitfallGuide,
  
  /// 实用技巧
  @JsonValue('practical_tips')
  practicalTips,
  
  /// 工具推荐
  @JsonValue('tool_recommendation')
  toolRecommendation,
  
  /// 材料对比
  @JsonValue('material_comparison')
  materialComparison,
  
  /// 成本分析
  @JsonValue('cost_analysis')
  costAnalysis,
  
  /// 时间规划
  @JsonValue('time_planning')
  timePlanning,
  
  /// 安全注意
  @JsonValue('safety_notes')
  safetyNotes,
  
  /// 创新方案
  @JsonValue('innovative_solution')
  innovativeSolution,
  
  /// 其他
  @JsonValue('other')
  other;
  
  String get displayName {
    switch (this) {
      case LearningContentType.coreExperience:
        return '核心经验';
      case LearningContentType.pitfallGuide:
        return '避坑指南';
      case LearningContentType.practicalTips:
        return '实用技巧';
      case LearningContentType.toolRecommendation:
        return '工具推荐';
      case LearningContentType.materialComparison:
        return '材料对比';
      case LearningContentType.costAnalysis:
        return '成本分析';
      case LearningContentType.timePlanning:
        return '时间规划';
      case LearningContentType.safetyNotes:
        return '安全注意';
      case LearningContentType.innovativeSolution:
        return '创新方案';
      case LearningContentType.other:
        return '其他';
    }
  }
}

/// LearningContent扩展方法
extension LearningContentX on LearningContent {
  /// 获取内容类型显示文本
  String get contentTypeDisplayText => contentType.displayName;
  
  /// 获取难度级别显示文本
  String get difficultyDisplayText => difficulty.displayName;
  
  /// 获取状态显示文本
  String get statusDisplayText => status.displayName;
  
  /// 获取综合评分
  double get overallRating => (learningValue + practicalValue + innovationValue) / 3;
  
  /// 获取综合评分显示文本
  String get overallRatingDisplayText => '${overallRating.toStringAsFixed(1)}分';
  
  /// 获取内容摘要
  String get summary {
    if (description != null && description!.isNotEmpty) {
      if (description!.length <= 100) {
        return description!;
      } else {
        return '${description!.substring(0, 97)}...';
      }
    }
    return title;
  }
  
  /// 获取关键要点数量
  int get keyPointCount => keyPoints.length;
  
  /// 获取常见问题数量
  int get problemCount => commonProblems.length;
  
  /// 获取解决方案数量
  int get solutionCount => solutions.length;
  
  /// 获取技巧数量
  int get tipCount => tips.length;
  
  /// 获取工具数量
  int get toolCount => toolsList.length;
  
  /// 获取材料数量
  int get materialCount => materialsList.length;
  
  /// 获取点赞数量
  int get likeCount => likedByUserIds.length;
  
  /// 获取相关案例数量
  int get relatedCaseCount => relatedCaseIds.length;
  
  /// 是否为高价值内容
  bool get isHighValue => overallRating >= 4.0;
  
  /// 是否为热门内容
  bool get isPopular => viewCount >= 100 || bookmarkCount >= 20;
  
  /// 是否为完整内容
  bool get isComplete => keyPoints.isNotEmpty && 
                        (solutions.isNotEmpty || tips.isNotEmpty);
  
  /// 获取内容类型图标
  String get contentTypeIcon {
    switch (contentType) {
      case LearningContentType.coreExperience:
        return '🎯';
      case LearningContentType.pitfallGuide:
        return '⚠️';
      case LearningContentType.practicalTips:
        return '💡';
      case LearningContentType.toolRecommendation:
        return '🔧';
      case LearningContentType.materialComparison:
        return '📊';
      case LearningContentType.costAnalysis:
        return '💰';
      case LearningContentType.timePlanning:
        return '⏰';
      case LearningContentType.safetyNotes:
        return '🛡️';
      case LearningContentType.innovativeSolution:
        return '🚀';
      case LearningContentType.other:
        return '📝';
    }
  }
  
  /// 获取内容类型颜色
  String get contentTypeColor {
    switch (contentType) {
      case LearningContentType.coreExperience:
        return '#4CAF50'; // 绿色
      case LearningContentType.pitfallGuide:
        return '#FF9800'; // 橙色
      case LearningContentType.practicalTips:
        return '#2196F3'; // 蓝色
      case LearningContentType.toolRecommendation:
        return '#9C27B0'; // 紫色
      case LearningContentType.materialComparison:
        return '#00BCD4'; // 青色
      case LearningContentType.costAnalysis:
        return '#FF5722'; // 深橙色
      case LearningContentType.timePlanning:
        return '#795548'; // 棕色
      case LearningContentType.safetyNotes:
        return '#F44336'; // 红色
      case LearningContentType.innovativeSolution:
        return '#E91E63'; // 粉色
      case LearningContentType.other:
        return '#607D8B'; // 蓝灰色
    }
  }
  
  /// 获取时间描述
  String get timeDescription {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前创建';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前创建';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前创建';
    } else {
      return '刚刚创建';
    }
  }
}
