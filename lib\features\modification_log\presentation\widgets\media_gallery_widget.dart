import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';

/// 媒体画廊组件
/// 用于展示日志关联的媒体文件，支持全屏预览
class MediaGalleryWidget extends ConsumerStatefulWidget {
  final List<LogMedia> mediaList;
  final bool showCaptions;
  final bool allowFullscreen;
  final Function(LogMedia)? onMediaTap;

  const MediaGalleryWidget({
    Key? key,
    required this.mediaList,
    this.showCaptions = true,
    this.allowFullscreen = true,
    this.onMediaTap,
  }) : super(key: key);

  @override
  ConsumerState<MediaGalleryWidget> createState() => _MediaGalleryWidgetState();
}

class _MediaGalleryWidgetState extends ConsumerState<MediaGalleryWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.mediaList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 12),
        _buildMediaGrid(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.photo_library, size: 20),
        const SizedBox(width: 8),
        Text(
          '媒体文件',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Text(
          '${widget.mediaList.length} 个文件',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMediaGrid() {
    // 根据媒体数量决定布局
    if (widget.mediaList.length == 1) {
      return _buildSingleMedia(widget.mediaList.first);
    } else if (widget.mediaList.length == 2) {
      return _buildTwoMediaLayout();
    } else {
      return _buildMultiMediaGrid();
    }
  }

  Widget _buildSingleMedia(LogMedia media) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildMediaContent(media, BoxFit.cover),
      ),
    );
  }

  Widget _buildTwoMediaLayout() {
    return Row(
      children: [
        Expanded(
          child: _buildMediaItem(widget.mediaList[0], height: 150),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildMediaItem(widget.mediaList[1], height: 150),
        ),
      ],
    );
  }

  Widget _buildMultiMediaGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: widget.mediaList.length,
      itemBuilder: (context, index) {
        return _buildMediaItem(widget.mediaList[index]);
      },
    );
  }

  Widget _buildMediaItem(LogMedia media, {double? height}) {
    return GestureDetector(
      onTap: () => _handleMediaTap(media),
      child: Container(
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              _buildMediaContent(media, BoxFit.cover),
              _buildMediaOverlay(media),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaContent(LogMedia media, BoxFit fit) {
    switch (media.type) {
      case MediaType.image:
        return _buildImageContent(media, fit);
      case MediaType.video:
        return _buildVideoContent(media, fit);
      default:
        return _buildFileContent(media);
    }
  }

  Widget _buildImageContent(LogMedia media, BoxFit fit) {
    if (media.url.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: media.url,
        fit: fit,
        width: double.infinity,
        height: double.infinity,
        placeholder: (context, url) => Container(
          color: Colors.grey[100],
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[200],
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red, size: 32),
                SizedBox(height: 8),
                Text('加载失败', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ),
      );
    } else {
      return Image.file(
        File(media.url),
        fit: fit,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey[200],
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red, size: 32),
                SizedBox(height: 8),
                Text('文件不存在', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ),
      );
    }
  }

  Widget _buildVideoContent(LogMedia media, BoxFit fit) {
    return Container(
      color: Colors.black87,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.play_circle_outline,
              color: Colors.white,
              size: 48,
            ),
            SizedBox(height: 8),
            Text(
              '点击播放视频',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileContent(LogMedia media) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getFileIcon(media.filename),
              size: 32,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 8),
            Text(
              media.filename,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaOverlay(LogMedia media) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showCaptions && media.caption.isNotEmpty)
              Text(
                media.caption,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            Row(
              children: [
                Icon(
                  _getMediaTypeIcon(media.type),
                  color: Colors.white,
                  size: 12,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    media.filename,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getMediaTypeIcon(MediaType type) {
    switch (type) {
      case MediaType.image:
        return Icons.image;
      case MediaType.video:
        return Icons.videocam;
      case MediaType.audio:
        return Icons.audiotrack;
      case MediaType.document:
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  IconData _getFileIcon(String filename) {
    final extension = filename.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _handleMediaTap(LogMedia media) {
    if (widget.onMediaTap != null) {
      widget.onMediaTap!(media);
    } else if (widget.allowFullscreen) {
      _showFullscreenMedia(media);
    }
  }

  void _showFullscreenMedia(LogMedia media) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullscreenMediaViewer(media: media),
        fullscreenDialog: true,
      ),
    );
  }
}

/// 全屏媒体查看器
class _FullscreenMediaViewer extends StatelessWidget {
  final LogMedia media;

  const _FullscreenMediaViewer({required this.media});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          media.filename,
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: Center(
        child: InteractiveViewer(
          child: _buildFullscreenContent(),
        ),
      ),
    );
  }

  Widget _buildFullscreenContent() {
    switch (media.type) {
      case MediaType.image:
        if (media.url.startsWith('http')) {
          return CachedNetworkImage(
            imageUrl: media.url,
            fit: BoxFit.contain,
            placeholder: (context, url) => const CircularProgressIndicator(),
            errorWidget: (context, url, error) => const Icon(
              Icons.error,
              color: Colors.white,
              size: 64,
            ),
          );
        } else {
          return Image.file(
            File(media.url),
            fit: BoxFit.contain,
          );
        }
      case MediaType.video:
        return Container(
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                color: Colors.white,
                size: 96,
              ),
              SizedBox(height: 16),
              Text(
                '视频播放功能待实现',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        );
      default:
        return const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.insert_drive_file,
              color: Colors.white,
              size: 96,
            ),
            SizedBox(height: 16),
            Text(
              '文件预览功能待实现',
              style: TextStyle(color: Colors.white),
            ),
          ],
        );
    }
  }
}