import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/chart_data.dart';

/// 材料分类饼图组件 - 符合Clean Architecture规范
class MaterialCategoryChart extends StatefulWidget {
  final List<MaterialCategoryData> data;
  final Function(String category)? onCategoryTap;
  final Function(String category)? onCategoryFilter;
  final Function(List<String> categories)? onMultipleFilter;
  final Set<String>? filteredCategories;
  final bool enableDrillDown;

  const MaterialCategoryChart({
    super.key,
    required this.data,
    this.onCategoryTap,
    this.onCategoryFilter,
    this.onMultipleFilter,
    this.filteredCategories,
    this.enableDrillDown = true,
  });

  @override
  State<MaterialCategoryChart> createState() => _MaterialCategoryChartState();
}

class _MaterialCategoryChartState extends State<MaterialCategoryChart> {
  int touchedIndex = -1;
  Set<String> selectedCategories = <String>{};
  bool isMultiSelectMode = false;

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return const Center(
        child: Text('No data available'),
      );
    }

    return Column(
      children: [
        // Filter controls
        if (widget.enableDrillDown) _buildFilterControls(),
        // Chart and legend
        Expanded(
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: PieChart(
            PieChartData(
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      touchedIndex = -1;
                      return;
                    }
                    touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                    
                    // Handle tap events for drill-down
                    if (event is FlTapUpEvent && widget.enableDrillDown && touchedIndex < widget.data.length) {
                      final categoryData = widget.data[touchedIndex];
                      final category = categoryData.categories.isNotEmpty ? categoryData.categories[0].category ?? 'Unknown' : 'Unknown';
                      if (isMultiSelectMode) {
                        _toggleCategorySelection(category);
                      } else {
                        widget.onCategoryTap?.call(category);
                      }
                    }
                  });
                },
              ),
              borderData: FlBorderData(show: false),
              sectionsSpace: 2,
              centerSpaceRadius: 40,
              sections: _buildPieChartSections(),
            ),
                ),
              ),
              Expanded(
                flex: 2,
                child: _buildLegend(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections() {
    final total = widget.data.fold<double>(0, (sum, item) => sum + item.totalCost);
    
    return widget.data.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final isTouched = index == touchedIndex;
      final fontSize = isTouched ? 16.0 : 12.0;
      final radius = isTouched ? 80.0 : 70.0;
      final percentage = (data.categories.isNotEmpty ? data.categories[0].value / total * 100 : 0);

      return PieChartSectionData(
        color: _getCategoryColor(index),
        value: data.categories.isNotEmpty ? data.categories[0].value : 0,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: const [
            Shadow(
              color: Colors.black26,
              offset: Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildFilterControls() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Text(
            'Filter Mode:',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(width: 8),
          Switch(
            value: isMultiSelectMode,
            onChanged: (value) {
              setState(() {
                isMultiSelectMode = value;
                if (!value) {
                  selectedCategories.clear();
                }
              });
            },
          ),
          const SizedBox(width: 8),
          Text(
            isMultiSelectMode ? 'Multi-Select' : 'Single-Select',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const Spacer(),
          if (selectedCategories.isNotEmpty) ...[
            TextButton(
              onPressed: () {
                setState(() {
                  selectedCategories.clear();
                });
                widget.onMultipleFilter?.call([]);
              },
              child: const Text('Clear All'),
            ),
            const SizedBox(width: 8),
            TextButton(
              onPressed: () {
                widget.onMultipleFilter?.call(selectedCategories.toList());
              },
              child: const Text('Apply Filter'),
            ),
          ],
        ],
      ),
    );
  }

  void _toggleCategorySelection(String category) {
    setState(() {
      if (selectedCategories.contains(category)) {
        selectedCategories.remove(category);
      } else {
        selectedCategories.add(category);
      }
    });
  }

  Color _getCategoryColor(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.amber,
      Colors.cyan,
    ];
    return colors[index % colors.length];
  }

  Widget _buildLegend() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.data.asMap().entries.map((entry) {
        final index = entry.key;
        final data = entry.value;
        final isSelected = index == touchedIndex;
        final category = data.categories.isNotEmpty ? data.categories[0].category ?? 'Unknown' : 'Unknown';
        final isFiltered = selectedCategories.contains(category);
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: GestureDetector(
            onTap: () {
              setState(() {
                touchedIndex = index == touchedIndex ? -1 : index;
              });
              
              if (isMultiSelectMode) {
                _toggleCategorySelection(category);
              } else {
                widget.onCategoryFilter?.call(category);
              }
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isFiltered ? Colors.blue.withValues(alpha: 0.1) : null,
                borderRadius: BorderRadius.circular(4),
                border: isFiltered ? Border.all(color: Colors.blue, width: 1) : null,
              ),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getCategoryColor(index),
                      shape: BoxShape.circle,
                      border: isSelected 
                          ? Border.all(color: Colors.black, width: 2)
                          : null,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category,
                          style: TextStyle(
                            fontSize: isSelected ? 14 : 12,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '${data.categories.length} items ¥${data.totalCost.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isFiltered)
                    const Icon(
                      Icons.check_circle,
                      size: 16,
                      color: Colors.blue,
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
