import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/log_search_criteria.dart';
import '../../domain/entities/enums.dart';
import '../../domain/repositories/log_repository.dart';
import '../datasources/log_remote_datasource.dart';
import '../models/log_entry_model.dart';

/// 日志仓库实现
class LogRepositoryImpl implements LogRepository {
  final LogRemoteDataSource remoteDataSource;

  LogRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<LogEntry>>> getProjectLogs(String projectId) async {
    try {
      final logModels = await remoteDataSource.getProjectLogs(projectId);
      final logs = logModels.map((model) => model.toEntity()).toList();
      return Right(logs);
    } catch (e) {
      return Left(ServerFailure(message: '获取项目日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogEntry>>> getSystemLogs(String systemId) async {
    try {
      final logModels = await remoteDataSource.getSystemLogs(systemId);
      final logs = logModels.map((model) => model.toEntity()).toList();
      return Right(logs);
    } catch (e) {
      return Left(ServerFailure(message: '获取系统日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogEntry>> getLogEntry(String logId) async {
    try {
      final logModel = await remoteDataSource.getLogEntry(logId);
      return Right(logModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '获取日志详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogEntry>> createLogEntry(LogEntry log) async {
    try {
      final logModel = LogEntryModel.fromEntity(log);
      final createdLogModel = await remoteDataSource.createLogEntry(logModel);
      return Right(createdLogModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '创建日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogEntry>> updateLogEntry(LogEntry log) async {
    try {
      final logModel = LogEntryModel.fromEntity(log);
      final updatedLogModel = await remoteDataSource.updateLogEntry(logModel);
      return Right(updatedLogModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '更新日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteLogEntry(String logId) async {
    try {
      await remoteDataSource.deleteLogEntry(logId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: '删除日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogEntry>>> searchLogs(LogSearchCriteria criteria) async {
    try {
      final logModels = await remoteDataSource.searchLogs(criteria);
      final logs = logModels.map((model) => model.toEntity()).toList();
      return Right(logs);
    } catch (e) {
      return Left(ServerFailure(message: '搜索日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogEntry>>> getUserLogs(String userId) async {
    try {
      final logModels = await remoteDataSource.getUserLogs(userId);
      final logs = logModels.map((model) => model.toEntity()).toList();
      return Right(logs);
    } catch (e) {
      return Left(ServerFailure(message: '获取用户日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LogEntry>>> getRecentLogs({int limit = 10}) async {
    try {
      final logModels = await remoteDataSource.getRecentLogs(limit: limit);
      final logs = logModels.map((model) => model.toEntity()).toList();
      return Right(logs);
    } catch (e) {
      return Left(ServerFailure(message: '获取最近日志失败: $e'));
    }
  }

  @override
  Future<Either<Failure, LogEntry>> updateLogStatus(String logId, LogStatus status) async {
    try {
      final statusString = LogEntryModel.logStatusToString(status);
      final updatedLogModel = await remoteDataSource.updateLogStatus(logId, statusString);
      return Right(updatedLogModel.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: '更新日志状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getLogCount(LogSearchCriteria criteria) async {
    try {
      final count = await remoteDataSource.getLogCount(criteria);
      return Right(count);
    } catch (e) {
      return Left(ServerFailure(message: '获取日志数量失败: $e'));
    }
  }
}