# VanHub改装宝 - 部署指南

## 🚀 快速部署

### 环境要求
- **Flutter SDK**: 3.0+
- **Dart SDK**: 3.0+
- **Node.js**: 16+ (可选，用于构建工具)
- **Web浏览器**: Chrome/Firefox/Safari最新版本

### 本地开发环境
```bash
# 1. 克隆项目
git clone <repository-url>
cd VanHub

# 2. 安装依赖
flutter pub get

# 3. 启动开发服务器
flutter run -d chrome --web-port=8080
```

## 🗄️ 数据库配置

### Supabase设置

#### 1. 创建Supabase项目
1. 访问 [supabase.com](https://supabase.com)
2. 创建新项目
3. 获取项目URL和API密钥

#### 2. 配置环境变量
在 `lib/core/api/supabase_config.dart` 中配置：
```dart
class SupabaseConfig {
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
}
```

#### 3. 创建数据库表

##### projects 表
```sql
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) DEFAULT '规划中',
  budget DECIMAL(12,2) DEFAULT 0,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);

-- 启用RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view all projects" ON projects
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING (auth.uid() = user_id);
```

##### material_categories 表
```sql
CREATE TABLE material_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入默认分类
INSERT INTO material_categories (name, description) VALUES
('电子设备', '各类电子设备和组件'),
('电器设备', '家用电器和设备'),
('水路系统', '供水和排水系统组件'),
('燃气系统', '燃气设备和管路'),
('家具定制', '定制家具和配件'),
('厨房设备', '厨房用具和设备'),
('安全设备', '安全防护设备'),
('工具配件', '工具和配件'),
('舒适配件', '提升舒适度的配件'),
('储物收纳', '储物和收纳解决方案'),
('外观改装', '外观装饰和改装'),
('内饰', '内部装饰材料'),
('其他', '其他未分类材料');

-- 启用RLS
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Anyone can view categories" ON material_categories
    FOR SELECT USING (true);
```

##### materials 表
```sql
CREATE TABLE materials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category_id UUID REFERENCES material_categories(id) ON DELETE SET NULL,
  unit VARCHAR(50) DEFAULT '个',
  price DECIMAL(10,2) DEFAULT 0,
  supplier VARCHAR(255),
  model VARCHAR(255),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_materials_user_id ON materials(user_id);
CREATE INDEX idx_materials_category_id ON materials(category_id);
CREATE INDEX idx_materials_name ON materials(name);

-- 启用RLS
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view all materials" ON materials
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own materials" ON materials
    FOR ALL USING (auth.uid() = user_id);
```

##### bom_items 表
```sql
CREATE TABLE bom_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price DECIMAL(10,2) DEFAULT 0,
  total_price DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
  status VARCHAR(50) DEFAULT '待采购',
  notes TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_bom_items_project_id ON bom_items(project_id);
CREATE INDEX idx_bom_items_material_id ON bom_items(material_id);
CREATE INDEX idx_bom_items_user_id ON bom_items(user_id);

-- 启用RLS
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view all bom items" ON bom_items
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own bom items" ON bom_items
    FOR ALL USING (auth.uid() = user_id);
```

## 🔐 用户认证配置

### 启用Supabase Auth
1. 在Supabase控制台启用Authentication
2. 配置邮箱/密码认证
3. 设置重定向URL

### 更新应用配置
在 `lib/main.dart` 中移除测试模式：
```dart
// 移除测试模式按钮
// 启用真实登录验证
```

## 🌐 生产环境部署

### Web部署

#### 1. 构建生产版本
```bash
# 构建Web版本
flutter build web --release

# 构建文件位于 build/web/ 目录
```

#### 2. 部署到静态托管

##### Netlify部署
1. 将 `build/web` 目录上传到Netlify
2. 配置自定义域名
3. 设置环境变量

##### Vercel部署
1. 连接GitHub仓库
2. 设置构建命令：`flutter build web --release`
3. 设置输出目录：`build/web`

##### Firebase Hosting
```bash
# 安装Firebase CLI
npm install -g firebase-tools

# 初始化Firebase项目
firebase init hosting

# 部署
firebase deploy
```

### 服务器部署

#### Docker部署
```dockerfile
# Dockerfile
FROM nginx:alpine

# 复制构建文件
COPY build/web /usr/share/nginx/html

# 配置nginx
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /usr/share/nginx/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript application/json;
}
```

## 🔧 环境配置

### 开发环境
```yaml
# pubspec.yaml
environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"
```

### 生产环境变量
```bash
# .env.production
SUPABASE_URL=your_production_supabase_url
SUPABASE_ANON_KEY=your_production_anon_key
APP_ENV=production
```

## 📊 监控和维护

### 性能监控
- 集成应用性能监控工具
- 设置错误追踪
- 配置日志收集

### 数据备份
```sql
-- 定期备份数据库
pg_dump your_database > backup_$(date +%Y%m%d).sql
```

### 更新部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 更新依赖
flutter pub get

# 3. 重新构建
flutter build web --release

# 4. 部署更新
# (根据部署方式执行相应命令)
```

## 🚨 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
flutter clean
flutter pub get

# 重新构建
flutter build web --release
```

#### 2. 数据库连接问题
- 检查Supabase URL和API密钥
- 验证网络连接
- 查看Supabase控制台日志

#### 3. 性能问题
- 启用Web渲染优化
- 压缩静态资源
- 配置CDN加速

### 日志查看
```bash
# 查看Flutter日志
flutter logs

# 查看Web控制台
# 在浏览器开发者工具中查看
```

## 📞 技术支持

### 文档资源
- [Flutter Web文档](https://flutter.dev/web)
- [Supabase文档](https://supabase.com/docs)
- [项目README](../README.md)

### 联系方式
- 开发者：Augment Agent
- 项目仓库：GitHub链接
- 技术支持：邮箱地址

---

**版本：** v1.0.0  
**最后更新：** 2025-07-16  
**部署状态：** 生产就绪
