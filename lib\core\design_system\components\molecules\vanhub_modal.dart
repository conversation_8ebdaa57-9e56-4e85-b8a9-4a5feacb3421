import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../vanhub_design_system.dart';

/// VanHub模态框组件
/// 
/// 支持多种尺寸、动画效果和响应式设计
/// 遵循Material Design 3规范
class VanHubModal extends StatefulWidget {
  /// 模态框内容
  final Widget child;
  
  /// 模态框尺寸
  final VanHubModalSize size;
  
  /// 动画类型
  final VanHubModalAnimation animation;
  
  /// 是否显示关闭按钮
  final bool showCloseButton;
  
  /// 是否可以通过点击背景关闭
  final bool barrierDismissible;
  
  /// 背景遮罩颜色
  final Color? barrierColor;
  
  /// 关闭回调
  final VoidCallback? onClose;
  
  /// 标题
  final String? title;
  
  /// 是否全屏显示（移动端）
  final bool fullscreenOnMobile;

  const VanHubModal({
    super.key,
    required this.child,
    this.size = VanHubModalSize.md,
    this.animation = VanHubModalAnimation.fade,
    this.showCloseButton = true,
    this.barrierDismissible = true,
    this.barrierColor,
    this.onClose,
    this.title,
    this.fullscreenOnMobile = false,
  });

  @override
  State<VanHubModal> createState() => _VanHubModalState();

  /// 显示模态框的静态方法
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    VanHubModalSize size = VanHubModalSize.md,
    VanHubModalAnimation animation = VanHubModalAnimation.fade,
    bool showCloseButton = true,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? title,
    bool fullscreenOnMobile = false,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      builder: (context) => VanHubModal(
        size: size,
        animation: animation,
        showCloseButton: showCloseButton,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
        title: title,
        fullscreenOnMobile: fullscreenOnMobile,
        onClose: () => Navigator.of(context).pop(),
        child: child,
      ),
    );
  }
}

class _VanHubModalState extends State<VanHubModal> {
  @override
  void initState() {
    super.initState();
    // 添加键盘监听
    HardwareKeyboard.instance.addHandler(_handleKeyEvent);
  }

  @override
  void dispose() {
    HardwareKeyboard.instance.removeHandler(_handleKeyEvent);
    super.dispose();
  }

  bool _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.escape) {
      if (widget.barrierDismissible) {
        _handleClose();
      }
      return true;
    }
    return false;
  }

  void _handleClose() {
    if (widget.onClose != null) {
      widget.onClose!();
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < VanHubDesignSystem.breakpointMd;
    
    // 移动端全屏模式
    if (widget.fullscreenOnMobile && isSmallScreen) {
      return _buildFullscreenModal(context);
    }
    
    return _buildRegularModal(context);
  }

  Widget _buildFullscreenModal(BuildContext context) {
    return Scaffold(
      appBar: widget.title != null || widget.showCloseButton
          ? AppBar(
              title: widget.title != null ? Text(widget.title!) : null,
              leading: widget.showCloseButton
                  ? IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _handleClose,
                    )
                  : null,
              elevation: 0,
            )
          : null,
      body: widget.child,
    );
  }

  Widget _buildRegularModal(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: _getModalPadding(context),
      child: _buildModalContent(context),
    );
  }

  Widget _buildModalContent(BuildContext context) {
    Widget content = Container(
      width: _getModalWidth(context),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
        boxShadow: const [VanHubDesignSystem.shadowXl],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.title != null || widget.showCloseButton)
            _buildModalHeader(context),
          Flexible(
            child: widget.child,
          ),
        ],
      ),
    );

    // 应用动画效果
    return _applyAnimation(content);
  }

  Widget _buildModalHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing6),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (widget.title != null)
            Expanded(
              child: Text(
                widget.title!,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: VanHubDesignSystem.fontWeightSemiBold,
                ),
              ),
            ),
          if (widget.showCloseButton)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _handleClose,
              tooltip: '关闭',
            ),
        ],
      ),
    );
  }

  Widget _applyAnimation(Widget child) {
    switch (widget.animation) {
      case VanHubModalAnimation.fade:
        return child.animate().fadeIn(
          duration: VanHubDesignSystem.durationBase,
          curve: VanHubDesignSystem.curveDefault,
        );
      case VanHubModalAnimation.slide:
        return child.animate().slideY(
          begin: -0.3,
          end: 0,
          duration: VanHubDesignSystem.durationBase,
          curve: VanHubDesignSystem.curveDefault,
        ).fadeIn(
          duration: VanHubDesignSystem.durationBase,
        );
      case VanHubModalAnimation.scale:
        return child.animate().scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1.0, 1.0),
          duration: VanHubDesignSystem.durationBase,
          curve: VanHubDesignSystem.curveDefault,
        ).fadeIn(
          duration: VanHubDesignSystem.durationBase,
        );
    }
  }

  EdgeInsets _getModalPadding(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    if (screenSize.width < VanHubDesignSystem.breakpointMd) {
      return const EdgeInsets.all(VanHubDesignSystem.spacing4);
    }
    return const EdgeInsets.all(VanHubDesignSystem.spacing8);
  }

  double _getModalWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    switch (widget.size) {
      case VanHubModalSize.sm:
        return _constrainWidth(320, screenWidth);
      case VanHubModalSize.md:
        return _constrainWidth(480, screenWidth);
      case VanHubModalSize.lg:
        return _constrainWidth(640, screenWidth);
      case VanHubModalSize.xl:
        return _constrainWidth(800, screenWidth);
      case VanHubModalSize.fullscreen:
        return screenWidth;
    }
  }

  double _constrainWidth(double targetWidth, double screenWidth) {
    if (screenWidth < VanHubDesignSystem.breakpointMd) {
      return screenWidth - (VanHubDesignSystem.spacing4 * 2);
    }
    return targetWidth.clamp(0, screenWidth - (VanHubDesignSystem.spacing8 * 2));
  }
}

/// 模态框尺寸枚举
enum VanHubModalSize {
  sm,    // 320px
  md,    // 480px
  lg,    // 640px
  xl,    // 800px
  fullscreen, // 全屏
}

/// 模态框动画类型枚举
enum VanHubModalAnimation {
  fade,   // 淡入淡出
  slide,  // 滑动
  scale,  // 缩放
}
