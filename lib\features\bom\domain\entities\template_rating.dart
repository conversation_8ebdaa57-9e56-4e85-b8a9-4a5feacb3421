import 'package:freezed_annotation/freezed_annotation.dart';

part 'template_rating.freezed.dart';
part 'template_rating.g.dart';

/// Template rating entity for BOM templates
@freezed
class TemplateRating with _$TemplateRating {
  const factory TemplateRating({
    required String id,
    required String templateId,
    required String userId,
    required double rating,
    String? comment,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) = _TemplateRating;

  factory TemplateRating.fromJson(Map<String, dynamic> json) =>
      _$TemplateRatingFromJson(json);
}
