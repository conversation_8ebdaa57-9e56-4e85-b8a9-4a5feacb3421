import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../entities/material_review.dart';
import '../repositories/material_review_repository.dart';

class GetMaterialReviewsUseCase {
  final MaterialReviewRepository repository;

  GetMaterialReviewsUseCase(this.repository);

  Future<Either<Failure, List<MaterialReview>>> call(GetMaterialReviewsParams params) async {
    return await repository.getMaterialReviews(
      params.materialId,
      filterCriteria: params.filterCriteria,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

class GetMaterialReviewsParams {
  final String materialId;
  final ReviewFilterCriteria? filterCriteria;
  final int? limit;
  final int? offset;

  GetMaterialReviewsParams({
    required this.materialId,
    this.filterCriteria,
    this.limit,
    this.offset,
  });
}

class GetMaterialReviewSummaryUseCase {
  final MaterialReviewRepository repository;

  GetMaterialReviewSummaryUseCase(this.repository);

  Future<Either<Failure, MaterialReviewSummary>> call(GetMaterialReviewSummaryParams params) async {
    return await repository.getMaterialReviewSummary(params.materialId);
  }
}

class GetMaterialReviewSummaryParams {
  final String materialId;

  GetMaterialReviewSummaryParams({required this.materialId});
}