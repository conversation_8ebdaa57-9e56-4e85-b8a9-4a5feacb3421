import 'package:fpdart/fpdart.dart';
import 'package:mockito/mockito.dart';

import '../../lib/core/errors/failures.dart';
import '../../lib/features/bom/domain/entities/bom_item.dart';
import '../../lib/features/bom/domain/repositories/bom_repository.dart';

/// Mock类：BomRepository
/// 用于测试BOM相关功能
class MockBomRepository extends Mock implements BomRepository {
  
  @override
  Future<Either<Failure, BomItem>> createBomItem(BomItem bomItem) {
    return super.noSuchMethod(
      Invocation.method(#createBomItem, [bomItem]),
      returnValue: Future.value(
        Right(BomItem(
          id: 'test-bom-item-id',
          projectId: bomItem.projectId,
          userId: 'test-user-id',
          materialName: bomItem.name,
          description: bomItem.description,
          status: BomItemStatus.pending,
          quantity: bomItem.quantity,
          unitPrice: bomItem.unitPrice,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          category: bomItem.category,
          supplier: bomItem.supplier,
          supplierUrl: bomItem.supplierUrl,
          notes: bomItem.notes,
          tags: bomItem.tags,
          actualPrice: bomItem.actualPrice,
          estimatedPrice: bomItem.estimatedPrice,
          metadata: bomItem.metadata,
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, BomItem>> updateBomItem(BomItem bomItem) {
    return super.noSuchMethod(
      Invocation.method(#updateBomItem, [bomItem]),
      returnValue: Future.value(Right(bomItem)),
    );
  }

  @override
  Future<Either<Failure, void>> deleteBomItem(String bomItemId) {
    return super.noSuchMethod(
      Invocation.method(#deleteBomItem, [bomItemId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, BomItem>> getBomItem(String bomItemId) {
    return super.noSuchMethod(
      Invocation.method(#getBomItem, [bomItemId]),
      returnValue: Future.value(
        Right(BomItem(
          id: bomItemId,
          projectId: 'test-project-id',
          userId: 'test-user-id',
          materialName: 'Test BOM Item',
          description: 'Test BOM item description',
          status: BomItemStatus.pending,
          quantity: 1,
          unitPrice: 100.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          category: 'electrical',
          estimatedPrice: 100.0,
          actualPrice: null,
          supplier: 'Test Supplier',
          supplierUrl: null,
          notes: 'Test notes',
          tags: ['test'],
          metadata: {},
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, List<BomItem>>> getProjectBomItems(
    String projectId, {
    BomCategory? category,
    BomItemStatus? status,
    BomItemPriority? priority,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getProjectBomItems, [projectId], {
        #category: category,
        #status: status,
        #priority: priority,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<BomItem>>> searchBomItems(
    String projectId,
    String query, {
    BomCategory? category,
    BomItemStatus? status,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#searchBomItems, [projectId, query], {
        #category: category,
        #status: status,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, void>> updateBomItemStatus(
    String bomItemId,
    BomItemStatus status,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateBomItemStatus, [bomItemId, status]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> updateBomItemPrice(
    String bomItemId,
    double actualPrice,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateBomItemPrice, [bomItemId, actualPrice]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> updateBomItemQuantity(
    String bomItemId,
    int quantity,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateBomItemQuantity, [bomItemId, quantity]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, Map<BomCategory, int>>> getBomItemCountByCategory(
    String projectId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#getBomItemCountByCategory, [projectId]),
      returnValue: Future.value(const Right({})),
    );
  }

  @override
  Future<Either<Failure, Map<BomItemStatus, int>>> getBomItemCountByStatus(
    String projectId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#getBomItemCountByStatus, [projectId]),
      returnValue: Future.value(const Right({})),
    );
  }

  @override
  Future<Either<Failure, double>> getTotalEstimatedCost(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#getTotalEstimatedCost, [projectId]),
      returnValue: Future.value(const Right(0.0)),
    );
  }

  @override
  Future<Either<Failure, double>> getTotalActualCost(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#getTotalActualCost, [projectId]),
      returnValue: Future.value(const Right(0.0)),
    );
  }

  @override
  Future<Either<Failure, List<String>>> getBomItemTags(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#getBomItemTags, [projectId]),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, void>> linkBomItemToMaterial(
    String bomItemId,
    String materialId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#linkBomItemToMaterial, [bomItemId, materialId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> unlinkBomItemFromMaterial(String bomItemId) {
    return super.noSuchMethod(
      Invocation.method(#unlinkBomItemFromMaterial, [bomItemId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, List<BomItem>>> getBomItemsByMaterial(String materialId) {
    return super.noSuchMethod(
      Invocation.method(#getBomItemsByMaterial, [materialId]),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, void>> duplicateBomItem(
    String bomItemId,
    String targetProjectId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#duplicateBomItem, [bomItemId, targetProjectId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> moveBomItem(
    String bomItemId,
    String targetProjectId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#moveBomItem, [bomItemId, targetProjectId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, List<BomItem>>> getBomItemsBySupplier(
    String projectId,
    String supplier,
  ) {
    return super.noSuchMethod(
      Invocation.method(#getBomItemsBySupplier, [projectId, supplier]),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, Map<String, double>>> getCostBySupplier(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#getCostBySupplier, [projectId]),
      returnValue: Future.value(const Right({})),
    );
  }
}
