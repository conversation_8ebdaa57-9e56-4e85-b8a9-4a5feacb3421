import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/bom_repository.dart';

/// 添加材料到BOM用例参数
class AddMaterialToBomParams {
  final String projectId;
  final String materialId;
  final int quantity;
  final String? notes;

  const AddMaterialToBomParams({
    required this.projectId,
    required this.materialId,
    required this.quantity,
    this.notes,
  });
}

/// 添加材料到BOM用例
class AddMaterialToBomUseCase implements UseCase<void, AddMaterialToBomParams> {
  final BomRepository repository;

  const AddMaterialToBomUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(AddMaterialToBomParams params) async {
    return await repository.addMaterialToBom(
      projectId: params.projectId,
      materialId: params.materialId,
      quantity: params.quantity,
      notes: params.notes,
    );
  }
}