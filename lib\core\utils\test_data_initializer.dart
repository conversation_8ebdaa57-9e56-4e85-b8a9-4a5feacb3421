import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

/// 测试数据初始化器
/// 
/// 用于创建完整的测试数据，包括：
/// 1. 测试用户
/// 2. 示例项目
/// 3. 材料库数据
/// 4. BOM数据
/// 5. 改装日志数据
class TestDataInitializer {
  static final SupabaseClient _client = SupabaseConfig.client;

  /// 初始化所有测试数据
  static Future<Map<String, dynamic>> initializeAllTestData() async {
    if (kDebugMode) {
      print('🧪 开始初始化测试数据...');
    }

    try {
      // 1. 创建测试用户
      final userId = await _createTestUser();
      
      // 2. 创建材料分类
      await _createMaterialCategories();
      
      // 3. 创建示例材料
      final materialIds = await _createSampleMaterials(userId);
      
      // 4. 创建示例项目
      final projectId = await _createSampleProject(userId);
      
      // 5. 创建BOM数据
      final bomItemIds = await _createSampleBomItems(projectId, materialIds);
      
      // 6. 创建改装日志
      final logIds = await _createSampleLogs(projectId, bomItemIds);

      final result = {
        'userId': userId,
        'projectId': projectId,
        'materialIds': materialIds,
        'bomItemIds': bomItemIds,
        'logIds': logIds,
      };

      if (kDebugMode) {
        print('✅ 测试数据初始化完成');
        print('📊 创建数据统计:');
        print('   - 用户: 1个');
        print('   - 项目: 1个');
        print('   - 材料: ${materialIds.length}个');
        print('   - BOM项目: ${bomItemIds.length}个');
        print('   - 改装日志: ${logIds.length}个');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ 测试数据初始化失败: $e');
      }
      rethrow;
    }
  }

  /// 创建测试用户
  static Future<String> _createTestUser() async {
    try {
      // 尝试注册测试用户
      final response = await _client.auth.signUp(
        email: '<EMAIL>',
        password: 'Test123456!',
        data: {
          'name': '测试用户',
          'avatar_url': 'https://api.dicebear.com/7.x/avataaars/svg?seed=test',
        },
      );

      if (response.user != null) {
        if (kDebugMode) {
          print('✅ 测试用户创建成功: ${response.user!.id}');
        }
        return response.user!.id;
      } else {
        throw Exception('用户创建失败');
      }
    } catch (e) {
      // 如果用户已存在，尝试登录
      try {
        final loginResponse = await _client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'Test123456!',
        );
        
        if (loginResponse.user != null) {
          if (kDebugMode) {
            print('✅ 测试用户登录成功: ${loginResponse.user!.id}');
          }
          return loginResponse.user!.id;
        }
      } catch (loginError) {
        if (kDebugMode) {
          print('❌ 测试用户创建/登录失败: $loginError');
        }
      }
      rethrow;
    }
  }

  /// 创建材料分类
  static Future<void> _createMaterialCategories() async {
    final categories = [
      {
        'name': '电力系统',
        'description': '电池、逆变器、充电器等电气设备',
        'icon': 'electrical_services',
        'color': '#FF9800',
        'sort_order': 1,
      },
      {
        'name': '水路系统',
        'description': '水泵、水箱、净水器等水系统设备',
        'icon': 'water_drop',
        'color': '#2196F3',
        'sort_order': 2,
      },
      {
        'name': '内饰改装',
        'description': '床铺、桌椅、储物柜等内饰家具',
        'icon': 'chair',
        'color': '#4CAF50',
        'sort_order': 3,
      },
      {
        'name': '外观改装',
        'description': '车身贴纸、装饰条、车顶架等外观配件',
        'icon': 'palette',
        'color': '#E91E63',
        'sort_order': 4,
      },
      {
        'name': '储物方案',
        'description': '储物箱、挂钩、收纳架等储物设备',
        'icon': 'inventory_2',
        'color': '#9C27B0',
        'sort_order': 5,
      },
    ];

    for (final category in categories) {
      try {
        await _client.from('material_categories').upsert(category);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ 材料分类创建失败: ${category['name']} - $e');
        }
      }
    }

    if (kDebugMode) {
      print('✅ 材料分类创建完成');
    }
  }

  /// 创建示例材料
  static Future<List<String>> _createSampleMaterials(String userId) async {
    final materials = [
      {
        'item_name': '磷酸铁锂电池',
        'description': '12V 100Ah 磷酸铁锂电池，适用于房车电力系统',
        'category': '电力系统',
        'brand': '比亚迪',
        'model': 'BYD-LFP100',
        'specification': '12V/100Ah，重量约13kg',
        'reference_price': 2500.0,
        'purchase_link': 'https://example.com/battery',
        'weight': 13.0,
        'attributes': {
          'voltage': '12V',
          'capacity': '100Ah',
          'type': '磷酸铁锂',
          'warranty': '3年',
        },
        'user_id': userId,
      },
      {
        'item_name': '纯正弦波逆变器',
        'description': '2000W纯正弦波逆变器，12V转220V',
        'category': '电力系统',
        'brand': '正弦',
        'model': 'ZX-2000W',
        'specification': '2000W，12V输入，220V输出',
        'reference_price': 800.0,
        'purchase_link': 'https://example.com/inverter',
        'weight': 3.5,
        'attributes': {
          'power': '2000W',
          'input_voltage': '12V',
          'output_voltage': '220V',
          'efficiency': '90%',
        },
        'user_id': userId,
      },
      {
        'item_name': '自吸水泵',
        'description': '12V直流自吸水泵，适用于房车供水系统',
        'category': '水路系统',
        'brand': '舒弗洛',
        'model': 'SHURflo-4008',
        'specification': '12V，流量11.3L/min，压力2.8bar',
        'reference_price': 450.0,
        'purchase_link': 'https://example.com/pump',
        'weight': 1.8,
        'attributes': {
          'voltage': '12V',
          'flow_rate': '11.3L/min',
          'pressure': '2.8bar',
          'type': '自吸泵',
        },
        'user_id': userId,
      },
      {
        'item_name': '折叠床',
        'description': '房车专用折叠床，节省空间设计',
        'category': '内饰改装',
        'brand': '多美达',
        'model': 'Dometic-FB120',
        'specification': '120cm×190cm，承重150kg',
        'reference_price': 1200.0,
        'purchase_link': 'https://example.com/bed',
        'weight': 25.0,
        'attributes': {
          'size': '120×190cm',
          'weight_capacity': '150kg',
          'material': '铝合金框架',
          'foldable': true,
        },
        'user_id': userId,
      },
      {
        'item_name': '车顶行李架',
        'description': '铝合金车顶行李架，增加储物空间',
        'category': '外观改装',
        'brand': '途乐',
        'model': 'TL-RR200',
        'specification': '200cm×120cm，承重75kg',
        'reference_price': 680.0,
        'purchase_link': 'https://example.com/roof-rack',
        'weight': 15.0,
        'attributes': {
          'size': '200×120cm',
          'weight_capacity': '75kg',
          'material': '铝合金',
          'color': '黑色',
        },
        'user_id': userId,
      },
    ];

    final materialIds = <String>[];

    for (final material in materials) {
      try {
        final response = await _client
            .from('material_library')
            .insert(material)
            .select('id')
            .single();
        
        materialIds.add(response['id'] as String);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ 材料创建失败: ${material['item_name']} - $e');
        }
      }
    }

    if (kDebugMode) {
      print('✅ 示例材料创建完成，共${materialIds.length}个');
    }

    return materialIds;
  }

  /// 创建示例项目
  static Future<String> _createSampleProject(String userId) async {
    final project = {
      'title': '我的第一台房车改装',
      'description': '''这是一个完整的房车改装项目，包含以下系统：

🔋 电力系统：
- 磷酸铁锂电池组
- 纯正弦波逆变器
- 太阳能充电控制器

💧 水路系统：
- 自吸水泵
- 净水器
- 水箱

🏠 内饰改装：
- 折叠床设计
- 储物柜定制
- LED照明系统

🚗 外观改装：
- 车顶行李架
- 侧面遮阳棚
- 车身贴纸

预计改装周期：3个月
预算范围：5-8万元''',
      'budget': 65000.0,
      'status': 'planning',
      'is_public': true,
      'vehicle_brand': '福特',
      'vehicle_model': 'Transit',
      'vehicle_year': '2023',
      'vehicle_info': '福特全顺长轴高顶版',
      'tags': ['电力系统', '水路系统', '内饰改装', '外观改装'],
      'author_id': userId,
    };

    try {
      final response = await _client
          .from('projects')
          .insert(project)
          .select('id')
          .single();

      final projectId = response['id'] as String;

      if (kDebugMode) {
        print('✅ 示例项目创建成功: $projectId');
      }

      return projectId;
    } catch (e) {
      if (kDebugMode) {
        print('❌ 示例项目创建失败: $e');
      }
      rethrow;
    }
  }

  /// 创建示例BOM项目
  static Future<List<String>> _createSampleBomItems(
    String projectId,
    List<String> materialIds,
  ) async {
    if (materialIds.isEmpty) {
      return [];
    }

    // 为每个材料创建BOM项目
    final bomItems = [
      {
        'material_id': materialIds[0], // 电池
        'quantity': 2,
        'unit_price': 2500.0,
        'status': 'purchased',
        'notes': '主电源系统，已采购2块电池',
        'purchase_date': DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
      },
      {
        'material_id': materialIds[1], // 逆变器
        'quantity': 1,
        'unit_price': 800.0,
        'status': 'received',
        'notes': '电力转换设备，已收货待安装',
        'purchase_date': DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      },
      {
        'material_id': materialIds[2], // 水泵
        'quantity': 1,
        'unit_price': 450.0,
        'status': 'pending',
        'notes': '供水系统核心设备，待采购',
      },
      {
        'material_id': materialIds[3], // 折叠床
        'quantity': 1,
        'unit_price': 1200.0,
        'status': 'ordered',
        'notes': '休息区家具，已下单',
        'purchase_date': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
      },
      {
        'material_id': materialIds[4], // 行李架
        'quantity': 1,
        'unit_price': 680.0,
        'status': 'installed',
        'notes': '外观改装，已安装完成',
        'purchase_date': DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
        'use_date': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
      },
    ];

    final bomItemIds = <String>[];

    for (int i = 0; i < bomItems.length && i < materialIds.length; i++) {
      try {
        // 首先创建一个commit
        final commitResponse = await _client
            .from('commits')
            .insert({
              'project_id': projectId,
              'title': '添加BOM项目',
              'message': '添加${i + 1}号材料到BOM',
              'description': '测试数据初始化',
            })
            .select('id')
            .single();

        // 然后创建BOM项目
        final bomItem = {
          ...bomItems[i],
          'project_id': projectId,
          'commit_id': commitResponse['id'],
        };

        final response = await _client
            .from('bom_items')
            .insert(bomItem)
            .select('id')
            .single();

        bomItemIds.add(response['id'] as String);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ BOM项目创建失败: ${i + 1} - $e');
        }
      }
    }

    if (kDebugMode) {
      print('✅ 示例BOM项目创建完成，共${bomItemIds.length}个');
    }

    return bomItemIds;
  }

  /// 创建示例改装日志
  static Future<List<String>> _createSampleLogs(
    String projectId,
    List<String> bomItemIds,
  ) async {
    final logs = [
      {
        'title': '项目启动 - 制定改装计划',
        'content': '''今天正式启动房车改装项目！

📋 **今日完成**：
- 制定详细改装计划
- 确定预算分配
- 选择核心设备型号

🎯 **下一步计划**：
- 采购电力系统设备
- 联系安装师傅
- 准备工具和材料

💡 **经验分享**：
改装前一定要做好详细规划，避免后期返工。''',
        'log_date': DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
        'status': 'published',
        'difficulty': 'beginner',
        'time_spent_minutes': 180,
        'total_cost': 0.0,
      },
      {
        'title': '电力系统安装 - 电池和逆变器',
        'content': '''今天完成了电力系统的核心设备安装。

🔋 **安装设备**：
- 2块磷酸铁锂电池（100Ah）
- 1台2000W纯正弦波逆变器
- 电池管理系统（BMS）

⚡ **安装要点**：
1. 电池安装位置要通风良好
2. 逆变器远离热源
3. 线缆规格要足够粗
4. 保险丝必不可少

💰 **本次花费**：5800元

🎉 **测试结果**：
- 电池充放电正常
- 逆变器输出稳定
- 系统运行良好''',
        'log_date': DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
        'status': 'published',
        'difficulty': 'intermediate',
        'time_spent_minutes': 480,
        'total_cost': 5800.0,
      },
      {
        'title': '水路系统调试 - 供水测试',
        'content': '''水路系统基本安装完成，今天进行了全面测试。

💧 **系统组成**：
- 自吸水泵
- 20L清水箱
- 15L灰水箱
- 水路管道和接头

🔧 **调试过程**：
1. 检查所有接头密封性
2. 测试水泵启停功能
3. 调整水压设置
4. 验证排水系统

⚠️ **遇到问题**：
- 初次启动水泵噪音较大
- 部分接头有轻微渗水

✅ **解决方案**：
- 加装减震垫降低噪音
- 重新紧固接头并涂密封胶

💰 **本次花费**：1200元''',
        'log_date': DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
        'status': 'published',
        'difficulty': 'intermediate',
        'time_spent_minutes': 360,
        'total_cost': 1200.0,
      },
    ];

    final logIds = <String>[];

    for (final log in logs) {
      try {
        final logData = {
          ...log,
          'project_id': projectId,
          'author_id': _client.auth.currentUser?.id,
        };

        final response = await _client
            .from('modification_logs')
            .insert(logData)
            .select('id')
            .single();

        logIds.add(response['id'] as String);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ 改装日志创建失败: ${log['title']} - $e');
        }
      }
    }

    if (kDebugMode) {
      print('✅ 示例改装日志创建完成，共${logIds.length}个');
    }

    return logIds;
  }

  /// 清理测试数据
  static Future<void> cleanupTestData() async {
    if (kDebugMode) {
      print('🧹 开始清理测试数据...');
    }

    try {
      // 删除测试用户的所有数据
      final userId = _client.auth.currentUser?.id;
      if (userId != null) {
        // 删除改装日志
        await _client.from('modification_logs').delete().eq('author_id', userId);
        
        // 删除BOM项目
        await _client.from('bom_items').delete().eq('project_id', userId);
        
        // 删除项目
        await _client.from('projects').delete().eq('author_id', userId);
        
        // 删除材料
        await _client.from('material_library').delete().eq('user_id', userId);
      }

      if (kDebugMode) {
        print('✅ 测试数据清理完成');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 测试数据清理失败: $e');
      }
    }
  }
}
