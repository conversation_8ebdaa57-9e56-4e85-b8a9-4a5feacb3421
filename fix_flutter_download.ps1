# 重置Flutter镜像设置
Write-Host "正在重置Flutter镜像设置..." -ForegroundColor Green

# 移除当前会话中的环境变量
$env:PUB_HOSTED_URL = $null
$env:FLUTTER_STORAGE_BASE_URL = $null

# 移除系统级环境变量
[Environment]::SetEnvironmentVariable("PUB_HOSTED_URL", $null, "User")
[Environment]::SetEnvironmentVariable("FLUTTER_STORAGE_BASE_URL", $null, "User")

# 清理Flutter缓存
Write-Host "正在清理Flutter缓存..." -ForegroundColor Green
flutter clean
flutter pub cache clean

# 重新获取依赖
Write-Host "正在重新获取依赖..." -ForegroundColor Green
flutter pub get

Write-Host "完成！如果仍有问题，请尝试以下命令手动设置国内镜像：" -ForegroundColor Cyan
Write-Host '$env:PUB_HOSTED_URL="https://pub.flutter-io.cn"' -ForegroundColor Yellow
Write-Host '$env:FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"' -ForegroundColor Yellow

Write-Host "或者设置官方源：" -ForegroundColor Cyan
Write-Host '$env:PUB_HOSTED_URL=$null' -ForegroundColor Yellow
Write-Host '$env:FLUTTER_STORAGE_BASE_URL=$null' -ForegroundColor Yellow
