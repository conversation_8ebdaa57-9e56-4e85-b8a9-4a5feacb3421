import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../domain/entities/material_review.dart';
import 'material_review_provider.dart';

part 'material_review_cache_provider.g.dart';

/// 评论缓存管理Provider
/// 提供评论数据的缓存和性能优化功能
class MaterialReviewCache {
  final Map<String, List<MaterialReview>> _reviewsCache = {};
  final Map<String, MaterialReviewSummary> _summaryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  static const Duration _cacheExpiration = Duration(minutes: 5);

  /// 获取缓存的评价列表
  List<MaterialReview>? getCachedReviews(String materialId) {
    final timestamp = _cacheTimestamps['reviews_$materialId'];
    if (timestamp == null || DateTime.now().difference(timestamp) > _cacheExpiration) {
      _reviewsCache.remove(materialId);
      _cacheTimestamps.remove('reviews_$materialId');
      return null;
    }
    return _reviewsCache[materialId];
  }

  /// 缓存评价列表
  void cacheReviews(String materialId, List<MaterialReview> reviews) {
    _reviewsCache[materialId] = reviews;
    _cacheTimestamps['reviews_$materialId'] = DateTime.now();
  }

  /// 获取缓存的评价摘要
  MaterialReviewSummary? getCachedSummary(String materialId) {
    final timestamp = _cacheTimestamps['summary_$materialId'];
    if (timestamp == null || DateTime.now().difference(timestamp) > _cacheExpiration) {
      _summaryCache.remove(materialId);
      _cacheTimestamps.remove('summary_$materialId');
      return null;
    }
    return _summaryCache[materialId];
  }

  /// 缓存评价摘要
  void cacheSummary(String materialId, MaterialReviewSummary summary) {
    _summaryCache[materialId] = summary;
    _cacheTimestamps['summary_$materialId'] = DateTime.now();
  }

  /// 清除特定材料的缓存
  void clearMaterialCache(String materialId) {
    _reviewsCache.remove(materialId);
    _summaryCache.remove(materialId);
    _cacheTimestamps.remove('reviews_$materialId');
    _cacheTimestamps.remove('summary_$materialId');
  }

  /// 清除所有缓存
  void clearAllCache() {
    _reviewsCache.clear();
    _summaryCache.clear();
    _cacheTimestamps.clear();
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int validReviewsCache = 0;
    int validSummaryCache = 0;
    int expiredCache = 0;

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) <= _cacheExpiration) {
        if (entry.key.startsWith('reviews_')) {
          validReviewsCache++;
        } else if (entry.key.startsWith('summary_')) {
          validSummaryCache++;
        }
      } else {
        expiredCache++;
      }
    }

    return {
      'validReviewsCache': validReviewsCache,
      'validSummaryCache': validSummaryCache,
      'expiredCache': expiredCache,
      'totalCacheEntries': _cacheTimestamps.length,
    };
  }
}

/// 评论缓存Provider
@riverpod
MaterialReviewCache materialReviewCache(MaterialReviewCacheRef ref) {
  return MaterialReviewCache();
}

/// 带缓存的材料评价列表Provider
@riverpod
Future<List<MaterialReview>> cachedMaterialReviews(
  CachedMaterialReviewsRef ref,
  String materialId, {
  ReviewFilterCriteria? filterCriteria,
  int? limit,
  int? offset,
}) async {
  final cache = ref.read(materialReviewCacheProvider);
  
  // 只有在没有筛选条件时才使用缓存
  if (filterCriteria == null && limit == null && offset == null) {
    final cachedReviews = cache.getCachedReviews(materialId);
    if (cachedReviews != null) {
      return cachedReviews;
    }
  }

  // 从网络获取数据
  final reviews = await ref.read(materialReviewsProvider(
    materialId,
    filterCriteria: filterCriteria,
    limit: limit,
    offset: offset,
  ).future);

  // 缓存数据（只缓存无筛选条件的完整列表）
  if (filterCriteria == null && limit == null && offset == null) {
    cache.cacheReviews(materialId, reviews);
  }

  return reviews;
}

/// 带缓存的材料评价摘要Provider
@riverpod
Future<MaterialReviewSummary> cachedMaterialReviewSummary(
  CachedMaterialReviewSummaryRef ref,
  String materialId,
) async {
  final cache = ref.read(materialReviewCacheProvider);
  
  // 尝试从缓存获取
  final cachedSummary = cache.getCachedSummary(materialId);
  if (cachedSummary != null) {
    return cachedSummary;
  }

  // 从网络获取数据
  final summary = await ref.read(materialReviewSummaryProvider(materialId).future);

  // 缓存数据
  cache.cacheSummary(materialId, summary);

  return summary;
}

/// 预加载评价数据Provider
@riverpod
class PreloadReviewsNotifier extends _$PreloadReviewsNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// 预加载指定材料的评价数据
  Future<void> preloadMaterialReviews(List<String> materialIds) async {
    state = const AsyncValue.loading();
    
    try {
      final futures = materialIds.map((materialId) async {
        // 预加载评价摘要
        await ref.read(cachedMaterialReviewSummaryProvider(materialId).future);
        
        // 预加载评价列表（前10条）
        await ref.read(cachedMaterialReviewsProvider(
          materialId,
          limit: 10,
        ).future);
      });

      await Future.wait(futures);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// 评价数据刷新Provider
@riverpod
class RefreshReviewsNotifier extends _$RefreshReviewsNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// 刷新指定材料的评价数据
  Future<void> refreshMaterialReviews(String materialId) async {
    state = const AsyncValue.loading();
    
    try {
      final cache = ref.read(materialReviewCacheProvider);
      
      // 清除缓存
      cache.clearMaterialCache(materialId);
      
      // 使相关Provider失效，触发重新获取
      ref.invalidate(materialReviewsProvider);
      ref.invalidate(materialReviewSummaryProvider);
      ref.invalidate(cachedMaterialReviewsProvider);
      ref.invalidate(cachedMaterialReviewSummaryProvider);
      
      // 重新获取数据
      await ref.read(cachedMaterialReviewSummaryProvider(materialId).future);
      await ref.read(cachedMaterialReviewsProvider(materialId).future);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新所有评价数据
  Future<void> refreshAllReviews() async {
    state = const AsyncValue.loading();
    
    try {
      final cache = ref.read(materialReviewCacheProvider);
      
      // 清除所有缓存
      cache.clearAllCache();
      
      // 使所有相关Provider失效
      ref.invalidate(materialReviewsProvider);
      ref.invalidate(materialReviewSummaryProvider);
      ref.invalidate(cachedMaterialReviewsProvider);
      ref.invalidate(cachedMaterialReviewSummaryProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// 评价统计Provider
@riverpod
Map<String, dynamic> reviewCacheStats(ReviewCacheStatsRef ref) {
  final cache = ref.read(materialReviewCacheProvider);
  return cache.getCacheStats();
}

/// 批量评价摘要Provider（带缓存）
@riverpod
Future<Map<String, MaterialReviewSummary>> batchCachedReviewSummaries(
  BatchCachedReviewSummariesRef ref,
  List<String> materialIds,
) async {
  final cache = ref.read(materialReviewCacheProvider);
  final Map<String, MaterialReviewSummary> result = {};
  final List<String> uncachedIds = [];

  // 检查缓存
  for (final materialId in materialIds) {
    final cachedSummary = cache.getCachedSummary(materialId);
    if (cachedSummary != null) {
      result[materialId] = cachedSummary;
    } else {
      uncachedIds.add(materialId);
    }
  }

  // 获取未缓存的数据
  if (uncachedIds.isNotEmpty) {
    final repository = ref.read(materialReviewRepositoryProvider);
    final batchResult = await repository.getBatchReviewSummaries(uncachedIds);
    
    batchResult.fold(
      (failure) => throw Exception(failure.message),
      (summaries) {
        // 缓存新获取的数据
        for (final entry in summaries.entries) {
          cache.cacheSummary(entry.key, entry.value);
          result[entry.key] = entry.value;
        }
      },
    );
  }

  return result;
}

/// 评价数据预热Provider
@riverpod
class WarmupReviewsNotifier extends _$WarmupReviewsNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// 预热评价数据缓存
  /// 在应用启动时调用，预加载热门材料的评价数据
  Future<void> warmupCache(List<String> popularMaterialIds) async {
    state = const AsyncValue.loading();
    
    try {
      // 分批预加载，避免同时发起太多请求
      const batchSize = 5;
      for (int i = 0; i < popularMaterialIds.length; i += batchSize) {
        final batch = popularMaterialIds.skip(i).take(batchSize).toList();
        
        final futures = batch.map((materialId) async {
          try {
            await ref.read(cachedMaterialReviewSummaryProvider(materialId).future);
          } catch (e) {
            // 忽略单个材料的加载失败，继续加载其他材料
          }
        });
        
        await Future.wait(futures);
        
        // 在批次之间添加小延迟，避免服务器压力过大
        if (i + batchSize < popularMaterialIds.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
