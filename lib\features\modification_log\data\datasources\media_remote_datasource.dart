import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/log_media_model.dart';

/// 媒体远程数据源接口
abstract class MediaRemoteDataSource {
  /// 上传媒体文件
  Future<LogMediaModel> uploadMedia({
    required File file,
    required String logId,
    required String type,
    required String uploadedBy,
    Map<String, dynamic>? metadata,
  });
  
  /// 获取日志关联的所有媒体
  Future<List<LogMediaModel>> getLogMedia(String logId);
  
  /// 获取单个媒体详情
  Future<LogMediaModel> getMedia(String mediaId);
  
  /// 删除媒体
  Future<void> deleteMedia(String mediaId);
  
  /// 更新媒体元数据
  Future<LogMediaModel> updateMediaMetadata(String mediaId, Map<String, dynamic> metadata);
  
  /// 批量上传媒体
  Future<List<LogMediaModel>> uploadMultipleMedia({
    required List<File> files,
    required String logId,
    required String type,
    required String uploadedBy,
  });
  
  /// 更新媒体排序
  Future<List<LogMediaModel>> updateMediaOrder(String logId, List<String> orderedMediaIds);
  
  /// 获取用户上传的所有媒体
  Future<List<LogMediaModel>> getUserMedia(String userId, {int? limit, int? offset});
  
  /// 获取项目的所有媒体
  Future<List<LogMediaModel>> getProjectMedia(String projectId, {int? limit, int? offset});
}

/// 媒体远程数据源实现
class MediaRemoteDataSourceImpl implements MediaRemoteDataSource {
  final SupabaseClient supabaseClient;

  const MediaRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<LogMediaModel> uploadMedia({
    required File file,
    required String logId,
    required String type,
    required String uploadedBy,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 生成唯一文件名
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final filePath = 'logs/$logId/media/$fileName';

      // 上传文件到Supabase Storage
      final uploadResponse = await supabaseClient.storage
          .from('media')
          .upload(filePath, file);

      // 获取文件URL
      final fileUrl = supabaseClient.storage
          .from('media')
          .getPublicUrl(filePath);

      // 创建媒体记录
      final mediaData = {
        'log_id': logId,
        'file_name': fileName,
        'file_path': filePath,
        'file_url': fileUrl,
        'file_type': type,
        'file_size': await file.length(),
        'uploaded_by': uploadedBy,
        'metadata': metadata ?? {},
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('media')
          .insert(mediaData)
          .select()
          .single();

      return LogMediaModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '媒体上传失败: $e');
    }
  }

  @override
  Future<List<LogMediaModel>> getLogMedia(String logId) async {
    try {
      final response = await supabaseClient
          .from('media')
          .select('*')
          .eq('log_id', logId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => LogMediaModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取日志媒体失败: $e');
    }
  }

  @override
  Future<LogMediaModel> getMedia(String mediaId) async {
    try {
      final response = await supabaseClient
          .from('media')
          .select('*')
          .eq('id', mediaId)
          .single();

      return LogMediaModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '获取媒体详情失败: $e');
    }
  }

  @override
  Future<void> deleteMedia(String mediaId) async {
    try {
      // 先获取媒体信息以删除存储文件
      final media = await getMedia(mediaId);

      // 删除存储文件
      await supabaseClient.storage
          .from('media')
          .remove([media.url]);

      // 删除数据库记录
      await supabaseClient
          .from('media')
          .delete()
          .eq('id', mediaId);
    } catch (e) {
      throw ServerException(message: '删除媒体失败: $e');
    }
  }

  @override
  Future<LogMediaModel> updateMediaMetadata(String mediaId, Map<String, dynamic> metadata) async {
    try {
      final response = await supabaseClient
          .from('media')
          .update({
            'metadata': metadata,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', mediaId)
          .select()
          .single();

      return LogMediaModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新媒体元数据失败: $e');
    }
  }

  @override
  Future<List<LogMediaModel>> uploadMultipleMedia({
    required List<File> files,
    required String logId,
    required String type,
    required String uploadedBy,
  }) async {
    try {
      final List<LogMediaModel> uploadedMedia = [];

      for (final file in files) {
        final media = await uploadMedia(
          file: file,
          logId: logId,
          type: type,
          uploadedBy: uploadedBy,
        );
        uploadedMedia.add(media);
      }

      return uploadedMedia;
    } catch (e) {
      throw ServerException(message: '批量上传媒体失败: $e');
    }
  }

  @override
  Future<List<LogMediaModel>> updateMediaOrder(String logId, List<String> orderedMediaIds) async {
    try {
      final List<LogMediaModel> updatedMedia = [];

      for (int i = 0; i < orderedMediaIds.length; i++) {
        final response = await supabaseClient
            .from('media')
            .update({
              'display_order': i,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', orderedMediaIds[i])
            .eq('log_id', logId)
            .select()
            .single();

        updatedMedia.add(LogMediaModel.fromJson(response));
      }

      return updatedMedia;
    } catch (e) {
      throw ServerException(message: '更新媒体顺序失败: $e');
    }
  }

  @override
  Future<List<LogMediaModel>> getUserMedia(String userId, {int? limit, int? offset}) async {
    try {
      var query = supabaseClient
          .from('media')
          .select('*')
          .eq('uploaded_by', userId)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query;

      return (response as List)
          .map((json) => LogMediaModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取用户媒体失败: $e');
    }
  }

  @override
  Future<List<LogMediaModel>> getProjectMedia(String projectId, {int? limit, int? offset}) async {
    try {
      // 通过日志获取项目的媒体
      var query = supabaseClient
          .from('media')
          .select('''
            *,
            commits!inner(project_id)
          ''')
          .eq('commits.project_id', projectId)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query;

      return (response as List)
          .map((json) => LogMediaModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取项目媒体失败: $e');
    }
  }
}