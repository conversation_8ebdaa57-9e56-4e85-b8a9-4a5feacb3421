import 'package:freezed_annotation/freezed_annotation.dart';
import 'modification_system.dart';
import 'project_statistics.dart';
import '../../../modification_log/domain/entities/enums.dart';

part 'modification_project.freezed.dart';
part 'modification_project.g.dart';

/// 改装项目实体
/// 代表一个完整的房车改装项目
@freezed
class ModificationProject with _$ModificationProject {
  const factory ModificationProject({
    /// 项目唯一标识
    required String id,
    
    /// 项目标题
    required String title,
    
    /// 车辆型号
    required String vehicleModel,
    
    /// 车辆品牌
    required String vehicleBrand,
    
    /// 项目描述
    String? description,
    
    /// 主图片URL
    String? mainImageUrl,
    
    /// 项目所有者ID
    required String ownerId,
    
    /// 改装系统列表
    @Default([]) List<ModificationSystem> systems,
    
    /// 项目统计信息
    required ProjectStatistics statistics,
    
    /// 项目状态
    @Default(ProjectStatus.planning) ProjectStatus status,
    
    /// 是否公开
    @Default(false) bool isPublic,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 项目标签
    @Default([]) List<String> tags,
    
    /// 项目README内容
    String? readme,
  }) = _ModificationProject;

  factory ModificationProject.fromJson(Map<String, dynamic> json) =>
      _$ModificationProjectFromJson(json);
}

/// 项目状态枚举
@JsonEnum()
enum ProjectStatus {
  /// 规划中
  @JsonValue('planning')
  planning,
  
  /// 进行中
  @JsonValue('in_progress')
  inProgress,
  
  /// 已完成
  @JsonValue('completed')
  completed,
  
  /// 暂停
  @JsonValue('paused')
  paused,
}

/// ModificationProject扩展方法
extension ModificationProjectX on ModificationProject {
  /// 计算项目完成百分比
  double get completionPercentage {
    if (systems.isEmpty) return 0.0;
    
    final completedSystems = systems.where((s) => s.isCompleted).length;
    return completedSystems / systems.length;
  }
  
  /// 获取项目总费用
  double get totalCost {
    return systems.fold(0.0, (sum, system) => sum + system.actualAmount);
  }
  
  /// 获取项目总预算
  double get totalBudget {
    return systems.fold(0.0, (sum, system) => sum + system.budgetAmount);
  }
  
  /// 获取预算差额（正数表示超支，负数表示节省）
  double get budgetDifference {
    return totalCost - totalBudget;
  }
  
  /// 是否超出预算
  bool get isOverBudget {
    return budgetDifference > 0;
  }
  
  /// 获取项目进度状态描述
  String get progressDescription {
    final percentage = (completionPercentage * 100).round();
    return '$percentage% 完成 (${systems.where((s) => s.isCompleted).length}/${systems.length} 系统)';
  }
  
  /// 获取最后更新时间描述
  String get lastUpdateDescription {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 获取状态显示文本
  String get statusDisplayText {
    switch (status) {
      case ProjectStatus.planning:
        return '规划中';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.paused:
        return '暂停';
    }
  }
  
  /// 获取按系统类型分组的费用统计
  Map<SystemType, double> get costBySystemType {
    final Map<SystemType, double> result = {};
    
    for (final system in systems) {
      result[system.type] = (result[system.type] ?? 0) + system.actualAmount;
    }
    
    return result;
  }
  
  /// 获取系统数量统计
  Map<SystemType, int> get systemCountByType {
    final Map<SystemType, int> result = {};
    
    for (final system in systems) {
      result[system.type] = (result[system.type] ?? 0) + 1;
    }
    
    return result;
  }
}