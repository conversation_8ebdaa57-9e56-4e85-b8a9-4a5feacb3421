import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/create_material_request.dart';
import '../providers/material_provider.dart';

/// VanHub创建材料对话框 - 房车改装材料专业化
class CreateMaterialDialogWidget extends ConsumerStatefulWidget {
  const CreateMaterialDialogWidget({super.key});

  @override
  ConsumerState<CreateMaterialDialogWidget> createState() => _CreateMaterialDialogWidgetState();
}

class _CreateMaterialDialogWidgetState extends ConsumerState<CreateMaterialDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _specificationsController = TextEditingController();
  final _priceController = TextEditingController();
  final _supplierController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedCategory = '电力系统';
  DateTime? _purchaseDate;
  
  // 房车改装专业分类 - 11个专业分类
  final List<String> _categories = [
    '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装',
    '车顶改装', '底盘改装', '其他配件'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _specificationsController.dispose();
    _priceController.dispose();
    _supplierController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 头部区域
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal, Colors.teal.shade700],
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.add_shopping_cart, color: Colors.white, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '添加改装材料',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '记录每个配件的详细信息',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 表单内容
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 基本信息
                      _buildSectionTitle('基本信息', Icons.info_outline),
                      const SizedBox(height: 12),
                      
                      // 材料名称
                      _buildTextField(
                        controller: _nameController,
                        label: '材料名称',
                        hint: '例如：锂电池、逆变器、水泵等',
                        icon: Icons.label,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入材料名称';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          // 品牌
                          Expanded(
                            child: _buildTextField(
                              controller: _brandController,
                              label: '品牌',
                              hint: '如：比亚迪、胜华波等',
                              icon: Icons.business,
                            ),
                          ),
                          const SizedBox(width: 12),
                          // 型号
                          Expanded(
                            child: _buildTextField(
                              controller: _modelController,
                              label: '型号',
                              hint: '具体型号规格',
                              icon: Icons.model_training,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 分类选择
                      _buildDropdownField(
                        value: _selectedCategory,
                        label: '材料分类',
                        hint: '选择改装分类',
                        icon: Icons.category,
                        items: _categories,
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value ?? _categories.first;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 规格描述
                      _buildTextField(
                        controller: _specificationsController,
                        label: '规格参数',
                        hint: '详细的技术规格和参数...',
                        icon: Icons.description,
                        maxLines: 2,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // 采购信息
                      _buildSectionTitle('采购信息', Icons.shopping_cart),
                      const SizedBox(height: 12),
                      
                      Row(
                        children: [
                          // 价格
                          Expanded(
                            child: _buildTextField(
                              controller: _priceController,
                              label: '价格 (¥)',
                              hint: '采购价格',
                              icon: Icons.attach_money,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入价格';
                                }
                                final price = double.tryParse(value);
                                if (price == null || price < 0) {
                                  return '请输入有效价格';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          // 采购日期
                          Expanded(
                            child: InkWell(
                              onTap: _selectPurchaseDate,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey[400]!),
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.grey[50],
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.calendar_today, color: Colors.teal, size: 20),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _purchaseDate != null
                                            ? '${_purchaseDate!.year}-${_purchaseDate!.month.toString().padLeft(2, '0')}-${_purchaseDate!.day.toString().padLeft(2, '0')}'
                                            : '采购日期',
                                        style: TextStyle(
                                          color: _purchaseDate != null ? Colors.black87 : Colors.grey[600],
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 供应商
                      _buildTextField(
                        controller: _supplierController,
                        label: '供应商',
                        hint: '购买渠道或供应商名称',
                        icon: Icons.store,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 备注
                      _buildTextField(
                        controller: _notesController,
                        label: '备注说明',
                        hint: '使用心得、安装注意事项等...',
                        icon: Icons.note,
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _createMaterial,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add, size: 18),
                          SizedBox(width: 8),
                          Text('添加到材料库', style: TextStyle(fontWeight: FontWeight.w600)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.teal, size: 20),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.teal),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.teal, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
    );
  }

  Widget _buildDropdownField({
    required String value,
    required String label,
    required String hint,
    required IconData icon,
    required List<String> items,
    void Function(String?)? onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.teal),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.teal, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: items.map((item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _purchaseDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.teal,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      setState(() {
        _purchaseDate = date;
      });
    }
  }

  Future<void> _createMaterial() async {
    if (!_formKey.currentState!.validate()) return;

    final request = CreateMaterialRequest(
      name: _nameController.text.trim(),
      category: _selectedCategory,
      brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
      model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
      specifications: _specificationsController.text.trim().isEmpty ? null : _specificationsController.text.trim(),
      price: double.parse(_priceController.text),
      supplier: _supplierController.text.trim().isEmpty ? null : _supplierController.text.trim(),
      purchaseDate: _purchaseDate,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
    );

    final result = await ref.read(materialControllerProvider.notifier).createMaterial(request);
    
    if (mounted) {
      result.fold(
        (failure) => _showError(failure.message),
        (material) {
          _showSuccess('材料添加成功！已保存到材料库');
          Navigator.of(context).pop();
        },
      );
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}