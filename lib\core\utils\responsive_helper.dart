import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 屏幕尺寸枚举
enum ScreenSize {
  mobile,
  tablet,
  desktop,
}

/// 响应式断点
class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
}

/// 响应式辅助工具
class ResponsiveHelper {
  /// 获取当前屏幕尺寸类型
  static ScreenSize getScreenSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < ResponsiveBreakpoints.mobile) {
      return ScreenSize.mobile;
    } else if (width < ResponsiveBreakpoints.tablet) {
      return ScreenSize.tablet;
    } else {
      return ScreenSize.desktop;
    }
  }

  /// 检查是否为移动端
  static bool isMobile(BuildContext context) {
    return getScreenSize(context) == ScreenSize.mobile;
  }

  /// 检查是否为平板
  static bool isTablet(BuildContext context) {
    return getScreenSize(context) == ScreenSize.tablet;
  }

  /// 检查是否为桌面端
  static bool isDesktop(BuildContext context) {
    return getScreenSize(context) == ScreenSize.desktop;
  }

  /// 获取响应式值
  static T getValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final screenSize = getScreenSize(context);
    
    switch (screenSize) {
      case ScreenSize.mobile:
        return mobile;
      case ScreenSize.tablet:
        return tablet ?? mobile;
      case ScreenSize.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// 获取响应式边距
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return EdgeInsets.all(getValue(
      context,
      mobile: 16.0,
      tablet: 24.0,
      desktop: 32.0,
    ));
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(
    BuildContext context, {
    required double baseFontSize,
    double mobileScale = 0.9,
    double tabletScale = 1.0,
    double desktopScale = 1.1,
  }) {
    final scale = getValue(
      context,
      mobile: mobileScale,
      tablet: tabletScale,
      desktop: desktopScale,
    );
    
    return baseFontSize * scale;
  }

  /// 获取响应式卡片宽度
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    return getValue(
      context,
      mobile: screenWidth - 32, // 左右各16的边距
      tablet: math.min(600, screenWidth - 48), // 最大600，左右各24的边距
      desktop: math.min(800, screenWidth * 0.6), // 最大800或屏幕宽度的60%
    );
  }

  /// 获取响应式列数
  static int getColumnCount(BuildContext context) {
    return getValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );
  }

  /// 获取响应式网格交叉轴数量
  static int getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 600) return 1;
    if (width < 900) return 2;
    if (width < 1200) return 3;
    return 4;
  }

  /// 获取响应式AppBar高度
  static double getAppBarHeight(BuildContext context) {
    return getValue(
      context,
      mobile: kToolbarHeight,
      tablet: kToolbarHeight + 8,
      desktop: kToolbarHeight + 16,
    );
  }

  /// 获取响应式按钮高度
  static double getButtonHeight(BuildContext context) {
    return getValue(
      context,
      mobile: 44.0,
      tablet: 48.0,
      desktop: 52.0,
    );
  }

  /// 获取响应式图标大小
  static double getIconSize(BuildContext context, {double baseSize = 24.0}) {
    return getValue(
      context,
      mobile: baseSize * 0.9,
      tablet: baseSize,
      desktop: baseSize * 1.1,
    );
  }

  /// 获取响应式间距
  static double getSpacing(BuildContext context, {double baseSpacing = 16.0}) {
    return getValue(
      context,
      mobile: baseSpacing * 0.8,
      tablet: baseSpacing,
      desktop: baseSpacing * 1.2,
    );
  }

  /// 获取响应式圆角半径
  static double getBorderRadius(BuildContext context, {double baseRadius = 8.0}) {
    return getValue(
      context,
      mobile: baseRadius,
      tablet: baseRadius * 1.2,
      desktop: baseRadius * 1.5,
    );
  }

  /// 获取响应式阴影
  static List<BoxShadow> getBoxShadow(BuildContext context) {
    final elevation = getValue(
      context,
      mobile: 2.0,
      tablet: 4.0,
      desktop: 6.0,
    );

    return [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        blurRadius: elevation,
        offset: Offset(0, elevation / 2),
      ),
    ];
  }

  /// 获取安全区域边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 检查是否有键盘显示
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部导航栏高度
  static double getBottomNavigationBarHeight(BuildContext context) {
    return kBottomNavigationBarHeight + MediaQuery.of(context).padding.bottom;
  }

  /// 获取屏幕方向
  static Orientation getOrientation(BuildContext context) {
    return MediaQuery.of(context).orientation;
  }

  /// 检查是否为横屏
  static bool isLandscape(BuildContext context) {
    return getOrientation(context) == Orientation.landscape;
  }

  /// 检查是否为竖屏
  static bool isPortrait(BuildContext context) {
    return getOrientation(context) == Orientation.portrait;
  }

  /// 获取设备像素比
  static double getDevicePixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// 获取文本缩放因子
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaler.scale(1.0);
  }

  /// 获取亮度模式
  static Brightness getBrightness(BuildContext context) {
    return MediaQuery.of(context).platformBrightness;
  }

  /// 检查是否为深色模式
  static bool isDarkMode(BuildContext context) {
    return getBrightness(context) == Brightness.dark;
  }

  /// 获取响应式最大宽度容器
  static Widget getMaxWidthContainer({
    required Widget child,
    double? maxWidth,
  }) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: maxWidth ?? 1200,
        ),
        child: child,
      ),
    );
  }

  /// 获取响应式Flex值
  static int getFlexValue(BuildContext context, {
    int mobile = 1,
    int? tablet,
    int? desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile,
      desktop: desktop ?? tablet ?? mobile,
    );
  }
}

/// 响应式构建器
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ScreenSize screenSize) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = ResponsiveHelper.getScreenSize(context);
    return builder(context, screenSize);
  }
}

/// 响应式布局构建器
class ResponsiveLayoutBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayoutBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenSize) {
        switch (screenSize) {
          case ScreenSize.mobile:
            return mobile;
          case ScreenSize.tablet:
            return tablet ?? mobile;
          case ScreenSize.desktop:
            return desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}


