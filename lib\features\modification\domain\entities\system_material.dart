import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../modification_log/domain/entities/enums.dart';

part 'system_material.freezed.dart';
part 'system_material.g.dart';

/// 系统材料实体
/// 代表改装系统中的具体材料
@freezed
class SystemMaterial with _$SystemMaterial {
  const factory SystemMaterial({
    /// 材料唯一标识
    required String id,
    
    /// 关联的材料库ID（用于智能联动）
    String? materialId,
    
    /// 材料名称
    required String name,
    
    /// 材料规格/型号
    required String specification,
    
    /// 材料品牌
    String? brand,
    
    /// 数量
    required int quantity,
    
    /// 单价
    required double unitPrice,
    
    /// 总价
    required double totalPrice,
    
    /// 购买状态
    @Default(MaterialStatus.pending) MaterialStatus purchaseStatus,
    
    /// 安装状态
    @Default(MaterialStatus.pending) MaterialStatus installStatus,
    
    /// 材料备注
    String? notes,
    
    /// 购买日期
    DateTime? purchaseDate,
    
    /// 安装日期
    DateTime? installDate,
    
    /// 供应商信息
    String? supplier,
    
    /// 材料图片URL
    String? imageUrl,
    
    /// 材料分类
    String? category,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 预计交付日期
    DateTime? expectedDeliveryDate,
    
    /// 保修期（月）
    int? warrantyMonths,
    
    /// 材料重量（kg）
    double? weight,
    
    /// 材料尺寸描述
    String? dimensions,
  }) = _SystemMaterial;

  factory SystemMaterial.fromJson(Map<String, dynamic> json) =>
      _$SystemMaterialFromJson(json);
}



/// SystemMaterial扩展方法
extension SystemMaterialX on SystemMaterial {
  /// 材料是否已购买
  bool get isPurchased {
    return purchaseStatus == MaterialStatus.received || 
           purchaseStatus == MaterialStatus.installed;
  }
  
  /// 材料是否已安装
  bool get isInstalled {
    return installStatus == MaterialStatus.installed;
  }
  
  /// 材料是否已下单
  bool get isOrdered {
    return purchaseStatus == MaterialStatus.ordered ||
           purchaseStatus == MaterialStatus.received ||
           purchaseStatus == MaterialStatus.installed;
  }
  
  /// 获取购买状态显示文本
  String get purchaseStatusDisplayText {
    switch (purchaseStatus) {
      case MaterialStatus.pending:
        return '待购买';
      case MaterialStatus.ordered:
        return '已下单';
      case MaterialStatus.received:
        return '已收货';
      case MaterialStatus.installed:
        return '已安装';
      case MaterialStatus.returned:
        return '已退货';
    }
  }
  
  /// 获取安装状态显示文本
  String get installStatusDisplayText {
    switch (installStatus) {
      case MaterialStatus.pending:
        return '待安装';
      case MaterialStatus.ordered:
        return '准备中';
      case MaterialStatus.received:
        return '可安装';
      case MaterialStatus.installed:
        return '已安装';
      case MaterialStatus.returned:
        return '已移除';
    }
  }
  
  /// 获取综合状态描述
  String get overallStatusDescription {
    if (isInstalled) {
      return '已完成';
    } else if (isPurchased) {
      return '待安装';
    } else if (isOrdered) {
      return '已下单';
    } else {
      return '待购买';
    }
  }
  
  /// 获取状态进度（0-1）
  double get statusProgress {
    if (isInstalled) {
      return 1.0;
    } else if (isPurchased) {
      return 0.75;
    } else if (isOrdered) {
      return 0.5;
    } else {
      return 0.25;
    }
  }
  
  /// 计算总价（基于数量和单价）
  SystemMaterial calculateTotalPrice() {
    return copyWith(totalPrice: quantity * unitPrice);
  }
  
  /// 获取预计交付状态
  String? get deliveryStatusDescription {
    if (expectedDeliveryDate == null) return null;
    
    final now = DateTime.now();
    final difference = expectedDeliveryDate!.difference(now);
    
    if (difference.isNegative) {
      final daysPast = difference.inDays.abs();
      return '已延期 $daysPast 天';
    } else if (difference.inDays == 0) {
      return '今日到货';
    } else if (difference.inDays == 1) {
      return '明日到货';
    } else {
      return '${difference.inDays} 天后到货';
    }
  }
  
  /// 是否延期交付
  bool get isDelayed {
    if (expectedDeliveryDate == null) return false;
    return DateTime.now().isAfter(expectedDeliveryDate!) && !isPurchased;
  }
  
  /// 获取保修到期日期
  DateTime? get warrantyExpiryDate {
    if (warrantyMonths == null || installDate == null) return null;
    return installDate!.add(Duration(days: warrantyMonths! * 30));
  }
  
  /// 保修是否即将到期（30天内）
  bool get isWarrantyExpiringSoon {
    final expiryDate = warrantyExpiryDate;
    if (expiryDate == null) return false;
    
    final now = DateTime.now();
    final difference = expiryDate.difference(now);
    return difference.inDays <= 30 && difference.inDays > 0;
  }
  
  /// 保修是否已过期
  bool get isWarrantyExpired {
    final expiryDate = warrantyExpiryDate;
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate);
  }
}