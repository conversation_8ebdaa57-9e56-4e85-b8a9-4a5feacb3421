import 'package:fpdart/fpdart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../../project/domain/entities/project.dart';
import '../../../project/data/models/project_model.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/bom_statistics.dart';
import '../../domain/entities/create_bom_item_request.dart';
import '../../domain/repositories/bom_repository.dart';
import '../../domain/services/bom_statistics_service.dart';
import '../../domain/services/bom_statistics_service_impl.dart';
import '../datasources/bom_remote_datasource.dart';
import '../models/bom_item_model.dart';

class BomRepositoryImpl implements BomRepository {
  final BomRemoteDataSource remoteDataSource;
  final Ref ref;
  final BomStatisticsService _statisticsService;

  const BomRepositoryImpl({
    required this.remoteDataSource,
    required this.ref,
    BomStatisticsService? statisticsService,
  }) : _statisticsService = statisticsService ?? const BomStatisticsServiceImpl();

  @override
  Future<Either<Failure, BomItem>> createBomItem(CreateBomItemRequest request) async {
    try {
      // 从request中获取userId，避免循环依赖
      final bomItemModel = await remoteDataSource.createBomItem(request, request.userId);
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '创建BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getProjectBomItems(String projectId) async {
    try {
      final bomItemModels = await remoteDataSource.getProjectBomItems(projectId);
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取项目BOM失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getBomItemsByStatus(
    String projectId, 
    BomItemStatus status,
  ) async {
    try {
      final bomItemModels = await remoteDataSource.getBomItemsByStatus(projectId, status);
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BomItem>> getBomItemById(String bomItemId) async {
    try {
      final bomItemModel = await remoteDataSource.getBomItemById(bomItemId);
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取BOM项详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BomItem>> updateBomItemById(
    String bomItemId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final bomItemModel = await remoteDataSource.updateBomItem(bomItemId, updates);
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBomItem(String bomItemId) async {
    try {
      await remoteDataSource.deleteBomItem(bomItemId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '删除BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BomItem>> updateBomItemStatus(
    String bomItemId, 
    BomItemStatus status,
  ) async {
    try {
      final bomItemModel = await remoteDataSource.updateBomItemStatus(bomItemId, status);
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新BOM项状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> batchUpdateBomItemStatus(
    List<String> bomItemIds, 
    BomItemStatus status,
  ) async {
    try {
      final bomItemModels = await remoteDataSource.batchUpdateBomItemStatus(bomItemIds, status);
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '批量更新BOM项状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BomStatistics>> getProjectBomStatistics(String projectId) async {
    try {
      // 获取项目的所有BOM项目
      final bomItemsResult = await getProjectBomItems(projectId);
      
      return bomItemsResult.fold(
        (failure) => Left(failure),
        (bomItems) {
          // 使用统计服务计算统计数据
          final statistics = _statisticsService.calculateStatistics(bomItems);
          return Right(statistics);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取BOM统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> searchBomItems(
    String projectId,
    String query, {
    BomItemStatus? status,
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final bomItemModels = await remoteDataSource.searchBomItems(
        projectId,
        query,
        status: status,
        category: category,
        minPrice: minPrice,
        maxPrice: maxPrice,
        limit: limit,
        offset: offset,
      );
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '搜索BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BomItem>> addMaterialToBom({
    required String projectId,
    required String materialId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  }) async {
    try {
      // 获取当前用户ID
      final userId = ref.read(currentUserIdProvider);
      if (userId == null) {
        return const Left(ValidationFailure(message: '用户未登录'));
      }

      final bomItemModel = await remoteDataSource.addMaterialToBom(
        projectId: projectId,
        materialId: materialId,
        userId: userId,
        quantity: quantity,
        customPrice: customPrice,
        plannedDate: plannedDate,
        notes: notes,
      );
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '从材料库添加到BOM失败: $e'));
    }
  }

  /// 添加材料到BOM的新方法，包含userId参数
  Future<Either<Failure, BomItem>> addMaterialToBomWithUserId({
    required String projectId,
    required String materialId,
    required String userId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  }) async {
    try {
      final bomItemModel = await remoteDataSource.addMaterialToBom(
        projectId: projectId,
        materialId: materialId,
        userId: userId,
        quantity: quantity,
        customPrice: customPrice,
        plannedDate: plannedDate,
        notes: notes,
      );
      return Right(bomItemModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '从材料库添加到BOM失败: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> saveBomItemToMaterialLibrary(String bomItemId) async {
    try {
      // 获取当前用户ID
      final userId = ref.read(currentUserIdProvider);
      if (userId == null) {
        return const Left(ValidationFailure(message: '用户未登录'));
      }

      final materialId = await remoteDataSource.saveBomItemToMaterialLibrary(bomItemId, userId);
      return Right(materialId);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '保存BOM项到材料库失败: $e'));
    }
  }

  /// 保存BOM项到材料库的新方法，包含userId参数
  Future<Either<Failure, String>> saveBomItemToMaterialLibraryWithUserId(String bomItemId, String userId) async {
    try {
      final materialId = await remoteDataSource.saveBomItemToMaterialLibrary(bomItemId, userId);
      return Right(materialId);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '保存BOM项到材料库失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getUserBomItems(String userId) async {
    try {
      final bomItemModels = await remoteDataSource.getUserBomItems(userId);
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getBomItemsByMaterialId(String materialId) async {
    try {
      final bomItemModels = await remoteDataSource.getBomItemsByMaterialId(materialId);
      final bomItems = bomItemModels.map((model) => model.toEntity()).toList();
      return Right(bomItems);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取材料相关BOM项失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getBomItemsByProjectId(String projectId) async {
    // 这个方法与getProjectBomItems功能相同，可以直接调用
    return getProjectBomItems(projectId);
  }

  @override
  Future<Either<Failure, List<Project>>> getUserProjects(String userId) async {
    try {
      final projectData = await remoteDataSource.getUserProjects(userId);
      final projects = projectData
          .map((data) => ProjectModel.fromJson(data as Map<String, dynamic>))
          .map((model) => model.toEntity())
          .toList();
      return Right(projects);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateBomItem(BomItem bomItem) async {
    try {
      // 将BomItem转换为更新字段Map
      final updates = {
        'material_name': bomItem.materialName,
        'description': bomItem.description,
        'status': bomItem.status.code,
        'quantity': bomItem.quantity,
        'unit_price': bomItem.unitPrice,
        'material_id': bomItem.materialId,
        'category': bomItem.category,
        'brand': bomItem.brand,
        'model': bomItem.model,
        'specifications': bomItem.specifications,
        'supplier': bomItem.supplier,
        'supplier_url': bomItem.supplierUrl,
        'image_url': bomItem.imageUrl,
        'planned_date': bomItem.plannedDate?.toIso8601String(),
        'purchased_date': bomItem.purchasedDate?.toIso8601String(),
        'used_date': bomItem.usedDate?.toIso8601String(),
        'estimated_price': bomItem.estimatedPrice,
        'actual_price': bomItem.actualPrice,
        'notes': bomItem.notes,
        'tags': bomItem.tags,
        'metadata': bomItem.metadata,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // 过滤掉null值
      final filteredUpdates = Map<String, dynamic>.from(updates)
        ..removeWhere((key, value) => value == null);

      // 调用更新方法
      await remoteDataSource.updateBomItem(bomItem.id, filteredUpdates);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新BOM项失败: $e'));
    }
  }
}