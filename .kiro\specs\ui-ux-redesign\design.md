# Design Document

## Overview

VanHub UI/UX重设计旨在创建一个专门为房车改装项目优化的用户界面。设计的核心理念是"层次清晰、信息丰富、操作高效"，通过现代化的Material Design 3设计语言，为用户提供直观的改装项目管理体验。

设计重点关注三个核心价值：
1. **结构化展示** - 清晰展示车辆→改装系统→物料的层次关系
2. **费用透明** - 多维度的费用统计和可视化分析
3. **智能联动** - 材料库与项目管理的无缝集成

## Architecture

### 信息架构设计

```
VanHub应用架构
├── 底部导航栏 (Bottom Navigation)
│   ├── 首页 (Home) - 个性化动态流
│   ├── 发现 (Explore) - 项目浏览和搜索
│   ├── 我的项目 (My Projects) - 项目管理中心
│   └── 我 (Profile) - 个人资料和设置
├── 浮动操作按钮 (FAB) - 快速创建入口
└── 项目详情页 (Project Detail)
    ├── 头部区域 (Header) - 项目概览和操作
    └── 标签页系统 (TabBar)
        ├── 概览 (Overview) - 项目介绍和摘要
        ├── 改装系统 (Systems) - 系统层次结构
        ├── 费用分析 (Cost Analysis) - 费用统计图表
        └── 时间轴 (Timeline) - 项目进度跟踪
```

### 数据流架构

```mermaid
graph TD
    A[用户操作] --> B[UI组件]
    B --> C[状态管理 Riverpod]
    C --> D[业务逻辑层]
    D --> E[数据层]
    E --> F[本地缓存]
    E --> G[远程API]
    
    H[材料库] --> I[智能推荐]
    I --> J[项目物料]
    J --> K[费用计算]
    K --> L[统计图表]
```

## Components and Interfaces

### 1. 核心组件设计

#### ProjectCard 组件
```dart
class ProjectCard extends StatelessWidget {
  final Project project;
  final VoidCallback? onTap;
  final bool showDetailedStats;
  
  // 组件结构：
  // ┌─────────────────────────────────┐
  // │ [车辆主图]           [状态标识] │
  // │ 项目标题 (大号粗体)              │
  // │ 车型信息 (小号灰色)              │
  // │ ┌─────┐ ┌─────┐ ┌─────┐        │
  // │ │💰费用│ │🔧系统│ │📦物料│        │
  // │ └─────┘ └─────┘ └─────┘        │
  // │ ████████████░░░░ 75%           │ <- 进度条
  // │ 最后更新：2天前                  │
  // └─────────────────────────────────┘
}
```

#### SystemCard 组件
```dart
class SystemCard extends StatelessWidget {
  final ModificationSystem system;
  final bool isExpanded;
  final VoidCallback? onToggle;
  
  // 组件结构：
  // ┌─────────────────────────────────┐
  // │ 🔌 电路系统        ¥15,000 [▼] │
  // │ 进度: ████████░░ 80%  (8/10)   │
  // │ ├─ 逆变器 ¥3,000 ✅            │
  // │ ├─ 电池组 ¥8,000 ✅            │
  // │ └─ 充电器 ¥4,000 🔄            │
  // └─────────────────────────────────┘
}
```

#### MaterialItem 组件
```dart
class MaterialItem extends StatelessWidget {
  final Material material;
  final int quantity;
  final MaterialStatus status;
  
  // 组件结构：
  // ┌─────────────────────────────────┐
  // │ 📦 逆变器 3000W                 │
  // │ 规格: AIMS 3000W 纯正弦波       │
  // │ 数量: 1个  单价: ¥3,000        │
  // │ 状态: [已购买] [待安装]          │
  // └─────────────────────────────────┘
}
```

### 2. 页面布局设计

#### 我的项目页面 (My Projects)
```
┌─────────────────────────────────┐
│ 我的项目                    [+] │ <- 标题栏
├─────────────────────────────────┤
│ 🔍 搜索项目...                  │ <- 搜索栏
├─────────────────────────────────┤
│ [ProjectCard - 项目1]           │
│ [ProjectCard - 项目2]           │
│ [ProjectCard - 项目3]           │
│ ...                             │
└─────────────────────────────────┘
```

#### 项目详情页面 (Project Detail)
```
┌─────────────────────────────────┐
│ [← 返回]  项目标题    [⋯ 更多]  │ <- 导航栏
├─────────────────────────────────┤
│ [车辆主图背景]                   │
│ 作者信息 👤 张三                 │
│ [复刻] [关注] [分享]             │ <- 操作按钮
├─────────────────────────────────┤
│ [概览] [系统] [费用] [时间轴]    │ <- 标签栏
├─────────────────────────────────┤
│                                 │
│ [标签页内容区域]                 │
│                                 │
└─────────────────────────────────┘
```

### 3. 交互设计规范

#### 手势交互
- **左右滑动** - 标签页切换
- **上下滑动** - 页面滚动
- **长按** - 显示上下文菜单
- **双击** - 快速操作（如标记完成）
- **拖拽** - 系统排序、物料移动

#### 状态反馈
- **加载状态** - 骨架屏 + 进度指示器
- **成功状态** - 绿色勾选 + 简短提示
- **错误状态** - 红色警告 + 重试按钮
- **空状态** - 插图 + 引导文案

## Data Models

### 核心数据模型

```dart
@freezed
class ModificationProject with _$ModificationProject {
  const factory ModificationProject({
    required String id,
    required String title,
    required String vehicleModel,
    required String vehicleBrand,
    String? description,
    String? mainImageUrl,
    required List<ModificationSystem> systems,
    required ProjectStatistics statistics,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(ProjectStatus.planning) ProjectStatus status,
  }) = _ModificationProject;
}

@freezed
class ModificationSystem with _$ModificationSystem {
  const factory ModificationSystem({
    required String id,
    required String name,
    required SystemType type,
    String? description,
    required double budgetAmount,
    required double actualAmount,
    required List<SystemMaterial> materials,
    required SystemStatus status,
    @Default(0) int sortOrder,
  }) = _ModificationSystem;
}

@freezed
class SystemMaterial with _$SystemMaterial {
  const factory SystemMaterial({
    required String id,
    required String materialId, // 关联材料库
    required String name,
    required String specification,
    required int quantity,
    required double unitPrice,
    required double totalPrice,
    required MaterialStatus purchaseStatus,
    required MaterialStatus installStatus,
    String? notes,
    DateTime? purchaseDate,
    DateTime? installDate,
  }) = _SystemMaterial;
}

@freezed
class ProjectStatistics with _$ProjectStatistics {
  const factory ProjectStatistics({
    required double totalBudget,
    required double totalActualCost,
    required int totalSystems,
    required int completedSystems,
    required int totalMaterials,
    required int completedMaterials,
    required Map<SystemType, double> costBySystem,
    required Map<String, double> costByMonth,
  }) = _ProjectStatistics;
}
```

### 枚举定义

```dart
enum SystemType {
  electrical,    // 电路系统
  plumbing,     // 水路系统
  storage,      // 储物系统
  bedding,      // 床铺系统
  kitchen,      // 厨房系统
  bathroom,     // 卫浴系统
  exterior,     // 外观改装
  chassis,      // 底盘改装
  custom,       // 自定义系统
}

enum ProjectStatus {
  planning,     // 规划中
  inProgress,   // 进行中
  completed,    // 已完成
  paused,       // 暂停
}

enum MaterialStatus {
  pending,      // 待处理
  ordered,      // 已下单
  received,     // 已收货
  installed,    // 已安装
  returned,     // 已退货
}
```

## Error Handling

### 错误分类和处理策略

```dart
@freezed
class UIFailure with _$UIFailure {
  const factory UIFailure.network({
    required String message,
    String? details,
  }) = NetworkFailure;
  
  const factory UIFailure.validation({
    required String field,
    required String message,
  }) = ValidationFailure;
  
  const factory UIFailure.permission({
    required String action,
    required String message,
  }) = PermissionFailure;
  
  const factory UIFailure.storage({
    required String operation,
    required String message,
  }) = StorageFailure;
}
```

### 错误展示组件

```dart
class ErrorDisplayWidget extends StatelessWidget {
  final UIFailure failure;
  final VoidCallback? onRetry;
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48),
            SizedBox(height: 16),
            Text(failure.message, style: Theme.of(context).textTheme.titleMedium),
            if (onRetry != null) ...[
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: onRetry,
                child: Text('重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
```

## Testing Strategy

### 1. 单元测试策略

```dart
// 测试数据模型
group('ModificationProject Tests', () {
  test('should calculate total cost correctly', () {
    final project = ModificationProject(/* ... */);
    expect(project.statistics.totalActualCost, equals(expectedCost));
  });
  
  test('should calculate completion percentage', () {
    final project = ModificationProject(/* ... */);
    expect(project.completionPercentage, equals(0.75));
  });
});

// 测试业务逻辑
group('ProjectStatisticsCalculator Tests', () {
  test('should group costs by system type', () {
    final calculator = ProjectStatisticsCalculator();
    final result = calculator.calculateCostBySystem(materials);
    expect(result[SystemType.electrical], equals(15000.0));
  });
});
```

### 2. Widget测试策略

```dart
group('ProjectCard Widget Tests', () {
  testWidgets('should display project information correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ProjectCard(project: mockProject),
      ),
    );
    
    expect(find.text(mockProject.title), findsOneWidget);
    expect(find.text('¥${mockProject.statistics.totalActualCost}'), findsOneWidget);
  });
  
  testWidgets('should navigate to detail page on tap', (tester) async {
    bool navigated = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: ProjectCard(
          project: mockProject,
          onTap: () => navigated = true,
        ),
      ),
    );
    
    await tester.tap(find.byType(ProjectCard));
    expect(navigated, isTrue);
  });
});
```

### 3. 集成测试策略

```dart
group('Project Management Flow Tests', () {
  testWidgets('complete project creation flow', (tester) async {
    // 1. 启动应用
    await tester.pumpWidget(MyApp());
    
    // 2. 点击创建项目按钮
    await tester.tap(find.byType(FloatingActionButton));
    await tester.pumpAndSettle();
    
    // 3. 填写项目信息
    await tester.enterText(find.byKey(Key('project_title')), '测试项目');
    await tester.enterText(find.byKey(Key('vehicle_model')), '大通V90');
    
    // 4. 提交创建
    await tester.tap(find.text('创建项目'));
    await tester.pumpAndSettle();
    
    // 5. 验证项目创建成功
    expect(find.text('测试项目'), findsOneWidget);
  });
});
```

### 4. 性能测试策略

```dart
group('Performance Tests', () {
  testWidgets('should render large project list smoothly', (tester) async {
    final largeProjectList = List.generate(1000, (i) => mockProject.copyWith(id: '$i'));
    
    await tester.pumpWidget(
      MaterialApp(
        home: ProjectListPage(projects: largeProjectList),
      ),
    );
    
    // 测试滚动性能
    await tester.fling(find.byType(ListView), Offset(0, -500), 1000);
    await tester.pumpAndSettle();
    
    // 验证没有性能问题
    expect(tester.binding.hasScheduledFrame, isFalse);
  });
});
```

### 5. 可访问性测试

```dart
group('Accessibility Tests', () {
  testWidgets('should have proper semantic labels', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ProjectCard(project: mockProject),
      ),
    );
    
    // 验证语义标签
    expect(
      tester.getSemantics(find.byType(ProjectCard)),
      matchesSemantics(
        label: '项目：${mockProject.title}，费用：¥${mockProject.statistics.totalActualCost}',
        button: true,
      ),
    );
  });
});
```

## 设计系统规范

### 颜色系统
```dart
class VanHubColors {
  // 主色调 - 代表专业和可靠
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryVariant = Color(0xFF1565C0);
  
  // 辅助色 - 代表创新和活力
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryVariant = Color(0xFFF57C00);
  
  // 系统状态色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // 改装系统专用色
  static const Map<SystemType, Color> systemColors = {
    SystemType.electrical: Color(0xFFFFEB3B),    // 电路-黄色
    SystemType.plumbing: Color(0xFF2196F3),      // 水路-蓝色
    SystemType.storage: Color(0xFF9C27B0),       // 储物-紫色
    SystemType.bedding: Color(0xFF795548),       // 床铺-棕色
    SystemType.kitchen: Color(0xFFFF5722),       // 厨房-橙色
    SystemType.bathroom: Color(0xFF00BCD4),      // 卫浴-青色
    SystemType.exterior: Color(0xFF607D8B),      // 外观-蓝灰
    SystemType.chassis: Color(0xFF424242),       // 底盘-深灰
  };
}
```

### 字体系统
```dart
class VanHubTextStyles {
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
  );
  
  static const TextStyle subtitle1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
  );
  
  static const TextStyle body1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.5,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.4,
  );
}
```

### 间距系统
```dart
class VanHubSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
}
```

这个设计文档提供了完整的UI/UX设计方案，包括组件设计、页面布局、数据模型、错误处理、测试策略和设计系统规范。设计重点关注改装项目的层次结构展示和费用管理，确保用户能够清晰地理解和管理他们的改装项目。