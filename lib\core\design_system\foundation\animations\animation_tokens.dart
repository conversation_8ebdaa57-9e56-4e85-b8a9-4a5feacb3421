/// VanHub动画令牌系统
/// 
/// 完整的动画系统，支持60FPS流畅动画
/// 包含时长、曲线、转场、微交互等

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 动画时长令牌
class VanHubAnimationDurations {
  VanHubAnimationDurations._();

  /// 瞬间 - 用于即时反馈
  static const Duration instant = Duration(milliseconds: 0);
  
  /// 极快 - 用于微交互
  static const Duration ultraFast = Duration(milliseconds: 100);
  
  /// 快速 - 用于悬停效果
  static const Duration fast = Duration(milliseconds: 150);
  
  /// 正常 - 用于一般动画
  static const Duration normal = Duration(milliseconds: 300);
  
  /// 慢速 - 用于页面转场
  static const Duration slow = Duration(milliseconds: 500);
  
  /// 极慢 - 用于复杂动画
  static const Duration ultraSlow = Duration(milliseconds: 800);
  
  /// 超长 - 用于特殊效果
  static const Duration extended = Duration(milliseconds: 1200);
}

/// 动画曲线令牌
class VanHubAnimationCurves {
  VanHubAnimationCurves._();

  /// 标准缓动
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;
  
  /// 快速缓动
  static const Curve fastOutSlowIn = Curves.fastOutSlowIn;
  
  /// 弹性缓动
  static const Curve elasticIn = Curves.elasticIn;
  static const Curve elasticOut = Curves.elasticOut;
  static const Curve elasticInOut = Curves.elasticInOut;
  
  /// 弹跳缓动
  static const Curve bounceIn = Curves.bounceIn;
  static const Curve bounceOut = Curves.bounceOut;
  static const Curve bounceInOut = Curves.bounceInOut;
  
  /// 自定义缓动
  static const Curve anticipate = Cubic(0.36, 0, 0.66, -0.56);
  static const Curve overshoot = Cubic(0.34, 1.56, 0.64, 1);
  static const Curve smooth = Cubic(0.4, 0.0, 0.2, 1.0);
  static const Curve sharp = Cubic(0.4, 0.0, 0.6, 1.0);
}

/// 页面转场动画
class VanHubPageTransitions {
  VanHubPageTransitions._();

  /// 滑动转场
  static Route<T> slideTransition<T>(
    Widget page, {
    SlideDirection direction = SlideDirection.right,
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        Offset begin;
        switch (direction) {
          case SlideDirection.right:
            begin = const Offset(1.0, 0.0);
            break;
          case SlideDirection.left:
            begin = const Offset(-1.0, 0.0);
            break;
          case SlideDirection.up:
            begin = const Offset(0.0, 1.0);
            break;
          case SlideDirection.down:
            begin = const Offset(0.0, -1.0);
            break;
        }
        
        return SlideTransition(
          position: Tween<Offset>(
            begin: begin,
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve,
          )),
          child: child,
        );
      },
    );
  }

  /// 淡入转场
  static Route<T> fadeTransition<T>(
    Widget page, {
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: curve,
          ),
          child: child,
        );
      },
    );
  }

  /// 缩放转场
  static Route<T> scaleTransition<T>(
    Widget page, {
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeInOut,
    double beginScale = 0.0,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(
            begin: beginScale,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve,
          )),
          child: child,
        );
      },
    );
  }

  /// 旋转转场
  static Route<T> rotationTransition<T>(
    Widget page, {
    Duration duration = VanHubAnimationDurations.slow,
    Curve curve = VanHubAnimationCurves.easeInOut,
    double beginRotation = 0.0,
    double endRotation = 1.0,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return RotationTransition(
          turns: Tween<double>(
            begin: beginRotation,
            end: endRotation,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve,
          )),
          child: child,
        );
      },
    );
  }
}

/// 组件动画效果
class VanHubComponentAnimations {
  VanHubComponentAnimations._();

  /// 淡入向上
  static Widget fadeInUp(
    Widget child, {
    Duration delay = Duration.zero,
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeOut,
    double distance = 20.0,
  }) {
    return child
        .animate(delay: delay)
        .fadeIn(duration: duration, curve: curve)
        .slideY(
          begin: distance / 100,
          end: 0,
          duration: duration,
          curve: curve,
        );
  }

  /// 滑入左侧
  static Widget slideInLeft(
    Widget child, {
    Duration delay = Duration.zero,
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeOut,
    double distance = 50.0,
  }) {
    return child
        .animate(delay: delay)
        .slideX(
          begin: -distance / 100,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration, curve: curve);
  }

  /// 缩放进入
  static Widget scaleIn(
    Widget child, {
    Duration delay = Duration.zero,
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.elasticOut,
    double beginScale = 0.0,
  }) {
    return child
        .animate(delay: delay)
        .scale(
          begin: Offset(beginScale, beginScale),
          end: const Offset(1.0, 1.0),
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration, curve: VanHubAnimationCurves.easeOut);
  }

  /// 旋转进入
  static Widget rotateIn(
    Widget child, {
    Duration delay = Duration.zero,
    Duration duration = VanHubAnimationDurations.slow,
    Curve curve = VanHubAnimationCurves.elasticOut,
    double beginRotation = -0.5,
  }) {
    return child
        .animate(delay: delay)
        .rotate(
          begin: beginRotation,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration, curve: VanHubAnimationCurves.easeOut);
  }
}

/// 微交互动画
class VanHubMicroInteractions {
  VanHubMicroInteractions._();

  /// 悬停缩放
  static Widget hoverScale(
    Widget child, {
    double scale = 1.05,
    Duration duration = VanHubAnimationDurations.fast,
    Curve curve = VanHubAnimationCurves.easeOut,
  }) {
    return MouseRegion(
      onEnter: (_) {},
      onExit: (_) {},
      child: AnimatedScale(
        scale: scale,
        duration: duration,
        curve: curve,
        child: child,
      ),
    );
  }

  /// 按压缩放
  static Widget pressScale(
    Widget child, {
    double scale = 0.95,
    Duration duration = VanHubAnimationDurations.ultraFast,
    Curve curve = VanHubAnimationCurves.easeOut,
  }) {
    return GestureDetector(
      onTapDown: (_) {},
      onTapUp: (_) {},
      onTapCancel: () {},
      child: AnimatedScale(
        scale: scale,
        duration: duration,
        curve: curve,
        child: child,
      ),
    );
  }

  /// 脉冲动画
  static Widget pulse(
    Widget child, {
    Duration duration = const Duration(milliseconds: 1000),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .scale(
          begin: Offset(minScale, minScale),
          end: Offset(maxScale, maxScale),
          duration: duration,
          curve: VanHubAnimationCurves.easeInOut,
        );
  }

  /// 摇摆动画
  static Widget shake(
    Widget child, {
    Duration duration = VanHubAnimationDurations.normal,
    double distance = 10.0,
    int count = 3,
  }) {
    return child
        .animate()
        .shake(
          duration: duration,
          hz: count.toDouble(),
          offset: Offset(distance, 0),
        );
  }
}

/// 交错动画
class VanHubStaggeredAnimations {
  VanHubStaggeredAnimations._();

  /// 创建交错动画列表
  static List<Widget> createStaggeredList(
    List<Widget> children, {
    Duration interval = const Duration(milliseconds: 100),
    Duration duration = VanHubAnimationDurations.normal,
    Curve curve = VanHubAnimationCurves.easeOut,
    StaggeredAnimationType type = StaggeredAnimationType.fadeInUp,
  }) {
    return children.asMap().entries.map((entry) {
      final index = entry.key;
      final child = entry.value;
      final delay = interval * index;

      switch (type) {
        case StaggeredAnimationType.fadeInUp:
          return VanHubComponentAnimations.fadeInUp(
            child,
            delay: delay,
            duration: duration,
            curve: curve,
          );
        case StaggeredAnimationType.slideInLeft:
          return VanHubComponentAnimations.slideInLeft(
            child,
            delay: delay,
            duration: duration,
            curve: curve,
          );
        case StaggeredAnimationType.scaleIn:
          return VanHubComponentAnimations.scaleIn(
            child,
            delay: delay,
            duration: duration,
            curve: curve,
          );
        case StaggeredAnimationType.rotateIn:
          return VanHubComponentAnimations.rotateIn(
            child,
            delay: delay,
            duration: duration,
            curve: curve,
          );
      }
    }).toList();
  }
}

/// 滑动方向枚举
enum SlideDirection {
  left,
  right,
  up,
  down,
}

/// 交错动画类型枚举
enum StaggeredAnimationType {
  fadeInUp,
  slideInLeft,
  scaleIn,
  rotateIn,
}
