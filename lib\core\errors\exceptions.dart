/// 抽象异常类
abstract class AppException implements Exception {
  final String message;
  final int? code;
  final dynamic data;

  const AppException({
    required this.message,
    this.code,
    this.data,
  });

  @override
  String toString() => 'AppException: $message (code: $code)';
}

/// 服务器异常
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 认证异常
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 验证异常
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 未找到异常
class NotFoundException extends AppException {
  const NotFoundException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
    super.data,
  });
}

/// 加密异常
class EncryptionException extends AppException {
  const EncryptionException({
    required super.message,
    super.code,
    super.data,
  });
}
