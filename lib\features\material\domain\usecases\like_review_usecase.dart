import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../repositories/material_review_repository.dart';

class LikeReviewUseCase {
  final MaterialReviewRepository repository;

  LikeReviewUseCase(this.repository);

  Future<Either<Failure, void>> call(LikeReviewParams params) async {
    if (params.isLiked) {
      return await repository.likeReview(
        params.reviewId,
        params.userId,
      );
    } else {
      return await repository.unlikeReview(
        params.reviewId,
        params.userId,
      );
    }
  }
}

class LikeReviewParams {
  final String reviewId;
  final String userId;
  final bool isLiked;

  LikeReviewParams({
    required this.reviewId,
    required this.userId,
    required this.isLiked,
  });
}