import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/dashboard_data.dart';
import '../../domain/services/simple_dashboard_service.dart';

/// 仪表盘服务Provider
final dashboardServiceProvider = Provider<SimpleDashboardService>((ref) {
  return SimpleDashboardService();
});

/// 项目仪表盘数据Provider
final projectDashboardProvider = FutureProvider.family<DashboardData, String>((ref, projectId) async {
  final dashboardService = ref.read(dashboardServiceProvider);
  final result = await dashboardService.getProjectDashboard(projectId);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (dashboardData) => dashboardData,
  );
});

/// 项目概览Provider
final projectOverviewProvider = FutureProvider.family<ProjectOverview, String>((ref, projectId) async {
  final dashboardData = await ref.watch(projectDashboardProvider(projectId).future);
  return dashboardData.projectOverview;
});

/// 成本分析Provider
final costAnalysisProvider = FutureProvider.family<CostAnalysis, String>((ref, projectId) async {
  final dashboardData = await ref.watch(projectDashboardProvider(projectId).future);
  return dashboardData.costAnalysis;
});

/// 进度跟踪Provider
final progressTrackingProvider = FutureProvider.family<ProgressTracking, String>((ref, projectId) async {
  final dashboardData = await ref.watch(projectDashboardProvider(projectId).future);
  return dashboardData.progressTracking;
});

/// 智能洞察Provider
final smartInsightsProvider = FutureProvider.family<List<SmartInsight>, String>((ref, projectId) async {
  final dashboardData = await ref.watch(projectDashboardProvider(projectId).future);
  return dashboardData.smartInsights;
});

/// 最近活动Provider
final recentActivitiesProvider = FutureProvider.family<List<RecentActivity>, String>((ref, projectId) async {
  final dashboardData = await ref.watch(projectDashboardProvider(projectId).future);
  return dashboardData.recentActivities;
});
