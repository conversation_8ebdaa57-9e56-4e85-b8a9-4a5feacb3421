import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../entities/tree_node.dart';

/// BOM树形结构服务接口
/// 严格遵循Clean Architecture原则，定义BOM树形结构的核心业务逻辑
abstract class BomTreeService {
  /// 从BOM项目构建树形结构
  /// 按材料分类组织BOM项目，支持多级分类
  Either<Failure, List<TreeNode>> buildTreeFromBomItems(
    List<BomItem> bomItems,
  );

  /// 在BOM树中搜索项目
  /// 支持按材料名称、分类、状态等条件搜索
  Either<Failure, List<String>> searchInBomTree(
    List<TreeNode> tree,
    String query,
  );

  /// 切换BOM树节点展开状态
  /// 管理分类节点的展开/折叠状态
  Either<Failure, List<TreeNode>> toggleBomNodeExpansion(
    List<TreeNode> tree,
    String nodeId,
  );

  /// 展开到指定BOM项目
  /// 自动展开包含指定BOM项目的所有父级节点
  Either<Failure, List<TreeNode>> expandPathToBomItem(
    List<TreeNode> tree,
    String bomItemId,
  );

  /// 计算BOM分类统计信息
  /// 统计每个分类的项目数量、总价值、状态分布等
  Either<Failure, Map<String, BomCategoryStats>> calculateBomCategoryStatistics(
    List<BomItem> bomItems,
  );

  /// 按状态过滤BOM树
  /// 根据BOM项目状态过滤显示的树节点
  Either<Failure, List<TreeNode>> filterBomTreeByStatus(
    List<TreeNode> tree,
    List<BomItemStatus> statuses,
  );

  /// 按价格范围过滤BOM树
  /// 根据价格范围过滤BOM项目
  Either<Failure, List<TreeNode>> filterBomTreeByPriceRange(
    List<TreeNode> tree,
    double minPrice,
    double maxPrice,
  );

  /// 排序BOM树节点
  /// 支持按名称、价格、状态、数量等排序
  Either<Failure, List<TreeNode>> sortBomTree(
    List<TreeNode> tree,
    BomTreeSortMode sortMode,
  );

  /// 更新BOM树中的项目
  /// 当BOM项目更新时，同步更新树形结构
  Either<Failure, List<TreeNode>> updateBomItemInTree(
    List<TreeNode> tree,
    BomItem updatedItem,
  );

  /// 从BOM树中移除项目
  /// 当BOM项目被删除时，从树形结构中移除
  Either<Failure, List<TreeNode>> removeBomItemFromTree(
    List<TreeNode> tree,
    String bomItemId,
  );

  /// 添加BOM项目到树
  /// 当新增BOM项目时，添加到相应的分类节点
  Either<Failure, List<TreeNode>> addBomItemToTree(
    List<TreeNode> tree,
    BomItem newItem,
  );

  /// 验证BOM树结构
  /// 检查树形结构的完整性和一致性
  Either<Failure, bool> validateBomTreeStructure(
    List<TreeNode> tree,
  );

  /// 重建BOM树索引
  /// 重新构建树形结构的索引，优化搜索性能
  Either<Failure, List<TreeNode>> rebuildBomTreeIndex(
    List<TreeNode> tree,
  );
}

/// BOM分类统计信息
class BomCategoryStats {
  final String categoryName;
  final int totalItems;
  final double totalValue;
  final Map<BomItemStatus, int> statusDistribution;
  final int pendingCount;
  final int completedCount;
  final double completionRate;

  const BomCategoryStats({
    required this.categoryName,
    required this.totalItems,
    required this.totalValue,
    required this.statusDistribution,
    required this.pendingCount,
    required this.completedCount,
    required this.completionRate,
  });
}

/// BOM树排序模式
enum BomTreeSortMode {
  byName,           // 按名称排序
  byPrice,          // 按价格排序
  byStatus,         // 按状态排序
  byQuantity,       // 按数量排序
  byCreatedDate,    // 按创建时间排序
  byUpdatedDate,    // 按更新时间排序
  byCategory,       // 按分类排序
}

extension BomTreeSortModeX on BomTreeSortMode {
  String get displayName {
    switch (this) {
      case BomTreeSortMode.byName:
        return '按名称';
      case BomTreeSortMode.byPrice:
        return '按价格';
      case BomTreeSortMode.byStatus:
        return '按状态';
      case BomTreeSortMode.byQuantity:
        return '按数量';
      case BomTreeSortMode.byCreatedDate:
        return '按创建时间';
      case BomTreeSortMode.byUpdatedDate:
        return '按更新时间';
      case BomTreeSortMode.byCategory:
        return '按分类';
    }
  }
}
