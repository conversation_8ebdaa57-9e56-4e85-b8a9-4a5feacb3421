import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../entities/login_request.dart';
import '../repositories/auth_repository.dart';

class LoginUseCase {
  final AuthRepository repository;

  const LoginUseCase(this.repository);

  Future<Either<Failure, User>> call(LoginRequest request) async {
    // 可以在这里添加业务逻辑验证
    if (request.email.isEmpty) {
      return const Left(ValidationFailure(message: '邮箱不能为空'));
    }
    
    if (request.password.isEmpty) {
      return const Left(ValidationFailure(message: '密码不能为空'));
    }
    
    if (request.password.length < 6) {
      return const Left(ValidationFailure(message: '密码长度不能少于6位'));
    }

    return await repository.login(request);
  }
}