/// VanHub Home Dashboard Widget 2.0
/// 
/// 高端个性化仪表盘主页组件
/// 使用新的设计系统和智能布局

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/components/vanhub_dashboard.dart';
import '../../../../core/design_system/components/vanhub_chart.dart';
import '../../../../core/design_system/components/vanhub_recommendation.dart';
import '../../../../core/design_system/components/vanhub_collaboration.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';

class HomeDashboardWidgetV2 extends ConsumerStatefulWidget {
  const HomeDashboardWidgetV2({Key? key}) : super(key: key);

  @override
  ConsumerState<HomeDashboardWidgetV2> createState() => _HomeDashboardWidgetV2State();
}

class _HomeDashboardWidgetV2State extends ConsumerState<HomeDashboardWidgetV2> {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VanHub改装宝'),
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: VanHubBrandColors.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: 实现通知功能
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: 实现搜索功能
            },
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
        child: VanHubDashboard(
          widgets: _buildDashboardWidgets(),
          enableDragAndDrop: true,
          enablePersonalization: true,
          onLayoutChanged: (widgets) {
            // TODO: 保存布局配置
          },
          onAddWidget: () {
            // TODO: 显示添加小部件对话框
          },
          onRemoveWidget: (widgetId) {
            // TODO: 移除小部件
          },
        ),
      ),
    );
  }

  /// 构建仪表盘小部件列表
  List<DashboardWidget> _buildDashboardWidgets() {
    return [
      // 欢迎卡片
      _buildWelcomeWidget(),
      
      // 项目概览
      DashboardWidgets.projectOverview(
        totalProjects: 5,
        activeProjects: 3,
        completedProjects: 2,
      ),
      
      // 快速操作
      DashboardWidgets.quickActions(
        actions: [
          QuickAction(
            label: '新建项目',
            icon: Icons.add_circle_outline,
            color: VanHubBrandColors.primary,
            onTap: () {
              // TODO: 导航到新建项目页面
            },
          ),
          QuickAction(
            label: '材料库',
            icon: Icons.inventory_2_outlined,
            color: VanHubSemanticColors.success,
            onTap: () {
              // TODO: 导航到材料库
            },
          ),
          QuickAction(
            label: '记录日志',
            icon: Icons.edit_note,
            color: VanHubSemanticColors.warning,
            onTap: () {
              // TODO: 导航到日志页面
            },
          ),
          QuickAction(
            label: 'BOM管理',
            icon: Icons.list_alt,
            color: VanHubSemanticColors.info,
            onTap: () {
              // TODO: 导航到BOM页面
            },
          ),
        ],
      ),
      
      // 最近项目
      _buildRecentProjectsWidget(),
      
      // 成本分析
      _buildCostAnalysisWidget(),
      
      // 进度统计
      _buildProgressStatsWidget(),
      
      // 最近活动
      _buildRecentActivityWidget(),

      // 数据可视化图表
      _buildDataVisualizationWidget(),

      // AI智能推荐
      _buildRecommendationWidget(),

      // 实时协作
      _buildCollaborationWidget(),
    ];
  }

  /// 构建欢迎小部件
  DashboardWidget _buildWelcomeWidget() {
    return DashboardWidget(
      id: 'welcome',
      title: '欢迎回来！',
      type: DashboardWidgetType.quickActions,
      size: DashboardWidgetSize.wide,
      icon: Icons.waving_hand,
      color: VanHubBrandColors.primary,
      content: Container(
        decoration: BoxDecoration(
          gradient: VanHubBrandColors.getEmotionalGradient(EmotionalState.confident),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '开始您的房车改装之旅',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: VanHubBrandColors.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Row(
              children: [
                _buildQuickStat('项目', '5', Icons.folder),
                SizedBox(width: VanHubResponsiveSpacing.lg),
                _buildQuickStat('材料', '127', Icons.inventory),
                SizedBox(width: VanHubResponsiveSpacing.lg),
                _buildQuickStat('进度', '68%', Icons.trending_up),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建最近项目小部件
  DashboardWidget _buildRecentProjectsWidget() {
    return DashboardWidget(
      id: 'recent_projects',
      title: '最近项目',
      type: DashboardWidgetType.recentActivity,
      size: DashboardWidgetSize.large,
      icon: Icons.folder_outlined,
      color: VanHubBrandColors.accent,
      content: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: 3,
              itemBuilder: (context, index) {
                return _buildProjectListItem(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建成本分析小部件
  DashboardWidget _buildCostAnalysisWidget() {
    return DashboardWidget(
      id: 'cost_analysis',
      title: '成本分析',
      type: DashboardWidgetType.costAnalysis,
      size: DashboardWidgetSize.medium,
      icon: Icons.analytics_outlined,
      color: VanHubSemanticColors.success,
      content: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildCostItem('预算', '¥50,000', VanHubSemanticColors.info),
              ),
              Expanded(
                child: _buildCostItem('已花费', '¥32,500', VanHubSemanticColors.warning),
              ),
            ],
          ),
          SizedBox(height: VanHubResponsiveSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildCostItem('剩余', '¥17,500', VanHubSemanticColors.success),
              ),
              Expanded(
                child: _buildCostItem('使用率', '65%', VanHubSemanticColors.error),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建进度统计小部件
  DashboardWidget _buildProgressStatsWidget() {
    return DashboardWidget(
      id: 'progress_stats',
      title: '进度统计',
      type: DashboardWidgetType.progressChart,
      size: DashboardWidgetSize.medium,
      icon: Icons.show_chart,
      color: VanHubBrandColors.primary,
      content: Column(
        children: [
          _buildProgressItem('整体进度', 0.68, VanHubBrandColors.primary),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          _buildProgressItem('材料采购', 0.85, VanHubSemanticColors.success),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          _buildProgressItem('安装进度', 0.45, VanHubSemanticColors.warning),
        ],
      ),
    );
  }

  /// 构建最近活动小部件
  DashboardWidget _buildRecentActivityWidget() {
    return DashboardWidget(
      id: 'recent_activity',
      title: '最近活动',
      type: DashboardWidgetType.timeline,
      size: DashboardWidgetSize.tall,
      icon: Icons.timeline,
      color: VanHubSemanticColors.info,
      content: ListView(
        children: [
          _buildActivityItem('添加了新材料：LED灯带', '2小时前', Icons.add_shopping_cart),
          _buildActivityItem('更新了项目进度', '5小时前', Icons.update),
          _buildActivityItem('完成了电路安装', '1天前', Icons.electrical_services),
          _buildActivityItem('创建了新项目', '3天前', Icons.create_new_folder),
        ],
      ),
    );
  }

  /// 构建快速统计项
  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: VanHubBrandColors.onPrimary,
          size: 20,
        ),
        SizedBox(height: VanHubResponsiveSpacing.xs),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: VanHubBrandColors.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: VanHubBrandColors.onPrimary.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  /// 构建项目列表项
  Widget _buildProjectListItem(int index) {
    final projects = [
      {'name': '房车内饰改装', 'progress': 0.75, 'status': '进行中'},
      {'name': '电路系统升级', 'progress': 0.45, 'status': '规划中'},
      {'name': '储物空间优化', 'progress': 1.0, 'status': '已完成'},
    ];
    
    final project = projects[index];
    
    return Container(
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      padding: EdgeInsets.all(VanHubResponsiveSpacing.sm),
      decoration: BoxDecoration(
        color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: VanHubSemanticColors.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  project['name'] as String,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                project['status'] as String,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
              ),
            ],
          ),
          SizedBox(height: VanHubResponsiveSpacing.xs),
          LinearProgressIndicator(
            value: project['progress'] as double,
            backgroundColor: VanHubSemanticColors.getBorderColor(context),
            valueColor: AlwaysStoppedAnimation<Color>(VanHubBrandColors.primary),
            minHeight: 4,
          ),
        ],
      ),
    );
  }

  /// 构建成本项
  Widget _buildCostItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
        ),
      ],
    );
  }

  /// 构建进度项
  Widget _buildProgressItem(String label, double progress, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const Spacer(),
            Text(
              '${(progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: VanHubResponsiveSpacing.xs),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: VanHubSemanticColors.getBorderColor(context),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  /// 构建活动项
  Widget _buildActivityItem(String title, String time, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: VanHubBrandColors.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: VanHubBrandColors.onPrimaryContainer,
            ),
          ),
          SizedBox(width: VanHubResponsiveSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: VanHubSemanticColors.getTextColor(context, secondary: true),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建数据可视化小部件
  DashboardWidget _buildDataVisualizationWidget() {
    return DashboardWidget(
      id: 'data_visualization',
      title: '数据分析',
      type: DashboardWidgetType.statistics,
      size: DashboardWidgetSize.large,
      icon: Icons.analytics_outlined,
      color: VanHubBrandColors.accent,
      content: Column(
        children: [
          // 成本趋势图
          Expanded(
            child: VanHubChart.line(
              series: [
                ChartDataSeries(
                  name: '月度支出',
                  color: VanHubBrandColors.primary,
                  data: [
                    ChartDataPoint(x: 1, y: 2500),
                    ChartDataPoint(x: 2, y: 3200),
                    ChartDataPoint(x: 3, y: 2800),
                    ChartDataPoint(x: 4, y: 4100),
                    ChartDataPoint(x: 5, y: 3600),
                    ChartDataPoint(x: 6, y: 4500),
                  ],
                ),
                ChartDataSeries(
                  name: '预算',
                  color: VanHubSemanticColors.warning,
                  data: [
                    ChartDataPoint(x: 1, y: 4000),
                    ChartDataPoint(x: 2, y: 4000),
                    ChartDataPoint(x: 3, y: 4000),
                    ChartDataPoint(x: 4, y: 4000),
                    ChartDataPoint(x: 5, y: 4000),
                    ChartDataPoint(x: 6, y: 4000),
                  ],
                ),
              ],
              emotionalState: EmotionalState.focused,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建AI推荐小部件
  DashboardWidget _buildRecommendationWidget() {
    return DashboardWidget(
      id: 'ai_recommendations',
      title: 'AI智能推荐',
      type: DashboardWidgetType.recommendations,
      size: DashboardWidgetSize.large,
      icon: Icons.auto_awesome,
      color: VanHubBrandColors.secondary,
      content: VanHubRecommendation(
        recommendations: [
          RecommendationItem(
            id: '1',
            title: 'LED灯带套装',
            description: '高亮度可调色温LED灯带，适合房车内饰照明',
            type: RecommendationType.material,
            confidence: RecommendationConfidence.high,
            reasons: [RecommendationReason.userHistory, RecommendationReason.costEffective],
            price: 299.0,
            rating: 4.8,
            reviewCount: 156,
            createdAt: DateTime.now(),
          ),
          RecommendationItem(
            id: '2',
            title: '房车改装完整教程',
            description: '从零开始的房车改装指南，包含电路、水路、内饰等',
            type: RecommendationType.tutorial,
            confidence: RecommendationConfidence.veryHigh,
            reasons: [RecommendationReason.expertChoice, RecommendationReason.trending],
            rating: 4.9,
            reviewCount: 89,
            createdAt: DateTime.now(),
          ),
          RecommendationItem(
            id: '3',
            title: '多功能储物箱',
            description: '可折叠多层储物箱，最大化利用房车空间',
            type: RecommendationType.material,
            confidence: RecommendationConfidence.medium,
            reasons: [RecommendationReason.similarUsers, RecommendationReason.innovative],
            price: 158.0,
            rating: 4.6,
            reviewCount: 234,
            createdAt: DateTime.now(),
          ),
        ],
        useGridLayout: false,
        maxItems: 3,
        onItemTap: (item) {
          // TODO: 导航到推荐详情
        },
        onFeedback: (item, isPositive) {
          // TODO: 记录用户反馈
        },
      ),
    );
  }

  /// 构建协作小部件
  DashboardWidget _buildCollaborationWidget() {
    return DashboardWidget(
      id: 'collaboration',
      title: '团队协作',
      type: DashboardWidgetType.recentActivity,
      size: DashboardWidgetSize.medium,
      icon: Icons.people,
      color: VanHubSemanticColors.info,
      content: VanHubCollaboration(
        collaborators: [
          Collaborator(
            id: '1',
            name: '张三',
            email: '<EMAIL>',
            status: CollaboratorStatus.online,
            permission: CollaborationPermission.owner,
            lastActiveAt: DateTime.now(),
            cursorColor: VanHubBrandColors.primary,
          ),
          Collaborator(
            id: '2',
            name: '李四',
            email: '<EMAIL>',
            status: CollaboratorStatus.online,
            permission: CollaborationPermission.editor,
            lastActiveAt: DateTime.now().subtract(const Duration(minutes: 5)),
            cursorColor: VanHubSemanticColors.success,
          ),
          Collaborator(
            id: '3',
            name: '王五',
            email: '<EMAIL>',
            status: CollaboratorStatus.away,
            permission: CollaborationPermission.viewer,
            lastActiveAt: DateTime.now().subtract(const Duration(hours: 2)),
            cursorColor: VanHubSemanticColors.warning,
          ),
        ],
        activities: [
          CollaborationActivity(
            id: '1',
            collaboratorId: '2',
            collaboratorName: '李四',
            action: CollaborationAction.edit,
            description: '更新了BOM清单',
            timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
          ),
          CollaborationActivity(
            id: '2',
            collaboratorId: '3',
            collaboratorName: '王五',
            action: CollaborationAction.comment,
            description: '添加了评论',
            timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          ),
          CollaborationActivity(
            id: '3',
            collaboratorId: '2',
            collaboratorName: '李四',
            action: CollaborationAction.join,
            description: '加入了项目',
            timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          ),
        ],
        currentUserId: '1',
        maxDisplayedCollaborators: 3,
        onInvite: (email) {
          // TODO: 发送邀请
        },
        onPermissionChange: (userId, permission) {
          // TODO: 更改权限
        },
      ),
    );
  }
}
