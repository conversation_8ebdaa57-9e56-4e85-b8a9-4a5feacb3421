# Design Document

## Overview

VanHub项目编译错误修复设计专注于系统性地解决678个编译错误，主要集中在改装日志系统模块。修复策略采用分层递进的方法，从底层的代码生成和类型定义开始，逐步向上修复数据层、领域层和表现层的问题。

核心设计原则：
1. **保持架构完整性** - 修复过程中严格遵循Clean Architecture原则
2. **类型安全优先** - 确保所有修复都符合Dart的类型安全和空安全要求
3. **最小化影响** - 修复时尽量减少对现有正确代码的影响
4. **可验证性** - 每个修复步骤都有明确的验证标准

## Architecture

### 错误分类和修复优先级

```
编译错误修复架构
├── 第一层：基础设施错误（优先级：最高）
│   ├── Freezed代码生成问题
│   ├── 缺失的类型定义
│   ├── 语法错误
│   └── 导入错误
├── 第二层：数据模型错误（优先级：高）
│   ├── 实体属性访问错误
│   ├── 模型转换错误
│   ├── 枚举定义错误
│   └── JSON序列化错误
├── 第三层：业务逻辑错误（优先级：中）
│   ├── Repository接口错误
│   ├── UseCase实现错误
│   ├── 服务类定义错误
│   └── 依赖注入错误
└── 第四层：表现层错误（优先级：低）
    ├── Provider状态管理错误
    ├── UI组件渲染错误
    ├── 页面导航错误
    └── 用户交互错误
```

### 修复流程设计

```mermaid
graph TD
    A[分析错误类型] --> B[生成修复计划]
    B --> C[修复基础设施]
    C --> D[验证代码生成]
    D --> E[修复数据模型]
    E --> F[验证模型转换]
    F --> G[修复业务逻辑]
    G --> H[验证依赖注入]
    H --> I[修复表现层]
    I --> J[验证UI渲染]
    J --> K[全面测试验证]
    K --> L[修复完成]
    
    D --> |失败| C
    F --> |失败| E
    H --> |失败| G
    J --> |失败| I
    K --> |失败| A
```

## Components and Interfaces

### 1. 基础设施修复组件

#### Freezed代码生成修复
```dart
// 确保所有实体都有正确的freezed注解
@freezed
class LogEntry with _$LogEntry {
  const factory LogEntry({
    required String id,
    required String projectId,
    required String systemId,
    required String title,
    required String content,
    required DateTime logDate,
    required String authorId,
    String? authorName,
    required DateTime createdAt,
    required DateTime updatedAt,
    required LogStatus status,
    required DifficultyLevel difficulty,
    int? timeSpentMinutes,
    List<String>? mediaIds,
    List<String>? relatedBomItemIds,
    double? totalCost,
    Map<String, dynamic>? metadata,
  }) = _LogEntry;
  
  factory LogEntry.fromJson(Map<String, dynamic> json) => 
    _$LogEntryFromJson(json);
}

// 确保所有枚举都有正确的定义
enum LogStatus {
  draft,
  inProgress,
  completed,
  onHold,
  cancelled;
  
  String get displayName {
    switch (this) {
      case LogStatus.draft:
        return '草稿';
      case LogStatus.inProgress:
        return '进行中';
      case LogStatus.completed:
        return '已完成';
      case LogStatus.onHold:
        return '暂停';
      case LogStatus.cancelled:
        return '已取消';
    }
  }
}

enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert;
  
  String get displayName {
    switch (this) {
      case DifficultyLevel.easy:
        return '简单';
      case DifficultyLevel.medium:
        return '中等';
      case DifficultyLevel.hard:
        return '困难';
      case DifficultyLevel.expert:
        return '专家';
    }
  }
}
```

#### 缺失类型定义修复
```dart
// Milestone相关类型定义
@freezed
class Milestone with _$Milestone {
  const factory Milestone({
    required String id,
    required String projectId,
    required String title,
    required String description,
    String? systemId,
    List<String>? relatedLogIds,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String createdBy,
    String? iconName,
    String? colorHex,
    required MilestoneStatus status,
    required MilestonePriority priority,
    DateTime? dueDate,
    DateTime? completedAt,
  }) = _Milestone;
  
  factory Milestone.fromJson(Map<String, dynamic> json) => 
    _$MilestoneFromJson(json);
}

enum MilestoneStatus {
  planned,
  inProgress,
  completed,
  cancelled,
  overdue;
  
  String get displayName {
    switch (this) {
      case MilestoneStatus.planned:
        return '计划中';
      case MilestoneStatus.inProgress:
        return '进行中';
      case MilestoneStatus.completed:
        return '已完成';
      case MilestoneStatus.cancelled:
        return '已取消';
      case MilestoneStatus.overdue:
        return '已逾期';
    }
  }
}

enum MilestonePriority {
  low,
  medium,
  high,
  critical;
  
  String get displayName {
    switch (this) {
      case MilestonePriority.low:
        return '低';
      case MilestonePriority.medium:
        return '中';
      case MilestonePriority.high:
        return '高';
      case MilestonePriority.critical:
        return '紧急';
    }
  }
}

// ProjectStatus枚举修复
enum ProjectStatus {
  planning,
  inProgress,
  completed,
  onHold,
  cancelled;
  
  String get displayName {
    switch (this) {
      case ProjectStatus.planning:
        return '规划中';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.onHold:
        return '暂停';
      case ProjectStatus.cancelled:
        return '已取消';
    }
  }
}
```

### 2. 数据模型修复组件

#### LogEntry相关修复
```dart
// LogEntryModel修复
class LogEntryModel {
  final String id;
  final String project_id;  // 保持数据库字段命名
  final String system_id;
  final String title;
  final String content;
  final DateTime log_date;
  final String author_id;
  final String? author_name;
  final DateTime created_at;
  final DateTime updated_at;
  final String status;
  final String difficulty;
  final int? time_spent_minutes;
  final List<String>? media_ids;
  final List<String>? related_bom_item_ids;
  final double? total_cost;
  final Map<String, dynamic>? metadata;
  
  const LogEntryModel({
    required this.id,
    required this.project_id,
    required this.system_id,
    required this.title,
    required this.content,
    required this.log_date,
    required this.author_id,
    this.author_name,
    required this.created_at,
    required this.updated_at,
    required this.status,
    required this.difficulty,
    this.time_spent_minutes,
    this.media_ids,
    this.related_bom_item_ids,
    this.total_cost,
    this.metadata,
  });
  
  // 添加缺失的转换方法
  String _logStatusToString(LogStatus status) {
    return status.name;
  }
  
  LogStatus _stringToLogStatus(String status) {
    return LogStatus.values.firstWhere(
      (e) => e.name == status,
      orElse: () => LogStatus.draft,
    );
  }
  
  String _difficultyLevelToString(DifficultyLevel difficulty) {
    return difficulty.name;
  }
  
  DifficultyLevel _stringToDifficultyLevel(String difficulty) {
    return DifficultyLevel.values.firstWhere(
      (e) => e.name == difficulty,
      orElse: () => DifficultyLevel.easy,
    );
  }
  
  // 修复toEntity方法
  LogEntry toEntity() {
    return LogEntry(
      id: id,
      projectId: project_id,
      systemId: system_id,
      title: title,
      content: content,
      logDate: log_date,
      authorId: author_id,
      authorName: author_name,
      createdAt: created_at,
      updatedAt: updated_at,
      status: _stringToLogStatus(status),
      difficulty: _stringToDifficultyLevel(difficulty),
      timeSpentMinutes: time_spent_minutes,
      mediaIds: media_ids,
      relatedBomItemIds: related_bom_item_ids,
      totalCost: total_cost,
      metadata: metadata,
    );
  }
  
  // 修复fromEntity方法
  factory LogEntryModel.fromEntity(LogEntry entity) {
    return LogEntryModel(
      id: entity.id,
      project_id: entity.projectId,
      system_id: entity.systemId,
      title: entity.title,
      content: entity.content,
      log_date: entity.logDate,
      author_id: entity.authorId,
      author_name: entity.authorName,
      created_at: entity.createdAt,
      updated_at: entity.updatedAt,
      status: entity.status.name,
      difficulty: entity.difficulty.name,
      time_spent_minutes: entity.timeSpentMinutes,
      media_ids: entity.mediaIds,
      related_bom_item_ids: entity.relatedBomItemIds,
      total_cost: entity.totalCost,
      metadata: entity.metadata,
    );
  }
}
```

#### Timeline相关修复
```dart
// Timeline实体修复
@freezed
class Timeline with _$Timeline {
  const factory Timeline({
    required String id,
    required String projectId,
    required List<TimelineItem> items,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Timeline;
  
  factory Timeline.fromJson(Map<String, dynamic> json) => 
    _$TimelineFromJson(json);
}

@freezed
class TimelineItem with _$TimelineItem {
  const factory TimelineItem.logEntry({
    required LogEntry logEntry,
    required DateTime timestamp,
  }) = LogEntryItem;
  
  const factory TimelineItem.milestone({
    required Milestone milestone,
    required DateTime timestamp,
    required MilestoneStatus status,
  }) = MilestoneItem;
  
  factory TimelineItem.fromJson(Map<String, dynamic> json) => 
    _$TimelineItemFromJson(json);
}

// Timeline扩展方法
extension TimelineExtensions on Timeline {
  List<TimelineItem> get sortedItems {
    final sorted = List<TimelineItem>.from(items);
    sorted.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted;
  }
  
  List<LogEntry> get logEntries {
    return items
        .where((item) => item is LogEntryItem)
        .map((item) => (item as LogEntryItem).logEntry)
        .toList();
  }
  
  List<Milestone> get milestones {
    return items
        .where((item) => item is MilestoneItem)
        .map((item) => (item as MilestoneItem).milestone)
        .toList();
  }
}

// TimelineItem扩展方法
extension TimelineItemExtensions on TimelineItem {
  DateTime get timestamp {
    return map(
      logEntry: (item) => item.timestamp,
      milestone: (item) => item.timestamp,
    );
  }
  
  String get title {
    return map(
      logEntry: (item) => item.logEntry.title,
      milestone: (item) => item.milestone.title,
    );
  }
  
  String get type {
    return map(
      logEntry: (_) => 'log_entry',
      milestone: (_) => 'milestone',
    );
  }
  
  bool get isCompleted {
    return map(
      logEntry: (item) => item.logEntry.status == LogStatus.completed,
      milestone: (item) => item.status == MilestoneStatus.completed,
    );
  }
  
  bool get isOverdue {
    return map(
      logEntry: (_) => false,
      milestone: (item) => item.status == MilestoneStatus.overdue,
    );
  }
}
```

### 3. 业务逻辑修复组件

#### Repository接口修复
```dart
// TimelineRepository接口定义
abstract class TimelineRepository {
  Future<Either<Failure, Timeline>> getProjectTimeline(String projectId);
  Future<Either<Failure, List<Milestone>>> getProjectMilestones(String projectId);
  Future<Either<Failure, Milestone>> addMilestone(Milestone milestone);
  Future<Either<Failure, Milestone>> updateMilestone(Milestone milestone);
  Future<Either<Failure, void>> deleteMilestone(String milestoneId);
  Future<Either<Failure, List<TimelineItem>>> getTimelineItems(
    String projectId, {
    DateTime? startDate,
    DateTime? endDate,
    List<String>? systemIds,
  });
}

// MediaRemoteDataSource定义
abstract class MediaRemoteDataSource {
  Future<List<LogMediaModel>> getLogMedia(String logId);
  Future<LogMediaModel> uploadMedia(String logId, File file);
  Future<void> deleteMedia(String mediaId);
  Future<LogMediaModel> updateMediaCaption(String mediaId, String caption);
}

class MediaRemoteDataSourceImpl implements MediaRemoteDataSource {
  final SupabaseClient supabaseClient;
  
  MediaRemoteDataSourceImpl({required this.supabaseClient});
  
  @override
  Future<List<LogMediaModel>> getLogMedia(String logId) async {
    try {
      final response = await supabaseClient
          .from('log_media')
          .select()
          .eq('log_id', logId)
          .order('created_at');
      
      return response
          .map<LogMediaModel>((json) => LogMediaModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<LogMediaModel> uploadMedia(String logId, File file) async {
    try {
      // 实现文件上传逻辑
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final uploadPath = 'log_media/$logId/$fileName';
      
      await supabaseClient.storage
          .from('media')
          .upload(uploadPath, file);
      
      final publicUrl = supabaseClient.storage
          .from('media')
          .getPublicUrl(uploadPath);
      
      final mediaData = {
        'log_id': logId,
        'filename': fileName,
        'url': publicUrl,
        'type': _getMediaType(file.path),
        'file_size': await file.length(),
        'created_at': DateTime.now().toIso8601String(),
      };
      
      final response = await supabaseClient
          .from('log_media')
          .insert(mediaData)
          .select()
          .single();
      
      return LogMediaModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<void> deleteMedia(String mediaId) async {
    try {
      await supabaseClient
          .from('log_media')
          .delete()
          .eq('id', mediaId);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<LogMediaModel> updateMediaCaption(String mediaId, String caption) async {
    try {
      final response = await supabaseClient
          .from('log_media')
          .update({'caption': caption})
          .eq('id', mediaId)
          .select()
          .single();
      
      return LogMediaModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  String _getMediaType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(extension)) {
      return 'image';
    } else if (['mp4', 'mov', 'avi', 'mkv'].contains(extension)) {
      return 'video';
    } else if (['mp3', 'wav', 'aac', 'm4a'].contains(extension)) {
      return 'audio';
    }
    return 'file';
  }
}
```

#### UseCase修复
```dart
// GetSystemLogsUseCase定义
class GetSystemLogsUseCase implements UseCase<List<LogEntry>, GetSystemLogsParams> {
  final LogRepository repository;
  
  GetSystemLogsUseCase({required this.repository});
  
  @override
  Future<Either<Failure, List<LogEntry>>> call(GetSystemLogsParams params) async {
    return await repository.getSystemLogs(
      projectId: params.projectId,
      systemId: params.systemId,
      criteria: params.criteria,
    );
  }
}

@freezed
class GetSystemLogsParams with _$GetSystemLogsParams {
  const factory GetSystemLogsParams({
    required String projectId,
    String? systemId,
    LogSearchCriteria? criteria,
  }) = _GetSystemLogsParams;
}

// LogSearchCriteria定义
@freezed
class LogSearchCriteria with _$LogSearchCriteria {
  const factory LogSearchCriteria({
    String? keyword,
    List<LogStatus>? statuses,
    List<DifficultyLevel>? difficulties,
    DateTime? startDate,
    DateTime? endDate,
    String? authorId,
    List<String>? systemIds,
    int? limit,
    int? offset,
  }) = _LogSearchCriteria;
  
  factory LogSearchCriteria.fromJson(Map<String, dynamic> json) => 
    _$LogSearchCriteriaFromJson(json);
}

// DeleteMediaUseCase定义
class DeleteMediaUseCase implements UseCase<void, DeleteMediaParams> {
  final MediaRepository repository;
  
  DeleteMediaUseCase({required this.repository});
  
  @override
  Future<Either<Failure, void>> call(DeleteMediaParams params) async {
    return await repository.deleteMedia(params.mediaId);
  }
}

@freezed
class DeleteMediaParams with _$DeleteMediaParams {
  const factory DeleteMediaParams({
    required String mediaId,
  }) = _DeleteMediaParams;
}
```

### 4. 表现层修复组件

#### Provider修复
```dart
// LogProvider修复
@riverpod
class LogNotifier extends _$LogNotifier {
  late final LogRepository _logRepository;
  late final GetSystemLogsUseCase _getSystemLogsUseCase;
  late final CreateLogEntryUseCase _createLogEntryUseCase;
  late final UpdateLogEntryUseCase _updateLogEntryUseCase;
  late final DeleteLogEntryUseCase _deleteLogEntryUseCase;
  
  @override
  Future<List<LogEntry>> build() async {
    _initializeDependencies();
    return [];
  }
  
  void _initializeDependencies() {
    _logRepository = ref.read(logRepositoryProvider);
    _getSystemLogsUseCase = ref.read(getSystemLogsUseCaseProvider);
    _createLogEntryUseCase = ref.read(createLogEntryUseCaseProvider);
    _updateLogEntryUseCase = ref.read(updateLogEntryUseCaseProvider);
    _deleteLogEntryUseCase = ref.read(deleteLogEntryUseCaseProvider);
  }
  
  Future<Either<Failure, void>> loadSystemLogs(
    String projectId, {
    String? systemId,
    LogSearchCriteria? criteria,
  }) async {
    state = const AsyncLoading();
    
    final params = GetSystemLogsParams(
      projectId: projectId,
      systemId: systemId,
      criteria: criteria,
    );
    
    final result = await _getSystemLogsUseCase(params);
    
    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
        return Left(failure);
      },
      (logs) {
        state = AsyncData(logs);
        return const Right(null);
      },
    );
  }
  
  Future<Either<Failure, LogEntry>> createLogEntry(LogEntry logEntry) async {
    final result = await _createLogEntryUseCase(
      CreateLogEntryParams(logEntry: logEntry),
    );
    
    return result.fold(
      (failure) => Left(failure),
      (createdLog) {
        // 更新状态
        state.whenData((logs) {
          state = AsyncData([...logs, createdLog]);
        });
        return Right(createdLog);
      },
    );
  }
  
  Future<Either<Failure, void>> deleteLogEntry(String logId) async {
    final result = await _deleteLogEntryUseCase(
      DeleteLogEntryParams(logId: logId),
    );
    
    return result.fold(
      (failure) => Left(failure),
      (_) {
        // 更新状态
        state.whenData((logs) {
          final updatedLogs = logs.where((log) => log.id != logId).toList();
          state = AsyncData(updatedLogs);
        });
        return const Right(null);
      },
    );
  }
}

// Provider定义
@riverpod
LogRepository logRepository(LogRepositoryRef ref) {
  return LogRepositoryImpl(
    remoteDataSource: ref.read(logRemoteDataSourceProvider),
  );
}

@riverpod
GetSystemLogsUseCase getSystemLogsUseCase(GetSystemLogsUseCaseRef ref) {
  return GetSystemLogsUseCase(
    repository: ref.read(logRepositoryProvider),
  );
}
```

## Data Models

### 修复后的数据模型结构

```dart
// LogMedia完整定义
@freezed
class LogMedia with _$LogMedia {
  const factory LogMedia({
    required String id,
    required String logId,
    required String filename,
    required String url,
    String? thumbnailUrl,
    String? caption,
    required MediaType type,
    required int fileSize,
    int? width,
    int? height,
    int? duration,
    required DateTime createdAt,
    required DateTime updatedAt,
    Map<String, dynamic>? metadata,
  }) = _LogMedia;
  
  factory LogMedia.fromJson(Map<String, dynamic> json) => 
    _$LogMediaFromJson(json);
}

enum MediaType {
  image,
  video,
  audio,
  document,
  other;
  
  String get displayName {
    switch (this) {
      case MediaType.image:
        return '图片';
      case MediaType.video:
        return '视频';
      case MediaType.audio:
        return '音频';
      case MediaType.document:
        return '文档';
      case MediaType.other:
        return '其他';
    }
  }
}

// LogMedia扩展方法
extension LogMediaExtensions on LogMedia {
  bool get isImage => type == MediaType.image;
  bool get isVideo => type == MediaType.video;
  bool get isAudio => type == MediaType.audio;
  
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
  
  String? get aspectRatio {
    if (width != null && height != null && width! > 0 && height! > 0) {
      return '${width}x${height}';
    }
    return null;
  }
  
  String? get formattedDuration {
    if (duration == null) return null;
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  String get displayUrl => thumbnailUrl ?? url;
}
```

## Error Handling

### 统一错误处理策略

```dart
// 修复后的错误处理
@freezed
class CompilationFixFailure with _$CompilationFixFailure {
  const factory CompilationFixFailure.codeGeneration({
    required String entity,
    required String reason,
  }) = CodeGenerationFailure;
  
  const factory CompilationFixFailure.typeDefinition({
    required String typeName,
    required String location,
  }) = TypeDefinitionFailure;
  
  const factory CompilationFixFailure.modelConversion({
    required String modelName,
    required String conversionType,
    required String error,
  }) = ModelConversionFailure;
  
  const factory CompilationFixFailure.dependencyInjection({
    required String serviceName,
    required String reason,
  }) = DependencyInjectionFailure;
}

// 错误处理工具类
class ErrorHandler {
  static void handle(Failure failure, BuildContext context) {
    String message = _getErrorMessage(failure);
    _showErrorSnackBar(context, message);
  }
  
  static String _getErrorMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message ?? '服务器错误';
    } else if (failure is NetworkFailure) {
      return '网络连接错误';
    } else if (failure is CacheFailure) {
      return '缓存错误';
    } else if (failure is CompilationFixFailure) {
      return failure.when(
        codeGeneration: (entity, reason) => '代码生成错误: $entity - $reason',
        typeDefinition: (typeName, location) => '类型定义错误: $typeName 在 $location',
        modelConversion: (modelName, conversionType, error) => 
          '模型转换错误: $modelName ($conversionType) - $error',
        dependencyInjection: (serviceName, reason) => 
          '依赖注入错误: $serviceName - $reason',
      );
    }
    return '未知错误';
  }
  
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
```

## Testing Strategy

### 修复验证测试

```dart
group('Compilation Fix Verification Tests', () {
  group('Code Generation Tests', () {
    test('should generate all freezed files', () {
      // 验证所有.freezed.dart文件是否存在
      final entities = [
        'LogEntry',
        'LogMedia',
        'Timeline',
        'Milestone',
        'Comment',
      ];
      
      for (final entity in entities) {
        final file = File('lib/features/modification_log/domain/entities/${entity.toLowerCase()}.freezed.dart');
        expect(file.existsSync(), true, reason: '$entity.freezed.dart should exist');
      }
    });
    
    test('should generate all json serialization files', () {
      // 验证所有.g.dart文件是否存在
      final entities = [
        'LogEntry',
        'LogMedia',
        'Timeline',
        'Milestone',
        'Comment',
      ];
      
      for (final entity in entities) {
        final file = File('lib/features/modification_log/domain/entities/${entity.toLowerCase()}.g.dart');
        expect(file.existsSync(), true, reason: '$entity.g.dart should exist');
      }
    });
  });
  
  group('Type Definition Tests', () {
    test('should define all required enums', () {
      // 验证枚举定义
      expect(LogStatus.values.isNotEmpty, true);
      expect(DifficultyLevel.values.isNotEmpty, true);
      expect(MilestoneStatus.values.isNotEmpty, true);
      expect(MilestonePriority.values.isNotEmpty, true);
      expect(ProjectStatus.values.isNotEmpty, true);
      expect(MediaType.values.isNotEmpty, true);
    });
    
    test('should access all entity properties', () {
      // 验证实体属性访问
      final logEntry = LogEntry(
        id: 'test',
        projectId: 'project1',
        systemId: 'system1',
        title: 'Test Log',
        content: 'Test content',
        logDate: DateTime.now(),
        authorId: 'author1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: LogStatus.draft,
        difficulty: DifficultyLevel.easy,
      );
      
      expect(logEntry.id, 'test');
      expect(logEntry.title, 'Test Log');
      expect(logEntry.status, LogStatus.draft);
      expect(logEntry.difficulty, DifficultyLevel.easy);
    });
  });
  
  group('Model Conversion Tests', () {
    test('should convert LogEntryModel to LogEntry', () {
      final model = LogEntryModel(
        id: 'test',
        project_id: 'project1',
        system_id: 'system1',
        title: 'Test Log',
        content: 'Test content',
        log_date: DateTime.now(),
        author_id: 'author1',
        created_at: DateTime.now(),
        updated_at: DateTime.now(),
        status: 'draft',
        difficulty: 'easy',
      );
      
      final entity = model.toEntity();
      
      expect(entity.id, model.id);
      expect(entity.projectId, model.project_id);
      expect(entity.status, LogStatus.draft);
      expect(entity.difficulty, DifficultyLevel.easy);
    });
  });
  
  group('Dependency Injection Tests', () {
    test('should resolve all providers', () {
      final container = ProviderContainer();
      
      // 验证所有Provider都能正确解析
      expect(() => container.read(logRepositoryProvider), returnsNormally);
      expect(() => container.read(getSystemLogsUseCaseProvider), returnsNormally);
      expect(() => container.read(deleteMediaUseCaseProvider), returnsNormally);
      
      container.dispose();
    });
  });
});

group('Integration Tests', () {
  testWidgets('should render pages without errors', (tester) async {
    await tester.pumpWidget(
      ProviderScope(
        child: MaterialApp(
          home: LogListPage(projectId: 'test'),
        ),
      ),
    );
    
    expect(find.byType(LogListPage), findsOneWidget);
    expect(tester.takeException(), isNull);
  });
  
  testWidgets('should handle user interactions', (tester) async {
    await tester.pumpWidget(
      ProviderScope(
        child: MaterialApp(
          home: TimelinePage(projectId: 'test'),
        ),
      ),
    );
    
    await tester.pumpAndSettle();
    expect(find.byType(TimelinePage), findsOneWidget);
    expect(tester.takeException(), isNull);
  });
});
```

## 实施优先级

### 第一阶段：基础设施修复（关键）
1. **运行代码生成** - 修复所有freezed和json_serializable生成问题
2. **定义缺失类型** - 创建所有未定义的类和枚举
3. **修复语法错误** - 清理所有语法和格式问题
4. **修复导入错误** - 确保所有导入路径正确

### 第二阶段：数据模型修复（重要）
1. **修复实体定义** - 确保所有实体属性正确定义
2. **修复模型转换** - 实现正确的toEntity和fromEntity方法
3. **修复枚举转换** - 实现字符串与枚举的正确转换
4. **验证序列化** - 确保JSON序列化和反序列化正常工作

### 第三阶段：业务逻辑修复（中等）
1. **修复Repository接口** - 定义所有缺失的Repository接口
2. **修复UseCase实现** - 实现所有缺失的UseCase类
3. **修复依赖注入** - 确保所有依赖都能正确注入
4. **修复服务定位** - 修复sl()函数和服务注册问题

### 第四阶段：表现层修复（一般）
1. **修复Provider状态** - 确保所有Provider正确管理状态
2. **修复UI组件** - 修复所有UI渲染和交互问题
3. **修复页面导航** - 确保页面间导航正常工作
4. **修复用户交互** - 确保所有用户操作正常响应

这个设计文档为系统性修复VanHub项目的编译错误提供了完整的技术方案和实施指南，确保修复过程的有序进行和架构的完整性。