import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../domain/entities/project.dart';
import '../providers/project_provider.dart';

/// 项目复刻确认对话框
class ForkProjectDialogWidget extends ConsumerStatefulWidget {
  final Project sourceProject;
  final VoidCallback? onForkSuccess;

  const ForkProjectDialogWidget({
    super.key,
    required this.sourceProject,
    this.onForkSuccess,
  });

  @override
  ConsumerState<ForkProjectDialogWidget> createState() => _ForkProjectDialogWidgetState();
}

class _ForkProjectDialogWidgetState extends ConsumerState<ForkProjectDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  bool _copyBomItems = true;
  bool _copySystems = true;
  bool _copyImages = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _titleController.text = '${widget.sourceProject.title} - 复刻版';
    _descriptionController.text = '复刻自: ${widget.sourceProject.title}';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        padding: const EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Icon(
                  Icons.fork_right,
                  color: VanHubColors.primary,
                  size: 28,
                ),
                const SizedBox(width: VanHubSpacing.sm),
                Expanded(
                  child: Text(
                    '复刻项目',
                    style: VanHubTypography.headlineMedium.copyWith(
                      color: VanHubColors.textPrimary,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  color: VanHubColors.textSecondary,
                ),
              ],
            ),
            
            const SizedBox(height: VanHubSpacing.lg),
            
            // 源项目信息
            Container(
              padding: const EdgeInsets.all(VanHubSpacing.md),
              decoration: BoxDecoration(
                color: VanHubColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: VanHubColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.source,
                        color: VanHubColors.textSecondary,
                        size: 20,
                      ),
                      const SizedBox(width: VanHubSpacing.xs),
                      Text(
                        '源项目',
                        style: VanHubTypography.titleMedium.copyWith(
                          color: VanHubColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: VanHubSpacing.sm),
                  Text(
                    widget.sourceProject.title,
                    style: VanHubTypography.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: VanHubColors.textPrimary,
                    ),
                  ),
                  if (widget.sourceProject.description.isNotEmpty) ...[
                    const SizedBox(height: VanHubSpacing.xs),
                    Text(
                      widget.sourceProject.description,
                      style: VanHubTypography.bodySmall.copyWith(
                        color: VanHubColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: VanHubSpacing.sm),
                  Row(
                    children: [
                      _buildInfoChip(
                        icon: Icons.account_circle,
                        label: widget.sourceProject.authorName ?? '未知作者',
                      ),
                      const SizedBox(width: VanHubSpacing.sm),
                      _buildInfoChip(
                        icon: Icons.directions_car,
                        label: widget.sourceProject.fullVehicleInfo.isNotEmpty 
                            ? widget.sourceProject.fullVehicleInfo 
                            : '未指定车型',
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: VanHubSpacing.lg),
            
            // 复刻设置表单
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 项目标题
                      Text(
                        '新项目标题',
                        style: VanHubTypography.titleMedium.copyWith(
                          color: VanHubColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: VanHubSpacing.sm),
                      TextFormField(
                        controller: _titleController,
                        decoration: InputDecoration(
                          hintText: '输入新项目的标题',
                          prefixIcon: const Icon(Icons.title),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入项目标题';
                          }
                          if (value.trim().length < 2) {
                            return '项目标题至少需要2个字符';
                          }
                          return null;
                        },
                        maxLength: 100,
                      ),
                      
                      const SizedBox(height: VanHubSpacing.md),
                      
                      // 项目描述
                      Text(
                        '项目描述（可选）',
                        style: VanHubTypography.titleMedium.copyWith(
                          color: VanHubColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: VanHubSpacing.sm),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          hintText: '描述你的复刻项目...',
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        maxLines: 3,
                        maxLength: 500,
                      ),
                      
                      const SizedBox(height: VanHubSpacing.lg),
                      
                      // 复制选项
                      Text(
                        '复制选项',
                        style: VanHubTypography.titleMedium.copyWith(
                          color: VanHubColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: VanHubSpacing.sm),
                      
                      _buildCopyOption(
                        title: '复制BOM清单',
                        subtitle: '复制所有物料清单项目',
                        value: _copyBomItems,
                        onChanged: (value) => setState(() => _copyBomItems = value),
                        icon: Icons.list_alt,
                      ),
                      
                      _buildCopyOption(
                        title: '复制改装系统',
                        subtitle: '复制所有改装系统配置',
                        value: _copySystems,
                        onChanged: (value) => setState(() => _copySystems = value),
                        icon: Icons.build_circle,
                      ),
                      
                      _buildCopyOption(
                        title: '复制图片',
                        subtitle: '复制项目中的所有图片',
                        value: _copyImages,
                        onChanged: (value) => setState(() => _copyImages = value),
                        icon: Icons.image,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: VanHubSpacing.lg),
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: VanHubSpacing.md),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleFork,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: VanHubColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.fork_right, size: 20),
                              const SizedBox(width: VanHubSpacing.xs),
                              const Text('开始复刻'),
                            ],
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: VanHubSpacing.sm,
        vertical: VanHubSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: VanHubColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: VanHubColors.primary,
          ),
          const SizedBox(width: VanHubSpacing.xs),
          Text(
            label,
            style: VanHubTypography.bodySmall.copyWith(
              color: VanHubColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCopyOption({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubSpacing.sm),
      child: InkWell(
        onTap: () => onChanged(!value),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(VanHubSpacing.md),
          decoration: BoxDecoration(
            border: Border.all(
              color: value ? VanHubColors.primary : VanHubColors.border,
            ),
            borderRadius: BorderRadius.circular(12),
            color: value ? VanHubColors.primary.withValues(alpha: 0.05) : null,
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: value ? VanHubColors.primary : VanHubColors.textSecondary,
                size: 24,
              ),
              const SizedBox(width: VanHubSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: VanHubTypography.bodyLarge.copyWith(
                        color: value ? VanHubColors.primary : VanHubColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: VanHubTypography.bodySmall.copyWith(
                        color: VanHubColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: value,
                onChanged: onChanged,
                activeColor: VanHubColors.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleFork() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await ref.read(projectControllerProvider.notifier).forkProject(
        sourceProjectId: widget.sourceProject.id,
        newProjectTitle: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        copyBomItems: _copyBomItems,
        copySystems: _copySystems,
        copyImages: _copyImages,
      );
      
      if (mounted) {
        result.fold(
          (failure) => _showError('复刻失败: ${failure.message}'),
          (forkResult) {
            Navigator.of(context).pop();
            _showSuccess();
            widget.onForkSuccess?.call();
          },
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('复刻失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: VanHubSpacing.sm),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: VanHubColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.check_circle_outline, color: Colors.white),
                const SizedBox(width: VanHubSpacing.sm),
                const Text('项目复刻成功！'),
              ],
            ),
            const SizedBox(height: VanHubSpacing.xs),
            const Text(
              '新项目已创建，正在跳转...',
              style: TextStyle(fontSize: 12, color: Colors.white70),
            ),
          ],
        ),
        backgroundColor: VanHubColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}