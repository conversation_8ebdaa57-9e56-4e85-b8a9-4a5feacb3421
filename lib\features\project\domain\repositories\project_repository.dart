import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/project.dart';
import '../entities/fork_request.dart';
import '../entities/create_project_request.dart';

abstract class ProjectRepository {
  /// 创建项目
  Future<Either<Failure, Project>> createProject(CreateProjectRequest request);
  
  /// 获取用户项目列表
  Future<Either<Failure, List<Project>>> getUserProjects(String userId);
  
  /// 获取公开项目列表（游客模式）
  Future<Either<Failure, List<Project>>> getPublicProjects({
    int limit = 20,
    int offset = 0,
  });
  
  /// 获取项目详情
  Future<Either<Failure, Project>> getProjectById(String projectId);
  
  /// 更新项目
  Future<Either<Failure, Project>> updateProject(String projectId, Map<String, dynamic> updates);
  
  /// 删除项目
  Future<Either<Failure, void>> deleteProject(String projectId);
  
  /// 点赞项目
  Future<Either<Failure, void>> likeProject(String projectId);
  
  /// 取消点赞
  Future<Either<Failure, void>> unlikeProject(String projectId);
  
  /// 复刻项目
  Future<Either<Failure, Project>> forkProject(String projectId, ForkRequest forkRequest);
  
  /// 获取点赞状态
  Future<Either<Failure, bool>> getLikeStatus(String projectId);
  
  /// 搜索项目
  Future<Either<Failure, List<Project>>> searchProjects(String query, {
    int limit = 20,
    int offset = 0,
  });
}