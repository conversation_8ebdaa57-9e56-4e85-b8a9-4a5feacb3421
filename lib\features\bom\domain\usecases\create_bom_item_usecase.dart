import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/bom_item.dart';
import '../entities/create_bom_item_request.dart';
import '../repositories/bom_repository.dart';

/// 创建BOM项目用例参数
class CreateBomItemParams {
  final String projectId;
  final CreateBomItemRequest request;

  const CreateBomItemParams({
    required this.projectId,
    required this.request,
  });
}

/// 创建BOM项目用例
class CreateBomItemUseCase implements UseCase<BomItem, CreateBomItemParams> {
  final BomRepository repository;

  const CreateBomItemUseCase(this.repository);

  @override
  Future<Either<Failure, BomItem>> call(CreateBomItemParams params) async {
    return await repository.createBomItem(params.request);
  }
}