import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/presentation/providers/bom_provider.dart';
import '../../../../core/widgets/loading_widget.dart';

/// BOM项目选择对话框
class BomItemSelectorDialog extends ConsumerStatefulWidget {
  final String projectId;
  final List<String> selectedItemIds;

  const BomItemSelectorDialog({
    super.key,
    required this.projectId,
    this.selectedItemIds = const [],
  });

  @override
  ConsumerState<BomItemSelectorDialog> createState() => _BomItemSelectorDialogState();
}

class _BomItemSelectorDialogState extends ConsumerState<BomItemSelectorDialog> {
  final Set<String> _selectedItems = {};
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedItems.addAll(widget.selectedItemIds);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(widget.projectId));

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.deepOrange,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.shopping_cart, color: Colors.white),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '选择BOM项目',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 搜索栏
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: '搜索BOM项目...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
              ),
            ),
            
            // BOM项目列表
            Expanded(
              child: bomItemsAsync.when(
                data: (bomItems) => _buildBomItemsList(bomItems),
                loading: () => const LoadingWidget(),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('加载失败: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.refresh(projectBomItemsProvider(widget.projectId)),
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Text(
                    '已选择 ${_selectedItems.length} 个项目',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _selectedItems.isNotEmpty
                        ? () => Navigator.of(context).pop(_selectedItems.toList())
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBomItemsList(List<BomItem> bomItems) {
    // 过滤搜索结果
    final filteredItems = bomItems.where((item) {
      if (_searchQuery.isEmpty) return true;
      return item.materialName.toLowerCase().contains(_searchQuery) ||
             (item.category?.toLowerCase().contains(_searchQuery) ?? false) ||
             (item.brand?.toLowerCase().contains(_searchQuery) ?? false);
    }).toList();

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? '该项目还没有BOM项目' : '没有找到匹配的项目',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        final isSelected = _selectedItems.contains(item.id);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: CheckboxListTile(
            value: isSelected,
            onChanged: (selected) {
              setState(() {
                if (selected == true) {
                  _selectedItems.add(item.id);
                } else {
                  _selectedItems.remove(item.id);
                }
              });
            },
            title: Text(
              item.materialName,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (item.category != null) ...[
                  Text('分类: ${item.category}'),
                ],
                Row(
                  children: [
                    Text('数量: ${item.quantity}'),
                    const SizedBox(width: 16),
                    if (item.unitPrice != null) ...[
                      Text('单价: ¥${item.unitPrice!.toStringAsFixed(2)}'),
                    ],
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getStatusColor(item.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(item.status),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            secondary: item.brand != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.business, size: 16),
                      Text(
                        item.brand!,
                        style: const TextStyle(fontSize: 10),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                : null,
          ),
        );
      },
    );
  }

  Color _getStatusColor(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return Colors.blue;
      case BomItemStatus.ordered:
        return Colors.orange;
      case BomItemStatus.received:
        return Colors.green;
      case BomItemStatus.installed:
        return Colors.purple;
      case BomItemStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return '计划中';
      case BomItemStatus.ordered:
        return '已订购';
      case BomItemStatus.received:
        return '已收货';
      case BomItemStatus.installed:
        return '已安装';
      case BomItemStatus.cancelled:
        return '已取消';
    }
  }
}

/// 显示BOM项目选择对话框的便捷方法
Future<List<String>?> showBomItemSelectorDialog({
  required BuildContext context,
  required String projectId,
  List<String> selectedItemIds = const [],
}) {
  return showDialog<List<String>>(
    context: context,
    builder: (context) => BomItemSelectorDialog(
      projectId: projectId,
      selectedItemIds: selectedItemIds,
    ),
  );
}
