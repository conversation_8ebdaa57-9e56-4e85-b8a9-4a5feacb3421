import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/api/supabase_config.dart';
import 'features/auth/presentation/pages/login_page.dart' as auth;
import 'features/auth/presentation/providers/auth_provider.dart';
import 'pages/safe_home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化Supabase
  await SupabaseConfig.initialize();

  runApp(
    const ProviderScope(
      child: VanHubApp(),
    ),
  );
}

class VanHubApp extends ConsumerWidget {
  const VanHubApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'VanHub改装宝',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepOrange),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
    );
  }
}

class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return authState.when(
      data: (user) {
        if (user != null) {
          return const SafeHomePage();
        } else {
          return const auth.LoginPage();
        }
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '应用初始化失败',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(authNotifierProvider),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}