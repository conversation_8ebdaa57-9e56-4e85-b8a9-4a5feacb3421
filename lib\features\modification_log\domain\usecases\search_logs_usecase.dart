import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../entities/log_search_criteria.dart';
import '../repositories/log_repository.dart';

/// 搜索日志条目用例
class SearchLogsUseCase implements UseCase<List<LogEntry>, LogSearchCriteria> {
  final LogRepository repository;

  SearchLogsUseCase(this.repository);

  @override
  Future<Either<Failure, List<LogEntry>>> call(LogSearchCriteria params) async {
    return await repository.searchLogs(params);
  }
}