import 'package:fpdart/fpdart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/create_project_request.dart';
import '../../domain/entities/fork_request.dart';
import '../../domain/repositories/project_repository.dart';
import '../datasources/project_remote_datasource.dart';
import '../models/project_model.dart';

class ProjectRepositoryImpl implements ProjectRepository {
  final ProjectRemoteDataSource remoteDataSource;
  final Ref ref;

  const ProjectRepositoryImpl({
    required this.remoteDataSource,
    required this.ref,
  });

  @override
  Future<Either<Failure, Project>> createProject(CreateProjectRequest request) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      final projectModel = await remoteDataSource.createProject(request, userId);
      return Right(projectModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '创建项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> getUserProjects(String userId) async {
    try {
      final projectModels = await remoteDataSource.getUserProjects(userId);
      final projects = projectModels.map((model) => model.toEntity()).toList();
      return Right(projects);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> getPublicProjects({int limit = 20, int offset = 0}) async {
    try {
      final projectModels = await remoteDataSource.getPublicProjects(limit: limit, offset: offset);
      final projects = projectModels.map((model) => model.toEntity()).toList();
      return Right(projects);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取公开项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> getProjectById(String projectId) async {
    try {
      final projectModel = await remoteDataSource.getProjectById(projectId);
      return Right(projectModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取项目详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> updateProject(String projectId, Map<String, dynamic> updates) async {
    try {
      final projectModel = await remoteDataSource.updateProject(projectId, updates);
      return Right(projectModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProject(String projectId) async {
    try {
      await remoteDataSource.deleteProject(projectId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '删除项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> likeProject(String projectId) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      await remoteDataSource.likeProject(projectId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '点赞项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> unlikeProject(String projectId) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      await remoteDataSource.unlikeProject(projectId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '取消点赞失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> forkProject(String projectId, ForkRequest forkRequest) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      // TODO: 更新remoteDataSource.forkProject以支持ForkRequest参数
      final projectModel = await remoteDataSource.forkProject(projectId, userId);
      return Right(projectModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '复刻项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> getLikeStatus(String projectId) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      final isLiked = await remoteDataSource.getLikeStatus(projectId, userId);
      return Right(isLiked);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取点赞状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> searchProjects(String query, {int limit = 20, int offset = 0}) async {
    try {
      final projectModels = await remoteDataSource.searchProjects(query, limit: limit, offset: offset);
      final projects = projectModels.map((model) => model.toEntity()).toList();
      return Right(projects);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '搜索项目失败: $e'));
    }
  }
}