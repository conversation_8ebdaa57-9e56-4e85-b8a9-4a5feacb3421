<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VanHub智能改装时间轴演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .timeline-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .timeline-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .view-modes {
            display: flex;
            gap: 10px;
        }

        .view-mode-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .view-mode-btn.active {
            background: #667eea;
            color: white;
        }

        .view-mode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            border-radius: 2px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 40px;
            opacity: 0;
            transform: translateX(-20px);
            animation: slideIn 0.6s ease forwards;
        }

        .timeline-item:nth-child(1) { animation-delay: 0.1s; }
        .timeline-item:nth-child(2) { animation-delay: 0.2s; }
        .timeline-item:nth-child(3) { animation-delay: 0.3s; }
        .timeline-item:nth-child(4) { animation-delay: 0.4s; }
        .timeline-item:nth-child(5) { animation-delay: 0.5s; }
        .timeline-item:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .timeline-marker {
            position: absolute;
            left: -28px;
            top: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 2;
        }

        .timeline-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .timeline-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .timeline-card.start { border-left-color: #4CAF50; }
        .timeline-card.problem { border-left-color: #FF9800; }
        .timeline-card.solution { border-left-color: #8BC34A; }
        .timeline-card.material { border-left-color: #673AB7; }
        .timeline-card.complete { border-left-color: #2196F3; }
        .timeline-card.milestone { border-left-color: #F44336; }

        .timeline-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
            color: #333;
        }

        .timeline-card .time {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .timeline-card .description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .timeline-card .tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .tag {
            background: #f0f0f0;
            color: #666;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
        }

        .timeline-card .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }

        .timeline-card .actions {
            display: flex;
            gap: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border: none;
            background: none;
            color: #666;
            cursor: pointer;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .action-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .stats-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stat-card .value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9em;
        }

        .floating-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .timeline {
                padding-left: 20px;
            }
            
            .timeline-marker {
                left: -20px;
                width: 30px;
                height: 30px;
                font-size: 16px;
            }
            
            .view-modes {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🎯 VanHub智能改装时间轴</h1>
            <p>让每一次改装经验都成为宝贵的知识财富 - 多层次、交互式的改装记录体验</p>
            <div style="display: flex; gap: 20px; flex-wrap: wrap; margin-top: 15px;">
                <span style="background: #e3f2fd; color: #1976d2; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">🔧 微观层：详细操作记录</span>
                <span style="background: #f3e5f5; color: #7b1fa2; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">📖 学习层：知识萃取</span>
                <span style="background: #e8f5e8; color: #388e3c; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">👥 互动层：社区协作</span>
                <span style="background: #fff3e0; color: #f57c00; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">🎮 游戏化：成就系统</span>
            </div>
        </div>

        <!-- 时间轴容器 -->
        <div class="timeline-container">
            <div class="timeline-header">
                <div class="timeline-title">
                    📅 依维柯Daily电气系统改装记录
                </div>
                <div class="view-modes">
                    <button class="view-mode-btn active" data-mode="detailed" data-testid="detailed-mode">详细模式</button>
                    <button class="view-mode-btn" data-mode="compact" data-testid="compact-mode">紧凑模式</button>
                    <button class="view-mode-btn" data-mode="learning" data-testid="learning-mode">学习模式</button>
                </div>
            </div>

            <div class="timeline" id="timeline">
                <!-- 时间轴项目1：开始工作 -->
                <div class="timeline-item" data-testid="timeline-item-1">
                    <div class="timeline-marker" style="background: #4CAF50;">🚀</div>
                    <div class="timeline-card start">
                        <h3>开始安装逆变器</h3>
                        <div class="time">⏰ 4小时前 • 耗时: 30分钟</div>
                        <div class="description">
                            准备安装3000W逆变器，选择床下位置，开始准备工具和材料。工具清单：电钻、螺丝刀、万用表、线鼻子压接钳。
                        </div>
                        <div class="tags">
                            <span class="tag">电气系统</span>
                            <span class="tag">逆变器</span>
                            <span class="tag">准备工作</span>
                        </div>
                        <div class="stats">
                            <div class="stat">💰 成本: ¥1,200</div>
                            <div class="stat">📸 3张图片</div>
                            <div class="stat">🎯 重要事件</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-1">👍 点赞 (2)</button>
                            <button class="action-btn" data-testid="comment-btn-1">💬 评论 (2)</button>
                            <button class="action-btn" data-testid="share-btn-1">🔗 分享</button>
                        </div>
                    </div>
                </div>

                <!-- 时间轴项目2：遇到问题 -->
                <div class="timeline-item" data-testid="timeline-item-2">
                    <div class="timeline-marker" style="background: #FF9800;">⚠️</div>
                    <div class="timeline-card problem">
                        <h3>发现空间不够</h3>
                        <div class="time">⏰ 3小时15分前 • 问题状态: 解决中</div>
                        <div class="description">
                            原计划的安装位置空间不够，逆变器无法放入，需要重新选择位置。这是新手常犯的错误，测量不够精确。
                        </div>
                        <div class="tags">
                            <span class="tag">问题记录</span>
                            <span class="tag">空间不足</span>
                            <span class="tag">避坑指南</span>
                        </div>
                        <div class="stats">
                            <div class="stat">⚠️ 问题事件</div>
                            <div class="stat">📸 2张图片</div>
                            <div class="stat">🆘 需要帮助</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-2">👍 点赞 (1)</button>
                            <button class="action-btn" data-testid="comment-btn-2">💬 评论 (5)</button>
                            <button class="action-btn" data-testid="help-btn-2">🙋 求助</button>
                        </div>
                    </div>
                </div>

                <!-- 时间轴项目3：解决问题 -->
                <div class="timeline-item" data-testid="timeline-item-3">
                    <div class="timeline-marker" style="background: #8BC34A;">💡</div>
                    <div class="timeline-card solution">
                        <h3>找到新的安装位置</h3>
                        <div class="time">⏰ 2小时45分前 • 问题状态: 已解决</div>
                        <div class="description">
                            经过测量，决定将逆变器安装在驾驶室后方，空间充足且散热良好。感谢 @老司机阿强 的建议！
                        </div>
                        <div class="tags">
                            <span class="tag">问题解决</span>
                            <span class="tag">重新定位</span>
                            <span class="tag">社区帮助</span>
                        </div>
                        <div class="stats">
                            <div class="stat">✅ 问题已解决</div>
                            <div class="stat">📸 1张图片</div>
                            <div class="stat">👥 1位帮助者</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-3">👍 点赞 (3)</button>
                            <button class="action-btn" data-testid="comment-btn-3">💬 评论 (3)</button>
                            <button class="action-btn" data-testid="thank-btn-3">🙏 感谢</button>
                        </div>
                    </div>
                </div>

                <!-- 时间轴项目4：添加材料 -->
                <div class="timeline-item" data-testid="timeline-item-4">
                    <div class="timeline-marker" style="background: #673AB7;">📦</div>
                    <div class="timeline-card material">
                        <h3>添加额外线材</h3>
                        <div class="time">⏰ 2小时30分前</div>
                        <div class="description">
                            由于位置变更，需要额外的电线和线管，增加了150元成本。选择了阻燃型电线，安全第一！
                        </div>
                        <div class="tags">
                            <span class="tag">材料采购</span>
                            <span class="tag">线材</span>
                            <span class="tag">成本增加</span>
                        </div>
                        <div class="stats">
                            <div class="stat">💰 成本: ¥150</div>
                            <div class="stat">📦 2个BOM项目</div>
                            <div class="stat">📸 1张图片</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-4">👍 点赞 (1)</button>
                            <button class="action-btn" data-testid="comment-btn-4">💬 评论 (1)</button>
                            <button class="action-btn" data-testid="bom-btn-4">📋 查看BOM</button>
                        </div>
                    </div>
                </div>

                <!-- 时间轴项目5：完成步骤 -->
                <div class="timeline-item" data-testid="timeline-item-5">
                    <div class="timeline-marker" style="background: #2196F3;">✅</div>
                    <div class="timeline-card complete">
                        <h3>逆变器安装完成</h3>
                        <div class="time">⏰ 30分钟前 • 耗时: 3小时</div>
                        <div class="description">
                            成功安装逆变器，接线完成，测试正常工作。记得预留散热空间，这是关键的安全要点！
                        </div>
                        <div class="tags">
                            <span class="tag">安装完成</span>
                            <span class="tag">测试通过</span>
                            <span class="tag">安全要点</span>
                        </div>
                        <div class="stats">
                            <div class="stat">⏱️ 耗时: 3小时</div>
                            <div class="stat">📸 5张图片</div>
                            <div class="stat">🎯 重要事件</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-5">👍 点赞 (4)</button>
                            <button class="action-btn" data-testid="comment-btn-5">💬 评论 (8)</button>
                            <button class="action-btn" data-testid="bookmark-btn-5">⭐ 收藏</button>
                        </div>
                    </div>
                </div>

                <!-- 时间轴项目6：里程碑 -->
                <div class="timeline-item" data-testid="timeline-item-6">
                    <div class="timeline-marker" style="background: #F44336;">🎯</div>
                    <div class="timeline-card milestone">
                        <h3>电气系统第一阶段完成</h3>
                        <div class="time">⏰ 15分钟前 • 里程碑事件</div>
                        <div class="description">
                            逆变器安装完成，标志着电气系统改装进入下一阶段。接下来将进行电池组和配电箱的安装。
                        </div>
                        <div class="tags">
                            <span class="tag">里程碑</span>
                            <span class="tag">阶段完成</span>
                            <span class="tag">电气系统</span>
                        </div>
                        <div class="stats">
                            <div class="stat">🎯 里程碑事件</div>
                            <div class="stat">📈 进度: 60%</div>
                            <div class="stat">🏆 获得徽章</div>
                        </div>
                        <div class="actions">
                            <button class="action-btn" data-testid="like-btn-6">👍 点赞 (5)</button>
                            <button class="action-btn" data-testid="comment-btn-6">💬 评论 (12)</button>
                            <button class="action-btn" data-testid="celebrate-btn-6">🎉 庆祝</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <h2 style="color: #333; margin-bottom: 10px;">📊 项目统计数据</h2>
            <p style="color: #666; margin-bottom: 20px;">基于真实改装数据的智能分析</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon" style="color: #2196F3;">📅</div>
                    <div class="value" style="color: #2196F3;">6</div>
                    <div class="label">时间轴事件</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #4CAF50;">💰</div>
                    <div class="value" style="color: #4CAF50;">¥1,350</div>
                    <div class="label">总成本</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #FF9800;">⏱️</div>
                    <div class="value" style="color: #FF9800;">4小时15分</div>
                    <div class="label">总耗时</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #9C27B0;">✅</div>
                    <div class="value" style="color: #9C27B0;">100%</div>
                    <div class="label">问题解决率</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #F44336;">👥</div>
                    <div class="value" style="color: #F44336;">31</div>
                    <div class="label">社区互动</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #607D8B;">📚</div>
                    <div class="value" style="color: #607D8B;">4.2</div>
                    <div class="label">学习价值</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <button class="floating-btn" data-testid="add-event-btn" title="添加新事件">+</button>

    <script>
        // 视图模式切换
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.view-mode-btn').forEach(b => b.classList.remove('active'));
                // 添加active类到当前按钮
                this.classList.add('active');
                
                const mode = this.dataset.mode;
                switchViewMode(mode);
            });
        });

        function switchViewMode(mode) {
            const timeline = document.getElementById('timeline');
            const items = document.querySelectorAll('.timeline-item');
            
            // 移除所有模式类
            timeline.classList.remove('compact-mode', 'learning-mode');
            
            switch(mode) {
                case 'compact':
                    timeline.classList.add('compact-mode');
                    // 紧凑模式：隐藏详细信息
                    items.forEach(item => {
                        const description = item.querySelector('.description');
                        const stats = item.querySelector('.stats');
                        const actions = item.querySelector('.actions');
                        
                        if (description) description.style.display = 'none';
                        if (stats) stats.style.display = 'none';
                        if (actions) actions.style.display = 'none';
                    });
                    showNotification('切换到紧凑模式');
                    break;
                    
                case 'learning':
                    timeline.classList.add('learning-mode');
                    // 学习模式：只显示重要事件和问题解决
                    items.forEach((item, index) => {
                        const isImportant = [0, 1, 2, 4, 5].includes(index); // 重要事件索引
                        item.style.display = isImportant ? 'block' : 'none';
                    });
                    showNotification('切换到学习模式 - 只显示关键学习点');
                    break;
                    
                case 'detailed':
                default:
                    // 详细模式：显示所有信息
                    items.forEach(item => {
                        item.style.display = 'block';
                        const description = item.querySelector('.description');
                        const stats = item.querySelector('.stats');
                        const actions = item.querySelector('.actions');
                        
                        if (description) description.style.display = 'block';
                        if (stats) stats.style.display = 'flex';
                        if (actions) actions.style.display = 'flex';
                    });
                    showNotification('切换到详细模式');
                    break;
            }
        }

        // 交互按钮事件
        document.addEventListener('click', function(e) {
            if (e.target.matches('[data-testid^="like-btn"]')) {
                const itemId = e.target.dataset.testid.split('-')[2];
                showNotification(`点赞时间轴事件 ${itemId}`);
                
                // 更新点赞数
                const currentText = e.target.textContent;
                const currentCount = parseInt(currentText.match(/\d+/)[0]);
                e.target.innerHTML = `👍 点赞 (${currentCount + 1})`;
            }
            
            if (e.target.matches('[data-testid^="comment-btn"]')) {
                const itemId = e.target.dataset.testid.split('-')[2];
                showNotification(`评论时间轴事件 ${itemId}`);
            }
            
            if (e.target.matches('[data-testid^="share-btn"]')) {
                const itemId = e.target.dataset.testid.split('-')[2];
                showNotification(`分享时间轴事件 ${itemId}`);
            }
            
            if (e.target.matches('[data-testid="add-event-btn"]')) {
                showNotification('打开添加事件对话框');
            }
        });

        // 时间轴项目点击事件
        document.querySelectorAll('.timeline-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮，不触发卡片点击
                if (e.target.matches('button') || e.target.closest('button')) {
                    return;
                }
                
                const item = this.closest('.timeline-item');
                const itemId = item.dataset.testid.split('-')[2];
                showNotification(`查看时间轴事件 ${itemId} 详情`);
                
                // 添加点击效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 通知函数
        function showNotification(message) {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }
            
            // 创建新通知
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                z-index: 1001;
                font-size: 14px;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('VanHub智能改装时间轴加载完成！');
        });
    </script>
</body>
</html>
