import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material.dart';
import '../repositories/material_repository.dart';

class GetMaterialsUseCase {
  final MaterialRepository repository;

  const GetMaterialsUseCase(this.repository);

  /// 获取用户所有材料
  Future<Either<Failure, List<Material>>> getUserMaterials(String userId) async {
    if (userId.isEmpty) {
      return const Left(ValidationFailure(message: '用户ID不能为空'));
    }

    return await repository.getUserMaterials(userId);
  }

  /// 获取公开材料列表（游客模式使用）
  Future<Either<Failure, List<Material>>> getPublicMaterials() async {
    return await repository.getPublicMaterials();
  }

  /// 根据分类获取材料
  Future<Either<Failure, List<Material>>> getMaterialsByCategory(
    String userId, 
    String category,
  ) async {
    if (userId.isEmpty) {
      return const Left(ValidationFailure(message: '用户ID不能为空'));
    }

    return await repository.getMaterialsByCategory(userId, category);
  }

  /// 搜索材料
  Future<Either<Failure, List<Material>>> searchMaterials(
    String userId,
    String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    if (userId.isEmpty) {
      return const Left(ValidationFailure(message: '用户ID不能为空'));
    }
    
    if (query.trim().isEmpty) {
      return const Left(ValidationFailure(message: '搜索关键词不能为空'));
    }
    
    if (limit <= 0) {
      return const Left(ValidationFailure(message: '限制数量必须大于0'));
    }
    
    if (offset < 0) {
      return const Left(ValidationFailure(message: '偏移量不能为负数'));
    }
    
    if (minPrice != null && minPrice < 0) {
      return const Left(ValidationFailure(message: '最低价格不能为负数'));
    }
    
    if (maxPrice != null && maxPrice < 0) {
      return const Left(ValidationFailure(message: '最高价格不能为负数'));
    }
    
    if (minPrice != null && maxPrice != null && minPrice > maxPrice) {
      return const Left(ValidationFailure(message: '最低价格不能高于最高价格'));
    }

    return await repository.searchMaterials(
      userId,
      query.trim(),
      category: category,
      minPrice: minPrice,
      maxPrice: maxPrice,
      limit: limit,
      offset: offset,
    );
  }

  /// 获取热门材料
  Future<Either<Failure, List<Material>>> getPopularMaterials(
    String userId, {
    int limit = 10,
  }) async {
    if (userId.isEmpty) {
      return const Left(ValidationFailure(message: '用户ID不能为空'));
    }
    
    if (limit <= 0) {
      return const Left(ValidationFailure(message: '限制数量必须大于0'));
    }

    return await repository.getPopularMaterials(userId, limit: limit);
  }

  /// 获取最近使用的材料
  Future<Either<Failure, List<Material>>> getRecentlyUsedMaterials(
    String userId, {
    int limit = 10,
  }) async {
    if (userId.isEmpty) {
      return const Left(ValidationFailure(message: '用户ID不能为空'));
    }
    
    if (limit <= 0) {
      return const Left(ValidationFailure(message: '限制数量必须大于0'));
    }

    return await repository.getRecentlyUsedMaterials(userId, limit: limit);
  }
}