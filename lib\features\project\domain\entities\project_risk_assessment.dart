/// 项目风险评估实体
class ProjectRiskAssessment {
  final String projectId;
  final RiskLevel overallRiskLevel;
  final double riskScore;
  final List<RiskFactor> riskFactors;
  final List<String> recommendations;
  final DateTime lastUpdated;

  const ProjectRiskAssessment({
    required this.projectId,
    required this.overallRiskLevel,
    required this.riskScore,
    required this.riskFactors,
    required this.recommendations,
    required this.lastUpdated,
  });

  factory ProjectRiskAssessment.fromJson(Map<String, dynamic> json) {
    return ProjectRiskAssessment(
      projectId: json['project_id'],
      overallRiskLevel: RiskLevel.values.firstWhere(
        (level) => level.name == json['overall_risk_level'],
        orElse: () => RiskLevel.medium,
      ),
      riskScore: (json['risk_score'] as num).toDouble(),
      riskFactors: (json['risk_factors'] as List<dynamic>)
          .map((item) => RiskFactor.fromJson(item))
          .toList(),
      recommendations: List<String>.from(json['recommendations']),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'project_id': projectId,
      'overall_risk_level': overallRiskLevel.name,
      'risk_score': riskScore,
      'risk_factors': riskFactors.map((factor) => factor.toJson()).toList(),
      'recommendations': recommendations,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// 风险等级枚举
enum RiskLevel {
  low,     // 低风险
  medium,  // 中等风险
  high,    // 高风险
  critical, // 严重风险
}

/// 风险因素
class RiskFactor {
  final String name;
  final String description;
  final RiskLevel level;
  final double impact;
  final double probability;
  final String category;

  const RiskFactor({
    required this.name,
    required this.description,
    required this.level,
    required this.impact,
    required this.probability,
    required this.category,
  });

  factory RiskFactor.fromJson(Map<String, dynamic> json) {
    return RiskFactor(
      name: json['name'],
      description: json['description'],
      level: RiskLevel.values.firstWhere(
        (level) => level.name == json['level'],
        orElse: () => RiskLevel.medium,
      ),
      impact: (json['impact'] as num).toDouble(),
      probability: (json['probability'] as num).toDouble(),
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'level': level.name,
      'impact': impact,
      'probability': probability,
      'category': category,
    };
  }
}