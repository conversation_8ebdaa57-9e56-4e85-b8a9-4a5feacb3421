import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../material/domain/entities/material.dart' as material_domain;
import '../../../material/presentation/providers/material_provider.dart';
import '../../../material/presentation/providers/material_filter_provider.dart';
import '../providers/bom_provider.dart';

/// VanHub从材料库添加到BOM对话框 - 智能联动功能
class AddMaterialToBomDialogWidget extends ConsumerStatefulWidget {
  final String projectId;
  final material_domain.Material? preselectedMaterial;

  const AddMaterialToBomDialogWidget({
    super.key,
    required this.projectId,
    this.preselectedMaterial,
  });

  @override
  ConsumerState<AddMaterialToBomDialogWidget> createState() => _AddMaterialToBomDialogWidgetState();
}

class _AddMaterialToBomDialogWidgetState extends ConsumerState<AddMaterialToBomDialogWidget> {
  // 移除本地状态，使用Provider管理
  material_domain.Material? _selectedMaterial;
  
  final _quantityController = TextEditingController(text: '1');
  final _customPriceController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime? _plannedDate;
  
  // 房车改装专业分类
  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '车顶改装', 
    '底盘改装', '其他配件'
  ];

  @override
  void initState() {
    super.initState();

    // 如果有预选材料，自动选择并填充价格
    if (widget.preselectedMaterial != null) {
      _selectedMaterial = widget.preselectedMaterial;
      _customPriceController.text = widget.preselectedMaterial!.price.toString();
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _customPriceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 600,
        height: 700,
        child: Column(
          children: [
            // 头部区域
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.indigo, Colors.indigo.shade700],
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.library_add, color: Colors.white, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '从材料库添加到BOM',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '智能联动，一键添加已有材料',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 内容区域
            Expanded(
              child: Row(
                children: [
                  // 左侧：材料选择区域
                  Expanded(
                    flex: 3,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Column(
                        children: [
                          // 搜索和筛选
                          Container(
                            padding: const EdgeInsets.all(16),
                            color: Colors.grey[50],
                            child: Column(
                              children: [
                                // 搜索框
                                TextField(
                                  decoration: InputDecoration(
                                    hintText: '搜索材料...',
                                    prefixIcon: const Icon(Icons.search),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(25),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  onChanged: (query) {
                                    // 通过Provider更新搜索查询，遵循Clean Architecture
                                    ref.read(materialSearchProvider.notifier).updateQuery(query);
                                  },
                                ),
                                
                                const SizedBox(height: 12),
                                
                                // 分类筛选
                                SizedBox(
                                  height: 35,
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: _categories.length,
                                    itemBuilder: (context, index) {
                                      final category = _categories[index];
                                      final selectedCategory = ref.watch(materialCategorySelectionProvider);
                                      final isSelected = category == selectedCategory;
                                      
                                      return Padding(
                                        padding: const EdgeInsets.only(right: 8),
                                        child: FilterChip(
                                          label: Text(category),
                                          selected: isSelected,
                                          onSelected: (selected) {
                                            // 通过Provider更新分类选择，遵循Clean Architecture
                                            ref.read(materialCategorySelectionProvider.notifier).updateCategory(category);
                                          },
                                          backgroundColor: Colors.grey[200],
                                          selectedColor: Colors.indigo.shade100,
                                          checkmarkColor: Colors.indigo,
                                          labelStyle: TextStyle(
                                            color: isSelected ? Colors.indigo : Colors.grey[700],
                                            fontSize: 12,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // 材料列表
                          Expanded(
                            child: _buildMaterialList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // 右侧：添加配置区域
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: _selectedMaterial != null
                          ? _buildAddConfiguration()
                          : _buildSelectPrompt(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaterialList() {
    // 使用过滤后的材料Provider，业务逻辑已移动到Provider中
    final filteredMaterialsAsync = ref.watch(filteredMaterialsByFilterProvider);

    return filteredMaterialsAsync.when(
      data: (filteredMaterials) {
        if (filteredMaterials.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.inventory_2,
            title: '没有找到材料',
            subtitle: '尝试调整搜索条件或分类筛选',
          );
        }

        return ListView.builder(
          itemCount: filteredMaterials.length,
          itemBuilder: (context, index) {
            final material = filteredMaterials[index];
            final isSelected = _selectedMaterial?.id == material.id;
            
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.indigo : Colors.transparent,
                  width: 2,
                ),
              ),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getCategoryColor(material.category),
                  child: Icon(
                    _getCategoryIcon(material.category),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(
                  material.name,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (material.brand != null)
                      Text('${material.brand} ${material.model ?? ''}'),
                    Text(
                      '¥${material.price.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                trailing: isSelected
                    ? Icon(Icons.check_circle, color: Colors.indigo)
                    : null,
                onTap: () {
                  setState(() {
                    _selectedMaterial = material;
                    // 自动填充价格
                    _customPriceController.text = material.price.toString();
                  });
                },
              ),
            );
          },
        );
      },
      loading: () => const LoadingWidget(message: '加载材料库...'),
      error: (error, stack) => ErrorDisplayWidget(
        message: '加载失败: ${error.toString()}',
        onRetry: () {
          ref.invalidate(materialsNotifierProvider);
        },
      ),
    );
  }

  Widget _buildSelectPrompt() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.touch_app,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '请选择要添加的材料',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '从左侧材料库中选择\n已有的材料项目',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddConfiguration() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 选中的材料信息
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.indigo.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.indigo, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      '已选择材料',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedMaterial!.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_selectedMaterial!.brand != null)
                  Text(
                    '${_selectedMaterial!.brand} ${_selectedMaterial!.model ?? ''}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                Text(
                  '库存价格: ¥${_selectedMaterial!.price.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: Colors.green[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 配置表单
          const Text(
            'BOM配置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // 数量
          TextField(
            controller: _quantityController,
            decoration: const InputDecoration(
              labelText: '数量',
              hintText: '需要的数量',
              prefixIcon: Icon(Icons.numbers),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          
          const SizedBox(height: 16),
          
          // 自定义价格
          TextField(
            controller: _customPriceController,
            decoration: const InputDecoration(
              labelText: '预估价格 (¥)',
              hintText: '可调整预估价格',
              prefixIcon: Icon(Icons.attach_money),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          
          const SizedBox(height: 16),
          
          // 计划日期
          InkWell(
            onTap: _selectPlannedDate,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _plannedDate != null
                          ? '计划日期: ${_plannedDate!.year}-${_plannedDate!.month.toString().padLeft(2, '0')}-${_plannedDate!.day.toString().padLeft(2, '0')}'
                          : '选择计划日期 (可选)',
                      style: TextStyle(
                        color: _plannedDate != null ? Colors.black87 : Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 备注
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: '备注说明',
              hintText: '安装注意事项、使用说明等...',
              prefixIcon: Icon(Icons.note),
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          
          const SizedBox(height: 24),
          
          // 添加按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isAddingToBom ? null : _addToBom,
              icon: _isAddingToBom
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.add_shopping_cart),
              label: Text(_isAddingToBom ? '添加中...' : '添加到BOM'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.purple;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.indigo;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '车顶改装':
        return Colors.red;
      case '底盘改装':
        return Colors.grey;
      default:
        return Colors.teal;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.brush;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathtub;
      case '车顶改装':
        return Icons.roofing;
      case '底盘改装':
        return Icons.build;
      default:
        return Icons.category;
    }
  }

  Future<void> _selectPlannedDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _plannedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.indigo,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      setState(() {
        _plannedDate = date;
      });
    }
  }

  bool _isAddingToBom = false; // 添加加载状态变量

  Future<void> _addToBom() async {
    if (_selectedMaterial == null) return;

    // 防止重复调用
    if (_isAddingToBom) {
      debugPrint('正在添加材料到BOM，忽略重复调用');
      return;
    }

    final quantity = int.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      _showError('请输入有效的数量');
      return;
    }

    final customPrice = double.tryParse(_customPriceController.text);

    try {
      // 安全地设置加载状态
      if (mounted) {
        setState(() {
          _isAddingToBom = true;
        });
      }

      // 调用BOM Provider添加材料
      final result = await ref.read(bomControllerProvider.notifier).addMaterialToBom(
        projectId: widget.projectId,
        materialId: _selectedMaterial!.id,
        quantity: quantity,
        customPrice: customPrice,
        plannedDate: _plannedDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      // 确保Widget仍然mounted才处理结果
      if (!mounted) return;

      result.fold(
        (failure) => _showError(failure.message),
        (bomItem) {
          _showSuccess('材料已成功添加到BOM！');
          // 延迟关闭对话框，确保成功消息能显示
          Future.delayed(const Duration(milliseconds: 1500), () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        },
      );
    } catch (e) {
      // 处理未预期的异常
      if (mounted) {
        _showError('添加失败: ${e.toString()}');
      }
    } finally {
      // 安全地重置加载状态
      if (mounted) {
        setState(() {
          _isAddingToBom = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}