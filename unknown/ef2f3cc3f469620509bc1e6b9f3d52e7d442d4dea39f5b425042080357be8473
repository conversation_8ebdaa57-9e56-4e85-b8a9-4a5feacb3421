import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';

/// 同步操作类型
enum SyncOperationType {
  materialToBom,    // 材料库到BOM
  bomToMaterial,    // BOM到材料库
  priceUpdate,      // 价格更新
  statusUpdate,     // 状态更新
}

/// 同步状态
enum SyncStatus {
  pending,    // 待处理
  completed,  // 已完成
  failed,     // 失败
  cancelled,  // 已取消
}

/// 同步操作实体
class SyncOperation {
  final String id;
  final SyncOperationType type;
  final String sourceId;
  final String targetId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final SyncStatus status;
  final String? errorMessage;

  const SyncOperation({
    required this.id,
    required this.type,
    required this.sourceId,
    required this.targetId,
    required this.data,
    required this.timestamp,
    this.status = SyncStatus.pending,
    this.errorMessage,
  });

  SyncOperation copyWith({
    String? id,
    SyncOperationType? type,
    String? sourceId,
    String? targetId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    SyncStatus? status,
    String? errorMessage,
  }) {
    return SyncOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      sourceId: sourceId ?? this.sourceId,
      targetId: targetId ?? this.targetId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// 价格同步提醒
class PriceSyncNotification {
  final String materialId;
  final String materialName;
  final double oldPrice;
  final double newPrice;
  final List<String> affectedBomItemIds;
  final DateTime timestamp;

  const PriceSyncNotification({
    required this.materialId,
    required this.materialName,
    required this.oldPrice,
    required this.newPrice,
    required this.affectedBomItemIds,
    required this.timestamp,
  });
}

/// 数据同步服务接口
abstract class DataSyncService {
  /// 将材料库物料同步到BOM项目
  Future<Either<Failure, void>> syncMaterialToBom(
    String materialId,
    String bomItemId,
  );

  /// 将BOM项目保存到材料库
  Future<Either<Failure, String>> syncBomToMaterial(String bomItemId);

  /// 同步材料价格更新到相关BOM项目
  Future<Either<Failure, List<PriceSyncNotification>>> syncPriceUpdates(
    String materialId,
  );

  /// 更新材料使用统计
  Future<Either<Failure, void>> updateMaterialUsageStats(
    String materialId,
    int usageIncrement,
  );

  /// 更新材料最近使用时间
  Future<Either<Failure, void>> updateMaterialLastUsed(String materialId);

  /// 获取价格同步提醒
  Future<Either<Failure, List<PriceSyncNotification>>> getPriceSyncNotifications(
    String userId,
  );

  /// 确认价格同步
  Future<Either<Failure, void>> confirmPriceSync(
    String materialId,
    List<String> bomItemIds,
    bool syncPrice,
  );

  /// 监听同步操作
  Stream<SyncOperation> watchSyncOperations();

  /// 获取同步历史
  Future<Either<Failure, List<SyncOperation>>> getSyncHistory({
    String? materialId,
    String? bomItemId,
    SyncOperationType? type,
    int limit = 50,
  });

  /// 重试失败的同步操作
  Future<Either<Failure, void>> retrySyncOperation(String operationId);

  /// 取消待处理的同步操作
  Future<Either<Failure, void>> cancelSyncOperation(String operationId);
}