import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/fork_request.dart';
import '../entities/project.dart';
import '../repositories/project_repository.dart';

class ForkProjectParams {
  final String sourceProjectId;
  final ForkRequest forkRequest;

  ForkProjectParams({
    required this.sourceProjectId,
    required this.forkRequest,
  });
}

class ForkProjectUseCase implements UseCase<Project, ForkProjectParams> {
  final ProjectRepository repository;

  ForkProjectUseCase(this.repository);

  @override
  Future<Either<Failure, Project>> call(ForkProjectParams params) async {
    return await repository.forkProject(
      params.sourceProjectId,
      params.forkRequest,
    );
  }
}

class ForkResult {
  final String newProjectId;
  final String sourceProjectId;
  final String sourceProjectTitle;
  final String sourceAuthorId;
  final String sourceAuthorName;
  final int copiedSystems;
  final int copiedBomItems;
  final int copiedImages;
  final DateTime forkedAt;

  ForkResult({
    required this.newProjectId,
    required this.sourceProjectId,
    required this.sourceProjectTitle,
    required this.sourceAuthorId,
    required this.sourceAuthorName,
    required this.copiedSystems,
    required this.copiedBomItems,
    required this.copiedImages,
    required this.forkedAt,
  });
}