import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/security/encryption_service.dart';

void main() {
  group('EncryptionService', () {
    const testKey = 'test_encryption_key';
    
    test('should encrypt and decrypt string correctly', () {
      // Arrange
      const plainText = 'This is a test string with sensitive information';
      
      // Act
      final encrypted = EncryptionService.encryptString(plainText, key: testKey);
      final decrypted = EncryptionService.decryptString(encrypted, key: testKey);
      
      // Assert
      expect(encrypted, isNot(equals(plainText)));
      expect(decrypted, equals(plainText));
    });
    
    test('should handle empty string', () {
      // Arrange
      const plainText = '';
      
      // Act
      final encrypted = EncryptionService.encryptString(plainText, key: testKey);
      final decrypted = EncryptionService.decryptString(encrypted, key: testKey);
      
      // Assert
      expect(encrypted, equals(plainText));
      expect(decrypted, equals(plainText));
    });
    
    test('should encrypt and decrypt price correctly', () {
      // Arrange
      const price = 1234.56;
      
      // Act
      final encrypted = EncryptionService.encryptPrice(price, key: testKey);
      final decrypted = EncryptionService.decryptPrice(encrypted, key: testKey);
      
      // Assert
      expect(encrypted, isNot(equals(price.toString())));
      expect(decrypted, equals(price));
    });
    
    test('should encrypt and decrypt sensitive fields in map', () {
      // Arrange
      final data = {
        'id': '123',
        'name': 'Test Item',
        'price': 99.99,
        'description': 'This is a sensitive description',
        'notes': 'These are sensitive notes',
        'category': 'test',
      };
      
      final sensitiveFields = ['price', 'description', 'notes'];
      
      // Act
      final encrypted = EncryptionService.encryptSensitiveFields(
        data,
        sensitiveFields,
        key: testKey,
      );
      
      final decrypted = EncryptionService.decryptSensitiveFields(
        encrypted,
        sensitiveFields,
        key: testKey,
      );
      
      // Assert
      expect(encrypted['id'], equals(data['id']));
      expect(encrypted['name'], equals(data['name']));
      expect(encrypted['category'], equals(data['category']));
      
      expect(encrypted['price'], isNot(equals(data['price'])));
      expect(encrypted['description'], isNot(equals(data['description'])));
      expect(encrypted['notes'], isNot(equals(data['notes'])));
      
      expect(decrypted['id'], equals(data['id']));
      expect(decrypted['name'], equals(data['name']));
      expect(decrypted['category'], equals(data['category']));
      expect(decrypted['price'], equals(data['price']));
      expect(decrypted['description'], equals(data['description']));
      expect(decrypted['notes'], equals(data['notes']));
    });
    
    test('should generate and verify hash correctly', () {
      // Arrange
      const data = 'Data to be hashed';
      
      // Act
      final hash = EncryptionService.generateHash(data);
      final isValid = EncryptionService.verifyHash(data, hash);
      final isInvalid = EncryptionService.verifyHash('Different data', hash);
      
      // Assert
      expect(hash, isNotEmpty);
      expect(isValid, isTrue);
      expect(isInvalid, isFalse);
    });
  });
}
