import '../entities/project_timeline.dart';

/// Project timeline service interface for managing project timelines, milestones, and tasks
abstract class ProjectTimelineService {
  // Timeline management
  Future<List<ProjectTimeline>> getProjectTimeline(String projectId);
  Future<ProjectTimeline> createTimelineItem(ProjectTimeline timeline);
  Future<ProjectTimeline> updateTimelineItem(ProjectTimeline timeline);
  Future<void> deleteTimelineItem(String timelineId);
  Future<void> reorderTimelineItems(String projectId, List<String> itemIds);

  // Milestone management
  Future<List<ProjectMilestone>> getProjectMilestones(String projectId);
  Future<ProjectMilestone> createMilestone(ProjectMilestone milestone);
  Future<ProjectMilestone> updateMilestone(ProjectMilestone milestone);
  Future<void> deleteMilestone(String milestoneId);
  Future<void> completeMilestone(String milestoneId, {String? notes});
  Future<List<ProjectMilestone>> getUpcomingMilestones(String projectId, {int days = 30});
  Future<List<ProjectMilestone>> getOverdueMilestones(String projectId);

  // Task management
  Future<List<ProjectTask>> getProjectTasks(String projectId, {String? milestoneId});
  Future<ProjectTask> createTask(ProjectTask task);
  Future<ProjectTask> updateTask(ProjectTask task);
  Future<void> deleteTask(String taskId);
  Future<void> completeTask(String taskId, {double? actualHours});
  Future<List<ProjectTask>> getTasksByStatus(String projectId, TaskStatus status);
  Future<List<ProjectTask>> getTasksByAssignee(String projectId, String assigneeId);
  Future<List<ProjectTask>> getOverdueTasks(String projectId);

  // Progress tracking
  Future<double> calculateProjectProgress(String projectId);
  Future<double> calculateMilestoneProgress(String milestoneId);
  Future<Map<String, dynamic>> getProjectStatistics(String projectId);
  Future<List<Map<String, dynamic>>> getProgressHistory(String projectId, {int days = 30});

  // Dependencies management
  Future<void> addTaskDependency(String taskId, String dependsOnTaskId);
  Future<void> removeTaskDependency(String taskId, String dependsOnTaskId);
  Future<List<ProjectTask>> getTaskDependencies(String taskId);
  Future<List<ProjectTask>> getDependentTasks(String taskId);
  Future<bool> validateTaskDependencies(String taskId);

  // Timeline analysis
  Future<Map<String, dynamic>> analyzeCriticalPath(String projectId);
  Future<List<Map<String, dynamic>>> getTimelineConflicts(String projectId);
  Future<Map<String, dynamic>> getResourceAllocation(String projectId);
  Future<DateTime?> estimateProjectCompletion(String projectId);

  // Reporting
  Future<Map<String, dynamic>> generateTimelineReport(String projectId);
  Future<Map<String, dynamic>> generateMilestoneReport(String projectId);
  Future<Map<String, dynamic>> generateTaskReport(String projectId);
  Future<List<Map<String, dynamic>>> getTeamPerformanceMetrics(String projectId);

  // Notifications and alerts
  Future<List<Map<String, dynamic>>> getUpcomingDeadlines(String projectId, {int days = 7});
  Future<List<Map<String, dynamic>>> getDelayedItems(String projectId);
  Future<void> sendMilestoneReminder(String milestoneId);
  Future<void> sendTaskReminder(String taskId);

  // Template management
  Future<void> saveAsTemplate(String projectId, String templateName);
  Future<void> applyTemplate(String projectId, String templateId);
  Future<List<Map<String, dynamic>>> getTimelineTemplates();
  Future<void> deleteTemplate(String templateId);

  // Collaboration features
  Future<void> assignTask(String taskId, String assigneeId);
  Future<void> unassignTask(String taskId);
  Future<List<Map<String, dynamic>>> getTaskComments(String taskId);
  Future<void> addTaskComment(String taskId, String comment, String userId);
  Future<void> mentionUser(String taskId, String userId, String message);

  // Time tracking
  Future<void> startTimeTracking(String taskId, String userId);
  Future<void> stopTimeTracking(String taskId, String userId);
  Future<double> getTrackedTime(String taskId, String userId);
  Future<Map<String, dynamic>> getTimeTrackingReport(String projectId, {DateTime? startDate, DateTime? endDate});

  // Integration features
  Future<void> syncWithCalendar(String projectId);
  Future<void> exportToGanttChart(String projectId);
  Future<void> importFromCSV(String projectId, String csvData);
  Future<String> exportToCSV(String projectId);
}
