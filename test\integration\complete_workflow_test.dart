import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vanhub/main.dart';
import 'package:vanhub/features/auth/presentation/providers/auth_provider.dart';
import 'package:vanhub/features/project/presentation/providers/project_provider.dart';
import 'package:vanhub/features/material/presentation/providers/material_provider.dart';
import 'package:vanhub/features/bom/presentation/providers/bom_provider.dart';
import 'package:vanhub/features/project/domain/entities/create_project_request.dart';
import 'package:vanhub/features/material/domain/entities/create_material_request.dart';

/// VanHub改装宝完整工作流程测试
/// 
/// 测试覆盖：
/// 1. 用户认证（游客模式 + 注册登录）
/// 2. 项目创建和管理
/// 3. 材料库管理
/// 4. BOM管理
/// 5. 智能联动功能
/// 6. 数据持久化验证
void main() {
  group('VanHub完整工作流程测试', () {
    late WidgetTester tester;
    late ProviderContainer container;

    setUpAll(() async {
      // 初始化测试环境
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      container = ProviderContainer();
    });

    tearDown(() async {
      container.dispose();
    });

    testWidgets('完整工作流程：从游客到项目完成', (WidgetTester widgetTester) async {
      tester = widgetTester;
      
      // 启动应用
      await tester.pumpWidget(
        ProviderScope(
          child: const VanHubApp(),
        ),
      );
      await tester.pumpAndSettle();

      // ==================== 第一阶段：用户认证测试 ====================
      await _testGuestMode();
      await _testUserRegistration();
      await _testUserLogin();

      // ==================== 第二阶段：项目管理测试 ====================
      final projectId = await _testProjectCreation();
      await _testProjectDetails(projectId);

      // ==================== 第三阶段：材料库管理测试 ====================
      final materialIds = await _testMaterialLibraryManagement();

      // ==================== 第四阶段：BOM管理测试 ====================
      await _testBomManagement(projectId, materialIds);

      // ==================== 第五阶段：智能联动测试 ====================
      await _testIntelligentIntegration(projectId, materialIds);

      // ==================== 第六阶段：数据验证测试 ====================
      await _testDataPersistence(projectId, materialIds);

      print('🎉 完整工作流程测试通过！');
    });
  });
}

/// 测试游客模式
Future<void> _testGuestMode() async {
  print('🔍 测试游客模式...');
  
  // 查找游客登录按钮
  final guestButton = find.text('以游客身份浏览');
  if (guestButton.evaluate().isNotEmpty) {
    await tester.tap(guestButton);
    await tester.pumpAndSettle();
    print('✅ 游客模式登录成功');
  }

  // 验证游客可以浏览公开内容
  expect(find.text('VanHub改装宝'), findsOneWidget);
  expect(find.text('首页'), findsOneWidget);
  expect(find.text('项目'), findsOneWidget);
  expect(find.text('材料'), findsOneWidget);
  
  print('✅ 游客模式界面验证通过');
}

/// 测试用户注册
Future<void> _testUserRegistration() async {
  print('🔍 测试用户注册...');
  
  // 查找注册按钮
  final registerButton = find.text('注册');
  if (registerButton.evaluate().isNotEmpty) {
    await tester.tap(registerButton);
    await tester.pumpAndSettle();

    // 填写注册表单
    await _fillRegistrationForm();
    
    // 提交注册
    final submitButton = find.text('创建账户');
    if (submitButton.evaluate().isNotEmpty) {
      await tester.tap(submitButton);
      await tester.pumpAndSettle();
      print('✅ 用户注册提交成功');
    }
  }
}

/// 测试用户登录
Future<void> _testUserLogin() async {
  print('🔍 测试用户登录...');
  
  // 查找登录按钮
  final loginButton = find.text('登录');
  if (loginButton.evaluate().isNotEmpty) {
    await tester.tap(loginButton);
    await tester.pumpAndSettle();

    // 填写登录表单
    await _fillLoginForm();
    
    // 提交登录
    final submitButton = find.text('登录');
    if (submitButton.evaluate().isNotEmpty) {
      await tester.tap(submitButton);
      await tester.pumpAndSettle();
      print('✅ 用户登录成功');
    }
  }
}

/// 测试项目创建
Future<String> _testProjectCreation() async {
  print('🔍 测试项目创建...');
  
  // 导航到项目页面
  final projectTab = find.text('项目');
  await tester.tap(projectTab);
  await tester.pumpAndSettle();

  // 点击创建项目按钮
  final createButton = find.text('创建项目');
  if (createButton.evaluate().isNotEmpty) {
    await tester.tap(createButton);
    await tester.pumpAndSettle();

    // 填写项目创建表单
    await _fillProjectCreationForm();
    
    // 提交项目创建
    final submitButton = find.text('创建');
    if (submitButton.evaluate().isNotEmpty) {
      await tester.tap(submitButton);
      await tester.pumpAndSettle();
      print('✅ 项目创建成功');
    }
  }

  // 返回模拟的项目ID
  return 'test_project_${DateTime.now().millisecondsSinceEpoch}';
}

/// 测试项目详情
Future<void> _testProjectDetails(String projectId) async {
  print('🔍 测试项目详情页面...');
  
  // 查找项目卡片并点击
  final projectCard = find.text('测试房车改装项目');
  if (projectCard.evaluate().isNotEmpty) {
    await tester.tap(projectCard);
    await tester.pumpAndSettle();

    // 验证项目详情页面元素
    expect(find.text('概览'), findsOneWidget);
    expect(find.text('BOM'), findsOneWidget);
    expect(find.text('材料库'), findsOneWidget);
    expect(find.text('改装日志'), findsOneWidget);
    
    print('✅ 项目详情页面验证通过');
  }
}

/// 测试材料库管理
Future<List<String>> _testMaterialLibraryManagement() async {
  print('🔍 测试材料库管理...');
  
  // 导航到材料库页面
  final materialTab = find.text('材料');
  await tester.tap(materialTab);
  await tester.pumpAndSettle();

  final materialIds = <String>[];

  // 测试添加多种材料
  final testMaterials = [
    {
      'name': '锂电池组',
      'category': '电力系统',
      'brand': '比亚迪',
      'price': '2500.00',
    },
    {
      'name': '逆变器',
      'category': '电力系统', 
      'brand': '正弦',
      'price': '800.00',
    },
    {
      'name': '水泵',
      'category': '水路系统',
      'brand': '舒弗洛',
      'price': '450.00',
    },
  ];

  for (final material in testMaterials) {
    final materialId = await _createTestMaterial(material);
    materialIds.add(materialId);
  }

  print('✅ 材料库管理测试完成，创建了${materialIds.length}个材料');
  return materialIds;
}

/// 测试BOM管理
Future<void> _testBomManagement(String projectId, List<String> materialIds) async {
  print('🔍 测试BOM管理...');
  
  // 导航到项目的BOM页面
  await _navigateToProjectBom(projectId);

  // 测试添加BOM项目
  for (int i = 0; i < materialIds.length; i++) {
    await _addMaterialToBom(materialIds[i], i + 1);
  }

  // 测试BOM状态流转
  await _testBomStatusFlow();

  // 测试BOM编辑和删除
  await _testBomEditAndDelete();

  print('✅ BOM管理测试完成');
}

/// 测试智能联动功能
Future<void> _testIntelligentIntegration(String projectId, List<String> materialIds) async {
  print('🔍 测试智能联动功能...');
  
  // 测试材料库到BOM的联动
  await _testMaterialToBomIntegration(materialIds.first);

  // 测试BOM到材料库的联动
  await _testBomToMaterialIntegration();

  // 测试成本自动计算
  await _testAutomaticCostCalculation();

  print('✅ 智能联动功能测试完成');
}

/// 测试数据持久化
Future<void> _testDataPersistence(String projectId, List<String> materialIds) async {
  print('🔍 测试数据持久化...');
  
  // 重启应用模拟
  await tester.pumpWidget(
    ProviderScope(
      child: const VanHubApp(),
    ),
  );
  await tester.pumpAndSettle();

  // 验证数据是否持久化
  await _verifyProjectPersistence(projectId);
  await _verifyMaterialPersistence(materialIds);
  await _verifyBomPersistence(projectId);

  print('✅ 数据持久化验证完成');
}

// ==================== 辅助方法 ====================

/// 填写注册表单
Future<void> _fillRegistrationForm() async {
  final emailField = find.byType(TextFormField).at(0);
  final passwordField = find.byType(TextFormField).at(1);
  final confirmPasswordField = find.byType(TextFormField).at(2);

  await tester.enterText(emailField, '<EMAIL>');
  await tester.enterText(passwordField, 'Test123456!');
  await tester.enterText(confirmPasswordField, 'Test123456!');
  await tester.pumpAndSettle();
}

/// 填写登录表单
Future<void> _fillLoginForm() async {
  final emailField = find.byType(TextFormField).at(0);
  final passwordField = find.byType(TextFormField).at(1);

  await tester.enterText(emailField, '<EMAIL>');
  await tester.enterText(passwordField, 'Test123456!');
  await tester.pumpAndSettle();
}

/// 填写项目创建表单
Future<void> _fillProjectCreationForm() async {
  // 项目名称
  final nameField = find.byKey(const Key('project_name_field'));
  if (nameField.evaluate().isNotEmpty) {
    await tester.enterText(nameField, '测试房车改装项目');
  }

  // 项目描述
  final descField = find.byKey(const Key('project_description_field'));
  if (descField.evaluate().isNotEmpty) {
    await tester.enterText(descField, '这是一个完整的房车改装测试项目，包含电力系统、水路系统等多个改装模块。');
  }

  // 预算设置
  final budgetField = find.byKey(const Key('project_budget_field'));
  if (budgetField.evaluate().isNotEmpty) {
    await tester.enterText(budgetField, '50000');
  }

  await tester.pumpAndSettle();
}

/// 创建测试材料
Future<String> _createTestMaterial(Map<String, String> materialData) async {
  // 点击添加材料按钮
  final addButton = find.text('添加材料');
  if (addButton.evaluate().isNotEmpty) {
    await tester.tap(addButton);
    await tester.pumpAndSettle();

    // 填写材料信息
    await _fillMaterialForm(materialData);

    // 提交创建
    final submitButton = find.text('保存');
    if (submitButton.evaluate().isNotEmpty) {
      await tester.tap(submitButton);
      await tester.pumpAndSettle();
    }
  }

  return 'material_${DateTime.now().millisecondsSinceEpoch}';
}

/// 填写材料表单
Future<void> _fillMaterialForm(Map<String, String> data) async {
  // 材料名称
  final nameField = find.byKey(const Key('material_name_field'));
  if (nameField.evaluate().isNotEmpty) {
    await tester.enterText(nameField, data['name']!);
  }

  // 品牌
  final brandField = find.byKey(const Key('material_brand_field'));
  if (brandField.evaluate().isNotEmpty) {
    await tester.enterText(brandField, data['brand']!);
  }

  // 价格
  final priceField = find.byKey(const Key('material_price_field'));
  if (priceField.evaluate().isNotEmpty) {
    await tester.enterText(priceField, data['price']!);
  }

  await tester.pumpAndSettle();
}

/// 导航到项目BOM页面
Future<void> _navigateToProjectBom(String projectId) async {
  // 点击BOM标签
  final bomTab = find.text('BOM');
  if (bomTab.evaluate().isNotEmpty) {
    await tester.tap(bomTab);
    await tester.pumpAndSettle();
  }
}

/// 添加材料到BOM
Future<void> _addMaterialToBom(String materialId, int quantity) async {
  // 点击添加到BOM按钮
  final addToBomButton = find.text('添加到BOM');
  if (addToBomButton.evaluate().isNotEmpty) {
    await tester.tap(addToBomButton);
    await tester.pumpAndSettle();

    // 设置数量
    final quantityField = find.byKey(const Key('bom_quantity_field'));
    if (quantityField.evaluate().isNotEmpty) {
      await tester.enterText(quantityField, quantity.toString());
    }

    // 确认添加
    final confirmButton = find.text('确认');
    if (confirmButton.evaluate().isNotEmpty) {
      await tester.tap(confirmButton);
      await tester.pumpAndSettle();
    }
  }
}

/// 测试BOM状态流转
Future<void> _testBomStatusFlow() async {
  // 测试状态变更：待采购 -> 已下单 -> 已收货 -> 已安装
  final statuses = ['已下单', '已收货', '已安装'];
  
  for (final status in statuses) {
    final statusButton = find.text(status);
    if (statusButton.evaluate().isNotEmpty) {
      await tester.tap(statusButton);
      await tester.pumpAndSettle();
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }
}

/// 测试BOM编辑和删除
Future<void> _testBomEditAndDelete() async {
  // 测试编辑功能
  final editButton = find.byIcon(Icons.edit);
  if (editButton.evaluate().isNotEmpty) {
    await tester.tap(editButton.first);
    await tester.pumpAndSettle();
    
    // 修改数量
    final quantityField = find.byKey(const Key('edit_quantity_field'));
    if (quantityField.evaluate().isNotEmpty) {
      await tester.enterText(quantityField, '3');
    }
    
    // 保存修改
    final saveButton = find.text('保存');
    if (saveButton.evaluate().isNotEmpty) {
      await tester.tap(saveButton);
      await tester.pumpAndSettle();
    }
  }
}

/// 测试材料库到BOM的联动
Future<void> _testMaterialToBomIntegration(String materialId) async {
  // 在材料库页面点击"添加到BOM"
  final addToBomButton = find.text('添加到BOM');
  if (addToBomButton.evaluate().isNotEmpty) {
    await tester.tap(addToBomButton.first);
    await tester.pumpAndSettle();
    
    // 验证材料信息自动填充
    expect(find.text('锂电池组'), findsOneWidget);
    expect(find.text('比亚迪'), findsOneWidget);
  }
}

/// 测试BOM到材料库的联动
Future<void> _testBomToMaterialIntegration() async {
  // 在BOM页面点击"保存到材料库"
  final saveToLibraryButton = find.text('保存到材料库');
  if (saveToLibraryButton.evaluate().isNotEmpty) {
    await tester.tap(saveToLibraryButton.first);
    await tester.pumpAndSettle();
    
    // 验证材料自动创建
    expect(find.text('材料已保存到库'), findsOneWidget);
  }
}

/// 测试自动成本计算
Future<void> _testAutomaticCostCalculation() async {
  // 验证总成本显示
  expect(find.textContaining('总成本'), findsOneWidget);
  expect(find.textContaining('¥'), findsAtLeastNWidgets(1));
}

/// 验证项目持久化
Future<void> _verifyProjectPersistence(String projectId) async {
  // 导航到项目页面
  final projectTab = find.text('项目');
  await tester.tap(projectTab);
  await tester.pumpAndSettle();
  
  // 验证项目仍然存在
  expect(find.text('测试房车改装项目'), findsOneWidget);
}

/// 验证材料持久化
Future<void> _verifyMaterialPersistence(List<String> materialIds) async {
  // 导航到材料库页面
  final materialTab = find.text('材料');
  await tester.tap(materialTab);
  await tester.pumpAndSettle();
  
  // 验证材料仍然存在
  expect(find.text('锂电池组'), findsOneWidget);
  expect(find.text('逆变器'), findsOneWidget);
  expect(find.text('水泵'), findsOneWidget);
}

/// 验证BOM持久化
Future<void> _verifyBomPersistence(String projectId) async {
  // 导航到项目BOM页面
  await _navigateToProjectBom(projectId);
  
  // 验证BOM项目仍然存在
  expect(find.text('锂电池组'), findsAtLeastNWidgets(1));
  expect(find.text('已安装'), findsAtLeastNWidgets(1));
}
