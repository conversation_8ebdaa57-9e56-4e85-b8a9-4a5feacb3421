<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VanHub评论功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .review-summary {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        .review-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: white;
        }
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
        }
        .stars {
            color: #ffc107;
            margin-right: 8px;
        }
        .verified {
            background: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 8px;
        }
        .review-content {
            margin: 12px 0;
            line-height: 1.5;
        }
        .tags {
            display: flex;
            gap: 8px;
            margin: 12px 0;
        }
        .tag {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .pros-cons {
            margin: 12px 0;
        }
        .pros-cons h4 {
            margin: 8px 0 4px 0;
            font-size: 14px;
        }
        .pros {
            color: #28a745;
        }
        .cons {
            color: #fd7e14;
        }
        .actions {
            display: flex;
            gap: 12px;
            margin-top: 12px;
        }
        .btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        .btn:hover {
            background: #f8f9fa;
        }
        .btn-primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .write-review-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .dialog-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .rating-input {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }
        .star-input {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
        }
        .star-input.active {
            color: #ffc107;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 16px;
            border-radius: 4px;
            display: none;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗣️ VanHub专业评价系统</h1>
            <p>帮助用户基于真实使用经验选择合适的房车改装材料</p>
        </div>

        <!-- 评价摘要 -->
        <div class="review-summary" id="reviewSummary">
            <h2>用户评价</h2>
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <div class="stars">★★★★☆</div>
                    <span>4.3分 (24条评价)</span>
                </div>
                <div>
                    <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px;">
                        推荐度 86%
                    </span>
                </div>
            </div>
            <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="showWriteReviewDialog()">写评价</button>
                <button class="btn" onclick="showAllReviews()">查看全部评价</button>
            </div>
        </div>

        <!-- 评价列表 -->
        <div id="reviewsList">
            <h3>用户评价列表</h3>
            
            <!-- 评价卡片1 -->
            <div class="review-card" data-testid="review-card-1">
                <div class="user-info">
                    <div class="avatar">王</div>
                    <div>
                        <div style="display: flex; align-items: center;">
                            <strong>改装达人小王</strong>
                            <span class="verified">已验证购买</span>
                        </div>
                        <div>
                            <span class="stars">★★★★★</span>
                            <span>4.5</span>
                            <span style="margin-left: 16px; color: #666; font-size: 12px;">2024-01-20</span>
                        </div>
                    </div>
                </div>
                <div class="review-content">
                    这个材料真的很不错！质量很好，安装也比较简单。我用在了我的依维柯房车上，效果很满意。特别是防水性能，经过几次大雨测试都没有问题。
                </div>
                <div class="tags">
                    <span class="tag">车型: 依维柯Daily</span>
                    <span class="tag">系统: 电气系统</span>
                    <span class="tag">使用时长: 使用3个月</span>
                </div>
                <div class="pros-cons">
                    <h4 class="pros">优点</h4>
                    <ul style="margin: 4px 0; padding-left: 20px;">
                        <li>质量优秀</li>
                        <li>防水性能好</li>
                        <li>安装简单</li>
                    </ul>
                    <h4 class="cons">缺点</h4>
                    <ul style="margin: 4px 0; padding-left: 20px;">
                        <li>价格稍高</li>
                    </ul>
                </div>
                <div class="actions">
                    <button class="btn" onclick="likeReview(1)" data-testid="like-btn-1">👍 点赞 (2)</button>
                    <button class="btn" onclick="markHelpful(1)" data-testid="helpful-btn-1">✓ 有用 (3)</button>
                    <button class="btn" onclick="reportReview(1)" data-testid="report-btn-1">举报</button>
                </div>
            </div>

            <!-- 评价卡片2 -->
            <div class="review-card" data-testid="review-card-2">
                <div class="user-info">
                    <div class="avatar">新</div>
                    <div>
                        <div style="display: flex; align-items: center;">
                            <strong>房车新手</strong>
                            <span class="verified">已验证购买</span>
                        </div>
                        <div>
                            <span class="stars">★★★★☆</span>
                            <span>4.0</span>
                            <span style="margin-left: 16px; color: #666; font-size: 12px;">2024-01-18</span>
                        </div>
                    </div>
                </div>
                <div class="review-content">
                    作为新手，这个材料对我来说安装有点困难，但是质量确实不错。客服很耐心地指导了我安装过程。
                </div>
                <div class="tags">
                    <span class="tag">车型: 福特全顺</span>
                    <span class="tag">系统: 水路系统</span>
                    <span class="tag">使用时长: 使用1个月</span>
                </div>
                <div class="pros-cons">
                    <h4 class="pros">优点</h4>
                    <ul style="margin: 4px 0; padding-left: 20px;">
                        <li>质量不错</li>
                        <li>客服服务好</li>
                    </ul>
                    <h4 class="cons">缺点</h4>
                    <ul style="margin: 4px 0; padding-left: 20px;">
                        <li>安装有难度</li>
                        <li>说明书不够详细</li>
                    </ul>
                </div>
                <div class="actions">
                    <button class="btn" onclick="likeReview(2)" data-testid="like-btn-2">👍 点赞 (1)</button>
                    <button class="btn" onclick="markHelpful(2)" data-testid="helpful-btn-2">✓ 有用 (2)</button>
                    <button class="btn" onclick="reportReview(2)" data-testid="report-btn-2">举报</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 写评价对话框 -->
    <div class="write-review-dialog" id="writeReviewDialog">
        <div class="dialog-content">
            <h3>写评价 - 演示材料</h3>
            <form id="reviewForm">
                <div class="form-group">
                    <label>总体评分</label>
                    <div class="rating-input" data-testid="overall-rating">
                        <span class="star-input" data-rating="1">★</span>
                        <span class="star-input" data-rating="2">★</span>
                        <span class="star-input" data-rating="3">★</span>
                        <span class="star-input" data-rating="4">★</span>
                        <span class="star-input" data-rating="5">★</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="reviewContent">评价内容</label>
                    <textarea id="reviewContent" class="form-control" rows="4" 
                              placeholder="请分享您的使用体验，包括材料的表现、安装过程、使用感受等..."
                              data-testid="review-content"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="vehicleType">车型</label>
                    <input type="text" id="vehicleType" class="form-control" 
                           placeholder="如：依维柯Daily" data-testid="vehicle-type">
                </div>
                
                <div class="form-group">
                    <label for="systemType">改装系统</label>
                    <input type="text" id="systemType" class="form-control" 
                           placeholder="如：电气系统" data-testid="system-type">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="verifiedPurchase" data-testid="verified-purchase">
                        我已购买并使用过此材料
                    </label>
                </div>
                
                <div style="display: flex; gap: 12px; margin-top: 24px;">
                    <button type="button" class="btn" onclick="closeWriteReviewDialog()" data-testid="cancel-btn">取消</button>
                    <button type="submit" class="btn btn-primary" data-testid="submit-btn">发布评价</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 通知 -->
    <div class="notification" id="notification"></div>

    <script>
        let currentRating = 5;

        // 星级评分交互
        document.querySelectorAll('.star-input').forEach(star => {
            star.addEventListener('click', function() {
                currentRating = parseInt(this.dataset.rating);
                updateStarDisplay();
            });
        });

        function updateStarDisplay() {
            document.querySelectorAll('.star-input').forEach((star, index) => {
                if (index < currentRating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // 显示写评价对话框
        function showWriteReviewDialog() {
            document.getElementById('writeReviewDialog').style.display = 'flex';
            updateStarDisplay();
        }

        // 关闭写评价对话框
        function closeWriteReviewDialog() {
            document.getElementById('writeReviewDialog').style.display = 'none';
        }

        // 提交评价
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const content = document.getElementById('reviewContent').value;
            const vehicleType = document.getElementById('vehicleType').value;
            const systemType = document.getElementById('systemType').value;
            const verified = document.getElementById('verifiedPurchase').checked;
            
            if (!content.trim()) {
                showNotification('请输入评价内容', 'error');
                return;
            }
            
            // 模拟提交
            setTimeout(() => {
                closeWriteReviewDialog();
                showNotification('评价发布成功！');
                
                // 重置表单
                document.getElementById('reviewForm').reset();
                currentRating = 5;
                updateStarDisplay();
            }, 1000);
        });

        // 点赞功能
        function likeReview(reviewId) {
            showNotification(`点赞评价 ${reviewId}`);
        }

        // 标记有用
        function markHelpful(reviewId) {
            showNotification(`标记评价 ${reviewId} 为有用`);
        }

        // 举报评价
        function reportReview(reviewId) {
            if (confirm('确定要举报这条评价吗？')) {
                showNotification(`举报评价 ${reviewId} 已提交`);
            }
        }

        // 查看全部评价
        function showAllReviews() {
            showNotification('导航到评价列表页面');
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.backgroundColor = type === 'error' ? '#dc3545' : '#28a745';
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // 初始化星级显示
        updateStarDisplay();

        // 点击对话框外部关闭
        document.getElementById('writeReviewDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                closeWriteReviewDialog();
            }
        });
    </script>
</body>
</html>
