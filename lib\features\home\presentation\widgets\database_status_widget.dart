import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/database_validator.dart';

/// 数据库连接状态组件
class DatabaseStatusWidget extends ConsumerStatefulWidget {
  const DatabaseStatusWidget({super.key});

  @override
  ConsumerState<DatabaseStatusWidget> createState() => _DatabaseStatusWidgetState();
}

class _DatabaseStatusWidgetState extends ConsumerState<DatabaseStatusWidget> {
  ValidationResult? _validationResult;
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _checkConnectionStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: _getStatusColor(),
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '数据库连接状态',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  IconButton(
                    onPressed: _checkConnectionStatus,
                    icon: const Icon(Icons.refresh, size: 16),
                    tooltip: '刷新状态',
                  ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 状态指示器
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getStatusColor().withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getStatusText(),
                    style: TextStyle(
                      color: _getStatusColor(),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            if (_validationResult != null) ...[
              const SizedBox(height: 12),
              Text(
                _validationResult!.summary,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              
              if (_validationResult!.passedCount > 0) ...[
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: _validationResult!.passRate,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_validationResult!.passedCount}/${_validationResult!.totalCount} 项检查通过',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ],
            
            // 错误信息
            if (_validationResult?.errors.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '发现的问题:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    ...(_validationResult!.errors.take(3).map((error) => 
                      Padding(
                        padding: const EdgeInsets.only(left: 8, top: 2),
                        child: Text(
                          '• $error',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.red.shade700,
                          ),
                        ),
                      ),
                    )),
                    if (_validationResult!.errors.length > 3)
                      Padding(
                        padding: const EdgeInsets.only(left: 8, top: 2),
                        child: Text(
                          '... 还有 ${_validationResult!.errors.length - 3} 个问题',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.red.shade600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    if (_isLoading) return Colors.blue;
    if (_validationResult == null) return Colors.grey;
    if (_validationResult!.isHealthy) return Colors.green;
    if (_validationResult!.success) return Colors.orange;
    return Colors.red;
  }

  String _getStatusText() {
    if (_isLoading) return '检查中...';
    if (_validationResult == null) return '未知';
    if (_validationResult!.isHealthy) return '连接正常';
    if (_validationResult!.success) return '部分异常';
    return '连接失败';
  }

  Future<void> _checkConnectionStatus() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await DatabaseValidator.validateConnection();
      if (mounted) {
        setState(() {
          _validationResult = result;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _validationResult = ValidationResult(
            success: false,
            results: {},
            errors: ['验证过程异常: $e'],
            summary: '验证失败',
          );
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}