import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../providers/material_favorite_provider.dart';
import '../widgets/enhanced_material_card_widget.dart';
import '../widgets/create_material_dialog_widget.dart';
import '../widgets/edit_material_dialog_widget.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 增强版材料库页面
/// 现代化设计，优秀的用户体验和视觉效果
class EnhancedMaterialLibraryPage extends ConsumerStatefulWidget {
  const EnhancedMaterialLibraryPage({super.key});

  @override
  ConsumerState<EnhancedMaterialLibraryPage> createState() => _EnhancedMaterialLibraryPageState();
}

class _EnhancedMaterialLibraryPageState extends ConsumerState<EnhancedMaterialLibraryPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  String _searchQuery = '';
  String _selectedCategory = '全部';
  bool _showFavoritesOnly = false;
  bool _isGridView = true;

  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '安全设备', 
    '通讯设备', '娱乐设备'
  ];

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // 启动入场动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final materialsAsync = ref.watch(userMaterialsProvider());

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildEnhancedAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // 搜索和筛选栏
              _buildSearchAndFilterBar(),
              
              // 分类筛选
              _buildCategoryFilter(),
              
              // 材料列表
              Expanded(
                child: materialsAsync.when(
                  data: (materials) => _buildMaterialList(materials),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('加载材料失败: $error'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildEnhancedAppBar() {
    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.inventory,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            '材料库',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
      backgroundColor: VanHubColors.primary,
      elevation: 0,
      actions: [
        // 视图切换按钮
        IconButton(
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
          icon: Icon(
            _isGridView ? Icons.view_list : Icons.grid_view,
            color: Colors.white,
          ),
          tooltip: _isGridView ? '列表视图' : '网格视图',
        ),
        
        // 收藏筛选按钮
        IconButton(
          onPressed: () {
            setState(() {
              _showFavoritesOnly = !_showFavoritesOnly;
            });
          },
          icon: Icon(
            _showFavoritesOnly ? Icons.favorite : Icons.favorite_border,
            color: _showFavoritesOnly ? Colors.red[300] : Colors.white,
          ),
          tooltip: _showFavoritesOnly ? '显示全部' : '只看收藏',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: VanHubColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: '搜索材料名称、品牌、型号...',
          hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
          prefixIcon: Icon(
            Icons.search,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: Icon(
                    Icons.clear,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white.withValues(alpha: 0.2),
        ),
        onChanged: (query) {
          setState(() {
            _searchQuery = query;
          });
        },
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: Colors.white,
              selectedColor: VanHubColors.primary.withValues(alpha: 0.1),
              checkmarkColor: VanHubColors.primary,
              labelStyle: TextStyle(
                color: isSelected ? VanHubColors.primary : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected ? VanHubColors.primary : Colors.grey[300]!,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMaterialList(List<domain.Material> materials) {
    return Consumer(
      builder: (context, ref, child) {
        // 获取收藏列表
        final favoritesAsync = ref.watch(userFavoritesProvider);

        return favoritesAsync.when(
          data: (favorites) {
            final favoriteIds = favorites.map((f) => f.materialId).toSet();

            // 过滤材料
            final filteredMaterials = materials.where((material) {
              final matchesSearch = _searchQuery.isEmpty ||
                  material.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                  (material.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                  (material.model?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

              final matchesCategory = _selectedCategory == '全部' ||
                  material.category == _selectedCategory;

              final matchesFavorites = !_showFavoritesOnly || favoriteIds.contains(material.id);

              return matchesSearch && matchesCategory && matchesFavorites;
            }).toList();

            return _buildFilteredMaterialList(filteredMaterials);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildFilteredMaterialList(materials.where((material) {
            final matchesSearch = _searchQuery.isEmpty ||
                material.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                (material.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                (material.model?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

            final matchesCategory = _selectedCategory == '全部' ||
                material.category == _selectedCategory;

            return matchesSearch && matchesCategory;
          }).toList()),
        );
      },
    );
  }

  Widget _buildFilteredMaterialList(List<domain.Material> filteredMaterials) {

    if (filteredMaterials.isEmpty) {
      return _buildEmptyState();
    }

    if (_isGridView) {
      return _buildGridView(filteredMaterials);
    } else {
      return _buildListView(filteredMaterials);
    }
  }

  Widget _buildGridView(List<domain.Material> materials) {
    return Scrollbar(
      controller: _scrollController,
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: materials.length,
        itemBuilder: (context, index) {
          final material = materials[index];
          return EnhancedMaterialCardWidget(
            material: material,
            onTap: () => _showMaterialDetails(material),
            onEdit: () => _showEditMaterialDialog(material),
            onDelete: () => _deleteMaterial(material.id),
            onAddToBom: () => _showAddToBomDialog(material),
            onFavorite: () => _toggleFavorite(material.id),
            showActions: true,
            isCompact: false,
          );
        },
      ),
    );
  }

  Widget _buildListView(List<domain.Material> materials) {
    return Scrollbar(
      controller: _scrollController,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        itemCount: materials.length,
        itemBuilder: (context, index) {
          final material = materials[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: EnhancedMaterialCardWidget(
              material: material,
              onTap: () => _showMaterialDetails(material),
              onEdit: () => _showEditMaterialDialog(material),
              onDelete: () => _deleteMaterial(material.id),
              onAddToBom: () => _showAddToBomDialog(material),
              onFavorite: () => _toggleFavorite(material.id),
              showActions: true,
              isCompact: true,
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != '全部' || _showFavoritesOnly
                ? '没有找到匹配的材料'
                : '材料库为空',
            style: VanHubTypography.headlineSmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != '全部' || _showFavoritesOnly
                ? '尝试调整搜索条件或筛选器'
                : '点击右下角的按钮添加第一个材料',
            style: VanHubTypography.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _showCreateMaterialDialog,
      icon: const Icon(Icons.add),
      label: const Text('添加材料'),
      backgroundColor: VanHubColors.primary,
      foregroundColor: Colors.white,
    );
  }

  // 事件处理方法
  void _showMaterialDetails(domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(material.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('分类: ${material.category}'),
            Text('价格: ¥${material.price.toStringAsFixed(2)}'),
            if (material.description.isNotEmpty)
              Text('描述: ${material.description}'),
            Text('使用次数: ${material.usageCount}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showCreateMaterialDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateMaterialDialogWidget(),
    );
  }

  void _showEditMaterialDialog(domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => EditMaterialDialogWidget(material: material),
    );
  }

  void _deleteMaterial(String materialId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个材料吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final result = await ref.read(materialControllerProvider.notifier)
                  .deleteMaterial(materialId);
              
              if (mounted) {
                result.fold(
                  (failure) => ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('删除失败: ${failure.message}'),
                      backgroundColor: Colors.red,
                    ),
                  ),
                  (_) => ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('材料删除成功'),
                      backgroundColor: Colors.green,
                    ),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showAddToBomDialog(domain.Material material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('将 ${material.name} 添加到BOM功能开发中...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _toggleFavorite(String materialId) {
    // 收藏功能已在EnhancedMaterialCardWidget中实现
    // 这里只是一个回调，实际逻辑在卡片组件中处理
  }
}
