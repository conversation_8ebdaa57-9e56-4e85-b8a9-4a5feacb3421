import 'package:fpdart/fpdart.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/material_favorite.dart';
import '../../domain/repositories/material_favorite_repository.dart';
import '../models/material_favorite_model.dart';

/// 材料收藏Repository实现
/// 基于Supabase的材料收藏功能数据访问实现
class MaterialFavoriteRepositoryImpl implements MaterialFavoriteRepository {
  final SupabaseClient supabaseClient;

  const MaterialFavoriteRepositoryImpl({
    required this.supabaseClient,
  });

  @override
  Future<Either<Failure, MaterialFavorite>> addToFavorites({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  }) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      // 检查是否已经收藏
      final existingResult = await getFavoriteByMaterialId(materialId);
      if (existingResult.isRight()) {
        final existing = existingResult.getOrElse((failure) => null);
        if (existing != null) {
          return Left(ValidationFailure(message: '该材料已在收藏列表中'));
        }
      }

      final now = DateTime.now();
      final favoriteData = {
        'user_id': userId,
        'material_id': materialId,
        'tags': tags,
        'notes': notes,
        'category': category,
        'priority': priority.clamp(1, 5),
        'is_pinned': false,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      final response = await supabaseClient
          .from('material_favorites')
          .insert(favoriteData)
          .select()
          .single();

      final favoriteModel = MaterialFavoriteModel.fromJson(response);
      return Right(favoriteModel.toDomain());
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '添加收藏失败: ${e.message}'));
    } on AuthException catch (e) {
      return Left(AuthFailure(message: '认证失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '添加收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromFavorites(String materialId) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      await supabaseClient
          .from('material_favorites')
          .delete()
          .eq('user_id', userId)
          .eq('material_id', materialId);

      return const Right(null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '移除收藏失败: ${e.message}'));
    } on AuthException catch (e) {
      return Left(AuthFailure(message: '认证失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '移除收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isFavorited(String materialId) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return const Right(false);
      }

      final response = await supabaseClient
          .from('material_favorites')
          .select('id')
          .eq('user_id', userId)
          .eq('material_id', materialId)
          .maybeSingle();

      return Right(response != null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '检查收藏状态失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '检查收藏状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getUserFavorites({
    int? limit,
    int? offset,
    String? sortBy,
    Map<String, dynamic>? filterBy,
  }) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      var queryBuilder = supabaseClient
          .from('material_favorites')
          .select()
          .eq('user_id', userId);

      // 应用筛选条件
      if (filterBy != null) {
        filterBy.forEach((key, value) {
          if (value != null) {
            queryBuilder = queryBuilder.eq(key, value);
          }
        });
      }

      // 应用排序
      final orderBy = sortBy ?? 'created_at';
      var orderedQuery = queryBuilder.order(orderBy, ascending: false);

      // 应用分页
      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }
      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await orderedQuery;
      final favorites = response
          .map((json) => MaterialFavoriteModel.fromJson(json).toDomain())
          .toList();

      return Right(favorites);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取收藏列表失败: ${e.message}'));
    } on AuthException catch (e) {
      return Left(AuthFailure(message: '认证失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取收藏列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialFavorite>> getFavoriteById(String favoriteId) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      final response = await supabaseClient
          .from('material_favorites')
          .select()
          .eq('id', favoriteId)
          .eq('user_id', userId)
          .single();

      final favoriteModel = MaterialFavoriteModel.fromJson(response);
      return Right(favoriteModel.toDomain());
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: '收藏记录不存在'));
      }
      return Left(ServerFailure(message: '获取收藏详情失败: ${e.message}'));
    } on AuthException catch (e) {
      return Left(AuthFailure(message: '认证失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取收藏详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialFavorite?>> getFavoriteByMaterialId(String materialId) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return const Right(null);
      }

      final response = await supabaseClient
          .from('material_favorites')
          .select()
          .eq('user_id', userId)
          .eq('material_id', materialId)
          .maybeSingle();

      if (response == null) {
        return const Right(null);
      }

      final favoriteModel = MaterialFavoriteModel.fromJson(response);
      return Right(favoriteModel.toDomain());
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取收藏记录失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取收藏记录失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialFavorite>> updateFavorite(
    String favoriteId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      final updateData = {
        ...updates,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('material_favorites')
          .update(updateData)
          .eq('id', favoriteId)
          .eq('user_id', userId)
          .select()
          .single();

      final favoriteModel = MaterialFavoriteModel.fromJson(response);
      return Right(favoriteModel.toDomain());
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '更新收藏失败: ${e.message}'));
    } on AuthException catch (e) {
      return Left(AuthFailure(message: '认证失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '更新收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> toggleFavorite({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  }) async {
    try {
      final isFavoritedResult = await isFavorited(materialId);
      if (isFavoritedResult.isLeft()) {
        return Left(isFavoritedResult.fold((l) => l, (r) => UnknownFailure(message: '检查收藏状态失败')));
      }

      final isCurrentlyFavorited = isFavoritedResult.getOrElse((failure) => false);

      if (isCurrentlyFavorited) {
        // 移除收藏
        final removeResult = await removeFromFavorites(materialId);
        if (removeResult.isLeft()) {
          return Left(removeResult.fold((l) => l, (r) => UnknownFailure(message: '移除收藏失败')));
        }
        return const Right(false);
      } else {
        // 添加收藏
        final addResult = await addToFavorites(
          materialId: materialId,
          tags: tags,
          notes: notes,
          category: category,
          priority: priority,
        );
        if (addResult.isLeft()) {
          return Left(addResult.fold((l) => l, (r) => UnknownFailure(message: '添加收藏失败')));
        }
        return const Right(true);
      }
    } catch (e) {
      return Left(UnknownFailure(message: '切换收藏状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, FavoriteStats>> getFavoriteStats() async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      final favoritesResult = await getUserFavorites();
      if (favoritesResult.isLeft()) {
        return Left(favoritesResult.fold((l) => l, (r) => UnknownFailure(message: '获取收藏列表失败')));
      }

      final favorites = favoritesResult.getOrElse((failure) => <MaterialFavorite>[]);
      final stats = FavoriteStats.fromFavorites(favorites);
      return Right(stats);
    } catch (e) {
      return Left(UnknownFailure(message: '获取收藏统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByCategory(
    String category, {
    int? limit,
  }) async {
    return getUserFavorites(
      filterBy: {'category': category},
      limit: limit,
    );
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByTag(
    String tag, {
    int? limit,
  }) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      var query = supabaseClient
          .from('material_favorites')
          .select()
          .eq('user_id', userId)
          .contains('tags', [tag])
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      final favorites = response
          .map((json) => MaterialFavoriteModel.fromJson(json).toDomain())
          .toList();

      return Right(favorites);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '按标签获取收藏失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '按标签获取收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByPriority(
    int priority, {
    int? limit,
  }) async {
    return getUserFavorites(
      filterBy: {'priority': priority},
      limit: limit,
    );
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getPinnedFavorites({
    int? limit,
  }) async {
    return getUserFavorites(
      filterBy: {'is_pinned': true},
      limit: limit,
    );
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> getRecentFavorites({
    int days = 7,
    int? limit,
  }) async {
    try {
      final userId = supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        return Left(AuthFailure(message: '用户未登录'));
      }

      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      
      var query = supabaseClient
          .from('material_favorites')
          .select()
          .eq('user_id', userId)
          .gte('created_at', cutoffDate.toIso8601String())
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      final favorites = response
          .map((json) => MaterialFavoriteModel.fromJson(json).toDomain())
          .toList();

      return Right(favorites);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取最近收藏失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取最近收藏失败: $e'));
    }
  }

  // 其他方法的实现...
  @override
  Future<Either<Failure, int>> batchUpdateFavorites(
    List<String> favoriteIds,
    Map<String, dynamic> updates,
  ) async {
    try {
      int updatedCount = 0;

      for (final favoriteId in favoriteIds) {
        final response = await supabaseClient
            .from('material_favorites')
            .update({
              ...updates,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', favoriteId)
            .select('id');

        if (response.isNotEmpty) {
          updatedCount++;
        }
      }

      return Right(updatedCount);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '批量更新收藏失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '批量更新收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialFavorite>>> searchFavorites({
    required String query,
    List<String>? searchIn,
    int? limit,
  }) async {
    try {
      var queryBuilder = supabaseClient
          .from('material_favorites')
          .select('''
            *,
            materials!inner(*)
          ''');

      // 构建搜索条件
      if (searchIn?.contains('name') == true) {
        queryBuilder = queryBuilder.ilike('materials.name', '%$query%');
      }

      if (searchIn?.contains('notes') == true) {
        queryBuilder = queryBuilder.or('notes.ilike.%$query%');
      }

      if (searchIn?.contains('tags') == true) {
        queryBuilder = queryBuilder.contains('tags', [query]);
      }

      // 应用排序和限制
      var orderedQuery = queryBuilder.order('created_at', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      final favorites = (response as List)
          .map((json) => MaterialFavoriteModel.fromJson(json).toDomain())
          .toList();

      return Right(favorites);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '搜索收藏失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '搜索收藏失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAllTags() async {
    try {
      final response = await supabaseClient
          .from('material_favorites')
          .select('tags')
          .not('tags', 'is', null);

      final Set<String> allTags = {};

      for (final row in response) {
        final tags = row['tags'] as List<dynamic>?;
        if (tags != null) {
          allTags.addAll(tags.cast<String>());
        }
      }

      return Right(allTags.toList()..sort());
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取所有标签失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取所有标签失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAllCategories() async {
    try {
      // 从材料收藏中获取所有分类
      final favoritesResponse = await supabaseClient
          .from('material_favorites')
          .select('''
            materials!inner(category)
          ''')
          .not('materials.category', 'is', null);

      // 从材料库中获取所有分类
      final materialsResponse = await supabaseClient
          .from('material_library')
          .select('category')
          .not('category', 'is', null);

      final Set<String> allCategories = {};

      // 添加收藏材料的分类
      for (final row in favoritesResponse) {
        final material = row['materials'];
        if (material != null && material['category'] != null) {
          allCategories.add(material['category'].toString());
        }
      }

      // 添加材料库的分类
      for (final row in materialsResponse) {
        final category = row['category'];
        if (category != null) {
          allCategories.add(category.toString());
        }
      }

      return Right(allCategories.toList()..sort());
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取所有分类失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取所有分类失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> clearAllFavorites() async {
    // TODO: 实现清空收藏
    return const Right(0);
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> exportFavorites({
    String format = 'json',
    bool includeMetadata = true,
  }) async {
    // TODO: 实现导出功能
    return const Right({});
  }

  @override
  Future<Either<Failure, int>> importFavorites({
    required Map<String, dynamic> data,
    String format = 'json',
    String mergeStrategy = 'merge',
  }) async {
    // TODO: 实现导入功能
    return const Right(0);
  }
}
