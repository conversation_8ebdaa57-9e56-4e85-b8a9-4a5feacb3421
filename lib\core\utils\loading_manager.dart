import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// 加载状态枚举
enum LoadingState {
  idle,
  loading,
  success,
  error,
}

/// 加载管理器
class LoadingManager extends ChangeNotifier {
  final Map<String, LoadingState> _states = {};
  final Map<String, String> _messages = {};
  final Map<String, double?> _progress = {};

  /// 获取指定key的加载状态
  LoadingState getState(String key) => _states[key] ?? LoadingState.idle;

  /// 获取指定key的加载消息
  String? getMessage(String key) => _messages[key];

  /// 获取指定key的加载进度
  double? getProgress(String key) => _progress[key];

  /// 检查是否正在加载
  bool isLoading(String key) => getState(key) == LoadingState.loading;

  /// 检查是否有任何加载中的操作
  bool get hasAnyLoading => _states.values.any((state) => state == LoadingState.loading);

  /// 开始加载
  void startLoading(String key, {String? message, double? progress}) {
    _states[key] = LoadingState.loading;
    if (message != null) _messages[key] = message;
    if (progress != null) _progress[key] = progress;
    
    if (kDebugMode) {
      print('🔄 开始加载: $key${message != null ? ' - $message' : ''}');
    }
    
    notifyListeners();
  }

  /// 更新加载进度
  void updateProgress(String key, double progress, {String? message}) {
    if (_states[key] == LoadingState.loading) {
      _progress[key] = progress;
      if (message != null) _messages[key] = message;
      
      if (kDebugMode) {
        print('📊 更新进度: $key - ${(progress * 100).toInt()}%${message != null ? ' - $message' : ''}');
      }
      
      notifyListeners();
    }
  }

  /// 完成加载（成功）
  void completeLoading(String key, {String? message}) {
    _states[key] = LoadingState.success;
    if (message != null) _messages[key] = message;
    _progress.remove(key);
    
    if (kDebugMode) {
      print('✅ 加载完成: $key${message != null ? ' - $message' : ''}');
    }
    
    notifyListeners();
    
    // 2秒后自动清除成功状态
    Future.delayed(const Duration(seconds: 2), () {
      if (_states[key] == LoadingState.success) {
        clearState(key);
      }
    });
  }

  /// 加载失败
  void failLoading(String key, {String? message}) {
    _states[key] = LoadingState.error;
    if (message != null) _messages[key] = message;
    _progress.remove(key);
    
    if (kDebugMode) {
      print('❌ 加载失败: $key${message != null ? ' - $message' : ''}');
    }
    
    notifyListeners();
    
    // 5秒后自动清除错误状态
    Future.delayed(const Duration(seconds: 5), () {
      if (_states[key] == LoadingState.error) {
        clearState(key);
      }
    });
  }

  /// 清除指定key的状态
  void clearState(String key) {
    _states.remove(key);
    _messages.remove(key);
    _progress.remove(key);
    
    if (kDebugMode) {
      print('🧹 清除状态: $key');
    }
    
    notifyListeners();
  }

  /// 清除所有状态
  void clearAll() {
    _states.clear();
    _messages.clear();
    _progress.clear();
    
    if (kDebugMode) {
      print('🧹 清除所有加载状态');
    }
    
    notifyListeners();
  }

  /// 包装异步操作
  Future<T?> wrap<T>(
    String key,
    Future<T> Function() operation, {
    String? loadingMessage,
    String? successMessage,
    String? errorMessage,
  }) async {
    try {
      startLoading(key, message: loadingMessage);
      final result = await operation();
      completeLoading(key, message: successMessage);
      return result;
    } catch (e) {
      failLoading(key, message: errorMessage ?? e.toString());
      rethrow;
    }
  }

  /// 获取所有加载状态
  Map<String, LoadingState> get allStates => Map.unmodifiable(_states);

  /// 获取所有加载消息
  Map<String, String> get allMessages => Map.unmodifiable(_messages);

  /// 获取所有进度信息
  Map<String, double?> get allProgress => Map.unmodifiable(_progress);
}

/// 全局加载管理器实例
final LoadingManager globalLoadingManager = LoadingManager();

/// 加载状态构建器
class LoadingBuilder extends StatelessWidget {
  final String loadingKey;
  final Widget Function(BuildContext context, LoadingState state, String? message, double? progress) builder;
  final LoadingManager? manager;

  const LoadingBuilder({
    super.key,
    required this.loadingKey,
    required this.builder,
    this.manager,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: manager ?? globalLoadingManager,
      builder: (context, child) {
        final loadingManager = manager ?? globalLoadingManager;
        final state = loadingManager.getState(loadingKey);
        final message = loadingManager.getMessage(loadingKey);
        final progress = loadingManager.getProgress(loadingKey);
        
        return builder(context, state, message, progress);
      },
    );
  }
}

/// 加载指示器组件
class LoadingIndicator extends StatelessWidget {
  final String loadingKey;
  final Widget? child;
  final LoadingManager? manager;
  final bool showMessage;
  final bool showProgress;

  const LoadingIndicator({
    super.key,
    required this.loadingKey,
    this.child,
    this.manager,
    this.showMessage = true,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingBuilder(
      loadingKey: loadingKey,
      manager: manager,
      builder: (context, state, message, progress) {
        if (state == LoadingState.loading) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showProgress && progress != null)
                LinearProgressIndicator(value: progress)
              else
                const LinearProgressIndicator(),
              if (showMessage && message != null) ...[
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          );
        }
        
        return child ?? const SizedBox.shrink();
      },
    );
  }
}

/// 加载覆盖层
class LoadingOverlay extends StatelessWidget {
  final String loadingKey;
  final Widget child;
  final LoadingManager? manager;
  final Color? overlayColor;
  final bool showMessage;
  final bool showProgress;

  const LoadingOverlay({
    super.key,
    required this.loadingKey,
    required this.child,
    this.manager,
    this.overlayColor,
    this.showMessage = true,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        LoadingBuilder(
          loadingKey: loadingKey,
          manager: manager,
          builder: (context, state, message, progress) {
            if (state != LoadingState.loading) {
              return const SizedBox.shrink();
            }
            
            return Container(
              color: overlayColor ?? Colors.black.withValues(alpha: 0.3),
              child: Center(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(),
                        if (showMessage && message != null) ...[
                          const SizedBox(height: 16),
                          Text(
                            message,
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ],
                        if (showProgress && progress != null) ...[
                          const SizedBox(height: 16),
                          LinearProgressIndicator(value: progress),
                          const SizedBox(height: 8),
                          Text(
                            '${(progress * 100).toInt()}%',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// 加载按钮
class LoadingButton extends StatelessWidget {
  final String loadingKey;
  final VoidCallback? onPressed;
  final Widget child;
  final LoadingManager? manager;
  final ButtonStyle? style;

  const LoadingButton({
    super.key,
    required this.loadingKey,
    required this.onPressed,
    required this.child,
    this.manager,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingBuilder(
      loadingKey: loadingKey,
      manager: manager,
      builder: (context, state, message, progress) {
        final isLoading = state == LoadingState.loading;
        
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: style,
          child: isLoading
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    if (message != null) ...[
                      const SizedBox(width: 8),
                      Text(message),
                    ],
                  ],
                )
              : child,
        );
      },
    );
  }
}
