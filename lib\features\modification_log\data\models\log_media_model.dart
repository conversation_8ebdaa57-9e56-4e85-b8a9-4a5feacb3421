import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';

/// 日志媒体数据模型
class LogMediaModel {
  final String id;
  final String log_id;
  final String type;
  final String url;
  final String filename;
  final String? caption;
  final int sort_order;
  final String uploaded_at;
  final String uploaded_by;
  final Map<String, dynamic>? metadata;
  final String? thumbnail_url;
  final int? file_size;
  final int? width;
  final int? height;
  final int? duration;

  LogMediaModel({
    required this.id,
    required this.log_id,
    required this.type,
    required this.url,
    required this.filename,
    this.caption,
    required this.sort_order,
    required this.uploaded_at,
    required this.uploaded_by,
    this.metadata,
    this.thumbnail_url,
    this.file_size,
    this.width,
    this.height,
    this.duration,
  });

  factory LogMediaModel.fromJson(Map<String, dynamic> json) {
    return LogMediaModel(
      id: json['id'],
      log_id: json['log_id'],
      type: json['type'],
      url: json['url'],
      filename: json['filename'],
      caption: json['caption'],
      sort_order: json['sort_order'] ?? 0,
      uploaded_at: json['uploaded_at'],
      uploaded_by: json['uploaded_by'],
      metadata: json['metadata'],
      thumbnail_url: json['thumbnail_url'],
      file_size: json['file_size'],
      width: json['width'],
      height: json['height'],
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'log_id': log_id,
      'type': type,
      'url': url,
      'filename': filename,
      'caption': caption,
      'sort_order': sort_order,
      'uploaded_at': uploaded_at,
      'uploaded_by': uploaded_by,
      'metadata': metadata,
      'thumbnail_url': thumbnail_url,
      'file_size': file_size,
      'width': width,
      'height': height,
      'duration': duration,
    };
  }

  /// 转换为领域实体
  LogMedia toEntity() {
    return LogMedia(
      id: id,
      logId: log_id,
      type: _parseMediaType(type),
      url: url,
      filename: filename,
      caption: caption,
      sortOrder: sort_order,
      uploadedAt: DateTime.parse(uploaded_at),
      uploadedBy: uploaded_by,
      metadata: metadata,
      thumbnailUrl: thumbnail_url,
      fileSize: file_size,
      width: width,
      height: height,
      duration: duration,
    );
  }

  /// 从领域实体创建
  factory LogMediaModel.fromEntity(LogMedia entity) {
    return LogMediaModel(
      id: entity.id,
      log_id: entity.logId,
      type: mediaTypeToString(entity.type),
      url: entity.url,
      filename: entity.filename,
      caption: entity.caption,
      sort_order: entity.sortOrder,
      uploaded_at: entity.uploadedAt.toIso8601String(),
      uploaded_by: entity.uploadedBy,
      metadata: entity.metadata,
      thumbnail_url: entity.thumbnailUrl,
      file_size: entity.fileSize,
      width: entity.width,
      height: entity.height,
      duration: entity.duration,
    );
  }

  /// 解析媒体类型
  static MediaType _parseMediaType(String type) {
    switch (type) {
      case 'image':
        return MediaType.image;
      case 'video':
        return MediaType.video;
      case 'document':
        return MediaType.document;
      case 'cad':
        return MediaType.cad;
      case 'panorama':
        return MediaType.panorama;
      case 'audio':
        return MediaType.audio;
      default:
        return MediaType.image;
    }
  }

  /// 媒体类型转字符串
  static String mediaTypeToString(MediaType type) {
    switch (type) {
      case MediaType.image:
        return 'image';
      case MediaType.video:
        return 'video';
      case MediaType.document:
        return 'document';
      case MediaType.cad:
        return 'cad';
      case MediaType.panorama:
        return 'panorama';
      case MediaType.audio:
        return 'audio';
      case MediaType.other:
        return 'other';
    }
  }
}