import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'timeline_event.freezed.dart';
part 'timeline_event.g.dart';

/// 时间轴事件实体
/// 代表改装过程中的一个具体事件或操作
@freezed
class TimelineEvent with _$TimelineEvent {
  const factory TimelineEvent({
    /// 事件唯一标识
    required String id,
    
    /// 关联的日志条目ID
    required String logEntryId,
    
    /// 关联的项目ID
    required String projectId,
    
    /// 关联的系统ID
    String? systemId,
    
    /// 事件类型
    required TimelineEventType eventType,
    
    /// 事件标题
    required String title,
    
    /// 事件描述
    String? description,
    
    /// 事件发生时间
    required DateTime eventTime,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 作者ID
    required String authorId,
    
    /// 作者名称
    String? authorName,
    
    /// 事件状态
    @Default(LogStatus.published) LogStatus status,
    
    /// 关联的媒体ID列表
    @Default([]) List<String> mediaIds,
    
    /// 关联的BOM物料ID列表
    @Default([]) List<String> relatedBomItemIds,
    
    /// 事件成本
    double? cost,
    
    /// 耗时（分钟）
    int? timeSpentMinutes,
    
    /// 位置信息
    String? location,
    
    /// 标签列表
    @Default([]) List<String> tags,
    
    /// 是否为重要事件
    @Default(false) bool isImportant,
    
    /// 是否为问题事件
    @Default(false) bool isProblem,
    
    /// 问题解决状态（仅当isProblem为true时有效）
    ProblemStatus? problemStatus,
    
    /// 解决方案描述
    String? solution,
    
    /// 帮助者ID列表
    @Default([]) List<String> helperIds,
    
    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,
    
    /// 评论数量
    @Default(0) int commentCount,
    
    /// 元数据
    Map<String, dynamic>? metadata,
  }) = _TimelineEvent;

  factory TimelineEvent.fromJson(Map<String, dynamic> json) =>
      _$TimelineEventFromJson(json);
}

/// TimelineEvent扩展方法
extension TimelineEventX on TimelineEvent {
  /// 获取事件类型显示文本
  String get eventTypeDisplayText => eventType.displayName;
  
  /// 获取状态显示文本
  String get statusDisplayText => status.displayName;
  
  /// 获取问题状态显示文本
  String get problemStatusDisplayText => problemStatus?.displayName ?? '';
  
  /// 获取耗时显示文本
  String get timeSpentDisplayText {
    if (timeSpentMinutes == null) return '';
    
    final minutes = timeSpentMinutes!;
    if (minutes < 60) {
      return '$minutes 分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '$hours 小时 ${remainingMinutes > 0 ? '$remainingMinutes 分钟' : ''}';
    }
  }
  
  /// 获取成本显示文本
  String get costDisplayText {
    if (cost == null) return '';
    return '¥${cost!.toStringAsFixed(2)}';
  }
  
  /// 获取事件摘要
  String get summary {
    if (description != null && description!.isNotEmpty) {
      if (description!.length <= 50) {
        return description!;
      } else {
        return '${description!.substring(0, 47)}...';
      }
    }
    return title;
  }
  
  /// 是否为已完成状态
  bool get isCompleted => status == LogStatus.completed;
  
  /// 是否为进行中状态
  bool get isInProgress => status == LogStatus.inProgress;
  
  /// 是否为问题已解决
  bool get isProblemSolved => isProblem && problemStatus == ProblemStatus.solved;
  
  /// 是否需要帮助
  bool get needsHelp => isProblem && problemStatus == ProblemStatus.needHelp;
  
  /// 获取事件图标
  String get eventIcon {
    switch (eventType) {
      case TimelineEventType.startWork:
        return '🚀';
      case TimelineEventType.completeStep:
        return '✅';
      case TimelineEventType.encounterProblem:
        return '⚠️';
      case TimelineEventType.solveProblem:
        return '💡';
      case TimelineEventType.pauseWork:
        return '⏸️';
      case TimelineEventType.resumeWork:
        return '▶️';
      case TimelineEventType.addMaterial:
        return '📦';
      case TimelineEventType.recordInsight:
        return '💭';
      case TimelineEventType.milestone:
        return '🎯';
      case TimelineEventType.other:
        return '📝';
    }
  }
  
  /// 获取事件颜色
  String get eventColor {
    switch (eventType) {
      case TimelineEventType.startWork:
        return '#4CAF50'; // 绿色
      case TimelineEventType.completeStep:
        return '#2196F3'; // 蓝色
      case TimelineEventType.encounterProblem:
        return '#FF9800'; // 橙色
      case TimelineEventType.solveProblem:
        return '#8BC34A'; // 浅绿色
      case TimelineEventType.pauseWork:
        return '#9E9E9E'; // 灰色
      case TimelineEventType.resumeWork:
        return '#4CAF50'; // 绿色
      case TimelineEventType.addMaterial:
        return '#673AB7'; // 紫色
      case TimelineEventType.recordInsight:
        return '#FF5722'; // 深橙色
      case TimelineEventType.milestone:
        return '#F44336'; // 红色
      case TimelineEventType.other:
        return '#607D8B'; // 蓝灰色
    }
  }
  
  /// 获取时间描述
  String get timeDescription {
    final now = DateTime.now();
    final difference = now.difference(eventTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 获取关联媒体数量
  int get mediaCount => mediaIds.length;
  
  /// 获取关联物料数量
  int get bomItemCount => relatedBomItemIds.length;
  
  /// 获取点赞数量
  int get likeCount => likedByUserIds.length;
  
  /// 获取帮助者数量
  int get helperCount => helperIds.length;
}
