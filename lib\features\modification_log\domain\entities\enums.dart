import 'package:freezed_annotation/freezed_annotation.dart';

/// 里程碑状态枚举
@JsonEnum()
enum MilestoneStatus {
  /// 计划中
  @JsonValue('planned')
  planned,
  
  /// 进行中
  @JsonValue('in_progress')
  inProgress,
  
  /// 已完成
  @JsonValue('completed')
  completed,
  
  /// 已取消
  @JsonValue('cancelled')
  cancelled,
  
  /// 已逾期
  @JsonValue('overdue')
  overdue;
  
  String get displayName {
    switch (this) {
      case MilestoneStatus.planned:
        return '计划中';
      case MilestoneStatus.inProgress:
        return '进行中';
      case MilestoneStatus.completed:
        return '已完成';
      case MilestoneStatus.cancelled:
        return '已取消';
      case MilestoneStatus.overdue:
        return '已逾期';
    }
  }
}

/// 里程碑优先级枚举
@JsonEnum()
enum MilestonePriority {
  /// 低优先级
  @JsonValue('low')
  low,
  
  /// 中优先级
  @JsonValue('medium')
  medium,
  
  /// 高优先级
  @JsonValue('high')
  high,
  
  /// 关键
  @JsonValue('critical')
  critical;
  
  String get displayName {
    switch (this) {
      case MilestonePriority.low:
        return '低';
      case MilestonePriority.medium:
        return '中';
      case MilestonePriority.high:
        return '高';
      case MilestonePriority.critical:
        return '紧急';
    }
  }
}

/// 日志状态枚举
@JsonEnum()
enum LogStatus {
  /// 草稿
  @JsonValue('draft')
  draft,
  
  /// 进行中
  @JsonValue('in_progress')
  inProgress,
  
  /// 已完成
  @JsonValue('completed')
  completed,
  
  /// 暂停
  @JsonValue('on_hold')
  onHold,
  
  /// 已取消
  @JsonValue('cancelled')
  cancelled,
  
  /// 已发布
  @JsonValue('published')
  published,
  
  /// 已归档
  @JsonValue('archived')
  archived;
  
  String get displayName {
    switch (this) {
      case LogStatus.draft:
        return '草稿';
      case LogStatus.inProgress:
        return '进行中';
      case LogStatus.completed:
        return '已完成';
      case LogStatus.onHold:
        return '暂停';
      case LogStatus.cancelled:
        return '已取消';
      case LogStatus.published:
        return '已发布';
      case LogStatus.archived:
        return '已归档';
    }
  }
}

/// 难度级别枚举
@JsonEnum()
enum DifficultyLevel {
  /// 简单
  @JsonValue('easy')
  easy,
  
  /// 中等
  @JsonValue('medium')
  medium,
  
  /// 困难
  @JsonValue('hard')
  hard,
  
  /// 专家
  @JsonValue('expert')
  expert,
  
  /// 初级
  @JsonValue('beginner')
  beginner,
  
  /// 中级
  @JsonValue('intermediate')
  intermediate,
  
  /// 高级
  @JsonValue('advanced')
  advanced;
  
  String get displayName {
    switch (this) {
      case DifficultyLevel.easy:
        return '简单';
      case DifficultyLevel.medium:
        return '中等';
      case DifficultyLevel.hard:
        return '困难';
      case DifficultyLevel.expert:
        return '专家';
      case DifficultyLevel.beginner:
        return '初级';
      case DifficultyLevel.intermediate:
        return '中级';
      case DifficultyLevel.advanced:
        return '高级';
    }
  }
}

/// 媒体类型枚举
@JsonEnum()
enum MediaType {
  /// 图片
  @JsonValue('image')
  image,
  
  /// 视频
  @JsonValue('video')
  video,
  
  /// 音频
  @JsonValue('audio')
  audio,
  
  /// 文档
  @JsonValue('document')
  document,
  
  /// 其他
  @JsonValue('other')
  other,
  
  /// CAD文件
  @JsonValue('cad')
  cad,
  
  /// 360°全景图
  @JsonValue('panorama')
  panorama;
  
  String get displayName {
    switch (this) {
      case MediaType.image:
        return '图片';
      case MediaType.video:
        return '视频';
      case MediaType.audio:
        return '音频';
      case MediaType.document:
        return '文档';
      case MediaType.other:
        return '其他';
      case MediaType.cad:
        return 'CAD文件';
      case MediaType.panorama:
        return '360°全景图';
    }
  }
}

/// 项目状态枚举
@JsonEnum()
enum ProjectStatus {
  /// 规划中
  @JsonValue('planning')
  planning,
  
  /// 进行中
  @JsonValue('in_progress')
  inProgress,
  
  /// 已完成
  @JsonValue('completed')
  completed,
  
  /// 暂停
  @JsonValue('on_hold')
  onHold,
  
  /// 已取消
  @JsonValue('cancelled')
  cancelled;
  
  String get displayName {
    switch (this) {
      case ProjectStatus.planning:
        return '规划中';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.onHold:
        return '暂停';
      case ProjectStatus.cancelled:
        return '已取消';
    }
  }
}

/// 系统类型枚举
@JsonEnum()
enum SystemType {
  /// 电路系统
  @JsonValue('electrical')
  electrical,
  
  /// 水路系统
  @JsonValue('plumbing')
  plumbing,
  
  /// 储物系统
  @JsonValue('storage')
  storage,
  
  /// 床铺系统
  @JsonValue('bedding')
  bedding,
  
  /// 厨房系统
  @JsonValue('kitchen')
  kitchen,
  
  /// 卫浴系统
  @JsonValue('bathroom')
  bathroom,
  
  /// 外观改装
  @JsonValue('exterior')
  exterior,
  
  /// 底盘改装
  @JsonValue('chassis')
  chassis,
  
  /// 自定义系统
  @JsonValue('custom')
  custom;
  
  String get displayName {
    switch (this) {
      case SystemType.electrical:
        return '电路系统';
      case SystemType.plumbing:
        return '水路系统';
      case SystemType.storage:
        return '储物系统';
      case SystemType.bedding:
        return '床铺系统';
      case SystemType.kitchen:
        return '厨房系统';
      case SystemType.bathroom:
        return '卫浴系统';
      case SystemType.exterior:
        return '外观改装';
      case SystemType.chassis:
        return '底盘改装';
      case SystemType.custom:
        return '自定义系统';
    }
  }
}

/// 材料状态枚举
@JsonEnum()
enum MaterialStatus {
  /// 待处理
  @JsonValue('pending')
  pending,
  
  /// 已下单
  @JsonValue('ordered')
  ordered,
  
  /// 已收货
  @JsonValue('received')
  received,
  
  /// 已安装
  @JsonValue('installed')
  installed,
  
  /// 已退货
  @JsonValue('returned')
  returned;
  
  String get displayName {
    switch (this) {
      case MaterialStatus.pending:
        return '待处理';
      case MaterialStatus.ordered:
        return '已下单';
      case MaterialStatus.received:
        return '已收货';
      case MaterialStatus.installed:
        return '已安装';
      case MaterialStatus.returned:
        return '已退货';
    }
  }
}

/// 系统状态枚举
@JsonEnum()
enum SystemStatus {
  /// 规划中
  @JsonValue('planning')
  planning,
  
  /// 采购中
  @JsonValue('purchasing')
  purchasing,
  
  /// 安装中
  @JsonValue('installing')
  installing,
  
  /// 已完成
  @JsonValue('completed')
  completed,
  
  /// 暂停
  @JsonValue('paused')
  paused;
  
  String get displayName {
    switch (this) {
      case SystemStatus.planning:
        return '规划中';
      case SystemStatus.purchasing:
        return '采购中';
      case SystemStatus.installing:
        return '安装中';
      case SystemStatus.completed:
        return '已完成';
      case SystemStatus.paused:
        return '暂停';
    }
  }
}

/// 时间轴状态枚举
@JsonEnum()
enum TimelineStatus {
  /// 规划中
  @JsonValue('planning')
  planning,

  /// 进行中
  @JsonValue('in_progress')
  inProgress,

  /// 已完成
  @JsonValue('completed')
  completed,

  /// 已暂停
  @JsonValue('paused')
  paused,

  /// 已取消
  @JsonValue('cancelled')
  cancelled;

  String get displayName {
    switch (this) {
      case TimelineStatus.planning:
        return '规划中';
      case TimelineStatus.inProgress:
        return '进行中';
      case TimelineStatus.completed:
        return '已完成';
      case TimelineStatus.paused:
        return '已暂停';
      case TimelineStatus.cancelled:
        return '已取消';
    }
  }
}

/// 问题解决状态枚举
@JsonEnum()
enum ProblemStatus {
  /// 未解决
  @JsonValue('unsolved')
  unsolved,

  /// 解决中
  @JsonValue('solving')
  solving,

  /// 已解决
  @JsonValue('solved')
  solved,

  /// 需要帮助
  @JsonValue('need_help')
  needHelp,

  /// 已放弃
  @JsonValue('abandoned')
  abandoned;

  String get displayName {
    switch (this) {
      case ProblemStatus.unsolved:
        return '未解决';
      case ProblemStatus.solving:
        return '解决中';
      case ProblemStatus.solved:
        return '已解决';
      case ProblemStatus.needHelp:
        return '需要帮助';
      case ProblemStatus.abandoned:
        return '已放弃';
    }
  }
}

/// 时间轴事件类型枚举
@JsonEnum()
enum TimelineEventType {
  /// 开始工作
  @JsonValue('start_work')
  startWork,

  /// 完成步骤
  @JsonValue('complete_step')
  completeStep,

  /// 遇到问题
  @JsonValue('encounter_problem')
  encounterProblem,

  /// 解决问题
  @JsonValue('solve_problem')
  solveProblem,

  /// 暂停工作
  @JsonValue('pause_work')
  pauseWork,

  /// 恢复工作
  @JsonValue('resume_work')
  resumeWork,

  /// 添加材料
  @JsonValue('add_material')
  addMaterial,

  /// 记录心得
  @JsonValue('record_insight')
  recordInsight,

  /// 里程碑
  @JsonValue('milestone')
  milestone,

  /// 其他
  @JsonValue('other')
  other;

  String get displayName {
    switch (this) {
      case TimelineEventType.startWork:
        return '开始工作';
      case TimelineEventType.completeStep:
        return '完成步骤';
      case TimelineEventType.encounterProblem:
        return '遇到问题';
      case TimelineEventType.solveProblem:
        return '解决问题';
      case TimelineEventType.pauseWork:
        return '暂停工作';
      case TimelineEventType.resumeWork:
        return '恢复工作';
      case TimelineEventType.addMaterial:
        return '添加材料';
      case TimelineEventType.recordInsight:
        return '记录心得';
      case TimelineEventType.milestone:
        return '里程碑';
      case TimelineEventType.other:
        return '其他';
    }
  }
}

/// 交互类型枚举
@JsonEnum()
enum InteractionType {
  /// 点赞
  @JsonValue('like')
  like,

  /// 收藏
  @JsonValue('bookmark')
  bookmark,

  /// 评论
  @JsonValue('comment')
  comment,

  /// 分享
  @JsonValue('share')
  share,

  /// 引用
  @JsonValue('reference')
  reference,

  /// 提问
  @JsonValue('question')
  question,

  /// 建议
  @JsonValue('suggestion')
  suggestion;

  String get displayName {
    switch (this) {
      case InteractionType.like:
        return '点赞';
      case InteractionType.bookmark:
        return '收藏';
      case InteractionType.comment:
        return '评论';
      case InteractionType.share:
        return '分享';
      case InteractionType.reference:
        return '引用';
      case InteractionType.question:
        return '提问';
      case InteractionType.suggestion:
        return '建议';
    }
  }
}