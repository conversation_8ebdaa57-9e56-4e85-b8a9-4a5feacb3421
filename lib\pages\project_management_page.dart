import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/api/supabase_config.dart';
import '../core/utils/performance_monitor.dart';
import '../core/utils/error_handler.dart';
import '../core/utils/loading_manager.dart';
import '../core/errors/app_error.dart';

/// 项目管理页面
class ProjectManagementPage extends ConsumerStatefulWidget {
  const ProjectManagementPage({super.key});

  @override
  ConsumerState<ProjectManagementPage> createState() => _ProjectManagementPageState();
}

class _ProjectManagementPageState extends ConsumerState<ProjectManagementPage> {
  List<Map<String, dynamic>> projects = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  /// 加载项目列表
  Future<void> _loadProjects() async {
    const loadingKey = 'load_projects';
    
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      globalLoadingManager.startLoading(loadingKey, message: '加载项目列表...');
      
      final result = await PerformanceMonitor.monitor('load_projects', () async {
        final response = await SupabaseConfig.client
            .from('projects')
            .select('*')
            .order('created_at', ascending: false);

        // 转换数据库字段到应用字段
        return (response as List<dynamic>).map((project) {
          return {
            'id': project['id'],
            'name': project['title'] ?? '未命名项目', // 数据库用title，应用用name
            'description': project['description'] ?? '',
            'status': (project['total_budget'] != null &&
                      double.tryParse(project['total_budget'].toString()) != null &&
                      double.parse(project['total_budget'].toString()) > 0)
                      ? '进行中' : '规划中', // 根据数据推断状态
            'budget': project['total_budget'] ?? 0,
            'created_at': project['created_at'],
            'updated_at': project['updated_at'],
            'user_id': project['user_id'],
            'vehicle_model': project['vehicle_model'],
            'is_public': project['is_public'],
          };
        }).toList();
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: '项目列表加载完成');
      
      if (mounted) {
        setState(() {
          projects = result.cast<Map<String, dynamic>>();
          isLoading = false;
        });
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '项目列表加载失败');
      
      if (mounted) {
        setState(() {
          errorMessage = e.toString();
          isLoading = false;
        });

        ErrorHandler.handle(
          context,
          AppError.network('加载项目列表失败', details: e.toString()),
          customMessage: '无法加载项目列表，请检查网络连接',
        );
      }
    }
  }

  /// 创建新项目
  Future<void> _createProject() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => const ProjectCreateDialog(),
    );

    if (result != null) {
      await _saveProject(result);
    }
  }

  /// 保存项目
  Future<void> _saveProject(Map<String, String> projectData) async {
    const loadingKey = 'save_project';
    
    try {
      globalLoadingManager.startLoading(loadingKey, message: '保存项目...');
      
      await PerformanceMonitor.monitor('save_project', () async {
        await SupabaseConfig.client.from('projects').insert({
          'name': projectData['name'],
          'description': projectData['description'],
          'budget': double.tryParse(projectData['budget'] ?? '0') ?? 0,
          'status': 'planning',
          'created_at': DateTime.now().toIso8601String(),
          'user_id': SupabaseConfig.client.auth.currentUser?.id,
        });
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: '项目保存成功');
      
      // 重新加载项目列表
      await _loadProjects();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('项目创建成功！'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '项目保存失败');
      
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('保存项目失败', details: e.toString()),
          customMessage: '无法保存项目，请重试',
        );
      }
    }
  }

  /// 删除项目
  Future<void> _deleteProject(String projectId, String projectName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除项目"$projectName"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      const loadingKey = 'delete_project';
      
      try {
        globalLoadingManager.startLoading(loadingKey, message: '删除项目...');
        
        await PerformanceMonitor.monitor('delete_project', () async {
          await SupabaseConfig.client
              .from('projects')
              .delete()
              .eq('id', projectId);
        });
        
        globalLoadingManager.completeLoading(loadingKey, message: '项目删除成功');
        
        // 重新加载项目列表
        await _loadProjects();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.delete, color: Colors.white),
                  SizedBox(width: 8),
                  Text('项目删除成功！'),
                ],
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        globalLoadingManager.failLoading(loadingKey, message: '项目删除失败');
        
        if (mounted) {
          ErrorHandler.handle(
            context,
            AppError.database('删除项目失败', details: e.toString()),
            customMessage: '无法删除项目，请重试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('项目管理'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadProjects,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createProject,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('加载项目列表中...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadProjects,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (projects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无项目',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              '点击右下角的 + 按钮创建第一个项目',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return _buildProjectCard(project);
      },
    );
  }

  Widget _buildProjectCard(Map<String, dynamic> project) {
    final status = project['status'] as String? ?? 'planning';
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    project['name'] ?? '未命名项目',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            if (project['description'] != null) ...[
              const SizedBox(height: 8),
              Text(
                project['description'],
                style: const TextStyle(color: Colors.grey),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.attach_money, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                Text(
                  '预算: ¥${project['budget']?.toString() ?? '0'}',
                  style: const TextStyle(fontSize: 14),
                ),
                const Spacer(),
                Text(
                  '创建时间: ${_formatDate(project['created_at'])}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _editProject(project),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('编辑'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteProject(
                    project['id'].toString(),
                    project['name'] ?? '未命名项目',
                  ),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('删除'),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'planning':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'planning':
        return '规划中';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  }

  String _formatDate(dynamic dateTime) {
    if (dateTime == null) return '未知';
    
    try {
      final date = DateTime.parse(dateTime.toString());
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '未知';
    }
  }

  void _editProject(Map<String, dynamic> project) {
    // TODO: 实现编辑功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('编辑功能即将推出'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// 项目创建对话框
class ProjectCreateDialog extends StatefulWidget {
  const ProjectCreateDialog({super.key});

  @override
  State<ProjectCreateDialog> createState() => _ProjectCreateDialogState();
}

class _ProjectCreateDialogState extends State<ProjectCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _budgetController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('创建新项目'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '项目名称',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入项目名称';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: '项目描述',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _budgetController,
              decoration: const InputDecoration(
                labelText: '预算金额',
                border: OutlineInputBorder(),
                prefixText: '¥ ',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (double.tryParse(value) == null) {
                    return '请输入有效的金额';
                  }
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop({
                'name': _nameController.text.trim(),
                'description': _descriptionController.text.trim(),
                'budget': _budgetController.text.trim(),
              });
            }
          },
          child: const Text('创建'),
        ),
      ],
    );
  }


}
