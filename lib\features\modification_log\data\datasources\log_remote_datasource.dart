import '../../../../core/error/exceptions.dart';
import '../../domain/entities/log_search_criteria.dart';
import '../models/log_entry_model.dart';

/// 日志远程数据源接口
abstract class LogRemoteDataSource {
  /// 创建日志条目
  Future<LogEntryModel> createLogEntry(LogEntryModel logEntry);
  
  /// 获取日志条目
  Future<LogEntryModel> getLogEntry(String id);
  
  /// 更新日志条目
  Future<LogEntryModel> updateLogEntry(LogEntryModel logEntry);
  
  /// 删除日志条目
  Future<void> deleteLogEntry(String id);
  
  /// 获取项目日志
  Future<List<LogEntryModel>> getProjectLogs(String projectId, {int limit = 20, int offset = 0});
  
  /// 获取系统日志
  Future<List<LogEntryModel>> getSystemLogs(String systemId, {int limit = 20, int offset = 0});
  
  /// 搜索日志
  Future<List<LogEntryModel>> searchLogs(LogSearchCriteria criteria);

  /// 获取日志数量
  Future<int> getLogCount(LogSearchCriteria criteria);

  /// 获取最近日志
  Future<List<LogEntryModel>> getRecentLogs({int limit = 10});

  /// 获取用户日志
  Future<List<LogEntryModel>> getUserLogs(String userId);

  /// 更新日志状态
  Future<LogEntryModel> updateLogStatus(String logId, String status);
}

/// 日志远程数据源实现
class LogRemoteDataSourceImpl implements LogRemoteDataSource {
  final dynamic supabaseClient;

  const LogRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<LogEntryModel> createLogEntry(LogEntryModel logEntry) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .insert(logEntry.toJson())
          .select()
          .single();

      return LogEntryModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to create log entry: $e');
    }
  }

  @override
  Future<LogEntryModel> getLogEntry(String id) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .select()
          .eq('id', id)
          .single();

      return LogEntryModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to get log entry: $e');
    }
  }

  @override
  Future<LogEntryModel> updateLogEntry(LogEntryModel logEntry) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .update(logEntry.toJson())
          .eq('id', logEntry.id)
          .select()
          .single();

      return LogEntryModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to update log entry: $e');
    }
  }

  @override
  Future<void> deleteLogEntry(String id) async {
    try {
      await supabaseClient
          .from('log_entries')
          .delete()
          .eq('id', id);
    } catch (e) {
      throw ServerException(message: 'Failed to delete log entry: $e');
    }
  }

  @override
  Future<List<LogEntryModel>> getProjectLogs(String projectId, {int limit = 20, int offset = 0}) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .select()
          .eq('project_id', projectId)
          .order('log_date', ascending: false)
          .limit(limit)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => LogEntryModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get project logs: $e');
    }
  }

  @override
  Future<List<LogEntryModel>> getSystemLogs(String systemId, {int limit = 20, int offset = 0}) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .select()
          .eq('system_id', systemId)
          .order('log_date', ascending: false)
          .limit(limit)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => LogEntryModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get system logs: $e');
    }
  }

  @override
  Future<List<LogEntryModel>> searchLogs(LogSearchCriteria criteria) async {
    try {
      var query = supabaseClient.from('log_entries').select();

      // Apply search filters
      if (criteria.keyword != null && criteria.keyword!.isNotEmpty) {
        query = query.or('title.ilike.%${criteria.keyword}%,content.ilike.%${criteria.keyword}%');
      }

      if (criteria.projectId != null) {
        query = query.eq('project_id', criteria.projectId);
      }

      if (criteria.systemId != null) {
        query = query.eq('system_id', criteria.systemId);
      }

      if (criteria.statuses != null && criteria.statuses!.isNotEmpty) {
        final statusNames = criteria.statuses!.map((s) => s.name).toList();
        if (statusNames.length == 1) {
          query = query.eq('status', statusNames.first);
        } else {
          query = query.inFilter('status', statusNames);
        }
      }

      if (criteria.difficulties != null && criteria.difficulties!.isNotEmpty) {
        final difficultyNames = criteria.difficulties!.map((d) => d.name).toList();
        if (difficultyNames.length == 1) {
          query = query.eq('difficulty', difficultyNames.first);
        } else {
          query = query.inFilter('difficulty', difficultyNames);
        }
      }

      if (criteria.startDate != null) {
        query = query.gte('log_date', criteria.startDate!.toIso8601String());
      }

      if (criteria.endDate != null) {
        query = query.lte('log_date', criteria.endDate!.toIso8601String());
      }

      // Apply sorting and pagination
      query = query.order('log_date', ascending: false);

      if (criteria.limit != null) {
        query = query.limit(criteria.limit!);
      }

      if (criteria.offset != null && criteria.limit != null) {
        query = query.range(criteria.offset!, criteria.offset! + criteria.limit! - 1);
      }

      final response = await query;

      return (response as List)
          .map((json) => LogEntryModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: 'Failed to search logs: $e');
    }
  }

  @override
  Future<int> getLogCount(LogSearchCriteria criteria) async {
    try {
      var query = supabaseClient.from('log_entries').select('id');

      // Apply search filters (same as searchLogs but only count)
      if (criteria.keyword != null && criteria.keyword!.isNotEmpty) {
        query = query.or('title.ilike.%${criteria.keyword}%,content.ilike.%${criteria.keyword}%');
      }

      if (criteria.projectId != null) {
        query = query.eq('project_id', criteria.projectId);
      }

      if (criteria.systemId != null) {
        query = query.eq('system_id', criteria.systemId);
      }

      if (criteria.statuses != null && criteria.statuses!.isNotEmpty) {
        final statusNames = criteria.statuses!.map((s) => s.name).toList();
        if (statusNames.length == 1) {
          query = query.eq('status', statusNames.first);
        } else {
          query = query.inFilter('status', statusNames);
        }
      }

      if (criteria.difficulties != null && criteria.difficulties!.isNotEmpty) {
        final difficultyNames = criteria.difficulties!.map((d) => d.name).toList();
        if (difficultyNames.length == 1) {
          query = query.eq('difficulty', difficultyNames.first);
        } else {
          query = query.inFilter('difficulty', difficultyNames);
        }
      }

      if (criteria.startDate != null) {
        query = query.gte('log_date', criteria.startDate!.toIso8601String());
      }

      if (criteria.endDate != null) {
        query = query.lte('log_date', criteria.endDate!.toIso8601String());
      }

      final response = await query;
      return (response as List).length;
    } catch (e) {
      throw ServerException(message: 'Failed to get log count: $e');
    }
  }

  @override
  Future<List<LogEntryModel>> getRecentLogs({int limit = 10}) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .select('*')
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((json) => LogEntryModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取最近日志失败: $e');
    }
  }

  @override
  Future<List<LogEntryModel>> getUserLogs(String userId) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .select('*')
          .eq('author_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => LogEntryModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取用户日志失败: $e');
    }
  }

  @override
  Future<LogEntryModel> updateLogStatus(String logId, String status) async {
    try {
      final response = await supabaseClient
          .from('log_entries')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', logId)
          .select()
          .single();

      return LogEntryModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新日志状态失败: $e');
    }
  }
}