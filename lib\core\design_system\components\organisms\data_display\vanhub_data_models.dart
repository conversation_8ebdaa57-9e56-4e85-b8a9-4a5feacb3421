import 'package:flutter/material.dart';

/// 数据列
class VanHubDataColumn {
  final String id;
  final String label;
  final bool numeric;
  final String? tooltip;
  final bool sortable;
  final bool filterable;
  final VanHubSortDirection? initialSortDirection;
  final VanHubColumnComparator? comparator;
  final bool primary;
  final bool showInMobileList;

  const VanHubDataColumn({
    required this.id,
    required this.label,
    this.numeric = false,
    this.tooltip,
    this.sortable = true,
    this.filterable = true,
    this.initialSortDirection,
    this.comparator,
    this.primary = false,
    this.showInMobileList = true,
  });
}

/// 数据行
class VanHubDataRow {
  final Map<String, VanHubDataCell> cells;
  final String? id;
  final Widget? actions;

  const VanHubDataRow({
    required this.cells,
    this.id,
    this.actions,
  });
}

/// 数据单元格
class VanHubDataCell {
  final dynamic value;
  final VoidCallback? onTap;
  final bool showEditIcon;
  final bool placeholder;
  final VanHubCellBuilder? customBuilder;

  const VanHubDataCell({
    this.value,
    this.onTap,
    this.showEditIcon = false,
    this.placeholder = false,
    this.customBuilder,
  });
}

/// 排序方向
enum VanHubSortDirection {
  ascending,
  descending,
}

/// 数据表格布局
enum VanHubDataTableLayout {
  card,
  list,
}

/// 导出格式
enum VanHubExportFormat {
  csv,
  excel,
  pdf,
}

/// 列比较器
typedef VanHubColumnComparator = int Function(VanHubDataCell a, VanHubDataCell b);

/// 单元格构建器
typedef VanHubCellBuilder = Widget Function(BuildContext context, dynamic value);

/// 数据表格导出选项
class VanHubDataTableExportOptions {
  final String? fileName;
  final bool allowCsv;
  final bool allowExcel;
  final bool allowPdf;

  const VanHubDataTableExportOptions({
    this.fileName,
    this.allowCsv = true,
    this.allowExcel = true,
    this.allowPdf = true,
  });
}