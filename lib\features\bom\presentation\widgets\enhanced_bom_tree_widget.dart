import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/bom_tree_state.dart';
import '../../domain/entities/tree_node.dart';
import '../providers/bom_tree_provider.dart';
import 'enhanced_bom_tree_node_widget.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 增强版BOM树形结构Widget
/// 现代化设计，流畅的动画效果和优秀的用户体验
class EnhancedBomTreeWidget extends ConsumerStatefulWidget {
  final String projectId;
  final VoidCallback? onRefresh;
  final Function(String)? onNodeSelected;
  final Function(String)? onBomItemEdit;
  final Function(String)? onBomItemDelete;

  const EnhancedBomTreeWidget({
    super.key,
    required this.projectId,
    this.onRefresh,
    this.onNodeSelected,
    this.onBomItemEdit,
    this.onBomItemDelete,
  });

  @override
  ConsumerState<EnhancedBomTreeWidget> createState() => _EnhancedBomTreeWidgetState();
}

class _EnhancedBomTreeWidgetState extends ConsumerState<EnhancedBomTreeWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // 启动入场动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bomTreeState = ref.watch(bomTreeProvider);

    // 初始化BOM树
    ref.listen<BomTreeState>(bomTreeProvider, (previous, next) {
      if (previous?.tree.isEmpty == true && next.tree.isEmpty && !next.isLoading) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 使用正确的方式访问provider
          final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
          if (bomTreeNotifier is BomTreeNotifier) {
            bomTreeNotifier.initializeBomTree(widget.projectId);
          }
        });
      }
    });

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 增强版工具栏
              _buildEnhancedToolbar(context, bomTreeState),
              
              // 搜索栏
              _buildSearchBar(context, bomTreeState),
              
              // 统计面板
              if (bomTreeState.showStatistics)
                _buildEnhancedStatisticsPanel(context, bomTreeState),
              
              // 树形结构内容
              Expanded(
                child: _buildTreeContent(context, bomTreeState),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedToolbar(BuildContext context, BomTreeState bomTreeState) {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubColors.primary.withOpacity(0.1),
            VanHubColors.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(VanHubSpacing.radiusMedium),
          topRight: Radius.circular(VanHubSpacing.radiusMedium),
        ),
      ),
      child: Row(
        children: [
          // 标题和图标
          Container(
            padding: EdgeInsets.all(VanHubSpacing.sm),
            decoration: BoxDecoration(
              color: VanHubColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
            ),
            child: Icon(
              Icons.account_tree,
              color: VanHubColors.primary,
              size: 24,
            ),
          ),
          
          SizedBox(width: VanHubSpacing.md),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'BOM树形结构',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: VanHubColors.primary,
                  ),
                ),
                SizedBox(height: VanHubSpacing.xs),
                Text(
                  '项目物料清单的层次化视图',
                  style: TextStyle(
                    fontSize: 14,
                    color: VanHubColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // 功能按钮
          _buildToolbarActions(bomTreeState),
        ],
      ),
    );
  }

  Widget _buildToolbarActions(BomTreeState bomTreeState) {
    return Row(
      children: [
        // 统计切换按钮
        IconButton(
          onPressed: () {
            final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
            if (bomTreeNotifier is BomTreeNotifier) {
              bomTreeNotifier.toggleStatistics();
            }
          },
          icon: Icon(
            bomTreeState.showStatistics 
                ? Icons.bar_chart 
                : Icons.bar_chart_outlined,
          ),
          style: IconButton.styleFrom(
            backgroundColor: bomTreeState.showStatistics
                ? VanHubColors.primary.withOpacity(0.1)
                : Colors.transparent,
            foregroundColor: bomTreeState.showStatistics
                ? VanHubColors.primary
                : VanHubColors.textSecondary,
          ),
          tooltip: bomTreeState.showStatistics ? '隐藏统计' : '显示统计',
        ),
        
        SizedBox(width: VanHubSpacing.sm),
        
        // 拖拽开关按钮
        IconButton(
          onPressed: () {
            final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
            if (bomTreeNotifier is BomTreeNotifier) {
              bomTreeNotifier.toggleDragMode();
            }
          },
          icon: Icon(
            bomTreeState.isDragEnabled 
                ? Icons.drag_indicator 
                : Icons.drag_indicator_outlined,
          ),
          style: IconButton.styleFrom(
            backgroundColor: bomTreeState.isDragEnabled
                ? VanHubColors.primary.withOpacity(0.1)
                : Colors.transparent,
            foregroundColor: bomTreeState.isDragEnabled
                ? VanHubColors.primary
                : VanHubColors.textSecondary,
          ),
          tooltip: bomTreeState.isDragEnabled ? '禁用拖拽' : '启用拖拽',
        ),
        
        SizedBox(width: VanHubSpacing.sm),
        
        // 刷新按钮
        IconButton(
          onPressed: () {
            widget.onRefresh?.call();
            final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
            if (bomTreeNotifier is BomTreeNotifier) {
              bomTreeNotifier.initializeBomTree(widget.projectId);
            }
          },
          icon: const Icon(Icons.refresh),
          style: IconButton.styleFrom(
            backgroundColor: VanHubColors.surfaceVariant,
            foregroundColor: VanHubColors.textSecondary,
          ),
          tooltip: '刷新数据',
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context, BomTreeState bomTreeState) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: VanHubSpacing.lg, vertical: VanHubSpacing.md),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索BOM项目...',
          prefixIcon: Icon(
            Icons.search,
            color: VanHubColors.primary,
          ),
          suffixIcon: bomTreeState.isSearchActive
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    ref.read(bomTreeProvider.notifier).searchBomTree('');
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
            borderSide: BorderSide(color: VanHubColors.outlineVariant),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
            borderSide: BorderSide(color: VanHubColors.primary, width: 2),
          ),
          filled: true,
          fillColor: VanHubColors.surfaceVariant,
        ),
        onChanged: (query) {
          final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
          if (bomTreeNotifier is BomTreeNotifier) {
            bomTreeNotifier.searchBomTree(query);
          }
        },
      ),
    );
  }

  Widget _buildEnhancedStatisticsPanel(BuildContext context, BomTreeState bomTreeState) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: VanHubSpacing.lg, vertical: VanHubSpacing.sm),
      padding: EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubColors.info.withOpacity(0.05),
            VanHubColors.secondary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
        border: Border.all(color: VanHubColors.outlineVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '统计概览',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: VanHubColors.textPrimary,
            ),
          ),
          SizedBox(height: VanHubSpacing.sm),
          _buildStatisticsGrid(bomTreeState),
        ],
      ),
    );
  }

  Widget _buildStatisticsGrid(BomTreeState bomTreeState) {
    final totalItems = _getTotalItems(bomTreeState.tree);
    final totalCost = _getTotalCost(bomTreeState.tree);
    final categories = _getCategoryCount(bomTreeState.tree);
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.inventory,
            label: '总项目',
            value: '$totalItems',
            color: VanHubColors.info,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: _buildStatCard(
            icon: Icons.attach_money,
            label: '总价值',
            value: '¥${totalCost.toStringAsFixed(2)}',
            color: VanHubColors.success,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: _buildStatCard(
            icon: Icons.category,
            label: '分类数',
            value: '$categories',
            color: VanHubColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          SizedBox(height: VanHubSpacing.xs),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: VanHubColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreeContent(BuildContext context, BomTreeState bomTreeState) {
    if (bomTreeState.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: VanHubColors.primary),
            SizedBox(height: VanHubSpacing.md),
            Text('加载BOM树形结构...', style: TextStyle(color: VanHubColors.textSecondary)),
          ],
        ),
      );
    }

    if (bomTreeState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: VanHubColors.error),
            SizedBox(height: VanHubSpacing.md),
            Text(
              bomTreeState.errorMessage ?? '加载BOM树形结构时出错',
              style: TextStyle(color: VanHubColors.error),
            ),
            SizedBox(height: VanHubSpacing.md),
            ElevatedButton(
              onPressed: () {
                final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
                if (bomTreeNotifier is BomTreeNotifier) {
                  bomTreeNotifier.initializeBomTree(widget.projectId);
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (bomTreeState.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_tree_outlined, size: 64, color: VanHubColors.outline),
            SizedBox(height: VanHubSpacing.md),
            Text(
              '暂无BOM数据',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: VanHubColors.textPrimary,
              ),
            ),
            SizedBox(height: VanHubSpacing.sm),
            Text(
              '该项目还没有添加任何BOM项目\n点击添加按钮开始构建您的BOM清单',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: VanHubColors.textSecondary,
              ),
            ),
            SizedBox(height: VanHubSpacing.md),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: 导航到添加BOM项目页面
              },
              icon: const Icon(Icons.add),
              label: const Text('添加BOM项目'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubColors.primary,
                foregroundColor: VanHubColors.onPrimary,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
      child: Scrollbar(
        controller: _scrollController,
        child: ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.all(VanHubSpacing.sm),
          itemCount: bomTreeState.tree.length,
          itemBuilder: (context, index) {
            final node = bomTreeState.tree[index];
            return EnhancedBomTreeNodeWidget(
              node: node,
              isSelected: node.id == bomTreeState.selectedNodeId,
              isHighlighted: bomTreeState.searchResults.contains(node.id),
              isDragEnabled: bomTreeState.isDragEnabled,
              isDragging: node.id == bomTreeState.draggingNodeId,
              isDropTarget: node.id == bomTreeState.dropTargetNodeId,
              onTap: () {
                final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
                if (bomTreeNotifier is BomTreeNotifier) {
                  bomTreeNotifier.selectNode(node.id);
                }
                widget.onNodeSelected?.call(node.id);
              },
              onExpansionChanged: (expanded) {
                final bomTreeNotifier = ref.read(bomTreeProvider.notifier);
                if (bomTreeNotifier is BomTreeNotifier) {
                  bomTreeNotifier.toggleNodeExpansion(node.id);
                }
              },
              onEdit: node.bomItem != null
                  ? () => widget.onBomItemEdit?.call(node.bomItem!.id)
                  : null,
              onDelete: node.bomItem != null
                  ? () => widget.onBomItemDelete?.call(node.bomItem!.id)
                  : null,
            );
          },
        ),
      ),
    );
  }

  // 统计辅助方法
  int _getTotalItems(List<TreeNode> tree) {
    int count = 0;
    for (final node in tree) {
      count += _countItems(node);
    }
    return count;
  }

  int _countItems(TreeNode node) {
    int count = node.bomItem != null ? 1 : 0;
    for (final child in node.children) {
      count += _countItems(child);
    }
    return count;
  }

  double _getTotalCost(List<TreeNode> tree) {
    double total = 0.0;
    for (final node in tree) {
      total += _calculateCost(node);
    }
    return total;
  }

  double _calculateCost(TreeNode node) {
    double cost = 0.0;
    if (node.bomItem != null) {
      cost = (node.bomItem!.estimatedPrice ?? 0) * node.bomItem!.quantity;
    }
    for (final child in node.children) {
      cost += _calculateCost(child);
    }
    return cost;
  }

  int _getCategoryCount(List<TreeNode> tree) {
    return tree.length;
  }
}