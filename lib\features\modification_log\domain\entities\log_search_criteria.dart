import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'log_search_criteria.freezed.dart';
part 'log_search_criteria.g.dart';

/// 日志搜索条件实体
/// 用于定义日志搜索的过滤条件
@freezed
class LogSearchCriteria with _$LogSearchCriteria {
  const factory LogSearchCriteria({
    /// 关键词搜索
    String? keyword,
    
    /// 项目ID
    String? projectId,
    
    /// 系统ID
    String? systemId,
    
    /// 作者ID
    String? authorId,
    
    /// 日志状态列表
    List<LogStatus>? statuses,
    
    /// 难度级别列表
    List<DifficultyLevel>? difficulties,
    
    /// 开始日期
    DateTime? startDate,
    
    /// 结束日期
    DateTime? endDate,
    
    /// 系统ID列表
    List<String>? systemIds,
    
    /// 限制返回数量
    int? limit,
    
    /// 分页偏移量
    int? offset,
    
    /// 排序字段
    String? sortBy,
    
    /// 是否降序排序
    @Default(false) bool descending,
    
    /// 是否包含已删除的日志
    @Default(false) bool includeDeleted,
    
    /// 是否只返回有媒体的日志
    @Default(false) bool onlyWithMedia,
    
    /// 是否只返回有BOM物料的日志
    @Default(false) bool onlyWithBomItems,
  }) = _LogSearchCriteria;

  factory LogSearchCriteria.fromJson(Map<String, dynamic> json) =>
      _$LogSearchCriteriaFromJson(json);
}

/// LogSearchCriteria扩展方法
extension LogSearchCriteriaX on LogSearchCriteria {
  /// 是否有任何过滤条件
  bool get hasFilters {
    return keyword != null ||
        projectId != null ||
        systemId != null ||
        authorId != null ||
        statuses != null ||
        difficulties != null ||
        startDate != null ||
        endDate != null ||
        systemIds != null ||
        onlyWithMedia ||
        onlyWithBomItems;
  }
  
  /// 获取日期范围描述
  String? get dateRangeDescription {
    if (startDate != null && endDate != null) {
      return '${_formatDate(startDate!)} 至 ${_formatDate(endDate!)}';
    } else if (startDate != null) {
      return '${_formatDate(startDate!)} 之后';
    } else if (endDate != null) {
      return '${_formatDate(endDate!)} 之前';
    }
    return null;
  }
  
  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
  
  /// 获取状态描述
  String? get statusesDescription {
    if (statuses == null || statuses!.isEmpty) return null;
    if (statuses!.length == 1) {
      return statuses!.first.displayName;
    } else {
      return '多个状态';
    }
  }
  
  /// 获取难度级别描述
  String? get difficultiesDescription {
    if (difficulties == null || difficulties!.isEmpty) return null;
    if (difficulties!.length == 1) {
      return difficulties!.first.displayName;
    } else {
      return '多个难度级别';
    }
  }
  
  /// 创建新的搜索条件，添加状态过滤
  LogSearchCriteria addStatus(LogStatus status) {
    final currentStatuses = statuses ?? [];
    if (!currentStatuses.contains(status)) {
      return copyWith(
        statuses: [...currentStatuses, status],
      );
    }
    return this;
  }
  
  /// 创建新的搜索条件，移除状态过滤
  LogSearchCriteria removeStatus(LogStatus status) {
    final currentStatuses = statuses ?? [];
    if (currentStatuses.contains(status)) {
      return copyWith(
        statuses: currentStatuses.where((s) => s != status).toList(),
      );
    }
    return this;
  }
  
  /// 创建新的搜索条件，添加难度级别过滤
  LogSearchCriteria addDifficulty(DifficultyLevel difficulty) {
    final currentDifficulties = difficulties ?? [];
    if (!currentDifficulties.contains(difficulty)) {
      return copyWith(
        difficulties: [...currentDifficulties, difficulty],
      );
    }
    return this;
  }
  
  /// 创建新的搜索条件，移除难度级别过滤
  LogSearchCriteria removeDifficulty(DifficultyLevel difficulty) {
    final currentDifficulties = difficulties ?? [];
    if (currentDifficulties.contains(difficulty)) {
      return copyWith(
        difficulties: currentDifficulties.where((d) => d != difficulty).toList(),
      );
    }
    return this;
  }
  
  /// 创建新的搜索条件，清除所有过滤
  LogSearchCriteria clearFilters() {
    return copyWith(
      keyword: null,
      projectId: null,
      systemId: null,
      authorId: null,
      statuses: null,
      difficulties: null,
      startDate: null,
      endDate: null,
      systemIds: null,
      onlyWithMedia: false,
      onlyWithBomItems: false,
    );
  }
}