import 'package:fpdart/fpdart.dart';
import 'package:mockito/mockito.dart';

import '../../lib/core/errors/failures.dart';
import '../../lib/features/material/domain/entities/material_review.dart';
import '../../lib/features/material/domain/repositories/material_review_repository.dart';

/// Mock类：MaterialReviewRepository
/// 用于测试Material Review相关功能
class MockMaterialReviewRepository extends Mock implements MaterialReviewRepository {
  
  @override
  Future<Either<Failure, MaterialReview>> createReview(MaterialReview review) {
    return super.noSuchMethod(
      Invocation.method(#createReview, [review]),
      returnValue: Future.value(
        Right(MaterialReview(
          id: 'test-review-id',
          materialId: review.materialId,
          userId: review.userId,
          userName: review.userName,
          userAvatarUrl: review.userAvatarUrl,
          content: review.content,
          rating: review.rating,
          qualityRating: review.qualityRating,
          valueRating: review.valueRating,
          durabilityRating: review.durabilityRating,
          installationRating: review.installationRating,
          vehicleType: review.vehicleType,
          systemType: review.systemType,
          usageDuration: review.usageDuration,
          pros: review.pros,
          cons: review.cons,
          tips: review.tips,
          isVerifiedPurchase: review.isVerifiedPurchase,
          purchaseDate: review.purchaseDate,
          imageUrls: review.imageUrls,
          videoUrls: review.videoUrls,
          likedByUserIds: review.likedByUserIds,
          helpfulUserIds: review.helpfulUserIds,
          helpfulCount: review.helpfulCount,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, MaterialReview>> updateReview(MaterialReview review) {
    return super.noSuchMethod(
      Invocation.method(#updateReview, [review]),
      returnValue: Future.value(Right(review)),
    );
  }

  @override
  Future<Either<Failure, void>> deleteReview(String reviewId) {
    return super.noSuchMethod(
      Invocation.method(#deleteReview, [reviewId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, MaterialReview>> getReview(String reviewId) {
    return super.noSuchMethod(
      Invocation.method(#getReview, [reviewId]),
      returnValue: Future.value(
        Right(MaterialReview(
          id: reviewId,
          materialId: 'test-material-id',
          userId: 'test-user-id',
          userName: 'Test User',
          userAvatarUrl: null,
          content: 'Test review content',
          rating: 4.5,
          qualityRating: 5.0,
          valueRating: 4.0,
          durabilityRating: 4.5,
          installationRating: 4.0,
          vehicleType: 'Test Vehicle',
          systemType: 'Test System',
          usageDuration: '3 months',
          pros: ['Good quality'],
          cons: ['Expensive'],
          tips: ['Read manual carefully'],
          isVerifiedPurchase: true,
          purchaseDate: DateTime.now().subtract(const Duration(days: 90)),
          imageUrls: [],
          videoUrls: [],
          likedByUserIds: [],
          helpfulUserIds: [],
          helpfulCount: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getMaterialReviews(
    String materialId, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getMaterialReviews, [materialId], {
        #filterCriteria: filterCriteria,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getUserReviews(
    String userId, {
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getUserReviews, [userId], {
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, MaterialReviewSummary>> getMaterialReviewSummary(
    String materialId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#getMaterialReviewSummary, [materialId]),
      returnValue: Future.value(
        Right(MaterialReviewSummary(
          materialId: materialId,
          totalReviews: 0,
          averageRating: 0.0,
          qualityAverage: 0.0,
          valueAverage: 0.0,
          durabilityAverage: 0.0,
          installationAverage: 0.0,
          ratingDistribution: const {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
          verifiedPurchaseCount: 0,
          topPros: const [],
          topCons: const [],
          commonTips: const [],
          recommendationScore: 0.0,
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, void>> markReviewAsHelpful(
    String reviewId,
    String userId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#markReviewAsHelpful, [reviewId, userId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> unmarkReviewAsHelpful(
    String reviewId,
    String userId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#unmarkReviewAsHelpful, [reviewId, userId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> likeReview(
    String reviewId,
    String userId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#likeReview, [reviewId, userId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> unlikeReview(
    String reviewId,
    String userId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#unlikeReview, [reviewId, userId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, bool>> hasUserReviewedMaterial(
    String materialId,
    String userId,
  ) {
    return super.noSuchMethod(
      Invocation.method(#hasUserReviewedMaterial, [materialId, userId]),
      returnValue: Future.value(const Right(false)),
    );
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getPopularReviews({
    int limit = 10,
    int timeRange = 30,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getPopularReviews, [], {
        #limit: limit,
        #timeRange: timeRange,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> searchReviews(
    String query, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#searchReviews, [query], {
        #filterCriteria: filterCriteria,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, Map<String, MaterialReviewSummary>>> 
      getBatchReviewSummaries(List<String> materialIds) {
    return super.noSuchMethod(
      Invocation.method(#getBatchReviewSummaries, [materialIds]),
      returnValue: Future.value(const Right({})),
    );
  }

  @override
  Future<Either<Failure, void>> reportReview(
    String reviewId,
    String reporterId,
    String reason,
  ) {
    return super.noSuchMethod(
      Invocation.method(#reportReview, [reviewId, reporterId, reason]),
      returnValue: Future.value(const Right(null)),
    );
  }
}
