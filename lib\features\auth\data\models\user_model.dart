import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart' as domain;

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String email,
    String? name,
    String? avatarUrl,
    String? createdAt,
    String? updatedAt,
    @Default(false) bool isAnonymous,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
}

extension UserModelX on UserModel {
  domain.User toEntity() {
    return domain.User(
      id: id,
      email: email,
      name: name,
      avatarUrl: avatarUrl,
      createdAt: createdAt != null ? DateTime.tryParse(createdAt!) : null,
      updatedAt: updatedAt != null ? DateTime.tryParse(updatedAt!) : null,
      isAnonymous: isAnonymous,
    );
  }
}

extension UserX on domain.User {
  UserModel toModel() {
    return UserModel(
      id: id,
      email: email,
      name: name,
      avatarUrl: avatarUrl,
      createdAt: createdAt?.toIso8601String(),
      updatedAt: updatedAt?.toIso8601String(),
      isAnonymous: isAnonymous,
    );
  }
}