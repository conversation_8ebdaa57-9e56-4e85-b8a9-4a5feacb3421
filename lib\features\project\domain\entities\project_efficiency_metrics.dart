/// 项目效率指标实体
class ProjectEfficiencyMetrics {
  final String projectId;
  final double overallEfficiency;
  final double timeEfficiency;
  final double costEfficiency;
  final double resourceUtilization;
  final int completedTasksPerWeek;
  final double averageTaskCompletionTime;
  final DateTime lastUpdated;

  const ProjectEfficiencyMetrics({
    required this.projectId,
    required this.overallEfficiency,
    required this.timeEfficiency,
    required this.costEfficiency,
    required this.resourceUtilization,
    required this.completedTasksPerWeek,
    required this.averageTaskCompletionTime,
    required this.lastUpdated,
  });

  factory ProjectEfficiencyMetrics.fromJson(Map<String, dynamic> json) {
    return ProjectEfficiencyMetrics(
      projectId: json['project_id'],
      overallEfficiency: (json['overall_efficiency'] as num).toDouble(),
      timeEfficiency: (json['time_efficiency'] as num).toDouble(),
      costEfficiency: (json['cost_efficiency'] as num).toDouble(),
      resourceUtilization: (json['resource_utilization'] as num).toDouble(),
      completedTasksPerWeek: json['completed_tasks_per_week'],
      averageTaskCompletionTime: (json['average_task_completion_time'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'project_id': projectId,
      'overall_efficiency': overallEfficiency,
      'time_efficiency': timeEfficiency,
      'cost_efficiency': costEfficiency,
      'resource_utilization': resourceUtilization,
      'completed_tasks_per_week': completedTasksPerWeek,
      'average_task_completion_time': averageTaskCompletionTime,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}