/// VanHub Card Component 2.0
/// 
/// 国际化高端卡片组件，支持3D悬浮效果和智能交互
/// 
/// 特性：
/// - 6种变体：elevated, outlined, filled, glass, gradient, interactive
/// - 智能悬浮效果：3D变换 + 阴影动画
/// - 内容自适应：自动调整内边距和布局
/// - 手势支持：点击、长按、拖拽
/// - 响应式设计：移动端/平板/桌面适配
/// - 60FPS流畅动画

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import '../utils/responsive_utils.dart';

/// 卡片变体枚举
enum VanHubCardVariant {
  elevated,     // 高度卡片 - 阴影效果
  outlined,     // 轮廓卡片 - 边框样式
  filled,       // 填充卡片 - 背景色
  glass,        // 玻璃态卡片 - 毛玻璃效果
  gradient,     // 渐变卡片 - 渐变背景
  interactive,  // 交互卡片 - 3D悬浮效果
}

/// 卡片尺寸枚举
enum VanHubCardSize {
  sm,   // 小卡片
  md,   // 中等卡片
  lg,   // 大卡片
  xl,   // 超大卡片
}

/// 卡片配置类
class VanHubCardConfig {
  final double borderRadius;
  final EdgeInsets padding;
  final double elevation;
  final Color backgroundColor;
  final Color borderColor;
  final Gradient? gradient;
  final List<BoxShadow>? boxShadow;

  const VanHubCardConfig({
    required this.borderRadius,
    required this.padding,
    required this.elevation,
    required this.backgroundColor,
    required this.borderColor,
    this.gradient,
    this.boxShadow,
  });

  /// 获取变体配置
  static VanHubCardConfig getConfig(
    BuildContext context,
    VanHubCardVariant variant,
    VanHubCardSize size, {
    EmotionalState? emotionalState,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final padding = _getPadding(context, size);
    final borderRadius = _getBorderRadius(size);

    switch (variant) {
      case VanHubCardVariant.elevated:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 4.0,
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
          borderColor: Colors.transparent,
          boxShadow: [
            BoxShadow(
              color: VanHubSemanticColors.shadowMedium,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        );

      case VanHubCardVariant.outlined:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 0.0,
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
          borderColor: VanHubSemanticColors.getBorderColor(context),
        );

      case VanHubCardVariant.filled:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 0.0,
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
          borderColor: Colors.transparent,
        );

      case VanHubCardVariant.glass:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 0.0,
          backgroundColor: isDark 
              ? VanHubSemanticColors.glassBackgroundDark
              : VanHubSemanticColors.glassBackground,
          borderColor: isDark
              ? VanHubSemanticColors.glassBorderDark
              : VanHubSemanticColors.glassBorder,
        );

      case VanHubCardVariant.gradient:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 2.0,
          backgroundColor: Colors.transparent,
          borderColor: Colors.transparent,
          gradient: emotionalState != null
              ? VanHubBrandColors.getEmotionalGradient(emotionalState)
              : VanHubBrandColors.primaryGradient,
        );

      case VanHubCardVariant.interactive:
        return VanHubCardConfig(
          borderRadius: borderRadius,
          padding: padding,
          elevation: 2.0,
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
          borderColor: Colors.transparent,
          boxShadow: [
            BoxShadow(
              color: VanHubSemanticColors.shadowLight,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        );
    }
  }

  /// 获取内边距
  static EdgeInsets _getPadding(BuildContext context, VanHubCardSize size) {
    final multiplier = VanHubResponsiveUtils.isDesktop(context) ? 1.2 : 1.0;
    
    switch (size) {
      case VanHubCardSize.sm:
        return EdgeInsets.all(VanHubResponsiveSpacing.md * multiplier);
      case VanHubCardSize.md:
        return EdgeInsets.all(VanHubResponsiveSpacing.lg * multiplier);
      case VanHubCardSize.lg:
        return EdgeInsets.all(VanHubResponsiveSpacing.xl * multiplier);
      case VanHubCardSize.xl:
        return EdgeInsets.all(VanHubResponsiveSpacing.xxl * multiplier);
    }
  }

  /// 获取圆角半径
  static double _getBorderRadius(VanHubCardSize size) {
    switch (size) {
      case VanHubCardSize.sm:
        return 8.0;
      case VanHubCardSize.md:
        return 12.0;
      case VanHubCardSize.lg:
        return 16.0;
      case VanHubCardSize.xl:
        return 20.0;
    }
  }
}

/// VanHub Card 2.0 - 高端卡片组件
class VanHubCardV2 extends StatefulWidget {
  final Widget child;
  final VanHubCardVariant variant;
  final VanHubCardSize size;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;
  final bool enableHoverEffect;
  final bool enablePressEffect;
  final bool enable3DEffect;
  final EmotionalState? emotionalState;
  final EdgeInsets? customPadding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  
  // 动画配置
  final Duration hoverDuration;
  final Duration pressDuration;
  final Curve animationCurve;

  const VanHubCardV2({
    Key? key,
    required this.child,
    this.variant = VanHubCardVariant.elevated,
    this.size = VanHubCardSize.md,
    this.onTap,
    this.onLongPress,
    this.onDoubleTap,
    this.enableHoverEffect = true,
    this.enablePressEffect = true,
    this.enable3DEffect = false,
    this.emotionalState,
    this.customPadding,
    this.margin,
    this.width,
    this.height,
    this.hoverDuration = VanHubAnimationDurations.fast,
    this.pressDuration = VanHubAnimationDurations.ultraFast,
    this.animationCurve = VanHubAnimationCurves.easeOut,
  }) : super(key: key);

  /// 高度卡片构造函数
  const VanHubCardV2.elevated({
    Key? key,
    required Widget child,
    VanHubCardSize size = VanHubCardSize.md,
    VoidCallback? onTap,
    bool enableHoverEffect = true,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) : this(
          key: key,
          child: child,
          variant: VanHubCardVariant.elevated,
          size: size,
          onTap: onTap,
          enableHoverEffect: enableHoverEffect,
          customPadding: padding,
          margin: margin,
        );

  /// 轮廓卡片构造函数
  const VanHubCardV2.outlined({
    Key? key,
    required Widget child,
    VanHubCardSize size = VanHubCardSize.md,
    VoidCallback? onTap,
    bool enableHoverEffect = true,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) : this(
          key: key,
          child: child,
          variant: VanHubCardVariant.outlined,
          size: size,
          onTap: onTap,
          enableHoverEffect: enableHoverEffect,
          customPadding: padding,
          margin: margin,
        );

  /// 交互卡片构造函数
  const VanHubCardV2.interactive({
    Key? key,
    required Widget child,
    VanHubCardSize size = VanHubCardSize.md,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    bool enable3DEffect = true,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) : this(
          key: key,
          child: child,
          variant: VanHubCardVariant.interactive,
          size: size,
          onTap: onTap,
          onLongPress: onLongPress,
          enableHoverEffect: true,
          enablePressEffect: true,
          enable3DEffect: enable3DEffect,
          customPadding: padding,
          margin: margin,
        );

  @override
  State<VanHubCardV2> createState() => _VanHubCardV2State();
}

/// VanHub Card 2.0 状态类
class _VanHubCardV2State extends State<VanHubCardV2>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late AnimationController _3dController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _rotationXAnimation;
  late Animation<double> _rotationYAnimation;

  bool _isHovered = false;
  bool _isPressed = false;
  Offset _mousePosition = Offset.zero;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _hoverController = AnimationController(
      duration: widget.hoverDuration,
      vsync: this,
    );

    _pressController = AnimationController(
      duration: widget.pressDuration,
      vsync: this,
    );

    _3dController = AnimationController(
      duration: widget.hoverDuration,
      vsync: this,
    );

    // 初始化动画
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: widget.animationCurve,
    ));

    _elevationAnimation = Tween<double>(
      begin: 1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: widget.animationCurve,
    ));

    _rotationXAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _3dController,
      curve: widget.animationCurve,
    ));

    _rotationYAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _3dController,
      curve: widget.animationCurve,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    _3dController.dispose();
    super.dispose();
  }

  /// 处理悬停进入
  void _onHoverEnter(PointerEnterEvent event) {
    if (!mounted || !widget.enableHoverEffect) return;

    if (mounted) {
      setState(() {
        _isHovered = true;
        _mousePosition = event.localPosition;
      });
    }

    if (mounted && _hoverController.isCompleted == false) {
      _hoverController.forward();
    }

    if (widget.enable3DEffect && mounted) {
      _update3DRotation(event.localPosition);
    }

    // 触觉反馈
    if (mounted && VanHubResponsiveUtils.isMobile(context)) {
      HapticFeedback.lightImpact();
    }
  }

  /// 处理悬停退出
  void _onHoverExit(PointerExitEvent event) {
    if (!mounted || !widget.enableHoverEffect) return;

    if (mounted) {
      setState(() {
        _isHovered = false;
      });
    }

    if (mounted && _hoverController.isCompleted == true) {
      _hoverController.reverse();
    }
    if (mounted && _3dController.isCompleted == true) {
      _3dController.reverse();
    }
  }

  /// 处理鼠标移动
  void _onMouseMove(PointerHoverEvent event) {
    if (!mounted || !widget.enable3DEffect || !_isHovered) return;

    if (mounted) {
      setState(() {
        _mousePosition = event.localPosition;
      });
    }

    if (mounted) {
      _update3DRotation(event.localPosition);
    }
  }

  /// 更新3D旋转
  void _update3DRotation(Offset position) {
    if (!mounted || !widget.enable3DEffect) return;

    try {
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null || !renderBox.hasSize) return;

      final size = renderBox.size;
      if (size.width <= 0 || size.height <= 0) return;

      final centerX = size.width / 2;
      final centerY = size.height / 2;

      // 计算旋转角度 (-0.1 到 0.1 弧度)
      final rotationY = ((position.dx - centerX) / centerX * 0.1).clamp(-0.1, 0.1);
      final rotationX = ((centerY - position.dy) / centerY * 0.1).clamp(-0.1, 0.1);

      if (mounted) {
        _rotationXAnimation = Tween<double>(
          begin: _rotationXAnimation.value,
          end: rotationX,
        ).animate(CurvedAnimation(
          parent: _3dController,
          curve: widget.animationCurve,
        ));

        _rotationYAnimation = Tween<double>(
          begin: _rotationYAnimation.value,
          end: rotationY,
        ).animate(CurvedAnimation(
          parent: _3dController,
          curve: widget.animationCurve,
        ));

        if (_3dController.isCompleted == false) {
          _3dController.forward();
        }
      }
    } catch (e) {
      // 静默处理渲染错误
      debugPrint('3D rotation update error: $e');
    }
  }

  /// 处理按压开始
  void _onTapDown(TapDownDetails details) {
    if (!mounted || !widget.enablePressEffect) return;

    if (mounted) {
      setState(() {
        _isPressed = true;
      });
    }

    if (mounted && !_pressController.isAnimating) {
      _pressController.forward();
    }

    // 触觉反馈
    if (mounted) {
      HapticFeedback.mediumImpact();
    }
  }

  /// 处理按压结束
  void _onTapUp(TapUpDetails details) {
    if (!mounted || !widget.enablePressEffect) return;

    if (mounted) {
      setState(() {
        _isPressed = false;
      });
    }

    if (mounted && !_pressController.isAnimating) {
      _pressController.reverse();
    }
  }

  /// 处理按压取消
  void _onTapCancel() {
    if (!mounted || !widget.enablePressEffect) return;

    if (mounted) {
      setState(() {
        _isPressed = false;
      });
    }

    if (mounted && !_pressController.isAnimating) {
      _pressController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!mounted) {
      return const SizedBox.shrink();
    }

    try {
      final config = VanHubCardConfig.getConfig(
        context,
        widget.variant,
        widget.size,
        emotionalState: widget.emotionalState,
      );

      final hasInteraction = widget.onTap != null ||
                            widget.onLongPress != null ||
                            widget.onDoubleTap != null;

      return AnimatedBuilder(
        animation: Listenable.merge([
          _hoverController,
          _pressController,
          _3dController,
        ]),
        builder: (context, child) {
          if (!mounted) {
            return const SizedBox.shrink();
          }

          return LayoutBuilder(
            builder: (context, constraints) {
              // 确保有有效的约束
              if (constraints.maxWidth.isInfinite || constraints.maxHeight.isInfinite) {
                return Container(
                  width: widget.width ?? 300,
                  height: widget.height ?? 200,
                  padding: widget.customPadding ?? const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                  child: widget.child,
                );
              }

              // 确保约束值在合理范围内
              if (constraints.maxWidth <= 0 || constraints.maxHeight <= 0) {
                return const SizedBox.shrink();
              }

            return Container(
              margin: widget.margin,
              width: widget.width,
              height: widget.height,
              constraints: BoxConstraints(
                minWidth: 0,
                minHeight: 0,
                maxWidth: constraints.maxWidth,
                maxHeight: constraints.maxHeight,
              ),
              child: Transform(
                alignment: Alignment.center,
                transform: _buildSafeTransform(),
                child: MouseRegion(
                  onEnter: _onHoverEnter,
                  onExit: _onHoverExit,
                  onHover: _onMouseMove,
                  child: GestureDetector(
                    onTap: widget.onTap,
                    onLongPress: widget.onLongPress,
                    onDoubleTap: widget.onDoubleTap,
                    onTapDown: hasInteraction ? _onTapDown : null,
                    onTapUp: hasInteraction ? _onTapUp : null,
                    onTapCancel: _onTapCancel,
                    child: AnimatedContainer(
                      duration: widget.hoverDuration,
                      curve: widget.animationCurve,
                      padding: widget.customPadding ?? config.padding,
                      decoration: BoxDecoration(
                        color: config.backgroundColor,
                        gradient: config.gradient,
                        borderRadius: BorderRadius.circular(config.borderRadius),
                        border: config.borderColor != Colors.transparent
                            ? Border.all(color: config.borderColor, width: 1.0)
                            : null,
                        boxShadow: _getAnimatedBoxShadow(config),
                      ),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
    } catch (e) {
      // 如果渲染失败，返回简化版本
      debugPrint('VanHubCardV2 渲染错误: $e');
      return Container(
        margin: widget.margin,
        width: widget.width,
        height: widget.height,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        child: widget.child,
      );
    }
  }

  /// 构建安全的Transform矩阵
  Matrix4 _buildSafeTransform() {
    try {
      final rotationX = _rotationXAnimation.value.clamp(-0.5, 0.5);
      final rotationY = _rotationYAnimation.value.clamp(-0.5, 0.5);
      final scale = _scaleAnimation.value.clamp(0.5, 1.5);

      // 检查值是否有效
      if (rotationX.isNaN || rotationY.isNaN || scale.isNaN) {
        return Matrix4.identity();
      }

      return Matrix4.identity()
        ..setEntry(3, 2, 0.001) // 透视效果
        ..rotateX(rotationX)
        ..rotateY(rotationY)
        ..scale(scale);
    } catch (e) {
      debugPrint('Transform构建错误: $e');
      return Matrix4.identity();
    }
  }

  /// 获取动画阴影
  List<BoxShadow>? _getAnimatedBoxShadow(VanHubCardConfig config) {
    if (config.boxShadow == null) return null;

    final elevationMultiplier = _elevationAnimation.value;

    return config.boxShadow!.map((shadow) {
      return BoxShadow(
        color: shadow.color,
        blurRadius: shadow.blurRadius * elevationMultiplier,
        offset: shadow.offset * elevationMultiplier,
        spreadRadius: shadow.spreadRadius,
      );
    }).toList();
  }
}
