# 设置PowerShell编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 显示当前环境变量
Write-Host "当前环境变量设置:" -ForegroundColor Cyan
Write-Host "PUB_HOSTED_URL = $env:PUB_HOSTED_URL" -ForegroundColor Yellow
Write-Host "FLUTTER_STORAGE_BASE_URL = $env:FLUTTER_STORAGE_BASE_URL" -ForegroundColor Yellow
Write-Host ""

# 临时覆盖环境变量，使用官方源
$env:PUB_HOSTED_URL = $null
$env:FLUTTER_STORAGE_BASE_URL = $null

Write-Host "已临时设置为官方源" -ForegroundColor Green
Write-Host ""

# 尝试删除Flutter锁文件
$lockFile = "$env:LOCALAPPDATA\Temp\flutter_tool.lock"
if (Test-Path $lockFile) {
    Write-Host "发现Flutter锁文件，尝试删除..." -ForegroundColor Yellow
    try {
        Remove-Item $lockFile -Force
        Write-Host "锁文件已成功删除" -ForegroundColor Green
    } catch {
        Write-Host "无法删除锁文件: $_" -ForegroundColor Red
    }
    Write-Host ""
}

# 执行Flutter命令
Write-Host "执行Flutter命令..." -ForegroundColor Cyan
flutter pub get

Write-Host "`n完成！" -ForegroundColor Green
Write-Host "如果要永久修改环境变量，请在系统环境变量中删除以下变量：" -ForegroundColor Yellow
Write-Host "PUB_HOSTED_URL 和 FLUTTER_STORAGE_BASE_URL" -ForegroundColor Yellow

# 显示如何永久修改环境变量的命令
Write-Host "`n要永久修改环境变量，可以在管理员PowerShell中运行以下命令：" -ForegroundColor Cyan
Write-Host '[Environment]::SetEnvironmentVariable("PUB_HOSTED_URL", $null, "User")' -ForegroundColor Magenta
Write-Host '[Environment]::SetEnvironmentVariable("FLUTTER_STORAGE_BASE_URL", $null, "User")' -ForegroundColor Magenta