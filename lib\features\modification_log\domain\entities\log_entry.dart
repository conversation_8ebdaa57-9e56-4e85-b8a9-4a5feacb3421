import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'log_entry.freezed.dart';
part 'log_entry.g.dart';

/// 日志条目实体
/// 代表改装过程中的一个具体步骤或记录
@freezed
class LogEntry with _$LogEntry {
  const factory LogEntry({
    /// 日志唯一标识
    required String id,
    
    /// 关联的项目ID
    required String projectId,
    
    /// 关联的系统ID
    required String systemId,
    
    /// 日志标题
    required String title,
    
    /// 日志内容（富文本）
    required String content,
    
    /// 日志记录的日期
    required DateTime logDate,
    
    /// 作者ID
    required String authorId,
    
    /// 作者名称
    String? authorName,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 日志状态
    @Default(LogStatus.published) LogStatus status,
    
    /// 难度级别
    @Default(DifficultyLevel.intermediate) DifficultyLevel difficulty,
    
    /// 耗时（分钟）
    @Default(0) int timeSpentMinutes,
    
    /// 关联的媒体ID列表
    @Default([]) List<String> mediaIds,
    
    /// 关联的BOM物料ID列表
    @Default([]) List<String> relatedBomItemIds,

    /// 总成本
    @Default(0.0) double totalCost,

    /// 预算成本
    double? budgetCost,

    /// 实际耗时（分钟）
    int? actualTimeMinutes,

    /// 预计耗时（分钟）
    int? estimatedTimeMinutes,

    /// 技能难度评级 (1-5星)
    @Default(3) int skillRating,

    /// 用户评价 (1-5星)
    double? userRating,

    /// 标签列表
    @Default([]) List<String> tags,

    /// 位置信息
    String? location,

    /// 天气信息
    String? weather,

    /// 协作者ID列表
    @Default([]) List<String> collaboratorIds,

    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,

    /// 收藏用户ID列表
    @Default([]) List<String> bookmarkedByUserIds,

    /// 浏览次数
    @Default(0) int viewCount,

    /// 被引用次数
    @Default(0) int referenceCount,

    /// 是否为里程碑
    @Default(false) bool isMilestone,

    /// 里程碑图标名称（当isMilestone为true时使用）
    String? milestoneIconName,

    /// 里程碑颜色（当isMilestone为true时使用）
    String? milestoneColorHex,

    /// 里程碑优先级（当isMilestone为true时使用）
    MilestonePriority? milestonePriority,

    /// 是否为问题记录
    @Default(false) bool isProblemReport,

    /// 问题解决状态
    ProblemStatus? problemStatus,

    /// 学习价值评分
    double? learningValue,

    /// 元数据
    Map<String, dynamic>? metadata,
  }) = _LogEntry;

  factory LogEntry.fromJson(Map<String, dynamic> json) =>
      _$LogEntryFromJson(json);
}

/// LogEntry扩展方法
extension LogEntryX on LogEntry {
  /// 获取日志状态显示文本
  String get statusDisplayText => status.displayName;
  
  /// 获取难度级别显示文本
  String get difficultyDisplayText => difficulty.displayName;
  
  /// 获取耗时显示文本
  String get timeSpentDisplayText {
    if (timeSpentMinutes < 60) {
      return '$timeSpentMinutes 分钟';
    } else {
      final hours = timeSpentMinutes ~/ 60;
      final minutes = timeSpentMinutes % 60;
      return '$hours 小时 ${minutes > 0 ? '$minutes 分钟' : ''}';
    }
  }
  
  /// 获取日志摘要（截取内容前100个字符）
  String get summary {
    // 移除HTML标签
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    
    if (plainText.length <= 100) {
      return plainText;
    } else {
      return '${plainText.substring(0, 97)}...';
    }
  }
  
  /// 获取关联的媒体数量
  int get mediaCount => mediaIds.length;
  
  /// 获取关联的物料数量
  int get bomItemCount => relatedBomItemIds.length;
  
  /// 是否为草稿
  bool get isDraft => status == LogStatus.draft;
  
  /// 是否已发布
  bool get isPublished => status == LogStatus.published;
  
  /// 是否已归档
  bool get isArchived => status == LogStatus.archived;
  
  /// 获取最后更新时间描述
  String get lastUpdateDescription {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}