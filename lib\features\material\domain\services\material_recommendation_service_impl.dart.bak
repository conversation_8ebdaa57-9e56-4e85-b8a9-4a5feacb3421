import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../project/domain/repositories/project_repository.dart';
import '../../../bom/domain/repositories/bom_repository.dart';
import '../entities/material.dart';
import '../entities/material_recommendation.dart';
import '../repositories/material_repository.dart';
import 'material_recommendation_service.dart';

/// 材料推荐服务实现
class MaterialRecommendationServiceImpl implements MaterialRecommendationService {
  final MaterialRepository materialRepository;
  final ProjectRepository projectRepository;
  final BomRepository bomRepository;

  const MaterialRecommendationServiceImpl({
    required this.materialRepository,
    required this.projectRepository,
    required this.bomRepository,
  });

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForProject(
    String projectId, {
    int limit = 10,
  }) async {
    try {
      if (projectId.isEmpty) {
        return const Left(ValidationFailure(message: '项目ID不能为空'));
      }

      // 获取项目信息
      final projectResult = await projectRepository.getProjectById(projectId);

      return projectResult.fold(
        (failure) => Left(failure),
        (project) async {
          // 获取项目的BOM项目
          final bomResult = await bomRepository.getProjectBomItems(projectId);

          return bomResult.fold(
            (failure) => Left(failure),
            (bomItems) async {
              // 获取用户的所有材料
              final materialsResult = await materialRepository.getUserMaterials(project.authorId);

              return materialsResult.fold(
                (failure) => Left(failure),
                (materials) {
                  // 已使用的材料ID列表
                  final usedMaterialIds = bomItems.map((item) => item.materialId).toSet();

                  // 过滤掉已经在BOM中的材料
                  final availableMaterials = materials
                      .where((material) => !usedMaterialIds.contains(material.id))
                      .toList();

                  // 按分类统计BOM中的材料
                  final categoryCount = <String, int>{};
                  for (final item in bomItems) {
                    categoryCount[item.category] = (categoryCount[item.category] ?? 0) + 1;
                  }

                  // 计算推荐分数
                  final recommendations = <MaterialRecommendation>[];

                  for (final material in availableMaterials) {
                    // 基础分数
                    double score = 50.0;

                    // 权重因素
                    final weightFactors = <String, double>{};

                    // 1. 分类匹配权重
                    final categoryWeight = categoryCount[material.category] != null
                        ? (categoryCount[material.category]! * 5.0)
                        : 0.0;
                    score += categoryWeight;
                    weightFactors['category_match'] = categoryWeight;

                    // 2. 使用频率权重
                    final usageWeight = material.usageCount * 2.0;
                    score += usageWeight;
                    weightFactors['usage_frequency'] = usageWeight;

                    // 3. 最近使用权重
                    double recencyWeight = 0.0;
                    if (material.lastUsedAt != null) {
                      final daysSinceLastUse =
                          DateTime.now().difference(material.lastUsedAt!).inDays;
                      if (daysSinceLastUse < 30) {
                        recencyWeight = 30.0 - daysSinceLastUse;
                      }
                    }
                    score += recencyWeight;
                    weightFactors['recency'] = recencyWeight;

                    // 4. 车型匹配权重
                    double vehicleMatchWeight = 0.0;
                    if (material.metadata != null &&
                        material.metadata!['vehicle_brand'] != null &&
                        material.metadata!['vehicle_brand'] == project.vehicleBrand) {
                      vehicleMatchWeight += 15.0;
                    }
                    if (material.metadata != null &&
                        material.metadata!['vehicle_model'] != null &&
                        material.metadata!['vehicle_model'] == project.vehicleModel) {
                      vehicleMatchWeight += 15.0;
                    }
                    score += vehicleMatchWeight;
                    weightFactors['vehicle_match'] = vehicleMatchWeight;

                    // 5. 标签匹配权重
                    double tagMatchWeight = 0.0;
                    if (material.tags != null && material.tags!.isNotEmpty) {
                      for (final item in bomItems) {
                        if (item.tags != null && item.tags!.isNotEmpty) {
                          for (final tag in material.tags!) {
                            if (item.tags!.contains(tag)) {
                              tagMatchWeight += 5.0;
                              break;
                            }
                          }
                        }
                      }
                    }
                    score += tagMatchWeight;
                    weightFactors['tag_match'] = tagMatchWeight;

                    // 限制分数范围在0-100之间
                    score = score.clamp(0.0, 100.0);

                    // 生成推荐原因
                    String reason;
                    if (categoryWeight > 0) {
                      reason = '与项目中的${material.category}类材料相匹配';
                    } else if (usageWeight > 10) {
                      reason = '您经常使用此材料';
                    } else if (recencyWeight > 10) {
                      reason = '您最近使用过此材料';
                    } else if (vehicleMatchWeight > 0) {
                      reason = '适用于${project.vehicleBrand ?? ''}${project.vehicleModel ?? ''}';
                    } else if (tagMatchWeight > 0) {
                      reason = '与项目中的其他材料标签相匹配';
                    } else {
                      reason = '可能适用于您的项目';
                    }

                    recommendations.add(MaterialRecommendation(
                      material: material,
                      relevanceScore: score,
                      reason: reason,
                      type: RecommendationType.projectBased,
                      usageCount: material.usageCount,
                      weightFactors: weightFactors,
                    ));
                  }

                  // 按相关度排序
                  recommendations.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

                  // 限制结果数量
                  return Right(recommendations.take(limit).toList());
                },
              );
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为项目推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForSystem(
    String projectId,
    String systemType, {
    int limit = 10,
  }) async {
    try {
      if (projectId.isEmpty) {
        return const Left(ValidationFailure(message: '项目ID不能为空'));
      }

      if (systemType.isEmpty) {
        return const Left(ValidationFailure(message: '系统类型不能为空'));
      }

      // 获取项目信息
      final projectResult = await projectRepository.getProjectById(projectId);

      return projectResult.fold(
        (failure) => Left(failure),
        (project) async {
          // 获取项目的BOM项目
          final bomResult = await bomRepository.getProjectBomItems(projectId);

          return bomResult.fold(
            (failure) => Left(failure),
            (bomItems) async {
              // 获取用户的所有材料
              final materialsResult = await materialRepository.getUserMaterials(project.authorId);

              return materialsResult.fold(
                (failure) => Left(failure),
                (materials) {
                  // 已使用的材料ID列表
                  final usedMaterialIds = bomItems.map((item) => item.materialId).toSet();

                  // 过滤掉已经在BOM中的材料
                  final availableMaterials = materials
                      .where((material) => !usedMaterialIds.contains(material.id))
                      .toList();

                  // 系统相关的材料分类映射
                  final systemCategoryMap = _getSystemCategoryMap();
                  final relevantCategories = systemCategoryMap[systemType] ?? [];

                  // 计算推荐分数
                  final recommendations = <MaterialRecommendation>[];

                  for (final material in availableMaterials) {
                    // 基础分数
                    double score = 50.0;

                    // 权重因素
                    final weightFactors = <String, double>{};

                    // 1. 系统分类匹配权重
                    double categoryWeight = 0.0;
                    if (relevantCategories.contains(material.category)) {
                      categoryWeight = 30.0;
                    }
                    score += categoryWeight;
                    weightFactors['system_category_match'] = categoryWeight;

                    // 2. 使用频率权重
                    final usageWeight = material.usageCount * 2.0;
                    score += usageWeight;
                    weightFactors['usage_frequency'] = usageWeight;

                    // 3. 系统类型元数据匹配
                    double systemMetadataWeight = 0.0;
                    if (material.metadata != null &&
                        material.metadata!['system_type'] != null &&
                        material.metadata!['system_type'] == systemType) {
                      systemMetadataWeight = 25.0;
                    }
                    score += systemMetadataWeight;
                    weightFactors['system_metadata_match'] = systemMetadataWeight;

                    // 4. 标签匹配权重
                    double tagMatchWeight = 0.0;
                    if (material.tags != null && material.tags!.isNotEmpty) {
                      if (material.tags!.contains(systemType)) {
                        tagMatchWeight += 20.0;
                      }
                      
                      // 系统相关标签
                      final systemTags = _getSystemTags(systemType);
                      for (final tag in material.tags!) {
                        if (systemTags.contains(tag)) {
                          tagMatchWeight += 10.0;
                        }
                      }
                    }
                    score += tagMatchWeight;
                    weightFactors['tag_match'] = tagMatchWeight;

                    // 限制分数范围在0-100之间
                    score = score.clamp(0.0, 100.0);

                    // 生成推荐原因
                    String reason;
                    if (categoryWeight > 0) {
                      reason = '适用于$systemType系统的${material.category}';
                    } else if (systemMetadataWeight > 0) {
                      reason = '专为$systemType系统设计';
                    } else if (tagMatchWeight > 0) {
                      reason = '与$systemType系统标签匹配';
                    } else if (usageWeight > 10) {
                      reason = '您经常使用此材料';
                    } else {
                      reason = '可能适用于$systemType系统';
                    }

                    recommendations.add(MaterialRecommendation(
                      material: material,
                      relevanceScore: score,
                      reason: reason,
                      type: RecommendationType.systemBased,
                      usageCount: material.usageCount,
                      weightFactors: weightFactors,
                    ));
                  }

                  // 按相关度排序
                  recommendations.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

                  // 限制结果数量
                  return Right(recommendations.take(limit).toList());
                },
              );
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为系统推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendSimilarMaterials(
    String materialId, {
    int limit = 10,
  }) async {
    try {
      if (materialId.isEmpty) {
        return const Left(ValidationFailure(message: '材料ID不能为空'));
      }

      // 获取材料详情
      final materialResult = await materialRepository.getMaterialById(materialId);

      return materialResult.fold(
        (failure) => Left(failure),
        (material) async {
          // 获取用户的所有材料
          final materialsResult = await materialRepository.getUserMaterials(material.userId);

          return materialsResult.fold(
            (failure) => Left(failure),
            (materials) {
              // 过滤掉自身
              final otherMaterials = materials
                  .where((m) => m.id != materialId)
                  .toList();

              // 计算相似度分数
              final recommendations = <MaterialRecommendation>[];

              for (final otherMaterial in otherMaterials) {
                // 基础分数
                double score = 50.0;

                // 权重因素
                final weightFactors = <String, double>{};

                // 1. 分类匹配权重
                final categoryWeight = material.category == otherMaterial.category ? 25.0 : 0.0;
                score += categoryWeight;
                weightFactors['category_match'] = categoryWeight;

                // 2. 品牌匹配权重
                double brandWeight = 0.0;
                if (material.brand != null &&
                    material.brand!.isNotEmpty &&
                    material.brand == otherMaterial.brand) {
                  brandWeight = 20.0;
                }
                score += brandWeight;
                weightFactors['brand_match'] = brandWeight;

                // 3. 价格相似度权重
                double priceWeight = 0.0;
                if (material.price > 0 && otherMaterial.price > 0) {
                  final priceDiff = (material.price - otherMaterial.price).abs();
                  final priceRatio = priceDiff / material.price;
                  if (priceRatio < 0.1) {
                    priceWeight = 15.0;
                  } else if (priceRatio < 0.3) {
                    priceWeight = 10.0;
                  } else if (priceRatio < 0.5) {
                    priceWeight = 5.0;
                  }
                }
                score += priceWeight;
                weightFactors['price_similarity'] = priceWeight;

                // 4. 标签匹配权重
                double tagMatchWeight = 0.0;
                if (material.tags != null &&
                    material.tags!.isNotEmpty &&
                    otherMaterial.tags != null &&
                    otherMaterial.tags!.isNotEmpty) {
                  final commonTags = material.tags!
                      .where((tag) => otherMaterial.tags!.contains(tag))
                      .length;
                  tagMatchWeight = commonTags * 5.0;
                }
                score += tagMatchWeight;
                weightFactors['tag_match'] = tagMatchWeight;

                // 5. 规格相似度权重
                double specWeight = 0.0;
                if (material.specifications != null &&
                    material.specifications!.isNotEmpty &&
                    otherMaterial.specifications != null &&
                    otherMaterial.specifications!.isNotEmpty) {
                  if (material.specifications!.toLowerCase().contains(otherMaterial.specifications!.toLowerCase()) ||
                      otherMaterial.specifications!.toLowerCase().contains(material.specifications!.toLowerCase())) {
                    specWeight = 15.0;
                  }
                }
                score += specWeight;
                weightFactors['spec_similarity'] = specWeight;

                // 限制分数范围在0-100之间
                score = score.clamp(0.0, 100.0);

                // 生成推荐原因
                String reason;
                if (categoryWeight > 0 && brandWeight > 0) {
                  reason = '相同品牌和分类的材料';
                } else if (categoryWeight > 0) {
                  reason = '相同分类的材料';
                } else if (brandWeight > 0) {
                  reason = '相同品牌的材料';
                } else if (priceWeight > 10) {
                  reason = '价格相近的材料';
                } else if (tagMatchWeight > 10) {
                  reason = '具有相似标签的材料';
                } else if (specWeight > 0) {
                  reason = '规格相似的材料';
                } else {
                  reason = '可能相似的材料';
                }

                recommendations.add(MaterialRecommendation(
                  material: otherMaterial,
                  relevanceScore: score,
                  reason: reason,
                  type: RecommendationType.similarMaterial,
                  usageCount: otherMaterial.usageCount,
                  weightFactors: weightFactors,
                ));
              }

              // 按相关度排序
              recommendations.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

              // 限制结果数量
              return Right(recommendations.take(limit).toList());
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐相似材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendComplementaryMaterials(
    String materialId,
    {int limit = 10},
  ) async {
    try {
      if (materialId.isEmpty) {
        return const Left(ValidationFailure(message: '材料ID不能为空'));
      }

      // 获取材料详情
      final materialResult = await materialRepository.getMaterialById(materialId);

      return materialResult.fold(
        (failure) => Left(failure),
        (material) async {
          // 获取包含该材料的BOM项目
          final bomResult = await bomRepository.getBomItemsByMaterialId(materialId);

          return bomResult.fold(
            (failure) => Left(failure),
            (bomItems) async {
              if (bomItems.isEmpty) {
                return const Right([]);
              }

              // 收集包含该材料的项目ID
              final projectIds = bomItems.map((item) => item.projectId).toSet();

              // 获取这些项目的所有BOM项目
              final allBomItems = <String, int>{};
              for (final projectId in projectIds) {
                final projectBomResult = await bomRepository.getProjectBomItems(projectId);
                projectBomResult.fold(
                  (failure) => null,
                  (items) {
                    for (final item in items) {
                      if (item.materialId != materialId && item.materialId.isNotEmpty) {
                        allBomItems[item.materialId] = (allBomItems[item.materialId] ?? 0) + 1;
                      }
                    }
                  },
                );
              }

              // 按共现次数排序
              final sortedMaterialIds = allBomItems.entries.toList()
                ..sort((a, b) => b.value.compareTo(a.value));

              // 获取前N个材料详情
              final recommendations = <MaterialRecommendation>[];
              final topMaterialIds = sortedMaterialIds.take(limit).map((e) => e.key).toList();

              for (final id in topMaterialIds) {
                final complementaryResult = await materialRepository.getMaterialById(id);
                complementaryResult.fold(
                  (failure) => null,
                  (complementaryMaterial) {
                    final coOccurrenceCount = allBomItems[id] ?? 0;
                    final score = (coOccurrenceCount / projectIds.length) * 100;
                    
                    recommendations.add(MaterialRecommendation(
                      material: complementaryMaterial,
                      relevanceScore: score.clamp(0.0, 100.0),
                      reason: '在${coOccurrenceCount}个项目中与${material.name}一起使用',
                      type: RecommendationType.complementary,
                      usageCount: complementaryMaterial.usageCount,
                      complementaryMaterialIds: [materialId],
                    ));
                  },
                );
              }

              return Right(recommendations);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐配套材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendPopularMaterials(
    String userId,
    {String? category, int limit = 10},
  ) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      // 获取热门材料
      final result = await materialRepository.getPopularMaterials(userId, limit: limit);

      return result.fold(
        (failure) => Left(failure),
        (materials) {
          // 按分类过滤
          final filteredMaterials = category != null
              ? materials.where((m) => m.category == category).toList()
              : materials;

          // 转换为推荐结果
          final recommendations = filteredMaterials.map((material) {
            final score = material.usageCount > 0
                ? (material.usageCount * 10.0).clamp(0.0, 100.0)
                : 50.0;

            return MaterialRecommendation(
              material: material,
              relevanceScore: score,
              reason: '使用频率高的热门材料',
              type: RecommendationType.popular,
              usageCount: material.usageCount,
            );
          }).toList();

          // 按相关度排序
          recommendations.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

          // 限制结果数量
          return Right(recommendations.take(limit).toList());
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐热门材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendValueForMoneyMaterials(
    String userId,
    {String? category, int limit = 10},
  ) async {
    try {
      if (userId.isEmpty) {
        return const Left(ValidationFailure(message: '用户ID不能为空'));
      }

      // 获取用户材料
      final result = await materialRepository.getUserMaterials(userId);

      return result.fold(
        (failure) => Left(failure),
        (materials) {
          // 按分类过滤
          final filteredMaterials = category != null
              ? materials.where((m) => m.category == category).toList()
              : materials;

          // 计算性价比分数
          final recommendations = <MaterialRecommendation>[];

          // 按分类分组
          final materialsByCategory = <String, List<Material>>{};
          for (final material in filteredMaterials) {
            materialsByCategory[material.category] = (materialsByCategory[material.category] ?? [])
              ..add(material);
          }

          // 计算每个分类的平均价格
          final categoryAvgPrice = <String, double>{};
          for (final entry in materialsByCategory.entries) {
            final totalPrice = entry.value.fold<double>(
                0.0, (sum, material) => sum + material.price);
            categoryAvgPrice[entry.key] = totalPrice / entry.value.length;
          }

          // 计算性价比分数
          for (final material in filteredMaterials) {
            final avgPrice = categoryAvgPrice[material.category] ?? material.price;
            
            // 基础分数
            double score = 50.0;

            // 权重因素
            final weightFactors = <String, double>{};

            // 1. 价格权重（低于平均价格加分）
            double priceWeight = 0.0;
            if (avgPrice > 0) {
              final priceRatio = material.price / avgPrice;
              if (priceRatio < 0.7) {
                priceWeight = 30.0;
              } else if (priceRatio < 0.9) {
                priceWeight = 20.0;
              } else if (priceRatio < 1.0) {
                priceWeight = 10.0;
              }
            }
            score += priceWeight;
            weightFactors['price_value'] = priceWeight;

            // 2. 使用频率权重
            final usageWeight = material.usageCount * 2.0;
            score += usageWeight;
            weightFactors['usage_frequency'] = usageWeight;

            // 3. 品牌权重（知名品牌加分）
            double brandWeight = 0.0;
            if (material.brand != null && _isWellKnownBrand(material.brand!)) {
              brandWeight = 15.0;
            }
            score += brandWeight;
            weightFactors['brand_value'] = brandWeight;

            // 限制分数范围在0-100之间
            score = score.clamp(0.0, 100.0);

            // 生成推荐原因
            String reason;
            if (priceWeight > 20) {
              reason = '价格低于同类平均价格${((1 - material.price / avgPrice) * 100).toStringAsFixed(0)}%';
            } else if (usageWeight > 10 && priceWeight > 0) {
              reason = '常用且价格合理的材料';
            } else if (brandWeight > 0 && priceWeight > 0) {
              reason = '知名品牌且价格合理';
            } else {
              reason = '性价比较高的材料';
            }

            recommendations.add(MaterialRecommendation(
              material: material,
              relevanceScore: score,
              reason: reason,
              type: RecommendationType.valueForMoney,
              usageCount: material.usageCount,
              averagePrice: avgPrice,
              weightFactors: weightFactors,
            ));
          }

          // 按相关度排序
          recommendations.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

          // 限制结果数量
          return Right(recommendations.take(limit).toList());
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐性价比高的材料失败: $e'));
    }
  }

  // 辅助方法：获取系统分类映射
  Map<String, List<String>> _getSystemCategoryMap() {
    return {
      '电路系统': ['电气设备', '电线电缆', '开关插座', '照明设备', '电池', '逆变器', '控制器'],
      '水路系统': ['水泵', '水箱', '管道配件', '净水设备', '水龙头', '淋浴设备', '排水系统'],
      '储物系统': ['储物箱', '收纳架', '抽屉', '置物架', '挂钩', '收纳袋', '工具箱'],
      '床铺系统': ['床垫', '床架', '床单', '枕头', '被子', '床头柜', '床帘'],
      '厨房系统': ['炉灶', '冰箱', '水槽', '橱柜', '餐具', '烹饪工具', '厨房电器'],
      '卫浴系统': ['马桶', '洗手盆', '淋浴', '浴室柜', '浴室镜', '卫浴五金', '浴帘'],
      '外观系统': ['车身贴纸', '车顶行李架', '侧裙', '前后包围', '轮毂', '车灯', '车顶帐篷'],
      '底盘系统': ['悬挂', '轮胎', '刹车', '减震器', '稳定杆', '差速器', '传动轴'],
    };
  }

  // 辅助方法：获取系统相关标签
  List<String> _getSystemTags(String systemType) {
    switch (systemType) {
      case '电路系统':
        return ['电路', '电气', '供电', '照明', '太阳能', '锂电池', '逆变器', '充电'];
      case '水路系统':
        return ['水路', '供水', '排水', '净水', '热水', '水箱', '水泵', '管道'];
      case '储物系统':
        return ['储物', '收纳', '抽屉', '置物', '整理', '分区', '工具箱'];
      case '床铺系统':
        return ['床铺', '睡眠', '床垫', '床架', '折叠床', '升降床', '舒适'];
      case '厨房系统':
        return ['厨房', '烹饪', '炉灶', '冰箱', '水槽', '橱柜', '餐具'];
      case '卫浴系统':
        return ['卫浴', '洗漱', '淋浴', '马桶', '洗手', '镜子', '洗浴'];
      case '外观系统':
        return ['外观', '车身', '贴纸', '行李架', '车顶', '侧裙', '包围'];
      case '底盘系统':
        return ['底盘', '悬挂', '轮胎', '刹车', '减震', '稳定', '升降'];
      default:
        return [];
    }
  }

  // 辅助方法：检查是否为知名品牌
  bool _isWellKnownBrand(String brand) {
    final wellKnownBrands = [
      '戴德', '博世', '松下', '索尼', '飞利浦', '西门子', '三星', 'LG', '大金', '美的',
      '格力', '海尔', '小米', '华为', '苹果', '德尔福', '博世', '法雷奥', '马勒', '博格华纳',
      '舍弗勒', '采埃孚', '李尔', '麦格纳', '大陆', '电装', '爱信', '现代摩比斯', '佛吉亚',
      '安波福', '日立', '东芝', '三菱', '本田', '丰田', '日产', '马自达', '斯巴鲁', '铃木',
      '雅马哈', '川崎', '宝马', '奔驰', '奥迪', '大众', '沃尔沃', '福特', '通用', '克莱斯勒',
      '菲亚特', '标致', '雪铁龙', '雷诺', '捷豹', '路虎', '阿斯顿·马丁', '宾利', '劳斯莱斯',
      '法拉利', '兰博基尼', '玛莎拉蒂', '保时捷', '布加迪', '科尼赛克', '特斯拉', '蔚来',
      '小鹏', '理想', '比亚迪', '吉利', '长城', '奇瑞', '长安', '广汽', '上汽', '一汽',
      '东风', '北汽', '江淮', '五菱', '威马', '哪吒', '零跑', '合创', '极氪', '高合',
    ];

    return wellKnownBrands.any((b) => brand.toLowerCase().contains(b.toLowerCase()));
  }
}