{"enabled": true, "name": "VanHub Clean Architecture Validator", "description": "当保存lib目录下的任何Dart文件时，验证VanHub项目是否遵循Clean Architecture原则：检查Widget业务逻辑、ConsumerWidget使用、Repository返回类型、Domain层纯净性和分层依赖关系", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "请验证VanHub项目的Clean Architecture合规性：\n\n1. **检查Widget业务逻辑违规**：\n   - 扫描所有Widget类，确保没有直接的数据调用（如Supabase、HTTP请求）\n   - 确保没有业务逻辑代码在Widget中\n   - Widget应该只处理UI渲染和用户交互\n\n2. **检查ConsumerWidget使用**：\n   - 所有有状态的Widget都应该继承ConsumerWidget或ConsumerStatefulWidget\n   - 状态管理必须通过Riverpod的ref.watch()和ref.read()\n\n3. **检查Repository返回类型**：\n   - 所有Repository接口和实现中的方法必须返回Either<Failure, Success>类型\n   - 特别检查可能失败的异步操作\n\n4. **检查Domain层纯净性**：\n   - Domain层文件不能导入Flutter包（如flutter/material.dart）\n   - Domain层不能导入外部包，只能导入dart:core和必要注解包\n   - 检查lib/features/*/domain/目录下的所有文件\n\n5. **检查分层依赖关系**：\n   - Presentation层不能直接导入Data层\n   - 必须通过Domain层的接口进行交互\n   - 检查import语句的依赖方向\n\n如果发现违规：\n- 显示具体的违规文件路径和行号\n- 说明违反了哪个Clean Architecture原则\n- 提供具体的修复建议和示例代码\n- 如果可能，提供自动修复的代码建议\n\n请以中文输出详细的验证报告。"}}