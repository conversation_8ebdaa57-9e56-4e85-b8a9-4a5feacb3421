{"enabled": true, "name": "Freezed Entity Validator", "description": "Validates that Domain layer entities correctly use freezed annotations, part declarations, factory constructors, and maintain immutability when saving entity files", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/features/**/domain/entities/*.dart"]}, "then": {"type": "askAgent", "prompt": "当保存Domain层实体文件时，验证是否正确使用freezed：\n1. 检查@freezed注解 - 所有Domain实体必须使用@freezed注解\n2. 检查part声明 - 必须包含.freezed.dart和.g.dart的part声明\n3. 检查factory构造函数 - 必须使用const factory构造函数\n4. 检查fromJson方法 - 如果需要序列化，必须包含fromJson工厂方法\n5. 检查不可变性 - 所有字段必须是final，不能有setter\n如果发现问题：\n- 指出缺失的注解或声明\n- 提供完整的freezed实体模板\n- 建议运行build_runner生成代码\n触发条件：保存lib/features/**/domain/entities/*.dart文件"}}