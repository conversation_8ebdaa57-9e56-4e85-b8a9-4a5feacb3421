import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/milestone.dart';
import '../../domain/entities/enums.dart';
import '../providers/timeline_provider.dart';
import '../../../../core/providers/auth_state_provider.dart';

/// 创建里程碑对话框
class CreateMilestoneDialog extends ConsumerStatefulWidget {
  final String projectId;
  final String? systemId;
  final DateTime? initialDate;

  const CreateMilestoneDialog({
    super.key,
    required this.projectId,
    this.systemId,
    this.initialDate,
  });

  @override
  ConsumerState<CreateMilestoneDialog> createState() =>
      _CreateMilestoneDialogState();
}

class _CreateMilestoneDialogState extends ConsumerState<CreateMilestoneDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  MilestoneStatus _selectedStatus = MilestoneStatus.planned;
  MilestonePriority _selectedPriority = MilestonePriority.medium;
  String? _selectedIcon;
  Color _selectedColor = Colors.blue;
  bool _isLoading = false;

  // 预定义的里程碑模板
  final List<Map<String, dynamic>> _milestoneTemplates = [
    {
      'title': '开始改装',
      'description': '项目正式开始改装工作',
      'icon': 'rocket_launch',
      'color': Colors.green,
      'priority': MilestonePriority.high,
    },
    {
      'title': '电路系统完成',
      'description': '完成电路系统的安装和调试',
      'icon': 'electrical_services',
      'color': Colors.yellow.shade700,
      'priority': MilestonePriority.high,
    },
    {
      'title': '水路系统完成',
      'description': '完成水路系统的安装和测试',
      'icon': 'water_drop',
      'color': Colors.blue,
      'priority': MilestonePriority.high,
    },
    {
      'title': '内饰装修完成',
      'description': '完成内饰的设计和装修工作',
      'icon': 'home_repair_service',
      'color': Colors.brown,
      'priority': MilestonePriority.medium,
    },
    {
      'title': '首次试住',
      'description': '第一次在改装完成的房车中过夜',
      'icon': 'hotel',
      'color': Colors.purple,
      'priority': MilestonePriority.medium,
    },
    {
      'title': '改装完成',
      'description': '项目全部完成，可以正式使用',
      'icon': 'check_circle',
      'color': Colors.green,
      'priority': MilestonePriority.high,
    },
  ];

  // 可选的图标列表
  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'rocket_launch', 'icon': Icons.rocket_launch, 'label': '开始'},
    {'name': 'electrical_services', 'icon': Icons.electrical_services, 'label': '电气'},
    {'name': 'water_drop', 'icon': Icons.water_drop, 'label': '水路'},
    {'name': 'local_fire_department', 'icon': Icons.local_fire_department, 'label': '燃气'},
    {'name': 'home_repair_service', 'icon': Icons.home_repair_service, 'label': '装修'},
    {'name': 'build', 'icon': Icons.build, 'label': '施工'},
    {'name': 'check_circle', 'icon': Icons.check_circle, 'label': '完成'},
    {'name': 'flag', 'icon': Icons.flag, 'label': '里程碑'},
    {'name': 'star', 'icon': Icons.star, 'label': '重要'},
    {'name': 'celebration', 'icon': Icons.celebration, 'label': '庆祝'},
  ];

  // 可选的颜色列表
  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.red,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.brown,
    Colors.grey,
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialDate != null) {
      _selectedDate = widget.initialDate!;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTemplateSection(),
                      const SizedBox(height: 24),
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildCustomizationSection(),
                      const SizedBox(height: 24),
                      _buildDateAndStatusSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _selectedColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(
            _getSelectedIcon(),
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Text(
              '创建里程碑',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模板选择区域
  Widget _buildTemplateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择模板',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        const Text(
          '选择一个预设模板快速创建里程碑，或者手动填写信息',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _milestoneTemplates.length,
            itemBuilder: (context, index) {
              final template = _milestoneTemplates[index];
              return Container(
                width: 160,
                margin: const EdgeInsets.only(right: 12),
                child: InkWell(
                  onTap: () => _applyTemplate(template),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: (template['color'] as Color).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: (template['color'] as Color).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          _getIconByName(template['icon']),
                          color: template['color'],
                          size: 24,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          template['title'],
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: template['color'],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          template['description'],
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本信息',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: '里程碑标题',
            hintText: '请输入里程碑标题',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入里程碑标题';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: '描述（可选）',
            hintText: '请输入里程碑描述',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// 构建自定义区域
  Widget _buildCustomizationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '自定义外观',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // 图标选择
        const Text(
          '选择图标',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableIcons.map((iconData) {
            final isSelected = _selectedIcon == iconData['name'];
            return InkWell(
              onTap: () => setState(() => _selectedIcon = iconData['name']),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected ? _selectedColor : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? _selectedColor : Colors.grey.shade300,
                    width: 2,
                  ),
                ),
                child: Icon(
                  iconData['icon'],
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                  size: 24,
                ),
              ),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 16),
        
        // 颜色选择
        const Text(
          '选择颜色',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableColors.map((color) {
            final isSelected = _selectedColor == color;
            return InkWell(
              onTap: () => setState(() => _selectedColor = color),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建日期和状态区域
  Widget _buildDateAndStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '日期和状态',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: _selectDate,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '目标日期',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            _formatDate(_selectedDate),
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<MilestonePriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: '优先级',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.priority_high),
                ),
                items: MilestonePriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(_getPriorityText(priority)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedPriority = value);
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _createMilestone,
              style: ElevatedButton.styleFrom(
                backgroundColor: _selectedColor,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('创建里程碑'),
            ),
          ),
        ],
      ),
    );
  }

  // 事件处理方法
  void _applyTemplate(Map<String, dynamic> template) {
    setState(() {
      _titleController.text = template['title'];
      _descriptionController.text = template['description'];
      _selectedIcon = template['icon'];
      _selectedColor = template['color'];
      _selectedPriority = template['priority'];
    });
  }

  void _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _createMilestone() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final currentUserId = ref.read(currentUserIdProvider);
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      final milestone = Milestone(
        id: '', // 由后端生成
        projectId: widget.projectId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        date: _selectedDate,
        status: _selectedStatus,
        systemId: widget.systemId,
        relatedLogIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: currentUserId,
        iconName: _selectedIcon,
        colorHex: '#${_selectedColor.value.toRadixString(16).substring(2)}',
        priority: _selectedPriority,
      );

      final result = await ref.read(timelineProvider.notifier)
          .addMilestone(milestone);

      result.fold(
        (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('创建里程碑失败: ${failure.message}'),
              backgroundColor: Colors.red,
            ),
          );
        },
        (createdMilestone) {
          Navigator.of(context).pop(createdMilestone);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('里程碑创建成功'),
              backgroundColor: Colors.green,
            ),
          );
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('创建里程碑失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // 辅助方法
  IconData _getSelectedIcon() {
    if (_selectedIcon == null) return Icons.flag;
    return _getIconByName(_selectedIcon!);
  }

  IconData _getIconByName(String iconName) {
    final iconMap = {
      for (var icon in _availableIcons) icon['name']: icon['icon']
    };
    return iconMap[iconName] ?? Icons.flag;
  }

  String _getPriorityText(MilestonePriority priority) {
    switch (priority) {
      case MilestonePriority.low:
        return '低';
      case MilestonePriority.medium:
        return '中';
      case MilestonePriority.high:
        return '高';
      case MilestonePriority.critical:
        return '紧急';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
