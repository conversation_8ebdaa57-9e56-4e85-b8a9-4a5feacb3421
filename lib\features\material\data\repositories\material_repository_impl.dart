import 'package:fpdart/fpdart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/create_material_request.dart';
import '../../domain/repositories/material_repository.dart';
import '../datasources/material_remote_datasource.dart';
import '../models/material_model.dart';

class MaterialRepositoryImpl implements MaterialRepository {
  final MaterialRemoteDataSource remoteDataSource;
  final Ref ref;

  const MaterialRepositoryImpl({
    required this.remoteDataSource,
    required this.ref,
  });

  @override
  Future<Either<Failure, Material>> createMaterial(CreateMaterialRequest request) async {
    try {
      final userId = await ref.read(requireCurrentUserIdProvider.future);
      final materialModel = await remoteDataSource.createMaterial(request, userId);
      return Right(materialModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '创建材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> getUserMaterials(String userId) async {
    try {
      final materialModels = await remoteDataSource.getUserMaterials(userId);
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> getPublicMaterials() async {
    try {
      final materialModels = await remoteDataSource.getPublicMaterials();
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取公开材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> getMaterialsByCategory(
    String userId, 
    String category,
  ) async {
    try {
      final materialModels = await remoteDataSource.getMaterialsByCategory(userId, category);
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取分类材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Material>> getMaterialById(String materialId) async {
    try {
      final materialModel = await remoteDataSource.getMaterialById(materialId);
      return Right(materialModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取材料详情失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Material>> updateMaterial(
    String materialId, 
    Map<String, dynamic> updates,
  ) async {
    try {
      final materialModel = await remoteDataSource.updateMaterial(materialId, updates);
      return Right(materialModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMaterial(String materialId) async {
    try {
      await remoteDataSource.deleteMaterial(materialId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '删除材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> searchMaterials(
    String userId,
    String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final materialModels = await remoteDataSource.searchMaterials(
        userId,
        query,
        category: category,
        minPrice: minPrice,
        maxPrice: maxPrice,
        limit: limit,
        offset: offset,
      );
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '搜索材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> incrementUsageCount(String materialId) async {
    try {
      await remoteDataSource.incrementUsageCount(materialId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新使用次数失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> getPopularMaterials(
    String userId, {
    int limit = 10,
  }) async {
    try {
      final materialModels = await remoteDataSource.getPopularMaterials(userId, limit: limit);
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取热门材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Material>>> getRecentlyUsedMaterials(
    String userId, {
    int limit = 10,
  }) async {
    try {
      final materialModels = await remoteDataSource.getRecentlyUsedMaterials(userId, limit: limit);
      final materials = materialModels.map((model) => model.toEntity()).toList();
      return Right(materials);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取最近使用材料失败: $e'));
    }
  }
}