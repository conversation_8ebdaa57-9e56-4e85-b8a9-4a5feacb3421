import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 增强版手势检测器
/// 提供移动端优化的手势识别和触觉反馈
class EnhancedGestureDetector extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final GestureDragStartCallback? onPanStart;
  final GestureDragUpdateCallback? onPanUpdate;
  final GestureDragEndCallback? onPanEnd;
  final GestureScaleStartCallback? onScaleStart;
  final GestureScaleUpdateCallback? onScaleUpdate;
  final GestureScaleEndCallback? onScaleEnd;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final VoidCallback? onSwipeUp;
  final VoidCallback? onSwipeDown;
  
  // 触觉反馈配置
  final bool enableHapticFeedback;
  final HapticFeedbackType hapticFeedbackType;
  
  // 手势配置
  final double swipeThreshold;
  final double scaleThreshold;
  final Duration longPressDelay;
  final Duration doubleTapDelay;
  
  // 视觉反馈配置
  final bool enableRippleEffect;
  final Color? rippleColor;
  final bool enableScaleAnimation;
  final double scaleAnimationFactor;
  
  const EnhancedGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onPanStart,
    this.onPanUpdate,
    this.onPanEnd,
    this.onScaleStart,
    this.onScaleUpdate,
    this.onScaleEnd,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.lightImpact,
    this.swipeThreshold = 100.0,
    this.scaleThreshold = 0.1,
    this.longPressDelay = const Duration(milliseconds: 500),
    this.doubleTapDelay = const Duration(milliseconds: 300),
    this.enableRippleEffect = true,
    this.rippleColor,
    this.enableScaleAnimation = true,
    this.scaleAnimationFactor = 0.95,
  });

  @override
  State<EnhancedGestureDetector> createState() => _EnhancedGestureDetectorState();
}

class _EnhancedGestureDetectorState extends State<EnhancedGestureDetector>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  
  Offset? _panStartPosition;
  bool _isScaling = false;
  int _tapCount = 0;
  DateTime? _lastTapTime;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleAnimationFactor,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget child = widget.child;

    // 添加缩放动画
    if (widget.enableScaleAnimation) {
      child = AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: child,
      );
    }

    // 添加水波纹效果
    if (widget.enableRippleEffect) {
      child = Material(
        color: Colors.transparent,
        child: InkWell(
          splashColor: widget.rippleColor ?? Theme.of(context).primaryColor.withValues(alpha: 0.2),
          highlightColor: widget.rippleColor?.withValues(alpha: 0.1) ?? 
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
          onTap: () {}, // 空实现，实际处理在GestureDetector中
          child: child,
        ),
      );
    }

    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      onScaleStart: _handleScaleStart,
      onScaleUpdate: _handleScaleUpdate,
      onScaleEnd: _handleScaleEnd,
      child: child,
    );
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enableScaleAnimation) {
      _scaleController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.enableScaleAnimation) {
      _scaleController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.enableScaleAnimation) {
      _scaleController.reverse();
    }
  }

  void _handleTap() {
    _provideTactileFeedback();
    
    final now = DateTime.now();
    
    // 检查双击
    if (widget.onDoubleTap != null && _lastTapTime != null) {
      final timeDiff = now.difference(_lastTapTime!);
      if (timeDiff <= widget.doubleTapDelay) {
        _tapCount++;
        if (_tapCount == 2) {
          widget.onDoubleTap!();
          _tapCount = 0;
          _lastTapTime = null;
          return;
        }
      } else {
        _tapCount = 1;
      }
    } else {
      _tapCount = 1;
    }
    
    _lastTapTime = now;
    
    // 延迟执行单击，等待可能的双击
    if (widget.onDoubleTap != null) {
      Future.delayed(widget.doubleTapDelay, () {
        if (_tapCount == 1 && _lastTapTime == now) {
          widget.onTap?.call();
          _tapCount = 0;
          _lastTapTime = null;
        }
      });
    } else {
      widget.onTap?.call();
    }
  }

  void _handleLongPress() {
    _provideTactileFeedback(HapticFeedbackType.mediumImpact);
    widget.onLongPress?.call();
  }

  void _handlePanStart(DragStartDetails details) {
    _panStartPosition = details.globalPosition;
    widget.onPanStart?.call(details);
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    widget.onPanUpdate?.call(details);
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_panStartPosition != null) {
      final delta = details.velocity.pixelsPerSecond;
      final distance = delta.distance;
      
      if (distance > widget.swipeThreshold) {
        final dx = delta.dx.abs();
        final dy = delta.dy.abs();
        
        if (dx > dy) {
          // 水平滑动
          if (delta.dx > 0) {
            widget.onSwipeRight?.call();
          } else {
            widget.onSwipeLeft?.call();
          }
        } else {
          // 垂直滑动
          if (delta.dy > 0) {
            widget.onSwipeDown?.call();
          } else {
            widget.onSwipeUp?.call();
          }
        }
        
        _provideTactileFeedback(HapticFeedbackType.lightImpact);
      }
    }
    
    _panStartPosition = null;
    widget.onPanEnd?.call(details);
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _isScaling = true;
    widget.onScaleStart?.call(details);
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_isScaling) {
      widget.onScaleUpdate?.call(details);
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    _isScaling = false;
    widget.onScaleEnd?.call(details);
  }

  void _provideTactileFeedback([HapticFeedbackType? type]) {
    if (widget.enableHapticFeedback) {
      switch (type ?? widget.hapticFeedbackType) {
        case HapticFeedbackType.lightImpact:
          HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.mediumImpact:
          HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavyImpact:
          HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selectionClick:
          HapticFeedback.selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          HapticFeedback.vibrate();
          break;
      }
    }
  }
}

/// 触觉反馈类型枚举
enum HapticFeedbackType {
  lightImpact,
  mediumImpact,
  heavyImpact,
  selectionClick,
  vibrate,
}

/// 滑动方向枚举
enum SwipeDirection {
  left,
  right,
  up,
  down,
}

/// 手势配置类
class GestureConfig {
  final bool enableHapticFeedback;
  final HapticFeedbackType hapticFeedbackType;
  final double swipeThreshold;
  final double scaleThreshold;
  final Duration longPressDelay;
  final Duration doubleTapDelay;
  final bool enableRippleEffect;
  final Color? rippleColor;
  final bool enableScaleAnimation;
  final double scaleAnimationFactor;

  const GestureConfig({
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.lightImpact,
    this.swipeThreshold = 100.0,
    this.scaleThreshold = 0.1,
    this.longPressDelay = const Duration(milliseconds: 500),
    this.doubleTapDelay = const Duration(milliseconds: 300),
    this.enableRippleEffect = true,
    this.rippleColor,
    this.enableScaleAnimation = true,
    this.scaleAnimationFactor = 0.95,
  });

  static const GestureConfig mobile = GestureConfig(
    enableHapticFeedback: true,
    hapticFeedbackType: HapticFeedbackType.lightImpact,
    swipeThreshold: 80.0,
    enableRippleEffect: true,
    enableScaleAnimation: true,
    scaleAnimationFactor: 0.95,
  );

  static const GestureConfig desktop = GestureConfig(
    enableHapticFeedback: false,
    swipeThreshold: 120.0,
    enableRippleEffect: false,
    enableScaleAnimation: false,
  );

  static const GestureConfig tablet = GestureConfig(
    enableHapticFeedback: true,
    hapticFeedbackType: HapticFeedbackType.mediumImpact,
    swipeThreshold: 100.0,
    enableRippleEffect: true,
    enableScaleAnimation: true,
    scaleAnimationFactor: 0.98,
  );
}
