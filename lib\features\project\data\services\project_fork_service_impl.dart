import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/project_fork_service.dart';
import '../../domain/services/fork_permission_service.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/fork_request.dart';
import '../../domain/entities/fork_result.dart';
import '../../domain/entities/fork_statistics.dart';
import '../../domain/repositories/project_repository.dart';
import '../../../bom/domain/repositories/bom_repository.dart';

/// 项目复刻服务实现
/// 遵循Clean Architecture原则，实现项目复刻的完整业务逻辑
class ProjectForkServiceImpl implements ProjectForkService {
  final ProjectRepository projectRepository;
  final BomRepository bomRepository;
  final ForkPermissionService permissionService;

  const ProjectForkServiceImpl({
    required this.projectRepository,
    required this.bomRepository,
    required this.permissionService,
  });

  @override
  Future<Either<Failure, ForkResult>> forkProject(ForkRequest request) async {
    try {
      // 1. 验证复刻权限
      final permissionResult = await permissionService.validateForkRequest(
        request.sourceProjectId,
        '', // TODO: 从当前用户上下文获取userId
        request,
      );
      
      if (permissionResult.isLeft()) {
        return permissionResult.fold(
          (failure) => Left(failure),
          (isValid) => Left(ValidationFailure(message: '复刻权限验证失败')),
        );
      }

      final isValid = permissionResult.getRight().getOrElse(() => false);
      if (!isValid) {
        return Left(ValidationFailure(message: '没有复刻权限'));
      }

      // 2. 获取源项目信息
      final sourceProjectResult = await projectRepository.getProjectById(request.sourceProjectId);
      if (sourceProjectResult.isLeft()) {
        return Left(ValidationFailure(message: '源项目不存在'));
      }

      final sourceProject = sourceProjectResult.getRight().getOrElse(() => throw Exception());

      // 3. 创建复刻项目
      final projectResult = await _createForkProject(sourceProject, request);
      if (projectResult.isLeft()) {
        return projectResult.fold(
          (failure) => Left(failure),
          (project) => throw Exception(), // 不会执行到这里
        );
      }

      final newProject = projectResult.getRight().getOrElse(() => throw Exception());

      // 4. 复制相关数据
      int copiedSystems = 0;
      int copiedBomItems = 0;
      int copiedImages = 0;

      if (request.copyBomItems) {
        final bomResult = await _copyBomItems(request.sourceProjectId, newProject.id);
        copiedBomItems = bomResult.fold(
          (failure) => 0, // 失败时返回0
          (count) => count,
        );
      }

      if (request.copySystems) {
        // TODO: 实现系统复制逻辑
        copiedSystems = 0;
      }

      if (request.copyImages) {
        // TODO: 实现图片复制逻辑
        copiedImages = 0;
      }

      // 5. 创建复刻结果
      final result = ForkResult(
        newProjectId: newProject.id,
        sourceProjectId: request.sourceProjectId,
        sourceProjectTitle: sourceProject.title,
        sourceAuthorId: sourceProject.authorId,
        sourceAuthorName: sourceProject.authorName ?? '未知用户',
        copiedSystems: copiedSystems,
        copiedBomItems: copiedBomItems,
        copiedImages: copiedImages,
        forkedAt: DateTime.now(),
      );

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '复刻项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> getForkHistory(String projectId) async {
    try {
      // TODO: 实现获取复刻历史的逻辑
      // 这需要在数据库中查询所有以此项目为源的复刻项目
      return const Right([]);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取复刻历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Project?>> getSourceProject(String forkedProjectId) async {
    try {
      final projectResult = await projectRepository.getProjectById(forkedProjectId);
      if (projectResult.isLeft()) {
        return Left(ValidationFailure(message: '项目不存在'));
      }

      final project = projectResult.getRight().getOrElse(() => throw Exception());
      
      if (project.sourceProjectId == null) {
        return const Right(null);
      }

      final sourceProjectResult = await projectRepository.getProjectById(project.sourceProjectId!);
      return sourceProjectResult.fold(
        (failure) => Left(failure),
        (sourceProject) => Right(sourceProject),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取源项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ForkStatistics>> getForkStatistics(String projectId) async {
    try {
      // TODO: 实现复刻统计逻辑
      final statistics = ForkStatistics(
        projectId: projectId,
        totalForks: 0,
        activeForks: 0,
        completedForks: 0,
        lastForkedAt: DateTime.now(),
      );
      return Right(statistics);
    } catch (e) {
      return Left(UnknownFailure(message: '获取复刻统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> canForkProject(String projectId, String userId) async {
    return await permissionService.canForkProject(projectId, userId);
  }

  /// 创建复刻项目
  Future<Either<Failure, Project>> _createForkProject(
    Project sourceProject,
    ForkRequest request,
  ) async {
    // TODO: 实现创建复刻项目的逻辑
    // 这里需要调用ProjectRepository创建新项目
    return Left(UnknownFailure(message: '创建复刻项目功能待实现'));
  }

  /// 复制BOM项目
  Future<Either<Failure, int>> _copyBomItems(String sourceProjectId, String targetProjectId) async {
    try {
      // TODO: 实现BOM项目复制逻辑
      return const Right(0);
    } catch (e) {
      return Left(UnknownFailure(message: '复制BOM项目失败: $e'));
    }
  }
}
