/// VanHub Material Library Page 2.0
/// 
/// 现代化物料库页面，使用新设计系统
/// 
/// 特性：
/// - 瀑布流布局展示
/// - 智能搜索和筛选
/// - 材料评价系统
/// - 供应商信息
/// - 价格趋势分析

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/components/vanhub_button_v2.dart';
import '../../../../core/design_system/components/vanhub_input_v2.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../widgets/create_material_dialog_widget.dart';

/// 材料视图类型枚举
enum MaterialViewType {
  grid,       // 网格视图
  list,       // 列表视图
  masonry,    // 瀑布流视图
}

/// 材料排序类型枚举
enum MaterialSortType {
  newest,     // 最新添加
  oldest,     // 最早添加
  name,       // 名称排序
  price,      // 价格排序
  rating,     // 评分排序
}

/// VanHub物料库页面 2.0
class MaterialLibraryPageV2 extends ConsumerStatefulWidget {
  final String? userId;

  const MaterialLibraryPageV2({
    Key? key,
    this.userId,
  }) : super(key: key);

  @override
  ConsumerState<MaterialLibraryPageV2> createState() => _MaterialLibraryPageV2State();
}

class _MaterialLibraryPageV2State extends ConsumerState<MaterialLibraryPageV2>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _filterController;
  
  String _searchQuery = '';
  String _selectedCategory = '全部';
  MaterialViewType _viewType = MaterialViewType.masonry;
  MaterialSortType _sortType = MaterialSortType.newest;
  bool _showFilters = false;
  double _minPrice = 0;
  double _maxPrice = 10000;
  double _minRating = 0;

  // 房车改装专业分类
  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装',
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '车顶改装',
    '底盘改装', '其他配件'
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filterController = AnimationController(
      duration: VanHubAnimationDurations.normal,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final userId = widget.userId ?? currentUserId;

    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(),
          _buildSearchAndFilters(),
          _buildMaterialGrid(userId),
        ],
      ),
      floatingActionButton: userId != null ? _buildCreateMaterialFAB() : null,
    );
  }

  /// 构建现代化AppBar
  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: VanHubBrandColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'VanHub 物料库',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: VanHubBrandColors.onPrimary,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: VanHubBrandColors.getEmotionalGradient(EmotionalState.energetic),
          ),
          child: Stack(
            children: [
              // 背景装饰
              Positioned(
                right: -50,
                top: 50,
                child: Icon(
                  Icons.inventory_2_outlined,
                  size: 150,
                  color: VanHubBrandColors.onPrimary.withOpacity(0.1),
                ),
              ),
              // 统计信息
              Positioned(
                left: VanHubResponsiveSpacing.lg,
                bottom: VanHubResponsiveSpacing.xl,
                child: _buildMaterialStats(),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _viewType == MaterialViewType.grid 
                ? Icons.grid_view 
                : _viewType == MaterialViewType.list
                    ? Icons.list
                    : Icons.view_quilt,
          ),
          onPressed: _toggleViewType,
          tooltip: '切换视图',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'import':
                _importMaterials();
                break;
              case 'export':
                _exportMaterials();
                break;
              case 'sync':
                _syncMaterials();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload, size: 20),
                  SizedBox(width: 8),
                  Text('导入材料'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 20),
                  SizedBox(width: 8),
                  Text('导出材料'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'sync',
              child: Row(
                children: [
                  Icon(Icons.sync, size: 20),
                  SizedBox(width: 8),
                  Text('同步数据'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建材料统计
  Widget _buildMaterialStats() {
    return Row(
      children: [
        _buildStatItem('总材料', '1,247', VanHubBrandColors.onPrimary),
        SizedBox(width: VanHubResponsiveSpacing.lg),
        _buildStatItem('分类', '11', VanHubSemanticColors.success),
        SizedBox(width: VanHubResponsiveSpacing.lg),
        _buildStatItem('供应商', '89', VanHubSemanticColors.warning),
      ],
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: VanHubBrandColors.onPrimary.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  /// 构建搜索和筛选
  Widget _buildSearchAndFilters() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
        child: Column(
          children: [
            // 搜索栏
            VanHubInputV2(
              controller: _searchController,
              hint: '搜索材料名称、品牌或规格...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: Icon(
                  _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
                  color: _showFilters ? VanHubBrandColors.primary : null,
                ),
                onPressed: _toggleFilters,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            
            // 筛选器
            AnimatedBuilder(
              animation: _filterController,
              builder: (context, child) {
                return SizeTransition(
                  sizeFactor: _filterController,
                  child: _buildFilterSection(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建筛选区域
  Widget _buildFilterSection() {
    return Padding(
      padding: EdgeInsets.only(top: VanHubResponsiveSpacing.md),
      child: VanHubCardV2.outlined(
        size: VanHubCardSize.sm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '筛选条件',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 分类筛选
            Text(
              '材料分类',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Wrap(
              spacing: VanHubResponsiveSpacing.sm,
              runSpacing: VanHubResponsiveSpacing.sm,
              children: _categories.map((category) {
                final isSelected = category == _selectedCategory;
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                  selectedColor: VanHubBrandColors.primaryContainer,
                  labelStyle: TextStyle(
                    color: isSelected 
                        ? VanHubBrandColors.onPrimaryContainer
                        : VanHubSemanticColors.getTextColor(context),
                  ),
                );
              }).toList(),
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 价格范围
            Text(
              '价格范围',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            RangeSlider(
              values: RangeValues(_minPrice, _maxPrice),
              min: 0,
              max: 10000,
              divisions: 100,
              labels: RangeLabels(
                '¥${_minPrice.toInt()}',
                '¥${_maxPrice.toInt()}',
              ),
              onChanged: (values) {
                setState(() {
                  _minPrice = values.start;
                  _maxPrice = values.end;
                });
              },
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 评分筛选
            Text(
              '最低评分',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Slider(
              value: _minRating,
              min: 0,
              max: 5,
              divisions: 10,
              label: '${_minRating.toStringAsFixed(1)}星',
              onChanged: (value) {
                setState(() {
                  _minRating = value;
                });
              },
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 排序选项
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<MaterialSortType>(
                    value: _sortType,
                    decoration: InputDecoration(
                      labelText: '排序方式',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: VanHubResponsiveSpacing.md,
                        vertical: VanHubResponsiveSpacing.sm,
                      ),
                    ),
                    items: MaterialSortType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(_getSortTypeText(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _sortType = value;
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: VanHubResponsiveSpacing.md),
                VanHubButtonV2(
                  text: '重置',
                  variant: VanHubButtonVariant.ghost,
                  size: VanHubButtonSize.sm,
                  onPressed: _resetFilters,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建材料网格
  Widget _buildMaterialGrid(String? userId) {
    final materialsAsync = userId != null
        ? ref.watch(userMaterialsProvider(userId))
        : ref.watch(materialsNotifierProvider);

    return materialsAsync.when(
      data: (materials) {
        final filteredMaterials = _filterMaterials(materials);
        
        if (filteredMaterials.isEmpty) {
          return SliverToBoxAdapter(
            child: _buildEmptyState(),
          );
        }
        
        return SliverPadding(
          padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
          sliver: _buildMaterialLayout(filteredMaterials),
        );
      },
      loading: () => SliverToBoxAdapter(
        child: _buildLoadingState(),
      ),
      error: (error, stack) => SliverToBoxAdapter(
        child: _buildErrorState(error),
      ),
    );
  }

  /// 构建材料布局
  Widget _buildMaterialLayout(List<domain.Material> materials) {
    switch (_viewType) {
      case MaterialViewType.grid:
        return SliverGrid(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, // 临时使用固定值
            crossAxisSpacing: 16.0, // 临时使用固定值
            mainAxisSpacing: 16.0, // 临时使用固定值
            childAspectRatio: 0.8,
          ),
          delegate: SliverChildBuilderDelegate(
            (context, index) => _buildMaterialCard(materials[index], index),
            childCount: materials.length,
          ),
        );
      
      case MaterialViewType.list:
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => Padding(
              padding: const EdgeInsets.only(bottom: 16.0), // 使用固定间距
              child: _buildMaterialListItem(materials[index], index),
            ),
            childCount: materials.length,
          ),
        );
      
      case MaterialViewType.masonry:
        return SliverGrid(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, // 临时使用固定值
            mainAxisSpacing: 16.0, // 临时使用固定值
            crossAxisSpacing: 16.0, // 临时使用固定值
            childAspectRatio: 0.8,
          ),
          delegate: SliverChildBuilderDelegate(
            (context, index) => _buildMaterialCard(materials[index], index),
            childCount: materials.length,
          ),
        );
    }
  }

  /// 构建材料卡片
  Widget _buildMaterialCard(domain.Material material, int index) {
    return VanHubCardV2.interactive(
      size: VanHubCardSize.md,
      enable3DEffect: false, // 临时禁用3D效果
      onTap: () {
        // TODO: 导航到材料详情
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 材料图片
          if (material.imageUrl?.isNotEmpty == true)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Image.network(
                  material.imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                      child: Icon(
                        Icons.image_not_supported,
                        color: VanHubSemanticColors.getTextColor(context, secondary: true),
                      ),
                    );
                  },
                ),
              ),
            )
          else
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  Icons.inventory_2_outlined,
                  size: 40,
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
              ),
            ),
          
          SizedBox(height: VanHubResponsiveSpacing.sm),
          
          // 材料名称
          Text(
            material.name,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          // 品牌和规格
          if (material.brand?.isNotEmpty == true) ...[
            SizedBox(height: VanHubResponsiveSpacing.xs),
            Text(
              material.brand!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: VanHubBrandColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          
          if (material.specifications?.isNotEmpty == true) ...[
            SizedBox(height: VanHubResponsiveSpacing.xs),
            Text(
              material.specifications!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          
          const Spacer(),
          
          // 价格和评分
          Row(
            children: [
              if (material.price != null) ...[
                Text(
                  '¥${material.price!.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: VanHubSemanticColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
              ],
              // 评分功能暂时移除，因为Material实体中没有rating字段
              // TODO: 添加评分功能
            ],
          ),
        ],
      ),
    );
  }

  /// 构建材料列表项
  Widget _buildMaterialListItem(domain.Material material, int index) {
    return VanHubCardV2.outlined(
      size: VanHubCardSize.sm,
      child: Row(
        children: [
          // 材料图片
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              width: 80,
              height: 80,
              child: material.imageUrl?.isNotEmpty == true
                  ? Image.network(
                      material.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                          child: Icon(
                            Icons.image_not_supported,
                            color: VanHubSemanticColors.getTextColor(context, secondary: true),
                          ),
                        );
                      },
                    )
                  : Container(
                      color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                      child: Icon(
                        Icons.inventory_2_outlined,
                        color: VanHubSemanticColors.getTextColor(context, secondary: true),
                      ),
                    ),
            ),
          ),
          
          SizedBox(width: VanHubResponsiveSpacing.md),
          
          // 材料信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  material.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (material.brand?.isNotEmpty == true) ...[
                  SizedBox(height: VanHubResponsiveSpacing.xs),
                  Text(
                    material.brand!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: VanHubBrandColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                if (material.specifications?.isNotEmpty == true) ...[
                  SizedBox(height: VanHubResponsiveSpacing.xs),
                  Text(
                    material.specifications!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: VanHubSemanticColors.getTextColor(context, secondary: true),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          
          // 价格和评分
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (material.price != null)
                Text(
                  '¥${material.price!.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: VanHubSemanticColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              // 评分功能暂时移除
              // TODO: 添加评分功能
            ],
          ),
        ],
      ),
    );
  }

  /// 构建创建材料FAB
  Widget _buildCreateMaterialFAB() {
    return FloatingActionButton.extended(
      heroTag: "material_fab",
      onPressed: _showCreateMaterialDialog,
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      icon: const Icon(Icons.add),
      label: const Text('添加材料'),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80,
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '暂无材料',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              '开始添加您的第一个材料吧！',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2.primary(
              text: '添加材料',
              leadingIcon: Icons.add,
              onPressed: _showCreateMaterialDialog,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: VanHubBrandColors.primary,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载材料中...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: VanHubSemanticColors.error,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.error,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2(
              text: '重试',
              variant: VanHubButtonVariant.outline,
              leadingIcon: Icons.refresh,
              onPressed: () {
                if (widget.userId != null) {
                  ref.invalidate(userMaterialsProvider(widget.userId!));
                } else {
                  ref.invalidate(materialsNotifierProvider);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  void _toggleViewType() {
    setState(() {
      switch (_viewType) {
        case MaterialViewType.grid:
          _viewType = MaterialViewType.list;
          break;
        case MaterialViewType.list:
          _viewType = MaterialViewType.masonry;
          break;
        case MaterialViewType.masonry:
          _viewType = MaterialViewType.grid;
          break;
      }
    });
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    
    if (_showFilters) {
      _filterController.forward();
    } else {
      _filterController.reverse();
    }
  }

  void _resetFilters() {
    setState(() {
      _selectedCategory = '全部';
      _sortType = MaterialSortType.newest;
      _searchQuery = '';
      _minPrice = 0;
      _maxPrice = 10000;
      _minRating = 0;
    });
    _searchController.clear();
  }

  void _showCreateMaterialDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateMaterialDialogWidget(),
    );
  }

  List<domain.Material> _filterMaterials(List<domain.Material> materials) {
    var filtered = materials.where((material) {
      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final name = material.name.toLowerCase();
        final brand = (material.brand ?? '').toLowerCase();
        final spec = (material.specifications ?? '').toLowerCase();
        if (!name.contains(query) && !brand.contains(query) && !spec.contains(query)) {
          return false;
        }
      }
      
      // 分类筛选
      if (_selectedCategory != '全部') {
        if (material.category != _selectedCategory) {
          return false;
        }
      }
      
      // 价格筛选
      if (material.price != null) {
        if (material.price! < _minPrice || material.price! > _maxPrice) {
          return false;
        }
      }
      
      // 评分筛选暂时移除
      // TODO: 添加评分功能
      
      return true;
    }).toList();

    // 排序
    filtered.sort((a, b) {
      switch (_sortType) {
        case MaterialSortType.newest:
          return (b.createdAt ?? DateTime.now()).compareTo(a.createdAt ?? DateTime.now());
        case MaterialSortType.oldest:
          return (a.createdAt ?? DateTime.now()).compareTo(b.createdAt ?? DateTime.now());
        case MaterialSortType.name:
          return a.name.compareTo(b.name);
        case MaterialSortType.price:
          return (b.price ?? 0).compareTo(a.price ?? 0);
        case MaterialSortType.rating:
          return 0; // 暂时返回0，因为没有rating字段
      }
    });

    return filtered;
  }

  String _getSortTypeText(MaterialSortType type) {
    switch (type) {
      case MaterialSortType.newest:
        return '最新添加';
      case MaterialSortType.oldest:
        return '最早添加';
      case MaterialSortType.name:
        return '名称排序';
      case MaterialSortType.price:
        return '价格排序';
      case MaterialSortType.rating:
        return '评分排序';
    }
  }

  void _importMaterials() {
    // TODO: 实现材料导入功能
  }

  void _exportMaterials() {
    // TODO: 实现材料导出功能
  }

  void _syncMaterials() {
    // TODO: 实现材料同步功能
  }
}
