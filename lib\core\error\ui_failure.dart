import 'package:freezed_annotation/freezed_annotation.dart';

part 'ui_failure.freezed.dart';

/// UI层专用的失败处理模型
/// 用于统一处理和展示各种错误情况
@freezed
class UIFailure with _$UIFailure {
  /// 网络相关错误
  const factory UIFailure.network({
    required String message,
    String? details,
    int? statusCode,
    @Default('network_error') String code,
  }) = NetworkFailure;

  /// 数据验证错误
  const factory UIFailure.validation({
    required String field,
    required String message,
    String? details,
    @Default('validation_error') String code,
  }) = ValidationFailure;

  /// 权限相关错误
  const factory UIFailure.permission({
    required String action,
    required String message,
    String? details,
    @Default('permission_error') String code,
  }) = PermissionFailure;

  /// 存储相关错误
  const factory UIFailure.storage({
    required String operation,
    required String message,
    String? details,
    @Default('storage_error') String code,
  }) = StorageFailure;

  /// 认证相关错误
  const factory UIFailure.authentication({
    required String message,
    String? details,
    @Default('auth_error') String code,
  }) = AuthenticationFailure;

  /// 业务逻辑错误
  const factory UIFailure.business({
    required String message,
    String? details,
    @Default('business_error') String code,
  }) = BusinessFailure;

  /// 服务器错误
  const factory UIFailure.server({
    required String message,
    String? details,
    int? statusCode,
    @Default('server_error') String code,
  }) = ServerFailure;

  /// 未知错误
  const factory UIFailure.unknown({
    required String message,
    String? details,
    @Default('unknown_error') String code,
  }) = UnknownFailure;

  /// 超时错误
  const factory UIFailure.timeout({
    required String message,
    String? details,
    int? timeoutSeconds,
    @Default('timeout_error') String code,
  }) = TimeoutFailure;

  /// 数据格式错误
  const factory UIFailure.format({
    required String message,
    String? details,
    String? expectedFormat,
    @Default('format_error') String code,
  }) = FormatFailure;
}

/// UIFailure扩展方法
extension UIFailureX on UIFailure {
  /// 获取用户友好的错误标题
  String get userFriendlyTitle {
    return when(
      network: (message, details, statusCode, code) => '网络连接问题',
      validation: (field, message, details, code) => '输入验证失败',
      permission: (action, message, details, code) => '权限不足',
      storage: (operation, message, details, code) => '存储操作失败',
      authentication: (message, details, code) => '身份验证失败',
      business: (message, details, code) => '操作失败',
      server: (message, details, statusCode, code) => '服务器错误',
      unknown: (message, details, code) => '未知错误',
      timeout: (message, details, timeoutSeconds, code) => '操作超时',
      format: (message, details, expectedFormat, code) => '数据格式错误',
    );
  }

  /// 获取用户友好的错误描述
  String get userFriendlyMessage {
    return when(
      network: (message, details, statusCode, code) {
        if (statusCode != null) {
          switch (statusCode) {
            case 400:
              return '请求参数有误，请检查输入信息';
            case 401:
              return '登录已过期，请重新登录';
            case 403:
              return '没有权限执行此操作';
            case 404:
              return '请求的资源不存在';
            case 500:
              return '服务器内部错误，请稍后重试';
            default:
              return message.isNotEmpty ? message : '网络连接失败，请检查网络设置';
          }
        }
        return message.isNotEmpty ? message : '网络连接失败，请检查网络设置';
      },
      validation: (field, message, details, code) => message,
      permission: (action, message, details, code) => 
          message.isNotEmpty ? message : '您没有权限执行"$action"操作',
      storage: (operation, message, details, code) => 
          message.isNotEmpty ? message : '存储操作"$operation"失败',
      authentication: (message, details, code) => 
          message.isNotEmpty ? message : '身份验证失败，请重新登录',
      business: (message, details, code) => message,
      server: (message, details, statusCode, code) => 
          message.isNotEmpty ? message : '服务器暂时无法响应，请稍后重试',
      unknown: (message, details, code) => 
          message.isNotEmpty ? message : '发生了未知错误，请稍后重试',
      timeout: (message, details, timeoutSeconds, code) => 
          message.isNotEmpty ? message : '操作超时，请检查网络连接后重试',
      format: (message, details, expectedFormat, code) => 
          message.isNotEmpty ? message : '数据格式不正确',
    );
  }

  /// 是否可以重试
  bool get canRetry {
    return when(
      network: (message, details, statusCode, code) => true,
      validation: (field, message, details, code) => false,
      permission: (action, message, details, code) => false,
      storage: (operation, message, details, code) => true,
      authentication: (message, details, code) => false,
      business: (message, details, code) => false,
      server: (message, details, statusCode, code) => true,
      unknown: (message, details, code) => true,
      timeout: (message, details, timeoutSeconds, code) => true,
      format: (message, details, expectedFormat, code) => false,
    );
  }

  /// 获取错误图标名称
  String get iconName {
    return when(
      network: (message, details, statusCode, code) => 'wifi_off',
      validation: (field, message, details, code) => 'error_outline',
      permission: (action, message, details, code) => 'lock',
      storage: (operation, message, details, code) => 'storage',
      authentication: (message, details, code) => 'person_off',
      business: (message, details, code) => 'warning',
      server: (message, details, statusCode, code) => 'cloud_off',
      unknown: (message, details, code) => 'help_outline',
      timeout: (message, details, timeoutSeconds, code) => 'schedule',
      format: (message, details, expectedFormat, code) => 'data_usage',
    );
  }

  /// 获取建议的用户操作
  List<String> get suggestedActions {
    return when(
      network: (message, details, statusCode, code) => [
        '检查网络连接',
        '重新尝试',
        '切换网络环境',
      ],
      validation: (field, message, details, code) => [
        '检查输入信息',
        '修正错误字段',
      ],
      permission: (action, message, details, code) => [
        '联系管理员',
        '检查账户权限',
      ],
      storage: (operation, message, details, code) => [
        '检查存储空间',
        '重新尝试',
        '清理缓存',
      ],
      authentication: (message, details, code) => [
        '重新登录',
        '检查账户状态',
      ],
      business: (message, details, code) => [
        '检查操作条件',
        '联系客服',
      ],
      server: (message, details, statusCode, code) => [
        '稍后重试',
        '联系技术支持',
      ],
      unknown: (message, details, code) => [
        '重新尝试',
        '重启应用',
        '联系技术支持',
      ],
      timeout: (message, details, timeoutSeconds, code) => [
        '检查网络连接',
        '重新尝试',
        '稍后再试',
      ],
      format: (message, details, expectedFormat, code) => [
        '检查数据格式',
        '重新输入',
      ],
    );
  }

  /// 错误严重程度（1-5，5最严重）
  int get severity {
    return when(
      network: (message, details, statusCode, code) => 3,
      validation: (field, message, details, code) => 2,
      permission: (action, message, details, code) => 4,
      storage: (operation, message, details, code) => 3,
      authentication: (message, details, code) => 4,
      business: (message, details, code) => 3,
      server: (message, details, statusCode, code) => 4,
      unknown: (message, details, code) => 5,
      timeout: (message, details, timeoutSeconds, code) => 2,
      format: (message, details, expectedFormat, code) => 2,
    );
  }
}