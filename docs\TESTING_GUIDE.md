# VanHub改装宝完整测试指南

## 📋 概述

本指南提供了VanHub改装宝项目的完整测试方案，包括数据创建、功能测试和验证流程。

## 🎯 测试目标

验证VanHub改装宝的核心功能：
- ✅ 用户认证系统（游客模式 + 注册登录）
- ✅ 项目管理系统（创建、编辑、删除项目）
- ✅ 材料库管理系统（11个专业分类）
- ✅ BOM管理系统（状态流转、成本计算）
- ✅ 智能联动功能（材料库↔BOM双向集成）
- ✅ 数据持久化验证

## 🚀 快速开始

### 方法1：使用应用内测试工具

1. **启动应用**
   ```bash
   flutter run -d chrome --web-port 3005
   ```

2. **访问测试中心**
   - 在应用中点击右下角的"测试"按钮
   - 或直接导航到测试中心页面

3. **运行完整测试**
   - 点击"完整工作流程测试"
   - 等待测试完成
   - 查看测试结果和日志

### 方法2：使用命令行脚本

```bash
# 运行完整测试套件
dart scripts/run_complete_test.dart

# 或者分步运行
flutter test                    # 单元测试
flutter test test/integration/  # 集成测试
flutter test test/e2e/         # 端到端测试
```

## 📊 测试数据结构

### 完整测试数据包含：

#### 1. 测试用户
- **邮箱**: <EMAIL>
- **密码**: Test123456!
- **角色**: 普通用户

#### 2. 示例项目
- **项目名称**: 我的第一台房车改装
- **车型**: 福特Transit长轴高顶版
- **预算**: 65,000元
- **状态**: 规划中
- **系统**: 电力、水路、内饰、外观改装

#### 3. 材料库数据（5个示例材料）
```
1. 磷酸铁锂电池 - 比亚迪 BYD-LFP100 - ¥2,500
2. 纯正弦波逆变器 - 正弦 ZX-2000W - ¥800
3. 自吸水泵 - 舒弗洛 SHURflo-4008 - ¥450
4. 折叠床 - 多美达 Dometic-FB120 - ¥1,200
5. 车顶行李架 - 途乐 TL-RR200 - ¥680
```

#### 4. BOM数据（5个BOM项目）
```
1. 电池×2 - 已采购 - ¥5,000
2. 逆变器×1 - 已收货 - ¥800
3. 水泵×1 - 待采购 - ¥450
4. 折叠床×1 - 已下单 - ¥1,200
5. 行李架×1 - 已安装 - ¥680
```

#### 5. 改装日志（3个日志条目）
```
1. 项目启动 - 制定改装计划
2. 电力系统安装 - 电池和逆变器
3. 水路系统调试 - 供水测试
```

## 🧪 测试流程详解

### 第一阶段：用户认证测试
1. **游客模式测试**
   - 验证游客可以浏览公开内容
   - 检查功能限制是否正确

2. **用户注册测试**
   - 创建测试用户账户
   - 验证邮箱验证流程

3. **用户登录测试**
   - 测试登录功能
   - 验证状态管理

### 第二阶段：项目管理测试
1. **项目创建**
   - 填写项目基本信息
   - 选择车型和改装系统
   - 验证项目保存

2. **项目列表**
   - 验证项目显示
   - 测试搜索和筛选

3. **项目详情**
   - 验证详情页面加载
   - 测试标签页切换

### 第三阶段：材料库管理测试
1. **材料创建**
   - 创建5种不同类型的材料
   - 验证分类和属性

2. **材料列表**
   - 验证材料显示
   - 测试搜索功能

3. **材料编辑**
   - 修改材料信息
   - 验证更新保存

### 第四阶段：BOM管理测试
1. **添加BOM项目**
   - 从材料库添加到BOM
   - 设置数量和状态

2. **状态流转**
   - 测试状态变更：待采购→已下单→已收货→已安装
   - 验证进度计算

3. **成本计算**
   - 验证自动成本计算
   - 检查总价统计

### 第五阶段：智能联动测试
1. **材料库→BOM联动**
   - 在材料库点击"添加到BOM"
   - 验证信息自动填充

2. **BOM→材料库联动**
   - 在BOM点击"保存到材料库"
   - 验证材料自动创建

3. **数据同步**
   - 验证双向数据同步
   - 检查数据一致性

### 第六阶段：数据验证测试
1. **数据持久化**
   - 重启应用验证数据保存
   - 检查数据库完整性

2. **关联关系**
   - 验证项目-BOM关联
   - 检查材料-BOM关联

## 📈 测试报告

测试完成后会生成详细报告，包含：

### 测试结果统计
- ✅ 通过的测试数量
- ❌ 失败的测试数量
- ⚠️ 警告的测试数量

### 数据创建统计
- 👤 用户数量: 1个
- 📁 项目数量: 1个
- 🔧 材料数量: 5个
- 📋 BOM项目数量: 5个
- 📝 改装日志数量: 3个

### 性能指标
- 🚀 应用启动时间
- 💾 内存使用情况
- 📦 包大小统计

## 🛠️ 故障排除

### 常见问题

1. **Supabase连接失败**
   ```
   解决方案：检查.env文件中的Supabase配置
   ```

2. **测试数据创建失败**
   ```
   解决方案：检查数据库RLS策略和权限设置
   ```

3. **应用启动超时**
   ```
   解决方案：增加等待时间或检查网络连接
   ```

### 调试技巧

1. **查看详细日志**
   ```bash
   flutter run --verbose
   ```

2. **检查数据库状态**
   - 登录Supabase控制台
   - 查看表数据和日志

3. **清理测试数据**
   ```dart
   await TestDataInitializer.cleanupTestData();
   ```

## 📝 自定义测试

### 添加新的测试用例

1. **创建测试文件**
   ```dart
   // test/custom/my_test.dart
   void main() {
     testWidgets('我的自定义测试', (WidgetTester tester) async {
       // 测试逻辑
     });
   }
   ```

2. **添加到测试套件**
   ```dart
   // 在complete_workflow_test.dart中添加
   await _testMyCustomFeature();
   ```

### 修改测试数据

1. **编辑测试数据初始化器**
   ```dart
   // lib/core/utils/test_data_initializer.dart
   static Future<List<String>> _createSampleMaterials(String userId) async {
     // 修改材料数据
   }
   ```

2. **更新测试验证逻辑**
   ```dart
   // 更新相应的验证代码
   ```

## 🔄 持续集成

### GitHub Actions配置

```yaml
name: VanHub Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: dart scripts/run_complete_test.dart
```

### 测试覆盖率

```bash
# 生成覆盖率报告
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 📞 支持

如果在测试过程中遇到问题：

1. 查看本指南的故障排除部分
2. 检查项目的GitHub Issues
3. 联系开发团队

---

**最后更新**: 2025年1月28日  
**版本**: v1.0.0
