import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/usecases/get_log_entry_usecase.dart';
import '../../di/providers.dart';
import '../widgets/log_entry_detail_widget.dart';
import '../widgets/bom_items_section_widget.dart';
import 'log_edit_page.dart';

part 'log_detail_page.g.dart';

/// 获取日志详情的Provider
@riverpod
Future<LogEntry> logDetail(Ref ref, String logId) async {
  final useCase = ref.read(getLogEntryUseCaseProvider);
  final result = await useCase.call(GetLogEntryParams(logId: logId));

  return result.fold(
    (failure) => throw Exception(failure.message),
    (logEntry) => logEntry,
  );
}

/// 日志详情页面
/// 遵循Clean Architecture原则，只负责UI展示和用户交互
class LogDetailPage extends ConsumerWidget {
  final String logId;

  const LogDetailPage({
    super.key,
    required this.logId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logDetailAsync = ref.watch(logDetailProvider(logId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('日志详情'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _navigateToEditPage(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareLog(context),
          ),
        ],
      ),
      body: logDetailAsync.when(
        data: (logEntry) => _buildLogDetailContent(context, logEntry),
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                '加载日志详情失败',
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(logDetailProvider(logId)),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogDetailContent(BuildContext context, LogEntry logEntry) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日志基本信息
          LogEntryDetailWidget(logEntry: logEntry),

          const SizedBox(height: 24),

          // 媒体画廊
          if (logEntry.mediaIds.isNotEmpty) ...[
            const Text(
              '相关媒体',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildMediaSection(logEntry.mediaIds),
            const SizedBox(height: 24),
          ],

          // 关联的BOM项目
          if (logEntry.relatedBomItemIds.isNotEmpty) ...[
            const Text(
              '使用的材料',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            BomItemsSectionWidget(bomItemIds: logEntry.relatedBomItemIds),
            const SizedBox(height: 24),
          ],

          // 成本信息
          if (logEntry.totalCost > 0) ...[
            _buildCostSection(logEntry),
            const SizedBox(height: 24),
          ],

          // 时间信息
          _buildTimeSection(logEntry),
        ],
      ),
    );
  }

  Widget _buildCostSection(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '成本信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('总成本:'),
                Text(
                  '¥${logEntry.totalCost.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            if (logEntry.timeSpentMinutes > 0) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('工时:'),
                  Text('${logEntry.timeSpentMinutes} 分钟'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '时间信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('记录日期:'),
                Text(logEntry.logDate.toString().split(' ')[0]),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('创建时间:'),
                Text(logEntry.createdAt.toString().split(' ')[0]),
              ],
            ),
            if (logEntry.updatedAt != logEntry.createdAt) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('最后更新:'),
                  Text(logEntry.updatedAt.toString().split(' ')[0]),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _navigateToEditPage(BuildContext context, WidgetRef ref) async {
    final logDetailAsync = ref.read(logDetailProvider(logId));

    logDetailAsync.when(
      data: (logEntry) async {
        final result = await Navigator.of(context).push<bool>(
          MaterialPageRoute(
            builder: (context) => LogEditPage(
              logEntry: logEntry,
              projectId: logEntry.projectId,
            ),
          ),
        );

        if (result == true) {
          // 刷新页面数据
          ref.invalidate(logDetailProvider(logId));
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('日志更新成功')),
          );
        }
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('正在加载日志数据...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $error')),
        );
      },
    );
  }

  void _shareLog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '分享日志',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.link),
              title: const Text('复制链接'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('链接已复制到剪贴板')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('分享到其他应用'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('分享功能开发中')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaSection(List<String> mediaIds) {
    if (mediaIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.photo_library,
                  color: Colors.deepOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  '媒体文件 (${mediaIds.length}项)',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: mediaIds.map((mediaId) => _buildMediaPlaceholder(mediaId)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String mediaId) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image,
            color: Colors.grey[600],
            size: 32,
          ),
          const SizedBox(height: 4),
          Text(
            '媒体',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}