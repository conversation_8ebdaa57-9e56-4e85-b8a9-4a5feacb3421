import 'package:fpdart/fpdart.dart';
import 'package:mockito/mockito.dart';

import '../../lib/core/errors/failures.dart';
import '../../lib/features/project/domain/entities/project.dart';
import '../../lib/features/project/domain/entities/create_project_request.dart';
import '../../lib/features/project/domain/repositories/project_repository.dart';

/// Mock类：ProjectRepository
/// 用于测试Project相关功能
class MockProjectRepository extends Mock implements ProjectRepository {
  
  @override
  Future<Either<Failure, Project>> createProject(CreateProjectRequest request) {
    return super.noSuchMethod(
      Invocation.method(#createProject, [request]),
      returnValue: Future.value(
        Right(Project(
          id: 'test-project-id',
          authorId: 'test-user-id',
          title: 'Test Project',
          description: 'Test project description',
          status: ProjectStatus.planning,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          budget: 10000.0,
          spentAmount: 0.0,
          progress: 0,
          tags: ['test'],
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, Project>> updateProject(String projectId, Map<String, dynamic> updates) {
    return super.noSuchMethod(
      Invocation.method(#updateProject, [projectId, updates]),
      returnValue: Future.value(Right(Project(
        id: projectId,
        authorId: 'test-user-id',
        title: 'Updated Project',
        description: 'Updated description',
        status: ProjectStatus.planning,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ))),
    );
  }

  @override
  Future<Either<Failure, void>> deleteProject(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#deleteProject, [projectId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, Project>> getProject(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#getProject, [projectId]),
      returnValue: Future.value(
        Right(Project(
          id: projectId,
          authorId: 'test-user-id',
          title: 'Test Project',
          description: 'Test project description',
          status: ProjectStatus.planning,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          budget: 10000.0,
          spentAmount: 0.0,
          tags: ['test'],
          vehicleBrand: 'Test Make',
          vehicleModel: 'Test Model',
          vehicleYear: '2020',
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 30)),
          progress: 0,
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, List<Project>>> getUserProjects(
    String userId, {
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getUserProjects, [userId], {
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<Project>>> getPublicProjects({
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getPublicProjects, [], {
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<Project>>> searchProjects(
    String query, {
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#searchProjects, [query], {
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<Project>>> getProjectsByStatus(
    ProjectStatus status, {
    String? userId,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getProjectsByStatus, [status], {
        #userId: userId,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, List<Project>>> getProjectsByTag(
    String tag, {
    String? userId,
    int? limit,
    int? offset,
  }) {
    return super.noSuchMethod(
      Invocation.method(#getProjectsByTag, [tag], {
        #userId: userId,
        #limit: limit,
        #offset: offset,
      }),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, void>> updateProjectProgress(
    String projectId,
    double progress,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateProjectProgress, [projectId, progress]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> updateProjectStatus(
    String projectId,
    ProjectStatus status,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateProjectStatus, [projectId, status]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> updateProjectBudget(
    String projectId,
    double actualBudget,
  ) {
    return super.noSuchMethod(
      Invocation.method(#updateProjectBudget, [projectId, actualBudget]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, List<String>>> getProjectTags(String? userId) {
    return super.noSuchMethod(
      Invocation.method(#getProjectTags, [userId]),
      returnValue: Future.value(const Right([])),
    );
  }

  @override
  Future<Either<Failure, Map<String, int>>> getProjectStatistics(String? userId) {
    return super.noSuchMethod(
      Invocation.method(#getProjectStatistics, [userId]),
      returnValue: Future.value(const Right({})),
    );
  }

  @override
  Future<Either<Failure, Project>> duplicateProject(
    String projectId,
    String newName,
  ) {
    return super.noSuchMethod(
      Invocation.method(#duplicateProject, [projectId, newName]),
      returnValue: Future.value(
        Right(Project(
          id: 'duplicated-project-id',
          authorId: 'test-user-id',
          title: newName,
          description: 'Duplicated project',
          status: ProjectStatus.planning,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          budget: 10000.0,
          spentAmount: 0.0,
          tags: ['duplicated'],
          vehicleBrand: 'Test Make',
          vehicleModel: 'Test Model',
          vehicleYear: '2020',
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 30)),
          progress: 0,
        )),
      ),
    );
  }

  @override
  Future<Either<Failure, void>> archiveProject(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#archiveProject, [projectId]),
      returnValue: Future.value(const Right(null)),
    );
  }

  @override
  Future<Either<Failure, void>> unarchiveProject(String projectId) {
    return super.noSuchMethod(
      Invocation.method(#unarchiveProject, [projectId]),
      returnValue: Future.value(const Right(null)),
    );
  }
}
