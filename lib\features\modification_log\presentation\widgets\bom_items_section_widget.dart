import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


import '../../../../core/widgets/loading_widget.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/presentation/providers/bom_provider.dart';

part 'bom_items_section_widget.g.dart';

/// 获取BOM项目列表的Provider
@riverpod
Future<List<BomItem>> bomItemsList(Ref ref, List<String> bomItemIds) async {
  final List<BomItem> bomItems = [];

  for (final itemId in bomItemIds) {
    try {
      // 使用BOM模块的bomItemDetail Provider
      final bomItem = await ref.read(bomItemDetailProvider(itemId).future);
      bomItems.add(bomItem);
    } catch (e) {
      debugPrint('获取BOM项目异常: $e');
    }
  }

  return bomItems;
}

/// BOM项目区域组件
/// 显示与日志关联的BOM项目列表
class BomItemsSectionWidget extends ConsumerWidget {
  final List<String> bomItemIds;

  const BomItemsSectionWidget({
    super.key,
    required this.bomItemIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (bomItemIds.isEmpty) {
      return const SizedBox.shrink();
    }

    final bomItemsAsync = ref.watch(bomItemsListProvider(bomItemIds));

    return bomItemsAsync.when(
      data: (bomItems) => _buildBomItemsList(context, bomItems),
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 12),
            const Text('加载关联材料失败'),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => ref.refresh(bomItemsListProvider(bomItemIds)),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBomItemsList(BuildContext context, List<BomItem> bomItems) {
    if (bomItems.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 12),
              const Text(
                '暂无关联的材料信息',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory_2,
                  color: Colors.deepOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  '使用材料 (${bomItems.length}项)',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...bomItems.map((bomItem) => _buildBomItemTile(context, bomItem)),
          ],
        ),
      ),
    );
  }

  Widget _buildBomItemTile(BuildContext context, BomItem bomItem) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // 材料图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getCategoryColor(bomItem.category ?? ''),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getCategoryIcon(bomItem.category ?? ''),
              color: Colors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 材料信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bomItem.materialName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '数量: ${bomItem.quantity}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '单价: ¥${bomItem.unitPrice.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // 总价
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '¥${(bomItem.quantity * bomItem.unitPrice).toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(bomItem.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getStatusText(bomItem.status),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.purple;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.indigo;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '安全设备':
        return Colors.red;
      case '通讯设备':
        return Colors.teal;
      case '娱乐设备':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.directions_car;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathroom;
      case '安全设备':
        return Icons.security;
      case '通讯设备':
        return Icons.wifi;
      case '娱乐设备':
        return Icons.tv;
      default:
        return Icons.category;
    }
  }

  Color _getStatusColor(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return Colors.blue;
      case BomItemStatus.ordered:
        return Colors.orange;
      case BomItemStatus.received:
        return Colors.amber;
      case BomItemStatus.installed:
        return Colors.green;
      case BomItemStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(BomItemStatus status) {
    return status.displayName;
  }
}
