import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../repositories/log_repository.dart';

/// 获取日志条目用例
class GetLogEntryUseCase implements UseCase<LogEntry, GetLogEntryParams> {
  final LogRepository repository;

  GetLogEntryUseCase(this.repository);

  @override
  Future<Either<Failure, LogEntry>> call(GetLogEntryParams params) async {
    return await repository.getLogEntry(params.logId);
  }
}

/// 获取日志条目参数
class GetLogEntryParams {
  final String logId;

  GetLogEntryParams({required this.logId});
}