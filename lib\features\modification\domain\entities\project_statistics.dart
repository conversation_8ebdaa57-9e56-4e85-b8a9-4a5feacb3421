import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../modification_log/domain/entities/enums.dart';

part 'project_statistics.freezed.dart';
part 'project_statistics.g.dart';

/// 项目统计信息实体
/// 包含项目的各种统计数据和分析信息
@freezed
class ProjectStatistics with _$ProjectStatistics {
  const factory ProjectStatistics({
    /// 项目ID
    required String projectId,
    
    /// 总预算
    required double totalBudget,
    
    /// 总实际费用
    required double totalActualCost,
    
    /// 系统总数
    required int totalSystems,
    
    /// 已完成系统数
    required int completedSystems,
    
    /// 材料总数
    required int totalMaterials,
    
    /// 已完成材料数
    required int completedMaterials,
    
    /// 按系统类型分组的费用统计
    required Map<SystemType, double> costBySystem,
    
    /// 按月份分组的费用统计
    required Map<String, double> costByMonth,
    
    /// 按系统类型分组的材料数量统计
    @Default({}) Map<SystemType, int> materialCountBySystem,
    
    /// 按状态分组的材料数量统计
    @Default({}) Map<String, int> materialCountByStatus,
    
    /// 项目开始日期
    DateTime? projectStartDate,
    
    /// 项目预计完成日期
    DateTime? expectedCompletionDate,
    
    /// 项目实际完成日期
    DateTime? actualCompletionDate,
    
    /// 统计数据更新时间
    required DateTime updatedAt,
    
    /// 平均每个系统费用
    @Default(0.0) double averageSystemCost,
    
    /// 最贵的系统费用
    @Default(0.0) double maxSystemCost,
    
    /// 最便宜的系统费用
    @Default(0.0) double minSystemCost,
    
    /// 项目总重量（kg）
    @Default(0.0) double totalWeight,
    
    /// 已购买材料总价值
    @Default(0.0) double purchasedMaterialValue,
    
    /// 待购买材料总价值
    @Default(0.0) double pendingMaterialValue,
  }) = _ProjectStatistics;

  factory ProjectStatistics.fromJson(Map<String, dynamic> json) =>
      _$ProjectStatisticsFromJson(json);
}

/// ProjectStatistics扩展方法
extension ProjectStatisticsX on ProjectStatistics {
  /// 计算项目完成百分比
  double get completionPercentage {
    if (totalSystems == 0) return 0.0;
    return completedSystems / totalSystems;
  }
  
  /// 计算材料完成百分比
  double get materialCompletionPercentage {
    if (totalMaterials == 0) return 0.0;
    return completedMaterials / totalMaterials;
  }
  
  /// 获取预算差额（正数表示超支，负数表示节省）
  double get budgetDifference {
    return totalActualCost - totalBudget;
  }
  
  /// 是否超出预算
  bool get isOverBudget {
    return budgetDifference > 0;
  }
  
  /// 获取预算使用率
  double get budgetUtilization {
    if (totalBudget == 0) return 0.0;
    return totalActualCost / totalBudget;
  }
  
  /// 获取项目进度描述
  String get progressDescription {
    final percentage = (completionPercentage * 100).round();
    return '$percentage% 完成 ($completedSystems/$totalSystems 系统)';
  }
  
  /// 获取材料进度描述
  String get materialProgressDescription {
    final percentage = (materialCompletionPercentage * 100).round();
    return '$percentage% 完成 ($completedMaterials/$totalMaterials 材料)';
  }
  
  /// 获取预算状态描述
  String get budgetStatusDescription {
    if (isOverBudget) {
      return '超支 ¥${budgetDifference.abs().toStringAsFixed(0)}';
    } else if (budgetDifference < 0) {
      return '节省 ¥${budgetDifference.abs().toStringAsFixed(0)}';
    } else {
      return '预算内';
    }
  }
  
  /// 获取费用最高的系统类型
  SystemType? get mostExpensiveSystemType {
    if (costBySystem.isEmpty) return null;
    
    SystemType? maxType;
    double maxCost = 0;
    
    costBySystem.forEach((type, cost) {
      if (cost > maxCost) {
        maxCost = cost;
        maxType = type;
      }
    });
    
    return maxType;
  }
  
  /// 获取费用最低的系统类型
  SystemType? get leastExpensiveSystemType {
    if (costBySystem.isEmpty) return null;
    
    SystemType? minType;
    double minCost = double.infinity;
    
    costBySystem.forEach((type, cost) {
      if (cost < minCost && cost > 0) {
        minCost = cost;
        minType = type;
      }
    });
    
    return minType;
  }
  
  /// 获取月度平均支出
  double get averageMonthlySpending {
    if (costByMonth.isEmpty) return 0.0;
    
    final totalSpending = costByMonth.values.fold(0.0, (sum, cost) => sum + cost);
    return totalSpending / costByMonth.length;
  }
  
  /// 获取支出最高的月份
  String? get highestSpendingMonth {
    if (costByMonth.isEmpty) return null;
    
    String? maxMonth;
    double maxCost = 0;
    
    costByMonth.forEach((month, cost) {
      if (cost > maxCost) {
        maxCost = cost;
        maxMonth = month;
      }
    });
    
    return maxMonth;
  }
  
  /// 获取项目持续时间（天）
  int? get projectDurationDays {
    if (projectStartDate == null) return null;
    
    final endDate = actualCompletionDate ?? DateTime.now();
    return endDate.difference(projectStartDate!).inDays;
  }
  
  /// 获取预计剩余时间（天）
  int? get estimatedRemainingDays {
    if (expectedCompletionDate == null) return null;
    
    final now = DateTime.now();
    final remaining = expectedCompletionDate!.difference(now).inDays;
    return remaining > 0 ? remaining : 0;
  }
  
  /// 项目是否延期
  bool get isDelayed {
    if (expectedCompletionDate == null || actualCompletionDate != null) {
      return false;
    }
    return DateTime.now().isAfter(expectedCompletionDate!);
  }
  
  /// 获取系统类型费用占比
  Map<SystemType, double> get systemCostPercentages {
    if (totalActualCost == 0) return {};
    
    final Map<SystemType, double> percentages = {};
    costBySystem.forEach((type, cost) {
      percentages[type] = cost / totalActualCost;
    });
    
    return percentages;
  }
  
  /// 获取购买完成率
  double get purchaseCompletionRate {
    final totalMaterialValue = purchasedMaterialValue + pendingMaterialValue;
    if (totalMaterialValue == 0) return 0.0;
    return purchasedMaterialValue / totalMaterialValue;
  }
  
  /// 获取项目健康度评分（0-100）
  int get projectHealthScore {
    int score = 100;
    
    // 预算超支扣分
    if (isOverBudget) {
      final overBudgetRate = budgetDifference / totalBudget;
      score -= (overBudgetRate * 30).round().clamp(0, 30);
    }
    
    // 项目延期扣分
    if (isDelayed) {
      score -= 20;
    }
    
    // 进度缓慢扣分
    if (completionPercentage < 0.5 && projectDurationDays != null && projectDurationDays! > 90) {
      score -= 15;
    }
    
    // 材料采购滞后扣分
    if (purchaseCompletionRate < 0.7) {
      score -= 10;
    }
    
    return score.clamp(0, 100);
  }
}