// 兼容性文件 - 重新导出新的设计系统
import 'package:flutter/material.dart';
import '../design_system/vanhub_design_system.dart';

class VanHubSpacing {
  // 基础间距
  static const double xs = VanHubDesignSystem.spacing1;
  static const double sm = VanHubDesignSystem.spacing2;
  static const double md = VanHubDesignSystem.spacing4;
  static const double lg = VanHubDesignSystem.spacing6;
  static const double xl = VanHubDesignSystem.spacing8;
  
  // 内边距
  static EdgeInsets get paddingXs => EdgeInsets.all(xs);
  static EdgeInsets get paddingSm => EdgeInsets.all(sm);
  static EdgeInsets get paddingMd => EdgeInsets.all(md);
  static EdgeInsets get paddingLg => EdgeInsets.all(lg);
  static EdgeInsets get paddingXl => EdgeInsets.all(xl);
  
  // 按钮内边距
  static EdgeInsets get paddingButtonSmall => EdgeInsets.symmetric(
    horizontal: VanHubDesignSystem.spacing3,
    vertical: VanHubDesignSystem.spacing2,
  );
  
  static EdgeInsets get paddingButton => EdgeInsets.symmetric(
    horizontal: VanHubDesignSystem.spacing4,
    vertical: VanHubDesignSystem.spacing2,
  );
  
  static EdgeInsets get paddingButtonLarge => EdgeInsets.symmetric(
    horizontal: VanHubDesignSystem.spacing5,
    vertical: VanHubDesignSystem.spacing3,
  );
  
  // 页面内边距
  static EdgeInsets get paddingPageHorizontal => EdgeInsets.symmetric(
    horizontal: VanHubDesignSystem.spacing4,
  );
  
  // 间隙组件
  static Widget get gapVerticalXs => SizedBox(height: xs);
  static Widget get gapVerticalSm => SizedBox(height: sm);
  static Widget get gapVerticalMd => SizedBox(height: md);
  static Widget get gapVerticalLg => SizedBox(height: lg);
  static Widget get gapVerticalXl => SizedBox(height: xl);
  
  static Widget get gapHorizontalXs => SizedBox(width: xs);
  static Widget get gapHorizontalSm => SizedBox(width: sm);
  static Widget get gapHorizontalMd => SizedBox(width: md);
  static Widget get gapHorizontalLg => SizedBox(width: lg);
  static Widget get gapHorizontalXl => SizedBox(width: xl);
}