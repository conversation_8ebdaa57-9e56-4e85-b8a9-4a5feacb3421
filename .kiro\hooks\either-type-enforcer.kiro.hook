{"enabled": true, "name": "Either Type Enforcer", "description": "当保存Repository或UseCase文件时，确保所有可能失败的操作使用Either<Failure, Success>类型：检查Repository接口方法、Repository实现、UseCase方法和错误处理，如果发现问题则标记不符合规范的方法签名并建议正确的Either类型用法", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/features/**/repositories/*.dart", "lib/features/**/usecases/*.dart"]}, "then": {"type": "askAgent", "prompt": "请检查刚保存的Repository或UseCase文件，确保遵循Either<Failure, Success>类型规范：\n\n1. **Repository接口方法检查**：\n   - 所有异步方法必须返回Either<Failure, T>\n   - 标记任何返回Future<T>而不是Future<Either<Failure, T>>的方法\n\n2. **Repository实现检查**：\n   - 必须正确处理异常并转换为Either类型\n   - try-catch块必须将异常转换为Left(Failure)\n   - 成功结果必须包装为Right(T)\n\n3. **UseCase方法检查**：\n   - call方法必须返回Either类型\n   - 业务逻辑错误必须转换为Left(Failure)\n\n4. **错误处理检查**：\n   - 验证try-catch块的正确实现\n   - 确保异常正确转换为Failure类型\n\n如果发现问题，请：\n- 明确标记不符合规范的方法签名\n- 提供正确的Either类型用法示例\n- 提供标准的异常处理模板\n- 建议具体的修复方案\n\n请分析文件内容并提供详细的合规性报告。"}}