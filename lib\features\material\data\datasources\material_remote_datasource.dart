import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../../../../core/errors/exceptions.dart';
import '../models/material_model.dart';
import '../../domain/entities/create_material_request.dart';

abstract class MaterialRemoteDataSource {
  Future<MaterialModel> createMaterial(CreateMaterialRequest request, String userId);
  Future<List<MaterialModel>> getUserMaterials(String userId);
  Future<List<MaterialModel>> getPublicMaterials();
  Future<List<MaterialModel>> getMaterialsByCategory(String userId, String category);
  Future<MaterialModel> getMaterialById(String materialId);
  Future<MaterialModel> updateMaterial(String materialId, Map<String, dynamic> updates);
  Future<void> deleteMaterial(String materialId);
  Future<List<MaterialModel>> searchMaterials(
    String userId,
    String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  });
  Future<void> incrementUsageCount(String materialId);
  Future<List<MaterialModel>> getPopularMaterials(String userId, {int limit = 10});
  Future<List<MaterialModel>> getRecentlyUsedMaterials(String userId, {int limit = 10});
}

class MaterialRemoteDataSourceImpl implements MaterialRemoteDataSource {
  final supabase.SupabaseClient supabaseClient;

  const MaterialRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<MaterialModel> createMaterial(CreateMaterialRequest request, String userId) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .insert({
            'user_id': userId,
            'item_name': request.name,  // 修复字段映射
            'description': request.description,
            'category': request.category,
            'brand': request.brand,
            'model': request.model,
            'specification': request.specifications,  // 修复字段映射
            'reference_price': request.price,  // 修复字段映射
            'purchase_link': request.supplierUrl,  // 修复字段映射
            'usage_count': 0,
            'attributes': request.metadata,  // 修复字段映射
            // 为新字段提供默认值
            'weight': null,  // 重量可以为空
            'rating': 0.0,   // 默认评分为0
            'review_count': 0,  // 默认评价数量为0
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return MaterialModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '创建材料失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> getUserMaterials(String userId) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取用户材料失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> getPublicMaterials() async {
    try {
      // 获取所有公开的材料数据，限制数量避免性能问题
      final response = await supabaseClient
          .from('material_library')
          .select()
          .order('created_at', ascending: false)
          .limit(100); // 限制返回100条记录

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取公开材料失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> getMaterialsByCategory(String userId, String category) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .select()
          .eq('user_id', userId)
          .eq('category', category)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取分类材料失败: $e');
    }
  }

  @override
  Future<MaterialModel> getMaterialById(String materialId) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .select()
          .eq('id', materialId)
          .single();

      return MaterialModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '获取材料详情失败: $e');
    }
  }

  @override
  Future<MaterialModel> updateMaterial(String materialId, Map<String, dynamic> updates) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .update(updates)
          .eq('id', materialId)
          .select()
          .single();

      return MaterialModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新材料失败: $e');
    }
  }

  @override
  Future<void> deleteMaterial(String materialId) async {
    try {
      await supabaseClient
          .from('material_library')
          .delete()
          .eq('id', materialId);
    } catch (e) {
      throw ServerException(message: '删除材料失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> searchMaterials(
    String userId,
    String query, {
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var queryBuilder = supabaseClient
          .from('material_library')
          .select()
          .eq('user_id', userId);

      // 文本搜索
      queryBuilder = queryBuilder.or(
        'name.ilike.%$query%,description.ilike.%$query%,brand.ilike.%$query%,model.ilike.%$query%'
      );

      // 分类过滤
      if (category != null) {
        queryBuilder = queryBuilder.eq('category', category);
      }

      // 价格过滤
      if (minPrice != null) {
        queryBuilder = queryBuilder.gte('price', minPrice);
      }
      if (maxPrice != null) {
        queryBuilder = queryBuilder.lte('price', maxPrice);
      }

      final response = await queryBuilder
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '搜索材料失败: $e');
    }
  }

  @override
  Future<void> incrementUsageCount(String materialId) async {
    try {
      await supabaseClient.rpc('increment_material_usage', params: {
        'material_id': materialId,
      });
    } catch (e) {
      throw ServerException(message: '更新使用次数失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> getPopularMaterials(String userId, {int limit = 10}) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .select()
          .eq('user_id', userId)
          .gt('usage_count', 0)
          .order('usage_count', ascending: false)
          .limit(limit);

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取热门材料失败: $e');
    }
  }

  @override
  Future<List<MaterialModel>> getRecentlyUsedMaterials(String userId, {int limit = 10}) async {
    try {
      final response = await supabaseClient
          .from('material_library')
          .select()
          .eq('user_id', userId)
          .not('last_used_at', 'is', null)
          .order('last_used_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((json) => MaterialModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取最近使用材料失败: $e');
    }
  }
}