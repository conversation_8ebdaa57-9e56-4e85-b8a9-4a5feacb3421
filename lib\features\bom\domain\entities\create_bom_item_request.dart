import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_bom_item_request.freezed.dart';
part 'create_bom_item_request.g.dart';

@freezed
class CreateBomItemRequest with _$CreateBomItemRequest {
  const factory CreateBomItemRequest({
    required String projectId,
    required String userId,      // 添加userId字段，避免循环依赖
    required String name,
    required String description,
    required int quantity,
    required double unitPrice,
    String? materialId,        // 从材料库添加时提供
    String? category,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _CreateBomItemRequest;

  factory CreateBomItemRequest.fromJson(Map<String, dynamic> json) => _$CreateBomItemRequestFromJson(json);
}

/// 从材料库创建BOM项的便捷构造函数
extension CreateBomItemRequestX on CreateBomItemRequest {
  /// 从材料库创建BOM项请求
  static CreateBomItemRequest fromMaterial({
    required String projectId,
    required String userId,
    required String materialId,
    required String materialName,
    required String materialDescription,
    required int quantity,
    required double unitPrice,
    String? category,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return CreateBomItemRequest(
      projectId: projectId,
      userId: userId,
      materialId: materialId,
      name: materialName,
      description: materialDescription,
      quantity: quantity,
      unitPrice: unitPrice,
      category: category,
      brand: brand,
      model: model,
      specifications: specifications,
      supplier: supplier,
      supplierUrl: supplierUrl,
      imageUrl: imageUrl,
      plannedDate: plannedDate,
      notes: notes,
      tags: tags,
      metadata: metadata,
    );
  }
}