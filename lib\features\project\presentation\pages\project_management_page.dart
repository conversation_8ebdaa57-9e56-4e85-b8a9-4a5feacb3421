import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../providers/project_provider.dart';
import '../widgets/project_card_widget.dart';
import '../widgets/create_project_dialog_widget.dart';
import 'project_detail_page.dart';

/// VanHub项目发现页面 - 房车改装项目展示
class ProjectManagementPage extends ConsumerStatefulWidget {
  const ProjectManagementPage({super.key});

  @override
  ConsumerState<ProjectManagementPage> createState() => _ProjectManagementPageState();
}

class _ProjectManagementPageState extends ConsumerState<ProjectManagementPage> {
  String _searchQuery = '';
  String _selectedCategory = '全部';
  
  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '其他'
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // 自定义AppBar - 房车改装主题
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.deepOrange,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'VanHub 改装宝',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.deepOrange,
                      Colors.deepOrange.shade700,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // 背景图案
                    Positioned(
                      right: -50,
                      top: 20,
                      child: Icon(
                        Icons.rv_hookup,
                        size: 150,
                        color: Colors.white.withValues(alpha: 0.1),
                      ),
                    ),
                    // 欢迎文字
                    const Positioned(
                      left: 20,
                      bottom: 60,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '发现精彩改装项目',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                          Text(
                            '分享你的轮上之家',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: () {
                  ref.invalidate(publicProjectsProvider);
                },
              ),
            ],
          ),
          
          // 搜索和筛选区域
          SliverToBoxAdapter(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 搜索框
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: '搜索改装项目、车型、关键词...',
                        prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 15,
                        ),
                      ),
                      onChanged: (query) {
                        setState(() {
                          _searchQuery = query;
                        });
                      },
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 分类筛选
                  SizedBox(
                    height: 40,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _categories.length,
                      itemBuilder: (context, index) {
                        final category = _categories[index];
                        final isSelected = category == _selectedCategory;
                        
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(category),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = category;
                              });
                            },
                            backgroundColor: Colors.grey[200],
                            selectedColor: Colors.deepOrange.shade100,
                            checkmarkColor: Colors.deepOrange,
                            labelStyle: TextStyle(
                              color: isSelected ? Colors.deepOrange : Colors.grey[700],
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 项目列表
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildProjectList(),
            ),
          ),
        ],
      ),
      
      // 创建项目按钮 - 房车主题
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "project_management_fab",  // 修复Hero Widget冲突
        onPressed: _showCreateProjectDialog,
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_road),
        label: const Text('开始改装'),
      ),
    );
  }

  Widget _buildProjectList() {
    final projectsAsync = ref.watch(publicProjectsProvider());

    return projectsAsync.when(
      data: (projects) {
        // 根据搜索查询和分类过滤项目
        final filteredProjects = projects.where((project) {
          final matchesSearch = _searchQuery.trim().isEmpty ||
              project.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              project.description.toLowerCase().contains(_searchQuery.toLowerCase());
          
          final matchesCategory = _selectedCategory == '全部' ||
              (project.tags?.contains(_selectedCategory) ?? false);
          
          return matchesSearch && matchesCategory;
        }).toList();

        if (filteredProjects.isEmpty) {
          return Container(
            height: 400,
            child: EmptyStateWidget(
              icon: Icons.rv_hookup,
              title: _searchQuery.trim().isEmpty && _selectedCategory == '全部' 
                  ? '还没有改装项目' 
                  : '未找到匹配的项目',
              subtitle: _searchQuery.trim().isEmpty && _selectedCategory == '全部'
                  ? '成为第一个分享改装经验的先驱者！\n让我们一起构建房车改装知识库'
                  : '尝试调整搜索条件或浏览其他分类',
              actionText: _searchQuery.trim().isEmpty && _selectedCategory == '全部' 
                  ? '开始我的改装项目' 
                  : null,
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 结果统计
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                children: [
                  Icon(Icons.folder_open, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '找到 ${filteredProjects.length} 个改装项目',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  // 排序选项
                  TextButton.icon(
                    onPressed: () {
                      // TODO: 实现排序功能
                    },
                    icon: Icon(Icons.sort, size: 16, color: Colors.grey[600]),
                    label: Text(
                      '最新',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
            
            // 项目网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: filteredProjects.length,
              itemBuilder: (context, index) {
                final project = filteredProjects[index];
                return ProjectCardWidget(
                  project: project,
                  onTap: () => _navigateToProjectDetail(project.id),
                );
              },
            ),
            
            const SizedBox(height: 100), // 为FAB留出空间
          ],
        );
      },
      loading: () => Container(
        height: 400,
        child: const LoadingWidget(message: '发现精彩改装项目...'),
      ),
      error: (error, stack) => Container(
        height: 400,
        child: ErrorDisplayWidget(
          message: '加载失败: ${error.toString()}',
          onRetry: () {
            ref.invalidate(publicProjectsProvider);
          },
        ),
      ),
    );
  }

  void _showCreateProjectDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // 防止意外关闭
      builder: (context) => const CreateProjectDialogWidget(),
    ).then((result) {
      // 如果创建成功，刷新项目列表
      if (result != null) {
        ref.invalidate(publicProjectsProvider);
        ref.invalidate(userProjectsProvider);
      }
    });
  }

  void _navigateToProjectDetail(String projectId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProjectDetailPage(projectId: projectId),
      ),
    );
  }
}