import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// VanHub语义化组件
/// 提供统一的无障碍语义支持，增强应用的可访问性
class VanHubSemantics extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 语义标签
  final String? label;
  
  /// 语义值
  final String? value;
  
  /// 语义提示
  final String? hint;
  
  /// 是否为按钮
  final bool? button;
  
  /// 是否为链接
  final bool? link;
  
  /// 是否为标题
  final bool? header;
  
  /// 是否为文本字段
  final bool? textField;
  
  /// 是否为图片
  final bool? image;
  
  /// 是否可聚焦
  final bool? focusable;
  
  /// 是否已聚焦
  final bool? focused;
  
  /// 是否已选中
  final bool? selected;
  
  /// 是否已启用
  final bool? enabled;
  
  /// 是否已选择
  final bool? checked;
  
  /// 是否为实时区域
  final bool? liveRegion;
  
  /// 是否隐藏
  final bool? hidden;
  
  /// 点击动作
  final VoidCallback? onTap;
  
  /// 长按动作
  final VoidCallback? onLongPress;
  
  /// 滚动左动作
  final VoidCallback? onScrollLeft;
  
  /// 滚动右动作
  final VoidCallback? onScrollRight;
  
  /// 滚动上动作
  final VoidCallback? onScrollUp;
  
  /// 滚动下动作
  final VoidCallback? onScrollDown;
  
  /// 增加动作
  final VoidCallback? onIncrease;
  
  /// 减少动作
  final VoidCallback? onDecrease;
  
  /// 复制动作
  final VoidCallback? onCopy;
  
  /// 剪切动作
  final VoidCallback? onCut;
  
  /// 粘贴动作
  final VoidCallback? onPaste;
  
  /// 移动光标到开头动作
  final bool Function(bool)? onMoveCursorForwardByCharacter;

  /// 移动光标到结尾动作
  final bool Function(bool)? onMoveCursorBackwardByCharacter;
  
  /// 自定义语义属性
  final SemanticsProperties? customProperties;

  const VanHubSemantics({
    super.key,
    required this.child,
    this.label,
    this.value,
    this.hint,
    this.button,
    this.link,
    this.header,
    this.textField,
    this.image,
    this.focusable,
    this.focused,
    this.selected,
    this.enabled,
    this.checked,
    this.liveRegion,
    this.hidden,
    this.onTap,
    this.onLongPress,
    this.onScrollLeft,
    this.onScrollRight,
    this.onScrollUp,
    this.onScrollDown,
    this.onIncrease,
    this.onDecrease,
    this.onCopy,
    this.onCut,
    this.onPaste,
    this.onMoveCursorForwardByCharacter,
    this.onMoveCursorBackwardByCharacter,
    this.customProperties,
  });

  /// 创建按钮语义
  const VanHubSemantics.button({
    super.key,
    required this.child,
    this.label,
    this.hint,
    this.enabled,
    this.onTap,
    this.onLongPress,
  }) : value = null,
       button = true,
       link = null,
       header = null,
       textField = null,
       image = null,
       focusable = null,
       focused = null,
       selected = null,
       checked = null,
       liveRegion = null,
       hidden = null,
       onScrollLeft = null,
       onScrollRight = null,
       onScrollUp = null,
       onScrollDown = null,
       onIncrease = null,
       onDecrease = null,
       onCopy = null,
       onCut = null,
       onPaste = null,
       onMoveCursorForwardByCharacter = null,
       onMoveCursorBackwardByCharacter = null,
       customProperties = null;

  /// 创建标题语义
  const VanHubSemantics.header({
    super.key,
    required this.child,
    this.label,
    this.value,
  }) : hint = null,
       button = null,
       link = null,
       header = true,
       textField = null,
       image = null,
       focusable = null,
       focused = null,
       selected = null,
       enabled = null,
       checked = null,
       liveRegion = null,
       hidden = null,
       onTap = null,
       onLongPress = null,
       onScrollLeft = null,
       onScrollRight = null,
       onScrollUp = null,
       onScrollDown = null,
       onIncrease = null,
       onDecrease = null,
       onCopy = null,
       onCut = null,
       onPaste = null,
       onMoveCursorForwardByCharacter = null,
       onMoveCursorBackwardByCharacter = null,
       customProperties = null;

  /// 创建图片语义
  const VanHubSemantics.image({
    super.key,
    required this.child,
    this.label,
    this.hint,
  }) : value = null,
       button = null,
       link = null,
       header = null,
       textField = null,
       image = true,
       focusable = null,
       focused = null,
       selected = null,
       enabled = null,
       checked = null,
       liveRegion = null,
       hidden = null,
       onTap = null,
       onLongPress = null,
       onScrollLeft = null,
       onScrollRight = null,
       onScrollUp = null,
       onScrollDown = null,
       onIncrease = null,
       onDecrease = null,
       onCopy = null,
       onCut = null,
       onPaste = null,
       onMoveCursorForwardByCharacter = null,
       onMoveCursorBackwardByCharacter = null,
       customProperties = null;

  /// 创建文本字段语义
  const VanHubSemantics.textField({
    super.key,
    required this.child,
    this.label,
    this.value,
    this.hint,
    this.enabled,
    this.focused,
    this.onTap,
    this.onCopy,
    this.onCut,
    this.onPaste,
    this.onMoveCursorForwardByCharacter,
    this.onMoveCursorBackwardByCharacter,
  }) : button = null,
       link = null,
       header = null,
       textField = true,
       image = null,
       focusable = null,
       selected = null,
       checked = null,
       liveRegion = null,
       hidden = null,
       onLongPress = null,
       onScrollLeft = null,
       onScrollRight = null,
       onScrollUp = null,
       onScrollDown = null,
       onIncrease = null,
       onDecrease = null,
       customProperties = null;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: label,
      value: value,
      hint: hint,
      button: button,
      link: link,
      header: header,
      textField: textField,
      image: image,
      focusable: focusable,
      focused: focused,
      selected: selected,
      enabled: enabled,
      checked: checked,
      liveRegion: liveRegion,
      hidden: hidden,
      onTap: onTap,
      onLongPress: onLongPress,
      onScrollLeft: onScrollLeft,
      onScrollRight: onScrollRight,
      onScrollUp: onScrollUp,
      onScrollDown: onScrollDown,
      onIncrease: onIncrease,
      onDecrease: onDecrease,
      onCopy: onCopy,
      onCut: onCut,
      onPaste: onPaste,
      onMoveCursorForwardByCharacter: onMoveCursorForwardByCharacter,
      onMoveCursorBackwardByCharacter: onMoveCursorBackwardByCharacter,
      child: child,
    );
  }
}

/// VanHub语义化工厂类
class VanHubSemanticsFactory {
  VanHubSemanticsFactory._();

  /// 创建可点击的语义组件
  static Widget clickable({
    required Widget child,
    required String label,
    String? hint,
    VoidCallback? onTap,
  }) {
    return VanHubSemantics.button(
      label: label,
      hint: hint,
      onTap: onTap,
      child: child,
    );
  }

  /// 创建标题语义组件
  static Widget heading({
    required Widget child,
    required String label,
  }) {
    return VanHubSemantics.header(
      label: label,
      child: child,
    );
  }

  /// 创建图片语义组件
  static Widget imageWithDescription({
    required Widget child,
    required String description,
  }) {
    return VanHubSemantics.image(
      label: description,
      child: child,
    );
  }

  /// 创建输入框语义组件
  static Widget inputField({
    required Widget child,
    required String label,
    String? value,
    String? hint,
  }) {
    return VanHubSemantics.textField(
      label: label,
      value: value,
      hint: hint,
      child: child,
    );
  }

  /// 创建隐藏的语义组件（对屏幕阅读器隐藏）
  static Widget hidden({
    required Widget child,
  }) {
    return VanHubSemantics(
      hidden: true,
      child: child,
    );
  }

  /// 创建实时区域语义组件（用于动态内容更新）
  static Widget liveRegion({
    required Widget child,
    String? label,
  }) {
    return VanHubSemantics(
      liveRegion: true,
      label: label,
      child: child,
    );
  }
}
