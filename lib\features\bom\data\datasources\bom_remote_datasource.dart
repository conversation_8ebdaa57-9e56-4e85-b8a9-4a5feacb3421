import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:supabase_flutter/supabase_flutter.dart' show PostgrestException;
import '../../../../core/errors/exceptions.dart';
import '../models/bom_item_model.dart';
import '../../domain/entities/create_bom_item_request.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/bom_item_status.dart';

abstract class BomRemoteDataSource {
  Future<BomItemModel> createBomItem(CreateBomItemRequest request, String userId);
  Future<List<BomItemModel>> getProjectBomItems(String projectId);
  Future<List<BomItemModel>> getUserBomItems(String userId);
  Future<List<BomItemModel>> getBomItemsByStatus(String projectId, BomItemStatus status);
  Future<BomItemModel> getBomItemById(String bomItemId);
  Future<BomItemModel> updateBomItem(String bomItemId, Map<String, dynamic> updates);
  Future<void> deleteBomItem(String bomItemId);
  Future<BomItemModel> updateBomItemStatus(String bomItemId, BomItemStatus status);
  Future<List<BomItemModel>> batchUpdateBomItemStatus(List<String> bomItemIds, BomItemStatus status);
  Future<Map<String, dynamic>> getProjectBomStatistics(String projectId);
  Future<List<BomItemModel>> searchBomItems(
    String projectId,
    String query, {
    BomItemStatus? status,
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  });
  Future<List<BomItemModel>> getBomItemsByMaterialId(String materialId);
  Future<List<dynamic>> getUserProjects(String userId);
  Future<BomItemModel> addMaterialToBom({
    required String projectId,
    required String materialId,
    required String userId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  });
  Future<String> saveBomItemToMaterialLibrary(String bomItemId, String userId);
}

class BomRemoteDataSourceImpl implements BomRemoteDataSource {
  final supabase.SupabaseClient supabaseClient;

  const BomRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<BomItemModel> createBomItem(CreateBomItemRequest request, String userId) async {
    try {
      debugPrint('=== 开始创建BOM项 ===');
      debugPrint('项目ID: ${request.projectId}');
      debugPrint('用户ID: $userId');
      debugPrint('物料名称: ${request.name}');
      debugPrint('数量: ${request.quantity}');
      debugPrint('单价: ${request.unitPrice}');

      // 检查认证状态
      final currentUser = supabaseClient.auth.currentUser;
      if (currentUser == null) {
        throw ServerException(message: '用户未认证，请重新登录');
      }

      debugPrint('当前认证用户: ${currentUser.id}');

      // 确保用户ID匹配
      if (currentUser.id != userId) {
        throw ServerException(message: '用户ID不匹配，请重新登录');
      }

      // 第一步：获取或创建项目的默认commit
      debugPrint('获取或创建默认commit...');
      final commitId = await _getOrCreateDefaultCommit(request.projectId, userId);
      debugPrint('获取到commit ID: $commitId');

      // 构建attributes JSON对象，包含扩展字段
      final attributes = <String, dynamic>{
        'user_id': userId,
        'material_id': request.materialId,
        'brand': request.brand,
        'model': request.model,
        'specifications': request.specifications,
        'supplier': request.supplier,
        'supplier_url': request.supplierUrl,
        'image_url': request.imageUrl,
        'planned_date': request.plannedDate?.toIso8601String(),
        'tags': request.tags,
        'metadata': request.metadata,
      };

      // 准备插入数据
      final insertData = {
        'project_id': request.projectId,
        'commit_id': commitId,
        'item_name': request.name,
        'description': request.description,
        'quantity': request.quantity,
        'price': request.unitPrice,
        'category': request.category ?? '其他',
        'notes': request.notes,
        'status': 'pending',
        'attributes': attributes,
      };

      debugPrint('准备插入的数据: $insertData');

      // 第二步：插入BOM项目
      debugPrint('开始插入BOM项到数据库...');
      final response = await supabaseClient
          .from('bom_items')
          .insert(insertData)
          .select()
          .single();

      debugPrint('数据库返回结果: $response');

      final bomItemModel = BomItemModel.fromJson(response);
      debugPrint('成功创建BOM项: ${bomItemModel.id}');

      return bomItemModel;
    } catch (e) {
      debugPrint('创建BOM项时发生错误: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      if (e is PostgrestException) {
        debugPrint('PostgrestException详情:');
        debugPrint('  code: ${e.code}');
        debugPrint('  message: ${e.message}');
        debugPrint('  details: ${e.details}');
        debugPrint('  hint: ${e.hint}');
      }
      throw ServerException(message: '创建BOM项失败: $e');
    }
  }

  /// 获取或创建项目的默认commit
  /// 这是为了满足RLS策略要求，每个bom_item必须关联到一个commit
  Future<String> _getOrCreateDefaultCommit(String projectId, String userId) async {
    try {
      debugPrint('查找项目 $projectId 的现有commit...');

      // 首先尝试获取项目的现有commit
      final existingCommits = await supabaseClient
          .from('commits')
          .select('id')
          .eq('project_id', projectId)
          .order('created_at', ascending: false)
          .limit(1);

      debugPrint('找到 ${existingCommits.length} 个现有commit');

      if (existingCommits.isNotEmpty) {
        final commitId = existingCommits.first['id'] as String;
        debugPrint('使用现有commit: $commitId');
        return commitId;
      }

      debugPrint('没有找到现有commit，创建新的commit...');

      // 如果没有现有commit，创建一个默认commit
      final newCommit = await supabaseClient
          .from('commits')
          .insert({
            'project_id': projectId,
            'message': 'BOM项目管理',
            'title': 'BOM项目管理',
            'description': '自动创建的BOM项目管理commit',
            'status': 'active',
          })
          .select('id')
          .single();

      final newCommitId = newCommit['id'] as String;
      debugPrint('创建新commit成功: $newCommitId');
      return newCommitId;
    } catch (e) {
      debugPrint('获取或创建commit时发生错误: $e');
      if (e is PostgrestException) {
        debugPrint('PostgrestException详情:');
        debugPrint('  code: ${e.code}');
        debugPrint('  message: ${e.message}');
        debugPrint('  details: ${e.details}');
        debugPrint('  hint: ${e.hint}');
      }
      throw ServerException(message: '获取或创建commit失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> getProjectBomItems(String projectId) async {
    try {
      // 修复查询：直接查询bom_items表，RLS策略会自动处理权限验证
      // 由于RLS策略已经通过commit_id验证了项目所有权，我们可以直接查询
      final response = await supabaseClient
          .from('bom_items')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => BomItemModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取项目BOM失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> getUserBomItems(String userId) async {
    try {
      final response = await supabaseClient
          .from('bom_items')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => BomItemModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取用户BOM项失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> getBomItemsByStatus(String projectId, BomItemStatus status) async {
    try {
      // 使用JOIN查询以符合RLS策略要求
      final response = await supabaseClient
          .from('bom_items')
          .select('''
            *,
            commits!inner(
              id,
              project_id,
              message
            )
          ''')
          .eq('commits.project_id', projectId)
          .eq('status', status.code)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => BomItemModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取BOM项失败: $e');
    }
  }

  @override
  Future<BomItemModel> getBomItemById(String bomItemId) async {
    try {
      // 首先尝试简单查询，如果失败再尝试JOIN查询
      try {
        final response = await supabaseClient
            .from('bom_items')
            .select('*')
            .eq('id', bomItemId)
            .single();

        return BomItemModel.fromJson(response);
      } catch (simpleQueryError) {
        // 如果简单查询失败，尝试JOIN查询以符合RLS策略要求
        final response = await supabaseClient
            .from('bom_items')
            .select('''
              *,
              commits!inner(
                id,
                project_id,
                message
              )
            ''')
            .eq('id', bomItemId)
            .single();

        return BomItemModel.fromJson(response);
      }
    } catch (e) {
      throw ServerException(message: '获取BOM项详情失败: $e');
    }
  }

  @override
  Future<BomItemModel> updateBomItem(String bomItemId, Map<String, dynamic> updates) async {
    try {
      final response = await supabaseClient
          .from('bom_items')
          .update(updates)
          .eq('id', bomItemId)
          .select()
          .single();

      return BomItemModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新BOM项失败: $e');
    }
  }

  @override
  Future<void> deleteBomItem(String bomItemId) async {
    try {
      await supabaseClient
          .from('bom_items')
          .delete()
          .eq('id', bomItemId);
    } catch (e) {
      throw ServerException(message: '删除BOM项失败: $e');
    }
  }

  @override
  Future<BomItemModel> updateBomItemStatus(String bomItemId, BomItemStatus status) async {
    try {
      final updates = <String, dynamic>{
        'status': status.code,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // 根据新状态枚举更新相关日期字段
      switch (status) {
        case BomItemStatus.pending:
          // 重置为待采购状态时清除日期
          updates['purchase_date'] = null;
          updates['use_date'] = null;
          break;
        case BomItemStatus.ordered:
          // 已下单状态不更新日期，等待收货
          break;
        case BomItemStatus.received:
          updates['purchase_date'] = DateTime.now().toIso8601String();
          break;
        case BomItemStatus.installed:
          updates['use_date'] = DateTime.now().toIso8601String();
          break;
        case BomItemStatus.cancelled:
          // 取消状态不更新日期
          break;
      }

      final response = await supabaseClient
          .from('bom_items')
          .update(updates)
          .eq('id', bomItemId)
          .select()
          .single();

      return BomItemModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '更新BOM项状态失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> batchUpdateBomItemStatus(List<String> bomItemIds, BomItemStatus status) async {
    try {
      final updates = <String, dynamic>{
        'status': status.code,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // 根据新状态枚举更新相关日期字段
      switch (status) {
        case BomItemStatus.pending:
          // 重置为待采购状态时清除日期
          updates['purchase_date'] = null;
          updates['use_date'] = null;
          break;
        case BomItemStatus.ordered:
          // 已下单状态不更新日期，等待收货
          break;
        case BomItemStatus.received:
          updates['purchase_date'] = DateTime.now().toIso8601String();
          break;
        case BomItemStatus.installed:
          updates['use_date'] = DateTime.now().toIso8601String();
          break;
        case BomItemStatus.cancelled:
          // 取消状态不更新日期
          break;
      }

      final response = await supabaseClient
          .from('bom_items')
          .update(updates)
          .inFilter('id', bomItemIds)
          .select();

      return (response as List)
          .map((json) => BomItemModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '批量更新BOM项状态失败: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProjectBomStatistics(String projectId) async {
    try {
      final response = await supabaseClient.rpc('get_project_bom_statistics', params: {
        'project_id': projectId,
      });

      return response as Map<String, dynamic>;
    } catch (e) {
      throw ServerException(message: '获取BOM统计失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> searchBomItems(
    String projectId,
    String query, {
    BomItemStatus? status,
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var queryBuilder = supabaseClient
          .from('bom_items')
          .select()
          .eq('project_id', projectId);

      // 文本搜索
      queryBuilder = queryBuilder.or(
        'name.ilike.%$query%,description.ilike.%$query%,brand.ilike.%$query%,model.ilike.%$query%'
      );

      // 状态过滤
      if (status != null) {
        queryBuilder = queryBuilder.eq('status', status.code);
      }

      // 分类过滤
      if (category != null) {
        queryBuilder = queryBuilder.eq('category', category);
      }

      // 价格过滤
      if (minPrice != null) {
        queryBuilder = queryBuilder.gte('unit_price', minPrice);
      }
      if (maxPrice != null) {
        queryBuilder = queryBuilder.lte('unit_price', maxPrice);
      }

      final response = await queryBuilder
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => BomItemModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: '搜索BOM项失败: $e');
    }
  }

  @override
  Future<BomItemModel> addMaterialToBom({
    required String projectId,
    required String materialId,
    required String userId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  }) async {
    try {
      // 检查认证状态
      final currentUser = supabaseClient.auth.currentUser;
      if (currentUser == null) {
        throw ServerException(message: '用户未认证，请重新登录');
      }

      debugPrint('当前认证用户: ${currentUser.id}, 邮箱: ${currentUser.email}');
      debugPrint('传入的用户ID: $userId');

      // 确保用户ID匹配
      if (currentUser.id != userId) {
        throw ServerException(message: '用户ID不匹配，请重新登录');
      }
      // 首先获取材料信息 - 修复字段名映射
      final materialResponse = await supabaseClient
          .from('material_library')
          .select('''
            id,
            item_name,
            category,
            brand,
            model,
            specification,
            reference_price,
            description,
            attributes,
            purchase_link,
            weight
          ''')
          .eq('id', materialId)
          .single();

      // 使用自定义价格或材料库中的价格 - 修复字段名和类型转换
      final unitPrice = customPrice ?? ((materialResponse['reference_price'] as num?)?.toDouble() ?? 0.0);

      // 首先创建一个commit（BOM项需要关联到commit才能被RLS策略允许）
      final commitResponse = await supabaseClient
          .from('commits')
          .insert({
            'project_id': projectId,
            'message': '添加BOM项: ${materialResponse['item_name']}',
            'title': '添加BOM项',
            'description': '从材料库添加 ${materialResponse['item_name']} 到BOM',
          })
          .select()
          .single();

      final commitId = commitResponse['id'] as String;

      // 创建BOM项 - 修复字段映射以匹配数据库表结构
      final attributes = {
        'user_id': userId,
        'material_id': materialId,
        'brand': materialResponse['brand'],
        'model': materialResponse['model'],
        'specification': materialResponse['specification'],
        'purchase_link': materialResponse['purchase_link'],
        'weight': (materialResponse['weight'] as num?)?.toDouble(),
        'planned_date': plannedDate?.toIso8601String(),
      };

      // 准备INSERT数据并添加详细日志
      final insertData = {
        'project_id': projectId,
        'commit_id': commitId,
        'item_name': materialResponse['item_name'] as String? ?? '未知材料',
        'description': materialResponse['description'] as String? ?? '',
        'quantity': quantity,
        'price': unitPrice,
        'category': materialResponse['category'] as String? ?? '其他',
        'notes': notes,
        'status': 'pending',
        'attributes': attributes,
      };

      debugPrint('准备插入BOM项数据: $insertData');

      final response = await supabaseClient
          .from('bom_items')
          .insert(insertData)
          .select()
          .single();

      // 更新材料使用次数
      await supabaseClient.rpc('increment_material_usage', params: {
        'material_id': materialId,
      });

      return BomItemModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: '从材料库添加到BOM失败: $e');
    }
  }

  @override
  Future<String> saveBomItemToMaterialLibrary(String bomItemId, String userId) async {
    try {
      // 获取BOM项信息
      final bomResponse = await supabaseClient
          .from('bom_items')
          .select()
          .eq('id', bomItemId)
          .single();

      // 如果已经关联了材料库，直接返回材料ID
      if (bomResponse['material_id'] != null) {
        return bomResponse['material_id'] as String;
      }

      // 创建新的材料库项
      final materialResponse = await supabaseClient
          .from('material_library')
          .insert({
            'user_id': userId,
            'name': bomResponse['name'],
            'description': bomResponse['description'],
            'category': bomResponse['category'],
            'brand': bomResponse['brand'],
            'model': bomResponse['model'],
            'specifications': bomResponse['specifications'],
            'price': bomResponse['unit_price'],
            'supplier': bomResponse['supplier'],
            'supplier_url': bomResponse['supplier_url'],
            'image_url': bomResponse['image_url'],
            'tags': bomResponse['tags'],
            'usage_count': 1,
            'last_used_at': DateTime.now().toIso8601String(),
          })
          .select('id')
          .single();

      final materialId = materialResponse['id'] as String;

      // 更新BOM项关联材料ID
      await supabaseClient
          .from('bom_items')
          .update({'material_id': materialId})
          .eq('id', bomItemId);

      return materialId;
    } catch (e) {
      throw ServerException(message: '保存BOM项到材料库失败: $e');
    }
  }

  @override
  Future<List<BomItemModel>> getBomItemsByMaterialId(String materialId) async {
    try {
      final response = await supabaseClient
          .from('bom_items')
          .select('*')
          .eq('material_id', materialId);

      return (response as List<dynamic>)
          .map((item) => BomItemModel.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw ServerException(message: '获取材料相关BOM项失败: $e');
    }
  }

  @override
  Future<List<dynamic>> getUserProjects(String userId) async {
    try {
      final response = await supabaseClient
          .from('projects')
          .select('*')
          .eq('user_id', userId);

      return response as List<dynamic>;
    } catch (e) {
      throw ServerException(message: '获取用户项目失败: $e');
    }
  }
}