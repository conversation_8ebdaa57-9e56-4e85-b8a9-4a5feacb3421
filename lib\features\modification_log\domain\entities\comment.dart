import 'package:freezed_annotation/freezed_annotation.dart';

part 'comment.freezed.dart';
part 'comment.g.dart';

/// 评论实体
/// 代表日志条目的评论
@freezed
class Comment with _$Comment {
  const factory Comment({
    /// 评论唯一标识
    required String id,
    
    /// 关联的日志ID
    required String logId,
    
    /// 评论内容
    required String content,
    
    /// 作者ID
    required String authorId,
    
    /// 作者名称
    String? authorName,
    
    /// 作者头像URL
    String? authorAvatarUrl,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 父评论ID（用于回复）
    String? parentId,
    
    /// 提及的用户ID列表
    @Default([]) List<String> mentionedUserIds,
    
    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,
  }) = _Comment;

  factory Comment.fromJson(Map<String, dynamic> json) =>
      _$CommentFromJson(json);
}

/// Comment扩展方法
extension CommentX on Comment {
  /// 获取点赞数量
  int get likeCount => likedByUserIds.length;
  
  /// 是否为回复
  bool get isReply => parentId != null;
  
  /// 是否包含提及
  bool get hasMentions => mentionedUserIds.isNotEmpty;
  
  /// 获取最后更新时间描述
  String get lastUpdateDescription {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 用户是否点赞了该评论
  bool isLikedByUser(String userId) {
    return likedByUserIds.contains(userId);
  }
  
  /// 添加点赞
  Comment addLike(String userId) {
    if (likedByUserIds.contains(userId)) return this;
    
    return copyWith(
      likedByUserIds: [...likedByUserIds, userId],
    );
  }
  
  /// 移除点赞
  Comment removeLike(String userId) {
    if (!likedByUserIds.contains(userId)) return this;
    
    return copyWith(
      likedByUserIds: likedByUserIds.where((id) => id != userId).toList(),
    );
  }
}