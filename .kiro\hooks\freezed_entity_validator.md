# Freezed Entity Validator Hook

## Hook Configuration
```yaml
name: "Freezed Entity Validator"
description: "验证Domain层实体是否正确使用freezed"
trigger: "on_file_save"
file_patterns: ["lib/features/**/domain/entities/*.dart"]
auto_execute: true
```

## Freezed Rules

### Entity Definition
Domain层实体必须使用freezed：
```dart
// ✅ 正确
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    String? name,
    DateTime? createdAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

// ❌ 错误 - 普通类
class User {
  final String id;
  final String email;
  
  User({required this.id, required this.email});
}
```

### Required Annotations
实体必须包含：
- `@freezed` 注解
- `part` 声明
- `factory` 构造函数
- `fromJson` 工厂方法（如果需要序列化）

### Immutability Rules
- 所有字段必须是final
- 不能有setter方法
- 不能有可变的集合类型

### Value Object Pattern
复杂值对象也必须使用freezed：
```dart
// ✅ 正确
@freezed
class Email with _$Email {
  const factory Email(String value) = _Email;
  
  // 可以添加验证逻辑
  factory Email.create(String value) {
    if (!value.contains('@')) {
      throw ValidationException('Invalid email format');
    }
    return Email(value);
  }
}
```

## 执行动作
1. 检查实体是否使用freezed
2. 验证必需的注解和part声明
3. 检查不可变性规则
4. 自动生成freezed模板代码