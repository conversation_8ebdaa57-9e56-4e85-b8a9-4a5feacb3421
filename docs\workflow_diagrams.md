# VanHub改装宝 - 工作流程图

## 📊 核心业务流程图

### 1. 完整改装项目流程

```mermaid
flowchart TD
    A[开始改装项目] --> B[用户注册/登录]
    B --> C[创建新项目]
    C --> D[设置项目基本信息]
    D --> E[选择房车类型和改装系统]
    E --> F[制定BOM物料清单]
    
    F --> G[采购物料]
    G --> H[开始改装工作]
    H --> I[记录改装日志]
    I --> J[更新物料状态]
    J --> K[关联日志与物料]
    
    K --> L{改装是否完成?}
    L -->|否| H
    L -->|是| M[标记里程碑完成]
    M --> N[生成项目报告]
    N --> O[分享改装经验]
    O --> P[项目完成]
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style L fill:#fff3e0
```

### 2. 用户认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant S as Supabase认证
    participant D as 数据库
    
    U->>F: 访问应用
    F->>U: 显示欢迎页面
    
    alt 选择游客模式
        U->>F: 点击"以游客身份浏览"
        F->>U: 进入游客模式（功能受限）
    else 选择注册
        U->>F: 点击"注册新账号"
        F->>U: 显示注册表单
        U->>F: 填写注册信息
        F->>S: 发送注册请求
        S->>F: 返回注册结果
        alt 注册成功
            S->>U: 发送验证邮件
            U->>S: 点击邮件验证链接
            S->>D: 激活用户账号
            F->>U: 显示登录成功
        else 注册失败
            F->>U: 显示错误信息
        end
    else 选择登录
        U->>F: 点击"登录账号"
        F->>U: 显示登录表单
        U->>F: 输入邮箱密码
        F->>S: 发送登录请求
        S->>F: 返回认证结果
        alt 登录成功
            S->>D: 获取用户信息
            F->>U: 跳转到主页面
        else 登录失败
            F->>U: 显示错误信息
        end
    end
```

### 3. 项目创建流程

```mermaid
flowchart TD
    A[点击"开始改装"按钮] --> B[打开项目创建对话框]
    B --> C[填写项目基本信息]
    
    C --> D[项目名称验证]
    D --> E{名称是否有效?}
    E -->|否| F[显示错误提示]
    F --> C
    E -->|是| G[选择房车类型]
    
    G --> H[选择改装系统]
    H --> I[设置预算金额]
    I --> J[填写项目描述]
    
    J --> K[表单验证]
    K --> L{所有信息是否完整?}
    L -->|否| M[高亮必填字段]
    M --> C
    L -->|是| N[提交项目数据]
    
    N --> O[保存到数据库]
    O --> P{保存是否成功?}
    P -->|否| Q[显示保存失败提示]
    Q --> N
    P -->|是| R[创建默认BOM模板]
    R --> S[跳转到项目详情页]
    
    style A fill:#e3f2fd
    style S fill:#e8f5e8
    style Q fill:#ffebee
```

### 4. BOM物料管理流程

```mermaid
flowchart TD
    A[进入BOM管理页面] --> B[查看当前物料列表]
    B --> C{选择操作}
    
    C -->|添加新物料| D[选择添加方式]
    D --> E[从材料库选择]
    D --> F[手动创建物料]
    
    E --> G[浏览材料库]
    G --> H[选择目标材料]
    H --> I[设置数量和计划日期]
    I --> J[添加到BOM]
    
    F --> K[填写物料详细信息]
    K --> L[设置成本信息]
    L --> M[选择物料分类]
    M --> J
    
    C -->|编辑物料| N[选择目标物料]
    N --> O[修改物料信息]
    O --> P[更新状态]
    P --> Q[保存修改]
    
    C -->|删除物料| R[选择目标物料]
    R --> S[确认删除操作]
    S --> T[从BOM中移除]
    
    J --> U[更新项目成本]
    Q --> U
    T --> U
    U --> V[刷新BOM列表]
    V --> B
    
    style A fill:#f3e5f5
    style U fill:#e8f5e8
```

### 5. 改装日志记录流程

```mermaid
flowchart TD
    A[点击"记录日志"按钮] --> B[选择编辑器模式]
    
    B --> C{编辑器类型}
    C -->|富文本编辑器| D[HTML编辑器界面]
    C -->|简单编辑器| E[纯文本编辑器]
    
    D --> F[填写日志标题]
    E --> F
    F --> G[编写日志内容]
    G --> H[设置工作信息]
    
    H --> I[选择工作日期]
    I --> J[记录耗时]
    J --> K[设置难度等级]
    K --> L[选择日志状态]
    
    L --> M[关联BOM物料]
    M --> N{是否关联物料?}
    N -->|是| O[选择相关物料]
    O --> P[设置物料使用情况]
    P --> Q[标记里程碑]
    N -->|否| Q
    
    Q --> R{是否为里程碑?}
    R -->|是| S[设置里程碑信息]
    S --> T[上传媒体文件]
    R -->|否| T
    
    T --> U[自动保存草稿]
    U --> V[最终保存]
    V --> W{保存是否成功?}
    W -->|否| X[显示错误信息]
    X --> V
    W -->|是| Y[更新项目进度]
    Y --> Z[跳转到日志列表]
    
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style X fill:#ffebee
```

### 6. 数据同步流程

```mermaid
sequenceDiagram
    participant U as 用户操作
    participant F as 前端应用
    participant P as Provider状态
    participant R as Repository
    participant S as Supabase
    
    U->>F: 执行操作（创建/编辑/删除）
    F->>P: 调用Provider方法
    P->>R: 调用Repository
    R->>S: 发送API请求
    
    alt 操作成功
        S->>R: 返回成功结果
        R->>P: 返回Either.Right
        P->>P: 更新本地状态
        P->>F: 通知状态变化
        F->>U: 显示成功反馈
        
        Note over P: 自动刷新相关数据
        P->>R: 获取最新数据
        R->>S: 查询请求
        S->>R: 返回最新数据
        R->>P: 更新缓存
        P->>F: 刷新UI显示
    else 操作失败
        S->>R: 返回错误信息
        R->>P: 返回Either.Left
        P->>F: 传递错误信息
        F->>U: 显示错误提示
        F->>U: 提供重试选项
    end
```

### 7. 智能联动流程

```mermaid
flowchart TD
    A[用户在材料库浏览] --> B[发现需要的材料]
    B --> C[点击"添加到BOM"按钮]
    C --> D[打开BOM添加对话框]
    D --> E[自动填充材料信息]
    
    E --> F[用户设置数量]
    F --> G[选择计划日期]
    G --> H[确认添加到BOM]
    H --> I[保存到项目BOM]
    
    I --> J[更新项目成本]
    J --> K[刷新BOM列表]
    K --> L[显示成功提示]
    
    M[用户在BOM中创建物料] --> N[填写物料详细信息]
    N --> O[保存BOM物料]
    O --> P[点击"保存到材料库"]
    P --> Q[创建材料库条目]
    Q --> R[供其他项目复用]
    
    style A fill:#e3f2fd
    style L fill:#e8f5e8
    style R fill:#e8f5e8
```

### 8. 项目进度计算流程

```mermaid
flowchart TD
    A[项目数据变更] --> B{变更类型}
    
    B -->|BOM物料状态更新| C[统计物料完成情况]
    B -->|改装日志添加| D[分析日志里程碑]
    B -->|手动进度更新| E[直接更新进度]
    
    C --> F[计算物料完成百分比]
    D --> G[识别里程碑完成]
    E --> H[验证进度合理性]
    
    F --> I[更新项目统计]
    G --> I
    H --> I
    
    I --> J[重新计算总进度]
    J --> K[更新进度指标]
    K --> L[刷新仪表盘显示]
    L --> M[通知相关组件]
    
    style A fill:#fff3e0
    style M fill:#e8f5e8
```

### 9. 成本分析流程

```mermaid
flowchart TD
    A[触发成本分析] --> B[收集BOM数据]
    B --> C[按分类汇总成本]
    C --> D[计算各分类占比]
    D --> E[生成饼图数据]
    
    E --> F[计算预算使用率]
    F --> G[判断预算状态]
    G --> H{预算状态}
    
    H -->|正常| I[绿色指示器]
    H -->|接近上限| J[橙色警告]
    H -->|超出预算| K[红色警告]
    
    I --> L[更新成本图表]
    J --> L
    K --> L
    
    L --> M[显示成本分析]
    M --> N[提供优化建议]
    
    style A fill:#e1f5fe
    style N fill:#e8f5e8
    style K fill:#ffebee
```

### 10. 错误处理流程

```mermaid
flowchart TD
    A[用户操作] --> B[前端验证]
    B --> C{验证通过?}
    C -->|否| D[显示验证错误]
    D --> E[高亮错误字段]
    E --> F[提供修正建议]
    F --> A
    
    C -->|是| G[发送请求]
    G --> H[网络请求]
    H --> I{请求成功?}
    
    I -->|否| J[网络错误处理]
    J --> K[显示网络错误提示]
    K --> L[提供重试按钮]
    L --> G
    
    I -->|是| M[服务器处理]
    M --> N{业务逻辑成功?}
    N -->|否| O[业务错误处理]
    O --> P[显示具体错误信息]
    P --> Q[提供解决方案]
    Q --> A
    
    N -->|是| R[操作成功]
    R --> S[更新UI状态]
    S --> T[显示成功反馈]
    
    style D fill:#ffebee
    style K fill:#ffebee
    style P fill:#ffebee
    style T fill:#e8f5e8
```

## 📱 移动端适配流程

### 响应式设计流程

```mermaid
flowchart TD
    A[检测设备类型] --> B{屏幕尺寸}
    B -->|桌面端| C[完整功能布局]
    B -->|平板端| D[适配布局]
    B -->|手机端| E[移动端布局]
    
    C --> F[多列网格布局]
    D --> G[两列布局]
    E --> H[单列布局]
    
    F --> I[完整侧边栏]
    G --> J[折叠侧边栏]
    H --> K[底部导航栏]
    
    I --> L[显示所有功能]
    J --> L
    K --> M[精简功能菜单]
    
    L --> N[渲染界面]
    M --> N
```

## 🔄 数据备份与恢复

### 自动备份流程

```mermaid
flowchart TD
    A[定时任务触发] --> B[检查用户活跃度]
    B --> C{用户是否活跃?}
    C -->|否| D[跳过备份]
    C -->|是| E[收集用户数据]
    
    E --> F[项目数据]
    E --> G[BOM数据]
    E --> H[日志数据]
    E --> I[材料库数据]
    
    F --> J[数据压缩]
    G --> J
    H --> J
    I --> J
    
    J --> K[加密处理]
    K --> L[上传到云存储]
    L --> M{上传成功?}
    M -->|否| N[重试机制]
    N --> L
    M -->|是| O[更新备份记录]
    O --> P[清理旧备份]
    
    style A fill:#e3f2fd
    style P fill:#e8f5e8
    style N fill:#fff3e0
```

---

*本文档包含VanHub改装宝的核心业务流程图，帮助用户和开发者理解系统的工作原理。*
