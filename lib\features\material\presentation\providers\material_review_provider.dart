import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/config/supabase_config.dart';
import '../../data/datasources/material_review_remote_datasource.dart';
import '../../data/repositories/material_review_repository_impl.dart';
import '../../domain/entities/material_review.dart';
import '../../domain/repositories/material_review_repository.dart';
import '../../domain/usecases/create_material_review_usecase.dart';
import '../../domain/usecases/get_material_reviews_usecase.dart';
import '../../domain/usecases/like_review_usecase.dart';
import '../../domain/usecases/mark_review_as_helpful_usecase.dart';

part 'material_review_provider.g.dart';

// ============================================================================
// Data Sources
// ============================================================================

@riverpod
MaterialReviewRemoteDataSource materialReviewRemoteDataSource(
  MaterialReviewRemoteDataSourceRef ref,
) {
  return MaterialReviewRemoteDataSourceImpl(
    supabaseClient: SupabaseConfig.client,
  );
}

// ============================================================================
// Repositories
// ============================================================================

@riverpod
MaterialReviewRepository materialReviewRepository(
  MaterialReviewRepositoryRef ref,
) {
  return MaterialReviewRepositoryImpl(
    remoteDataSource: ref.read(materialReviewRemoteDataSourceProvider),
  );
}

// ============================================================================
// Use Cases
// ============================================================================

@riverpod
CreateMaterialReviewUseCase createMaterialReviewUseCase(
  CreateMaterialReviewUseCaseRef ref,
) {
  return CreateMaterialReviewUseCase(
    ref.read(materialReviewRepositoryProvider),
  );
}

@riverpod
GetMaterialReviewsUseCase getMaterialReviewsUseCase(
  GetMaterialReviewsUseCaseRef ref,
) {
  return GetMaterialReviewsUseCase(
    ref.read(materialReviewRepositoryProvider),
  );
}

@riverpod
GetMaterialReviewSummaryUseCase getMaterialReviewSummaryUseCase(
  GetMaterialReviewSummaryUseCaseRef ref,
) {
  return GetMaterialReviewSummaryUseCase(
    ref.read(materialReviewRepositoryProvider),
  );
}

@riverpod
MarkReviewAsHelpfulUseCase markReviewAsHelpfulUseCase(
  MarkReviewAsHelpfulUseCaseRef ref,
) {
  return MarkReviewAsHelpfulUseCase(
    ref.read(materialReviewRepositoryProvider),
  );
}

@riverpod
LikeReviewUseCase likeReviewUseCase(
  LikeReviewUseCaseRef ref,
) {
  return LikeReviewUseCase(
    ref.read(materialReviewRepositoryProvider),
  );
}

// ============================================================================
// State Providers
// ============================================================================

/// 获取材料评价列表
@riverpod
Future<List<MaterialReview>> materialReviews(
  MaterialReviewsRef ref,
  String materialId, {
  ReviewFilterCriteria? filterCriteria,
  int? limit,
  int? offset,
}) async {
  final useCase = ref.read(getMaterialReviewsUseCaseProvider);
  final result = await useCase.call(GetMaterialReviewsParams(
    materialId: materialId,
    filterCriteria: filterCriteria,
    limit: limit,
    offset: offset,
  ));

  return result.fold(
    (failure) => throw Exception(failure.message),
    (reviews) => reviews,
  );
}

/// 获取材料评价摘要
@riverpod
Future<MaterialReviewSummary> materialReviewSummary(
  MaterialReviewSummaryRef ref,
  String materialId,
) async {
  final useCase = ref.read(getMaterialReviewSummaryUseCaseProvider);
  final result = await useCase.call(GetMaterialReviewSummaryParams(
    materialId: materialId,
  ));

  return result.fold(
    (failure) => throw Exception(failure.message),
    (summary) => summary,
  );
}

/// 获取单个评价详情
@riverpod
Future<MaterialReview> materialReviewDetail(
  MaterialReviewDetailRef ref,
  String reviewId,
) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.getReview(reviewId);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (review) => review,
  );
}

/// 获取用户评价列表
@riverpod
Future<List<MaterialReview>> userReviews(
  UserReviewsRef ref,
  String userId, {
  int? limit,
  int? offset,
}) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.getUserReviews(
    userId,
    limit: limit,
    offset: offset,
  );

  return result.fold(
    (failure) => throw Exception(failure.message),
    (reviews) => reviews,
  );
}

/// 获取热门评价
@riverpod
Future<List<MaterialReview>> popularReviews(
  PopularReviewsRef ref, {
  int limit = 10,
  int timeRange = 30,
}) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.getPopularReviews(
    limit: limit,
    timeRange: timeRange,
  );

  return result.fold(
    (failure) => throw Exception(failure.message),
    (reviews) => reviews,
  );
}

/// 搜索评价
@riverpod
Future<List<MaterialReview>> searchReviews(
  SearchReviewsRef ref,
  String query, {
  ReviewFilterCriteria? filterCriteria,
  int? limit,
  int? offset,
}) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.searchReviews(
    query,
    filterCriteria: filterCriteria,
    limit: limit,
    offset: offset,
  );

  return result.fold(
    (failure) => throw Exception(failure.message),
    (reviews) => reviews,
  );
}

/// 检查用户是否已评价材料
@riverpod
Future<bool> hasUserReviewedMaterial(
  HasUserReviewedMaterialRef ref,
  String materialId,
  String userId,
) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.hasUserReviewedMaterial(materialId, userId);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (hasReviewed) => hasReviewed,
  );
}

/// 批量获取材料评价摘要
@riverpod
Future<Map<String, MaterialReviewSummary>> batchMaterialReviewSummaries(
  BatchMaterialReviewSummariesRef ref,
  List<String> materialIds,
) async {
  final repository = ref.read(materialReviewRepositoryProvider);
  final result = await repository.getBatchReviewSummaries(materialIds);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (summaries) => summaries,
  );
}

// ============================================================================
// Action Providers
// ============================================================================

/// 创建评价Action Provider
@riverpod
class CreateReviewNotifier extends _$CreateReviewNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> createReview(MaterialReview review) async {
    state = const AsyncValue.loading();
    
    final useCase = ref.read(createMaterialReviewUseCaseProvider);
    final result = await useCase.call(CreateMaterialReviewParams(review: review));

    state = result.fold(
      (failure) => AsyncValue.error(failure.message, StackTrace.current),
      (_) {
        // 刷新相关的Provider
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewSummaryProvider);
        return const AsyncValue.data(null);
      },
    );
  }
}

/// 更新评价Action Provider
@riverpod
class UpdateReviewNotifier extends _$UpdateReviewNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> updateReview(MaterialReview review) async {
    state = const AsyncValue.loading();
    
    final repository = ref.read(materialReviewRepositoryProvider);
    final result = await repository.updateReview(review);

    state = result.fold(
      (failure) => AsyncValue.error(failure.message, StackTrace.current),
      (_) {
        // 刷新相关的Provider
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewSummaryProvider);
        ref.invalidate(materialReviewDetailProvider);
        return const AsyncValue.data(null);
      },
    );
  }
}

/// 删除评价Action Provider
@riverpod
class DeleteReviewNotifier extends _$DeleteReviewNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> deleteReview(String reviewId) async {
    state = const AsyncValue.loading();
    
    final repository = ref.read(materialReviewRepositoryProvider);
    final result = await repository.deleteReview(reviewId);

    state = result.fold(
      (failure) => AsyncValue.error(failure.message, StackTrace.current),
      (_) {
        // 刷新相关的Provider
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewSummaryProvider);
        return const AsyncValue.data(null);
      },
    );
  }
}

/// 标记评价为有用Action Provider
@riverpod
class MarkHelpfulNotifier extends _$MarkHelpfulNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> markAsHelpful(String reviewId, String userId, bool isHelpful) async {
    state = const AsyncValue.loading();
    
    final useCase = ref.read(markReviewAsHelpfulUseCaseProvider);
    final result = await useCase.call(MarkReviewAsHelpfulParams(
      reviewId: reviewId,
      userId: userId,
      isHelpful: isHelpful,
    ));

    state = result.fold(
      (failure) => AsyncValue.error(failure.message, StackTrace.current),
      (_) {
        // 刷新相关的Provider
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewDetailProvider);
        return const AsyncValue.data(null);
      },
    );
  }
}

/// 点赞评价Action Provider
@riverpod
class LikeReviewNotifier extends _$LikeReviewNotifier {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> likeReview(String reviewId, String userId, bool isLiked) async {
    state = const AsyncValue.loading();
    
    final useCase = ref.read(likeReviewUseCaseProvider);
    final result = await useCase.call(LikeReviewParams(
      reviewId: reviewId,
      userId: userId,
      isLiked: isLiked,
    ));

    state = result.fold(
      (failure) => AsyncValue.error(failure.message, StackTrace.current),
      (_) {
        // 刷新相关的Provider
        ref.invalidate(materialReviewsProvider);
        ref.invalidate(materialReviewDetailProvider);
        return const AsyncValue.data(null);
      },
    );
  }
}
