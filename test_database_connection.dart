import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 命令行数据库连接测试脚本
void main() async {
  print('=== VanHub 数据库连接测试 ===\n');
  
  try {
    // 1. 加载环境变量
    print('1. 加载环境变量...');
    await dotenv.load(fileName: '.env.local');
    print('   ✅ 环境变量加载成功');
    
    // 2. 检查配置
    print('\n2. 检查配置...');
    final url = dotenv.env['SUPABASE_URL'] ?? '';
    final key = dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    
    if (url.isEmpty) {
      print('   ❌ SUPABASE_URL 未配置');
      exit(1);
    }
    if (key.isEmpty) {
      print('   ❌ SUPABASE_ANON_KEY 未配置');
      exit(1);
    }
    
    print('   ✅ URL: ${url.substring(0, 30)}...');
    print('   ✅ Key: ${key.substring(0, 20)}...');
    
    // 3. 初始化Supabase
    print('\n3. 初始化Supabase...');
    await Supabase.initialize(
      url: url,
      anonKey: key,
      debug: false,
    );
    print('   ✅ Supabase初始化成功');
    
    // 4. 测试连接
    print('\n4. 测试数据库连接...');
    final client = Supabase.instance.client;
    
    try {
      // 尝试查询一个不存在的表来测试连接
      await client.from('test_connection').select('*').limit(1);
      print('   ✅ 连接测试成功');
    } catch (e) {
      if (e.toString().contains('relation') || 
          e.toString().contains('does not exist')) {
        print('   ✅ 连接测试成功（通过错误验证）');
      } else {
        print('   ❌ 连接测试失败: $e');
        exit(1);
      }
    }
    
    // 5. 测试认证系统
    print('\n5. 测试认证系统...');
    final auth = client.auth;
    print('   ✅ 认证系统可用');
    print('   当前用户: ${auth.currentUser?.email ?? '未登录'}');
    
    // 6. 获取连接信息
    print('\n6. 连接信息摘要:');
    print('   数据库URL: $url');
    print('   连接状态: 正常');
    print('   认证状态: ${auth.currentUser != null ? '已登录' : '未登录'}');
    
    print('\n🎉 数据库连接测试全部通过！');
    
  } catch (e, stackTrace) {
    print('\n❌ 测试失败: $e');
    print('堆栈跟踪: $stackTrace');
    exit(1);
  }
}