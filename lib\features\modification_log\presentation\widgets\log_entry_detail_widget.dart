import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';

/// 日志条目详情组件
/// 遵循Clean Architecture原则，只负责UI展示
class LogEntryDetailWidget extends ConsumerWidget {
  final LogEntry logEntry;

  const LogEntryDetailWidget({
    super.key,
    required this.logEntry,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和状态
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    logEntry.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                _buildStatusChip(logEntry.status),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 系统信息
            if (logEntry.systemId.isNotEmpty) ...[
              Row(
                children: [
                  const Icon(
                    Icons.settings,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '系统: ${logEntry.systemId}',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            // 作者信息
            Row(
              children: [
                const Icon(
                  Icons.person,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  '作者: ${logEntry.authorName}',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 描述内容
            if (logEntry.content.isNotEmpty) ...[
              const Text(
                '详细内容',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  logEntry.content,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(LogStatus status) {
    Color backgroundColor;
    Color textColor;
    String label;

    switch (status) {
      case LogStatus.draft:
        backgroundColor = Colors.grey[300]!;
        textColor = Colors.grey[700]!;
        label = '草稿';
        break;
      case LogStatus.inProgress:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        label = '进行中';
        break;
      case LogStatus.completed:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        label = '已完成';
        break;
      case LogStatus.onHold:
        backgroundColor = Colors.yellow[100]!;
        textColor = Colors.yellow[700]!;
        label = '暂停';
        break;
      case LogStatus.cancelled:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        label = '已取消';
        break;
      case LogStatus.published:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        label = '已发布';
        break;
      case LogStatus.archived:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        label = '已归档';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
