import 'package:flutter/material.dart';
import '../../../foundation/colors/colors.dart';
import '../../../foundation/spacing/spacing.dart';
import '../../../foundation/typography/typography.dart';

/// 错误状态类型枚举
enum VanHubErrorType {
  /// 网络错误
  network,
  /// 服务器错误
  server,
  /// 认证错误
  auth,
  /// 验证错误
  validation,
  /// 未找到错误
  notFound,
  /// 权限错误
  permission,
  /// 通用错误
  generic,
}

/// VanHub错误状态组件
/// 提供统一的错误状态显示，支持多种错误类型和自定义操作
class VanHubErrorState extends StatelessWidget {
  /// 错误类型
  final VanHubErrorType type;
  
  /// 错误标题
  final String? title;
  
  /// 错误消息
  final String? message;
  
  /// 错误图标
  final IconData? icon;
  
  /// 重试按钮文本
  final String? retryText;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 次要操作按钮文本
  final String? secondaryActionText;
  
  /// 次要操作回调
  final VoidCallback? onSecondaryAction;
  
  /// 是否显示图标
  final bool showIcon;
  
  /// 是否居中显示
  final bool centered;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 最大宽度
  final double? maxWidth;

  const VanHubErrorState({
    super.key,
    this.type = VanHubErrorType.generic,
    this.title,
    this.message,
    this.icon,
    this.retryText,
    this.onRetry,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIcon = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  });

  /// 创建网络错误状态
  const VanHubErrorState.network({
    super.key,
    this.title,
    this.message,
    this.retryText,
    this.onRetry,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIcon = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubErrorType.network,
       icon = null;

  /// 创建服务器错误状态
  const VanHubErrorState.server({
    super.key,
    this.title,
    this.message,
    this.retryText,
    this.onRetry,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIcon = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubErrorType.server,
       icon = null;

  /// 创建认证错误状态
  const VanHubErrorState.auth({
    super.key,
    this.title,
    this.message,
    this.retryText,
    this.onRetry,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIcon = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubErrorType.auth,
       icon = null;

  /// 创建未找到错误状态
  const VanHubErrorState.notFound({
    super.key,
    this.title,
    this.message,
    this.retryText,
    this.onRetry,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIcon = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubErrorType.notFound,
       icon = null;

  @override
  Widget build(BuildContext context) {
    final content = ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? 400,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              _getErrorIcon(),
              size: 64,
              color: VanHubColors.error,
            ),
            VanHubSpacing.vLg,
          ],
          Text(
            _getErrorTitle(),
            style: VanHubTypography.headlineSmall.copyWith(
              color: VanHubColors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          VanHubSpacing.vMd,
          Text(
            _getErrorMessage(),
            style: VanHubTypography.bodyMedium.copyWith(
              color: VanHubColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          VanHubSpacing.vLg,
          _buildActions(context),
        ],
      ),
    );

    final paddedContent = padding != null
        ? Padding(padding: padding!, child: content)
        : content;

    if (centered) {
      return Center(child: paddedContent);
    }

    return paddedContent;
  }

  Widget _buildActions(BuildContext context) {
    final actions = <Widget>[];

    if (onRetry != null) {
      actions.add(
        ElevatedButton(
          onPressed: onRetry,
          child: Text(retryText ?? '重试'),
        ),
      );
    }

    if (onSecondaryAction != null) {
      actions.add(
        TextButton(
          onPressed: onSecondaryAction,
          child: Text(secondaryActionText ?? '取消'),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    if (actions.length == 1) {
      return actions.first;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        actions.first,
        VanHubSpacing.hMd,
        actions.last,
      ],
    );
  }

  IconData _getErrorIcon() {
    if (icon != null) return icon!;

    switch (type) {
      case VanHubErrorType.network:
        return Icons.wifi_off;
      case VanHubErrorType.server:
        return Icons.cloud_off;
      case VanHubErrorType.auth:
        return Icons.lock_outline;
      case VanHubErrorType.validation:
        return Icons.warning_outlined;
      case VanHubErrorType.notFound:
        return Icons.search_off;
      case VanHubErrorType.permission:
        return Icons.block;
      case VanHubErrorType.generic:
        return Icons.error_outline;
    }
  }

  String _getErrorTitle() {
    if (title != null) return title!;

    switch (type) {
      case VanHubErrorType.network:
        return '网络连接失败';
      case VanHubErrorType.server:
        return '服务器错误';
      case VanHubErrorType.auth:
        return '认证失败';
      case VanHubErrorType.validation:
        return '输入验证失败';
      case VanHubErrorType.notFound:
        return '内容未找到';
      case VanHubErrorType.permission:
        return '权限不足';
      case VanHubErrorType.generic:
        return '出现错误';
    }
  }

  String _getErrorMessage() {
    if (message != null) return message!;

    switch (type) {
      case VanHubErrorType.network:
        return '请检查您的网络连接，然后重试';
      case VanHubErrorType.server:
        return '服务器暂时无法响应，请稍后重试';
      case VanHubErrorType.auth:
        return '您的登录状态已过期，请重新登录';
      case VanHubErrorType.validation:
        return '请检查输入的信息是否正确';
      case VanHubErrorType.notFound:
        return '您要查找的内容不存在或已被删除';
      case VanHubErrorType.permission:
        return '您没有权限执行此操作';
      case VanHubErrorType.generic:
        return '发生了未知错误，请重试';
    }
  }
}

/// VanHub错误状态工厂类
class VanHubErrorStateFactory {
  VanHubErrorStateFactory._();

  /// 创建网络错误
  static Widget networkError({VoidCallback? onRetry}) {
    return VanHubErrorState.network(
      onRetry: onRetry,
      retryText: '重新连接',
    );
  }

  /// 创建服务器错误
  static Widget serverError({VoidCallback? onRetry}) {
    return VanHubErrorState.server(
      onRetry: onRetry,
      retryText: '重试',
    );
  }

  /// 创建认证错误
  static Widget authError({VoidCallback? onLogin}) {
    return VanHubErrorState.auth(
      onRetry: onLogin,
      retryText: '重新登录',
    );
  }

  /// 创建未找到错误
  static Widget notFoundError({VoidCallback? onGoBack}) {
    return VanHubErrorState.notFound(
      onRetry: onGoBack,
      retryText: '返回',
    );
  }
}
