{"enabled": true, "name": "VanHub Dart Structure Enforcer", "description": "Enforces VanHub project's code structure standards when creating new Dart files: validates file location, naming conventions (snake_case), module structure (domain/data/presentation layers), import standards, and generates template code. Suggests correct file locations and creates necessary folders/templates if structure is incorrect.", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/features/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "A new Dart file has been created in the VanHub project features directory. Please validate and enforce the following code structure standards:\n\n1. **File Location Validation**: \n   - Ensure the file is in the correct directory structure within lib/features/\n   - Each feature should follow the pattern: lib/features/{feature_name}/{layer}/\n   - Valid layers are: domain/, data/, presentation/\n\n2. **Naming Convention Validation**:\n   - File names must follow snake_case convention\n   - Check if the file name matches Dart naming standards\n\n3. **Module Structure Validation**:\n   - Each feature must contain three layers: domain/, data/, presentation/\n   - Domain layer should have: entities/, repositories/, usecases/\n   - Data layer should have: models/, datasources/, repositories/\n   - Presentation layer should have: pages/, providers/, widgets/\n\n4. **Import Standards Validation**:\n   - Check import statements comply with layered architecture requirements\n   - Domain layer should not import from data or presentation layers\n   - Data layer should not import from presentation layer\n   - Presentation layer can import from both domain and data layers\n\n5. **Template Code Generation**:\n   - Generate appropriate template code based on the file location and type\n   - For entities: use freezed templates with proper annotations\n   - For repositories: use Either<Failure, Success> return types\n   - For providers: use Riverpod Notifier patterns\n   - For pages: use ConsumerWidget templates\n\nIf the structure is incorrect:\n- Suggest the correct file location\n- Provide standard directory structure recommendations\n- Auto-generate necessary folders and template files\n- Show examples of proper Clean Architecture implementation\n\nFocus on VanHub's specific requirements:\n- All entities must use freezed\n- All repository methods must return Either<Failure, Success>\n- All UI components must use ConsumerWidget\n- State management through Riverpod Notifier\n- Follow the established patterns from auth/, project/, material/, and bom/ modules"}}