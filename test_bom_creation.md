# BOM创建功能测试指南

## 修复内容总结

### 1. 字段映射修复
- `materialName` → `item_name`
- `unitPrice` → `price`
- `purchasedDate` → `purchase_date`
- `usedDate` → `use_date`
- 添加了 `commit_id` 字段支持

### 2. 错误处理改进
- 添加了详细的调试日志
- 改进了异常处理和错误信息
- 添加了PostgrestException的详细错误信息

### 3. 数据类型转换修复
- 修复了数字类型转换问题
- 改进了JSON解析的容错性
- 添加了try-catch包装

## 测试步骤

### 1. 编译测试
```bash
flutter pub get
flutter analyze
```

### 2. 运行应用
```bash
flutter run
```

### 3. 功能测试
1. 登录应用
2. 进入项目详情页面
3. 点击"BOM"标签
4. 点击"手动添加BOM项"按钮
5. 填写表单：
   - 物料名称：测试材料
   - 分类：电力系统
   - 数量：1
   - 单价：100
6. 点击"创建BOM项"按钮

### 4. 预期结果
- 不再出现"Bad state: Future already completed"错误
- 成功创建BOM项并显示在列表中
- 控制台显示详细的调试信息

### 5. 错误排查
如果仍然出现错误，检查控制台输出：

1. **认证错误**：检查用户是否已登录
2. **权限错误**：检查RLS策略配置
3. **字段错误**：检查数据库表结构是否匹配
4. **网络错误**：检查Supabase连接

## 可能的数据库表结构

基于代码分析，实际的bom_items表结构应该是：

```sql
CREATE TABLE bom_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    commit_id UUID NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    price DECIMAL(10,2) DEFAULT 0,
    category VARCHAR(100),
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    attributes JSONB,
    purchase_date TIMESTAMP,
    use_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 下一步优化

1. **移除调试日志**：测试成功后移除print语句
2. **优化错误处理**：简化错误信息，提高用户体验
3. **添加单元测试**：为BOM创建功能添加自动化测试
4. **性能优化**：优化数据库查询和状态管理