import 'package:freezed_annotation/freezed_annotation.dart';

part 'template_statistics.freezed.dart';
part 'template_statistics.g.dart';

/// Template statistics entity for BOM templates
@freezed
class TemplateStatistics with _$TemplateStatistics {
  const factory TemplateStatistics({
    required String templateId,
    required int usageCount,
    required double averageRating,
    required int totalRatings,
    required DateTime lastUsed,
    required DateTime createdAt,
    @Default(0) int downloadCount,
    @Default(0) int favoriteCount,
    @Default([]) List<String> popularCategories,
  }) = _TemplateStatistics;

  factory TemplateStatistics.fromJson(Map<String, dynamic> json) =>
      _$TemplateStatisticsFromJson(json);
}
