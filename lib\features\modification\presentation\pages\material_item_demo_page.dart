import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/system_material.dart';
import '../widgets/material_item_widget.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../modification_log/domain/entities/enums.dart';

/// MaterialItem组件演示页面
class MaterialItemDemoPage extends ConsumerStatefulWidget {
  const MaterialItemDemoPage({super.key});

  @override
  ConsumerState<MaterialItemDemoPage> createState() => _MaterialItemDemoPageState();
}

class _MaterialItemDemoPageState extends ConsumerState<MaterialItemDemoPage> {
  late List<SystemMaterial> _materials;

  @override
  void initState() {
    super.initState();
    _materials = _createDemoMaterials();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MaterialItem 组件演示'),
        backgroundColor: VanHubColors.primary,
        foregroundColor: VanHubColors.onPrimary,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: _materials.length * 2, // 每个材料显示完整版和紧凑版
        itemBuilder: (context, index) {
          final materialIndex = index ~/ 2;
          final isCompact = index % 2 == 1;
          final material = _materials[materialIndex];

          if (isCompact) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: VanHubSpacing.symmetric(
                    horizontal: VanHubSpacing.md,
                    vertical: VanHubSpacing.sm,
                  ),
                  child: Text(
                    '紧凑模式',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                MaterialItem(
                  material: material,
                  compact: true,
                  onTap: () => _showMaterialDetails(material),
                  onEdit: () => _editMaterial(material),
                  onDelete: () => _deleteMaterial(material),
                  onStatusChange: (purchaseStatus, installStatus) =>
                      _updateMaterialStatus(material, purchaseStatus, installStatus),
                ),
                SizedBox(height: VanHubSpacing.lg),
              ],
            );
          } else {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: VanHubSpacing.symmetric(
                    horizontal: VanHubSpacing.md,
                    vertical: VanHubSpacing.sm,
                  ),
                  child: Text(
                    '完整模式 - ${material.name}',
                    style: VanHubTypography.titleMedium,
                  ),
                ),
                MaterialItem(
                  material: material,
                  compact: false,
                  onTap: () => _showMaterialDetails(material),
                  onEdit: () => _editMaterial(material),
                  onDelete: () => _deleteMaterial(material),
                  onStatusChange: (purchaseStatus, installStatus) =>
                      _updateMaterialStatus(material, purchaseStatus, installStatus),
                ),
                SizedBox(height: VanHubSpacing.md),
              ],
            );
          }
        },
      ),
    );
  }

  /// 创建演示用的物料数据
  List<SystemMaterial> _createDemoMaterials() {
    final now = DateTime.now();
    
    return [
      SystemMaterial(
        id: '1',
        materialId: 'mat_001',
        name: '逆变器 3000W',
        specification: 'AIMS 3000W 纯正弦波逆变器',
        brand: 'AIMS Power',
        quantity: 1,
        unitPrice: 3000.0,
        totalPrice: 3000.0,
        purchaseStatus: MaterialStatus.received,
        installStatus: MaterialStatus.pending,
        supplier: '电力设备专营店',
        category: '电路系统',
        weight: 15.5,
        dimensions: '350×200×100mm',
        warrantyMonths: 24,
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 2)),
        purchaseDate: now.subtract(const Duration(days: 5)),
        expectedDeliveryDate: now.subtract(const Duration(days: 3)),
      ),
      SystemMaterial(
        id: '2',
        materialId: 'mat_002',
        name: '锂电池组 200Ah',
        specification: '磷酸铁锂电池 12V 200Ah',
        brand: 'Battle Born',
        quantity: 2,
        unitPrice: 4000.0,
        totalPrice: 8000.0,
        purchaseStatus: MaterialStatus.ordered,
        installStatus: MaterialStatus.pending,
        supplier: '新能源电池商城',
        category: '电路系统',
        weight: 25.0,
        dimensions: '330×173×220mm',
        warrantyMonths: 60,
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 1)),
        expectedDeliveryDate: now.add(const Duration(days: 3)),
      ),
      SystemMaterial(
        id: '3',
        materialId: 'mat_003',
        name: '水泵 12V',
        specification: '隔膜式水泵 12V 5.5L/min',
        brand: 'Seaflo',
        quantity: 1,
        unitPrice: 280.0,
        totalPrice: 280.0,
        purchaseStatus: MaterialStatus.installed,
        installStatus: MaterialStatus.installed,
        supplier: '房车配件专营',
        category: '水路系统',
        weight: 1.2,
        dimensions: '120×80×60mm',
        warrantyMonths: 12,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 7)),
        purchaseDate: now.subtract(const Duration(days: 25)),
        installDate: now.subtract(const Duration(days: 7)),
      ),
      SystemMaterial(
        id: '4',
        materialId: 'mat_004',
        name: '储物柜',
        specification: '多功能储物柜 60×40×80cm',
        quantity: 3,
        unitPrice: 450.0,
        totalPrice: 1350.0,
        purchaseStatus: MaterialStatus.pending,
        installStatus: MaterialStatus.pending,
        category: '储物系统',
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
    ];
  }

  /// 显示物料详情
  void _showMaterialDetails(SystemMaterial material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(material.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('规格: ${material.specification}'),
            if (material.brand != null) Text('品牌: ${material.brand}'),
            Text('数量: ${material.quantity}个'),
            Text('单价: ¥${material.unitPrice.toStringAsFixed(0)}'),
            Text('总价: ¥${material.totalPrice.toStringAsFixed(0)}'),
            Text('购买状态: ${material.purchaseStatusDisplayText}'),
            Text('安装状态: ${material.installStatusDisplayText}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 编辑物料
  void _editMaterial(SystemMaterial material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('编辑物料: ${material.name}'),
        backgroundColor: VanHubColors.info,
      ),
    );
  }

  /// 删除物料
  void _deleteMaterial(SystemMaterial material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除物料"${material.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _materials.removeWhere((m) => m.id == material.id);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已删除物料: ${material.name}'),
                  backgroundColor: VanHubColors.success,
                ),
              );
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 更新物料状态
  void _updateMaterialStatus(
    SystemMaterial material,
    MaterialStatus purchaseStatus,
    MaterialStatus installStatus,
  ) {
    setState(() {
      final index = _materials.indexWhere((m) => m.id == material.id);
      if (index != -1) {
        _materials[index] = _materials[index].copyWith(
          purchaseStatus: purchaseStatus,
          installStatus: installStatus,
          updatedAt: DateTime.now(),
        );
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已更新物料状态: ${material.name}'),
        backgroundColor: VanHubColors.success,
      ),
    );
  }
}