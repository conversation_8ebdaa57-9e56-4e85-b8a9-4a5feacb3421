import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/project.dart';
import '../entities/fork_request.dart';
import '../entities/fork_result.dart';
import '../entities/fork_statistics.dart';

/// 项目复刻服务接口
/// 遵循Clean Architecture原则，定义项目复刻相关的业务逻辑
abstract class ProjectForkService {
  /// 复刻项目
  ///
  /// 执行完整的项目复刻流程，包括：
  /// - 验证复刻权限
  /// - 复制项目基本信息
  /// - 复制BOM项目（如果选择）
  /// - 复制系统信息（如果选择）
  /// - 复制图片资源（如果选择）
  /// - 建立复刻关系
  Future<Either<Failure, ForkResult>> forkProject(ForkRequest request);

  /// 获取项目的复刻历史
  ///
  /// 返回指定项目的所有复刻记录
  Future<Either<Failure, List<Project>>> getForkHistory(String projectId);

  /// 获取复刻项目的源项目
  ///
  /// 如果项目是复刻而来，返回其源项目信息
  Future<Either<Failure, Project?>> getSourceProject(String forkedProjectId);

  /// 获取项目的复刻统计信息
  ///
  /// 返回项目被复刻的次数和相关统计
  Future<Either<Failure, ForkStatistics>> getForkStatistics(String projectId);

  /// 检查项目是否可以被复刻
  ///
  /// 验证项目的复刻设置和权限
  Future<Either<Failure, bool>> canForkProject(String projectId, String userId);
}