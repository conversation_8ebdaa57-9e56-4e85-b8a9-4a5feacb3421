import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../providers/material_favorite_provider.dart';
import '../providers/material_review_provider.dart';
import '../../../bom/presentation/providers/bom_provider.dart';

/// 增强的物料详情对话框
/// 提供丰富的物料信息展示和交互功能
class EnhancedMaterialDetailDialog extends ConsumerStatefulWidget {
  final domain.Material material;
  final String? projectId;

  const EnhancedMaterialDetailDialog({
    super.key,
    required this.material,
    this.projectId,
  });

  @override
  ConsumerState<EnhancedMaterialDetailDialog> createState() =>
      _EnhancedMaterialDetailDialogState();
}

class _EnhancedMaterialDetailDialogState
    extends ConsumerState<EnhancedMaterialDetailDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isFavorited = false;
  int _selectedQuantity = 1;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _checkFavoriteStatus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _checkFavoriteStatus() async {
    // TODO: 检查收藏状态
    setState(() {
      _isFavorited = false; // 临时设置
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildSpecificationsTab(),
                  _buildUsageHistoryTab(),
                  _buildReviewsTab(),
                ],
              ),
            ),
            _buildActionBar(),
          ],
        ),
      ),
    );
  }

  /// 构建头部区域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor(widget.material.category),
            _getCategoryColor(widget.material.category).withValues(alpha: 0.7),
          ],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 物料图片
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: widget.material.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          widget.material.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildDefaultIcon(),
                        ),
                      )
                    : _buildDefaultIcon(),
              ),
              
              const SizedBox(width: 16),
              
              // 物料基本信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.material.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            widget.material.category,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (widget.material.brand?.isNotEmpty == true) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              widget.material.brand!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              // 操作按钮
              Column(
                children: [
                  IconButton(
                    onPressed: _toggleFavorite,
                    icon: Icon(
                      _isFavorited ? Icons.favorite : Icons.favorite_border,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  IconButton(
                    onPressed: _shareMaterial,
                    icon: const Icon(
                      Icons.share,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 价格和评分
          Row(
            children: [
              Text(
                '¥${widget.material.price.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              _buildRatingStars(4.5), // TODO: 从实际数据获取
              const SizedBox(width: 8),
              const Text(
                '(23条评价)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.5),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.blue,
        unselectedLabelColor: Colors.grey,
        indicatorColor: Colors.blue,
        tabs: const [
          Tab(text: '概览'),
          Tab(text: '规格'),
          Tab(text: '使用记录'),
          Tab(text: '评价'),
        ],
      ),
    );
  }

  /// 构建概览标签页
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 描述信息
          if (widget.material.description.isNotEmpty) ...[
            const Text(
              '产品描述',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.material.description,
              style: const TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 20),
          ],
          
          // 关键信息卡片
          _buildInfoCards(),
          
          const SizedBox(height: 20),
          
          // 使用统计
          _buildUsageStats(),
          
          const SizedBox(height: 20),
          
          // 相关推荐
          _buildRelatedMaterials(),
        ],
      ),
    );
  }

  /// 构建规格标签页
  Widget _buildSpecificationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '技术规格',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSpecificationsList(),
        ],
      ),
    );
  }

  /// 构建使用记录标签页
  Widget _buildUsageHistoryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '使用历史',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildUsageHistory(),
        ],
      ),
    );
  }

  /// 构建评价标签页
  Widget _buildReviewsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '用户评价',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: _writeReview,
                child: const Text('写评价'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildReviewsList(),
        ],
      ),
    );
  }

  /// 构建操作栏
  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 数量选择器
          if (widget.projectId != null) ...[
            const Text('数量: '),
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: _selectedQuantity > 1
                        ? () => setState(() => _selectedQuantity--)
                        : null,
                    icon: const Icon(Icons.remove),
                    iconSize: 20,
                  ),
                  Container(
                    width: 40,
                    alignment: Alignment.center,
                    child: Text(
                      _selectedQuantity.toString(),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  IconButton(
                    onPressed: () => setState(() => _selectedQuantity++),
                    icon: const Icon(Icons.add),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            const Spacer(),
          ],
          
          // 主要操作按钮
          if (widget.projectId != null) ...[
            ElevatedButton.icon(
              onPressed: _addToBom,
              icon: const Icon(Icons.add_shopping_cart),
              label: const Text('添加到BOM'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ] else ...[
            ElevatedButton.icon(
              onPressed: _collectMaterial,
              icon: const Icon(Icons.bookmark_add),
              label: const Text('收藏物料'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 辅助方法和组件构建方法
  Widget _buildDefaultIcon() {
    return Icon(
      _getCategoryIcon(widget.material.category),
      size: 40,
      color: _getCategoryColor(widget.material.category),
    );
  }

  Widget _buildRatingStars(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
                  ? Icons.star_half
                  : Icons.star_border,
          color: Colors.amber,
          size: 16,
        );
      }),
    );
  }

  Widget _buildInfoCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                '使用次数',
                '${widget.material.usageCount}',
                Icons.repeat,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                '平均评分',
                '4.5',
                Icons.star,
                Colors.amber,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                '创建时间',
                _formatDate(widget.material.createdAt),
                Icons.calendar_today,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                '更新时间',
                _formatDate(widget.material.updatedAt),
                Icons.update,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageStats() {
    // TODO: 实现使用统计图表
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('使用统计图表'),
      ),
    );
  }

  Widget _buildRelatedMaterials() {
    // TODO: 实现相关推荐
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '相关推荐',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 100,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Text('相关物料推荐'),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecificationsList() {
    final specs = [
      if (widget.material.brand?.isNotEmpty == true)
        {'label': '品牌', 'value': widget.material.brand!},
      if (widget.material.model?.isNotEmpty == true)
        {'label': '型号', 'value': widget.material.model!},
      {'label': '分类', 'value': widget.material.category},
      {'label': '价格', 'value': '¥${widget.material.price.toStringAsFixed(2)}'},
      {'label': '创建时间', 'value': _formatDate(widget.material.createdAt)},
      {'label': '更新时间', 'value': _formatDate(widget.material.updatedAt)},
    ];

    return Column(
      children: specs.map((spec) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  spec['label']!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  spec['value']!,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildUsageHistory() {
    // TODO: 实现使用历史
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('使用历史记录'),
      ),
    );
  }

  Widget _buildReviewsList() {
    // TODO: 实现评价列表
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Text('用户评价列表'),
      ),
    );
  }

  // 事件处理方法
  void _toggleFavorite() {
    setState(() {
      _isFavorited = !_isFavorited;
    });
    // TODO: 实现收藏功能
  }

  void _shareMaterial() {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能开发中...')),
    );
  }

  void _addToBom() {
    // TODO: 实现添加到BOM功能
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已添加 ${_selectedQuantity} 个 ${widget.material.name} 到BOM'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _collectMaterial() {
    // TODO: 实现收藏物料功能
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已收藏 ${widget.material.name}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _writeReview() {
    // TODO: 实现写评价功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('写评价功能开发中...')),
    );
  }

  // 辅助方法
  Color _getCategoryColor(String category) {
    final colors = {
      '电气': Colors.yellow.shade700,
      '水路': Colors.blue,
      '燃气': Colors.red,
      '家具': Colors.brown,
      '装饰': Colors.purple,
      '工具': Colors.grey,
      '其他': Colors.teal,
    };
    return colors[category] ?? Colors.grey;
  }

  IconData _getCategoryIcon(String category) {
    final icons = {
      '电气': Icons.electrical_services,
      '水路': Icons.water_drop,
      '燃气': Icons.local_fire_department,
      '家具': Icons.chair,
      '装饰': Icons.palette,
      '工具': Icons.build,
      '其他': Icons.category,
    };
    return icons[category] ?? Icons.category;
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
