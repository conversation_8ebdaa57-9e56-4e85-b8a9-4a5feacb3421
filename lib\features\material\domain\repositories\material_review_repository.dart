import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/failures.dart';
import '../entities/material_review.dart';

/// 材料评价仓储接口
/// 遵循Clean Architecture原则，定义材料评价相关的数据操作抽象
abstract class MaterialReviewRepository {
  /// 创建材料评价
  /// 
  /// [review] 要创建的评价实体
  /// 返回 Either<Failure, MaterialReview> 创建结果
  Future<Either<Failure, MaterialReview>> createReview(MaterialReview review);

  /// 更新材料评价
  /// 
  /// [review] 要更新的评价实体
  /// 返回 Either<Failure, MaterialReview> 更新结果
  Future<Either<Failure, MaterialReview>> updateReview(MaterialReview review);

  /// 删除材料评价
  /// 
  /// [reviewId] 要删除的评价ID
  /// 返回 Either<Failure, void> 删除结果
  Future<Either<Failure, void>> deleteReview(String reviewId);

  /// 获取单个材料评价
  /// 
  /// [reviewId] 评价ID
  /// 返回 Either<Failure, MaterialReview> 评价详情
  Future<Either<Failure, MaterialReview>> getReview(String reviewId);

  /// 获取材料的所有评价
  /// 
  /// [materialId] 材料ID
  /// [filterCriteria] 过滤条件
  /// [limit] 限制数量
  /// [offset] 偏移量
  /// 返回 Either<Failure, List<MaterialReview>> 评价列表
  Future<Either<Failure, List<MaterialReview>>> getMaterialReviews(
    String materialId, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  });

  /// 获取用户的所有评价
  /// 
  /// [userId] 用户ID
  /// [limit] 限制数量
  /// [offset] 偏移量
  /// 返回 Either<Failure, List<MaterialReview>> 用户评价列表
  Future<Either<Failure, List<MaterialReview>>> getUserReviews(
    String userId, {
    int? limit,
    int? offset,
  });

  /// 获取材料评价摘要
  /// 
  /// [materialId] 材料ID
  /// 返回 Either<Failure, MaterialReviewSummary> 评价摘要
  Future<Either<Failure, MaterialReviewSummary>> getMaterialReviewSummary(
    String materialId,
  );

  /// 标记评价为有用
  /// 
  /// [reviewId] 评价ID
  /// [userId] 用户ID
  /// 返回 Either<Failure, void> 操作结果
  Future<Either<Failure, void>> markReviewAsHelpful(
    String reviewId,
    String userId,
  );

  /// 取消标记评价为有用
  /// 
  /// [reviewId] 评价ID
  /// [userId] 用户ID
  /// 返回 Either<Failure, void> 操作结果
  Future<Either<Failure, void>> unmarkReviewAsHelpful(
    String reviewId,
    String userId,
  );

  /// 点赞评价
  /// 
  /// [reviewId] 评价ID
  /// [userId] 用户ID
  /// 返回 Either<Failure, void> 操作结果
  Future<Either<Failure, void>> likeReview(
    String reviewId,
    String userId,
  );

  /// 取消点赞评价
  /// 
  /// [reviewId] 评价ID
  /// [userId] 用户ID
  /// 返回 Either<Failure, void> 操作结果
  Future<Either<Failure, void>> unlikeReview(
    String reviewId,
    String userId,
  );

  /// 检查用户是否已评价某材料
  /// 
  /// [materialId] 材料ID
  /// [userId] 用户ID
  /// 返回 Either<Failure, bool> 是否已评价
  Future<Either<Failure, bool>> hasUserReviewedMaterial(
    String materialId,
    String userId,
  );

  /// 获取热门评价
  /// 
  /// [limit] 限制数量
  /// [timeRange] 时间范围（天数）
  /// 返回 Either<Failure, List<MaterialReview>> 热门评价列表
  Future<Either<Failure, List<MaterialReview>>> getPopularReviews({
    int limit = 10,
    int timeRange = 30,
  });

  /// 搜索评价
  /// 
  /// [query] 搜索关键词
  /// [filterCriteria] 过滤条件
  /// [limit] 限制数量
  /// [offset] 偏移量
  /// 返回 Either<Failure, List<MaterialReview>> 搜索结果
  Future<Either<Failure, List<MaterialReview>>> searchReviews(
    String query, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  });

  /// 获取评价统计信息
  /// 
  /// [materialIds] 材料ID列表
  /// 返回 Either<Failure, Map<String, MaterialReviewSummary>> 统计信息映射
  Future<Either<Failure, Map<String, MaterialReviewSummary>>> 
      getBatchReviewSummaries(List<String> materialIds);

  /// 举报评价
  /// 
  /// [reviewId] 评价ID
  /// [reporterId] 举报者ID
  /// [reason] 举报原因
  /// 返回 Either<Failure, void> 操作结果
  Future<Either<Failure, void>> reportReview(
    String reviewId,
    String reporterId,
    String reason,
  );
}
