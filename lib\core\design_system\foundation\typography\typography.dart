import 'package:flutter/material.dart';
import '../colors/colors.dart';

/// VanHub字体系统
/// 定义各级标题、正文、说明文字样式
class VanHubTypography {
  VanHubTypography._();

  // ============================================================================
  // 字体族 (Font Families)
  // ============================================================================
  
  /// 主要字体族
  static const String primaryFontFamily = 'PingFang SC';
  
  /// 辅助字体族
  static const String secondaryFontFamily = 'Helvetica Neue';
  
  /// 等宽字体族
  static const String monospaceFontFamily = 'SF Mono';

  // ============================================================================
  // 显示文本样式 (Display Text Styles)
  // ============================================================================
  
  /// 大型显示文本 - 用于重要标题
  static const TextStyle displayLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
    color: VanHubColors.onBackground,
  );

  /// 中型显示文本 - 用于次要标题
  static const TextStyle displayMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 45,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.16,
    color: VanHubColors.onBackground,
  );

  /// 小型显示文本 - 用于小标题
  static const TextStyle displaySmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 36,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.22,
    color: VanHubColors.onBackground,
  );

  // ============================================================================
  // 标题文本样式 (Headline Text Styles)
  // ============================================================================
  
  /// 大型标题 - 用于页面主标题
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
    color: VanHubColors.onBackground,
  );

  /// 中型标题 - 用于章节标题
  static const TextStyle headlineMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.29,
    color: VanHubColors.onBackground,
  );

  /// 小型标题 - 用于子章节标题
  static const TextStyle headlineSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.33,
    color: VanHubColors.onBackground,
  );

  // ============================================================================
  // 标题文本样式 (Title Text Styles)
  // ============================================================================
  
  /// 大型标题 - 用于卡片标题
  static const TextStyle titleLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.27,
    color: VanHubColors.onSurface,
  );

  /// 中型标题 - 用于列表项标题
  static const TextStyle titleMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    height: 1.50,
    color: VanHubColors.onSurface,
  );

  /// 小型标题 - 用于小组件标题
  static const TextStyle titleSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
    color: VanHubColors.onSurfaceVariant,
  );

  // ============================================================================
  // 标签文本样式 (Label Text Styles)
  // ============================================================================
  
  /// 大型标签 - 用于按钮文本
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
    color: VanHubColors.onSurface,
  );

  /// 中型标签 - 用于表单标签
  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.33,
    color: VanHubColors.onSurfaceVariant,
  );

  /// 小型标签 - 用于辅助标签
  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 11,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.45,
    color: VanHubColors.onSurfaceVariant,
  );

  // ============================================================================
  // 正文文本样式 (Body Text Styles)
  // ============================================================================
  
  /// 大型正文 - 用于重要内容
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.50,
    color: VanHubColors.onSurface,
  );

  /// 中型正文 - 用于一般内容
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
    color: VanHubColors.onSurface,
  );

  /// 小型正文 - 用于说明文字
  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: VanHubColors.onSurfaceVariant,
  );

  // ============================================================================
  // 特殊用途文本样式 (Special Purpose Text Styles)
  // ============================================================================
  
  /// 等宽字体 - 用于代码显示
  static const TextStyle monospace = TextStyle(
    fontFamily: monospaceFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.43,
    color: VanHubColors.onSurface,
  );

  /// 链接文本 - 用于可点击链接
  static const TextStyle link = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
    color: VanHubColors.primary,
    decoration: TextDecoration.underline,
  );

  /// 错误文本 - 用于错误提示
  static const TextStyle error = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: VanHubColors.error,
  );

  /// 成功文本 - 用于成功提示
  static const TextStyle success = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: VanHubColors.success,
  );

  /// 警告文本 - 用于警告提示
  static const TextStyle warning = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: VanHubColors.warning,
  );

  /// 信息文本 - 用于信息提示
  static const TextStyle info = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
    color: VanHubColors.info,
  );

  // ============================================================================
  // 文本主题 (Text Theme)
  // ============================================================================
  
  /// 获取完整的文本主题
  static TextTheme get textTheme => const TextTheme(
    displayLarge: displayLarge,
    displayMedium: displayMedium,
    displaySmall: displaySmall,
    headlineLarge: headlineLarge,
    headlineMedium: headlineMedium,
    headlineSmall: headlineSmall,
    titleLarge: titleLarge,
    titleMedium: titleMedium,
    titleSmall: titleSmall,
    labelLarge: labelLarge,
    labelMedium: labelMedium,
    labelSmall: labelSmall,
    bodyLarge: bodyLarge,
    bodyMedium: bodyMedium,
    bodySmall: bodySmall,
  );

  // ============================================================================
  // 工具方法 (Utility Methods)
  // ============================================================================
  
  /// 创建自定义文本样式
  static TextStyle custom({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
    String? fontFamily,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontFamily: fontFamily ?? primaryFontFamily,
      fontSize: fontSize ?? 14,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color ?? VanHubColors.onSurface,
      letterSpacing: letterSpacing ?? 0.25,
      height: height ?? 1.43,
      decoration: decoration,
    );
  }

  // ============================================================================
  // 字体权重常量 (Font Weight Constants) - 兼容性属性
  // ============================================================================

  /// 字体权重getter - 兼容性属性
  static FontWeight get weightSemiBold => FontWeight.w600;
  static FontWeight get weightMedium => FontWeight.w500;
  static FontWeight get weightBold => FontWeight.w700;
  static FontWeight get weightRegular => FontWeight.w400;
  static FontWeight get weightLight => FontWeight.w300;

  /// 获取响应式字体大小
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      // 手机端
      return baseFontSize * 0.9;
    } else if (screenWidth < 1200) {
      // 平板端
      return baseFontSize;
    } else {
      // 桌面端
      return baseFontSize * 1.1;
    }
  }

  /// 获取适合的行高
  static double getLineHeight(double fontSize) {
    if (fontSize <= 12) {
      return 1.33;
    } else if (fontSize <= 16) {
      return 1.43;
    } else if (fontSize <= 24) {
      return 1.33;
    } else {
      return 1.25;
    }
  }
}