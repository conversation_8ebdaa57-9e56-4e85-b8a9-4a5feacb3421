# VanHub Design System 使用示例

> 实际项目中的设计系统应用示例

## 📋 目录

- [基础使用](#基础使用)
- [响应式设计](#响应式设计)
- [主题定制](#主题定制)
- [组件组合](#组件组合)
- [实际案例](#实际案例)

## 🚀 基础使用

### 导入设计系统

```dart
import 'package:vanhub/core/design_system/vanhub_design_system.dart';
```

### 使用设计令牌

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(VanHubDesignSystem.spacing4),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.brandPrimary,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
        boxShadow: [VanHubDesignSystem.shadowBase],
      ),
      child: Text(
        'VanHub Design System',
        style: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeLg,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralWhite,
        ),
      ),
    );
  }
}
```

### 使用扩展方法

```dart
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(
        context.responsive<double>(
          mobile: VanHubDesignSystem.spacing2,
          tablet: VanHubDesignSystem.spacing4,
          desktop: VanHubDesignSystem.spacing6,
        ),
      ),
      child: Text('响应式内容'),
    );
  }
}
```

## 📱 响应式设计

### 使用 VanHubResponsiveUtils

```dart
class MaterialLibraryPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('材料库'),
        // 响应式应用栏高度
        toolbarHeight: context.responsiveValue(
          xs: 56,
          sm: 64,
          md: 72,
          lg: 80,
          xl: 88,
          defaultValue: 64,
        ),
      ),
      body: Padding(
        // 响应式内边距
        padding: EdgeInsets.all(context.responsiveValue(
          xs: 8,
          sm: 12,
          md: 16,
          lg: 20,
          xl: 24,
          defaultValue: 16,
        )),
        child: GridView.builder(
          // 响应式网格列数
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: context.responsiveValue(
              xs: 1,
              sm: 2,
              md: 2,
              lg: 3,
              xl: 4,
              defaultValue: 2,
            ),
            crossAxisSpacing: context.responsiveValue(
              xs: 8,
              sm: 12,
              md: 16,
              lg: 20,
              xl: 24,
              defaultValue: 16,
            ),
            mainAxisSpacing: context.responsiveValue(
              xs: 8,
              sm: 12,
              md: 16,
              lg: 20,
              xl: 24,
              defaultValue: 16,
            ),
          ),
          itemBuilder: (context, index) => MaterialCard(),
        ),
      ),
    );
  }
}
```

### 设备类型判断

```dart
class AdaptiveLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    if (context.isMobile) {
      return MobileLayout();
    } else if (context.isTablet) {
      return TabletLayout();
    } else {
      return DesktopLayout();
    }
  }
}

// 或使用响应式构建器
class ResponsiveLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return VanHubResponsiveBuilder(
      mobile: MobileLayout(),
      tablet: TabletLayout(),
      desktop: DesktopLayout(),
    );
  }
}
```

## 🎨 主题定制

### 创建自定义主题

```dart
class VanHubTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: VanHubDesignSystem.brandPrimary,
        brightness: Brightness.light,
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSize4xl,
          fontWeight: VanHubDesignSystem.fontWeightBold,
          fontFamily: VanHubDesignSystem.fontFamilyPrimary,
        ),
        headlineLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSize3xl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          fontFamily: VanHubDesignSystem.fontFamilyPrimary,
        ),
        bodyLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeBase,
          fontWeight: VanHubDesignSystem.fontWeightRegular,
          fontFamily: VanHubDesignSystem.fontFamilyPrimary,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: VanHubDesignSystem.spacing4,
            vertical: VanHubDesignSystem.spacing3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
          ),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: VanHubDesignSystem.brandPrimary,
        brightness: Brightness.dark,
      ),
      // 其他主题配置...
    );
  }
}
```

### 应用主题

```dart
class VanHubApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VanHub',
      theme: VanHubTheme.lightTheme,
      darkTheme: VanHubTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: HomePage(),
    );
  }
}
```

## 🧩 组件组合

### 材料卡片组件

```dart
class MaterialCard extends StatelessWidget {
  final Material material;
  final bool isGuestMode;

  const MaterialCard({
    Key? key,
    required this.material,
    this.isGuestMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          context.responsiveValue(
            xs: VanHubDesignSystem.radiusBase,
            sm: VanHubDesignSystem.radiusLg,
            md: VanHubDesignSystem.radiusXl,
            defaultValue: VanHubDesignSystem.radiusLg,
          ),
        ),
      ),
      child: InkWell(
        onTap: () => _showMaterialDetails(context),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
        child: Padding(
          padding: EdgeInsets.all(
            context.responsiveValue(
              xs: VanHubDesignSystem.spacing2,
              sm: VanHubDesignSystem.spacing3,
              md: VanHubDesignSystem.spacing4,
              defaultValue: VanHubDesignSystem.spacing3,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImage(context),
              SizedBox(height: VanHubDesignSystem.spacing2),
              _buildTitle(context),
              SizedBox(height: VanHubDesignSystem.spacing1),
              _buildSubtitle(context),
              const Spacer(),
              _buildPriceAndActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImage(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: Container(
          decoration: BoxDecoration(
            gradient: VanHubDesignSystem.brandGradient,
          ),
          child: Icon(
            Icons.image,
            color: VanHubDesignSystem.neutralWhite,
            size: context.responsiveValue(
              xs: 24,
              sm: 32,
              md: 40,
              defaultValue: 32,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      material.name,
      style: TextStyle(
        fontSize: context.responsiveValue(
          xs: VanHubDesignSystem.fontSizeSm,
          sm: VanHubDesignSystem.fontSizeBase,
          md: VanHubDesignSystem.fontSizeLg,
          defaultValue: VanHubDesignSystem.fontSizeBase,
        ),
        fontWeight: VanHubDesignSystem.fontWeightSemiBold,
        color: VanHubDesignSystem.neutralGray900,
      ),
      maxLines: context.isMobile ? 1 : 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Text(
      '${material.brand} • ${material.model}',
      style: TextStyle(
        fontSize: context.responsiveValue(
          xs: VanHubDesignSystem.fontSizeXs,
          sm: VanHubDesignSystem.fontSizeSm,
          md: VanHubDesignSystem.fontSizeBase,
          defaultValue: VanHubDesignSystem.fontSizeSm,
        ),
        color: VanHubDesignSystem.neutralGray600,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceAndActions(BuildContext context) {
    return Row(
      children: [
        Text(
          '¥${material.price.toStringAsFixed(0)}',
          style: TextStyle(
            fontSize: context.responsiveValue(
              xs: VanHubDesignSystem.fontSizeBase,
              sm: VanHubDesignSystem.fontSizeLg,
              md: VanHubDesignSystem.fontSizeXl,
              defaultValue: VanHubDesignSystem.fontSizeLg,
            ),
            fontWeight: VanHubDesignSystem.fontWeightBold,
            color: VanHubDesignSystem.brandPrimary,
          ),
        ),
        const Spacer(),
        if (!isGuestMode) _buildActionButtons(context),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(
            Icons.favorite_border,
            size: context.responsiveValue(
              xs: 16,
              sm: 18,
              md: 20,
              defaultValue: 18,
            ),
          ),
          onPressed: () => _toggleFavorite(context),
          tooltip: '收藏',
        ),
        IconButton(
          icon: Icon(
            Icons.add_shopping_cart,
            size: context.responsiveValue(
              xs: 16,
              sm: 18,
              md: 20,
              defaultValue: 18,
            ),
          ),
          onPressed: () => _addToBom(context),
          tooltip: '添加到BOM',
        ),
      ],
    );
  }

  void _showMaterialDetails(BuildContext context) {
    // 显示材料详情
  }

  void _toggleFavorite(BuildContext context) {
    // 切换收藏状态
  }

  void _addToBom(BuildContext context) {
    // 添加到BOM
  }
}
```

## 🎯 实际案例

### 案例1：材料库页面

这是我们在项目中实际实现的材料库页面，展示了设计系统的完整应用：

```dart
class MaterialLibraryPageUnified extends ConsumerStatefulWidget {
  final String? userId;

  const MaterialLibraryPageUnified({
    super.key,
    this.userId,
  });

  @override
  ConsumerState<MaterialLibraryPageUnified> createState() => 
      _MaterialLibraryPageUnifiedState();
}

class _MaterialLibraryPageUnifiedState 
    extends ConsumerState<MaterialLibraryPageUnified> {
  
  @override
  Widget build(BuildContext context) {
    final isGuest = widget.userId == null;
    
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(isGuest),
          _buildSearchSection(),
          if (_isFilterExpanded) _buildFilterSection(),
          _buildMaterialGrid(isGuest),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(isGuest),
    );
  }

  Widget _buildModernAppBar(bool isGuest) {
    return SliverAppBar(
      expandedHeight: context.responsiveValue(
        xs: 100,
        sm: 110,
        md: 120,
        lg: 140,
        xl: 160,
        defaultValue: 120,
      ),
      floating: true,
      pinned: true,
      backgroundColor: VanHubDesignSystem.brandPrimary,
      foregroundColor: VanHubDesignSystem.neutralWhite,
      flexibleSpace: FlexibleSpaceBar(
        title: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '材料库',
              style: TextStyle(
                color: VanHubDesignSystem.neutralWhite,
                fontWeight: VanHubDesignSystem.fontWeightBold,
                fontSize: context.responsiveValue(
                  xs: VanHubDesignSystem.fontSizeBase,
                  sm: VanHubDesignSystem.fontSizeLg,
                  md: VanHubDesignSystem.fontSizeXl,
                  lg: VanHubDesignSystem.fontSize2xl,
                  xl: VanHubDesignSystem.fontSize3xl,
                  defaultValue: VanHubDesignSystem.fontSizeXl,
                ),
              ),
            ),
            if (!context.isMobile)
              Text(
                isGuest ? '发现优质改装材料' : '管理您的材料收藏',
                style: TextStyle(
                  color: VanHubDesignSystem.neutralWhite.withValues(alpha: 0.9),
                  fontSize: context.responsiveValue(
                    xs: VanHubDesignSystem.fontSizeXs,
                    sm: VanHubDesignSystem.fontSizeSm,
                    md: VanHubDesignSystem.fontSizeBase,
                    lg: VanHubDesignSystem.fontSizeLg,
                    xl: VanHubDesignSystem.fontSizeXl,
                    defaultValue: VanHubDesignSystem.fontSizeBase,
                  ),
                ),
              ),
          ],
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: VanHubDesignSystem.brandGradient,
          ),
        ),
      ),
      actions: _buildAppBarActions(isGuest),
    );
  }

  // 其他方法实现...
}
```

### 案例2：响应式搜索栏

```dart
class MaterialSearchBarWidget extends ConsumerStatefulWidget {
  final Function(String) onSearchChanged;
  final VoidCallback? onFilterTap;
  final String? hintText;

  const MaterialSearchBarWidget({
    super.key,
    required this.onSearchChanged,
    this.onFilterTap,
    this.hintText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: VanHubDesignSystem.neutralWhite,
        borderRadius: BorderRadius.circular(
          context.responsiveValue(
            xs: VanHubDesignSystem.radiusBase,
            sm: VanHubDesignSystem.radiusLg,
            md: VanHubDesignSystem.radiusXl,
            defaultValue: VanHubDesignSystem.radiusLg,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: VanHubDesignSystem.neutralBlack.withValues(alpha: 0.1),
            blurRadius: context.responsiveValue(
              xs: 4,
              sm: 6,
              md: 8,
              lg: 10,
              xl: 12,
              defaultValue: 8,
            ),
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: widget.hintText ?? (context.isMobile 
              ? '搜索材料...' 
              : '搜索材料名称、品牌、型号...'),
          hintStyle: TextStyle(
            color: VanHubDesignSystem.neutralGray500,
            fontSize: context.responsiveValue(
              xs: VanHubDesignSystem.fontSizeSm,
              sm: VanHubDesignSystem.fontSizeBase,
              md: VanHubDesignSystem.fontSizeLg,
              defaultValue: VanHubDesignSystem.fontSizeBase,
            ),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: VanHubDesignSystem.neutralGray500,
            size: context.responsiveValue(
              xs: 20,
              sm: 22,
              md: 24,
              lg: 26,
              xl: 28,
              defaultValue: 24,
            ),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: context.responsiveValue(
              xs: VanHubDesignSystem.spacing2,
              sm: VanHubDesignSystem.spacing3,
              md: VanHubDesignSystem.spacing4,
              lg: VanHubDesignSystem.spacing5,
              xl: VanHubDesignSystem.spacing6,
              defaultValue: VanHubDesignSystem.spacing4,
            ),
            vertical: context.responsiveValue(
              xs: VanHubDesignSystem.spacing2,
              sm: VanHubDesignSystem.spacing3,
              md: VanHubDesignSystem.spacing4,
              defaultValue: VanHubDesignSystem.spacing3,
            ),
          ),
        ),
        onChanged: widget.onSearchChanged,
      ),
    );
  }
}
```

## 📝 总结

通过这些示例，我们可以看到 VanHub Design System 的强大功能：

1. **一致性**: 所有组件都使用统一的设计令牌
2. **响应式**: 自动适配不同设备和屏幕尺寸
3. **可扩展**: 易于定制和扩展
4. **易用性**: 简洁的API和丰富的扩展方法
5. **性能**: 优化的组件和合理的默认值

设计系统不仅提供了视觉一致性，更重要的是提供了开发效率和用户体验的保障。
