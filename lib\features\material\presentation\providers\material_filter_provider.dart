import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/material.dart';
import 'material_provider.dart';

part 'material_filter_provider.g.dart';

/// 材料过滤条件
class MaterialFilterCriteria {
  final String searchQuery;
  final String selectedCategory;
  
  const MaterialFilterCriteria({
    this.searchQuery = '',
    this.selectedCategory = '全部',
  });
  
  MaterialFilterCriteria copyWith({
    String? searchQuery,
    String? selectedCategory,
  }) {
    return MaterialFilterCriteria(
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
    );
  }
}

/// 材料过滤条件Provider
@riverpod
class MaterialFilterCriteriaNotifier extends _$MaterialFilterCriteriaNotifier {
  @override
  MaterialFilterCriteria build() {
    return const MaterialFilterCriteria();
  }
  
  /// 更新搜索查询
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }
  
  /// 更新选择的分类
  void updateSelectedCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }
  
  /// 重置过滤条件
  void reset() {
    state = const MaterialFilterCriteria();
  }
}

/// 过滤后的材料列表Provider
@riverpod
Future<List<Material>> filteredMaterialsByFilter(FilteredMaterialsByFilterRef ref) async {
  final materials = await ref.watch(materialsNotifierProvider.future);
  final criteria = ref.watch(materialFilterCriteriaNotifierProvider);
  
  return _filterMaterials(materials, criteria);
}

/// 材料过滤业务逻辑
/// 从Widget中移动到Provider，遵循Clean Architecture原则
List<Material> _filterMaterials(List<Material> materials, MaterialFilterCriteria criteria) {
  return materials.where((material) {
    // 搜索匹配逻辑
    final matchesSearch = criteria.searchQuery.trim().isEmpty ||
        material.name.toLowerCase().contains(criteria.searchQuery.toLowerCase()) ||
        (material.brand?.toLowerCase().contains(criteria.searchQuery.toLowerCase()) ?? false) ||
        (material.description?.toLowerCase().contains(criteria.searchQuery.toLowerCase()) ?? false);
    
    // 分类匹配逻辑
    final matchesCategory = criteria.selectedCategory == '全部' ||
        material.category == criteria.selectedCategory;
    
    return matchesSearch && matchesCategory;
  }).toList();
}

/// 材料搜索Provider
/// 提供搜索相关的业务逻辑
@riverpod
class MaterialSearch extends _$MaterialSearch {
  @override
  String build() => '';
  
  /// 更新搜索查询
  void updateQuery(String query) {
    state = query;
    // 同时更新过滤条件
    ref.read(materialFilterCriteriaNotifierProvider.notifier).updateSearchQuery(query);
  }
  
  /// 清除搜索
  void clear() {
    state = '';
    ref.read(materialFilterCriteriaNotifierProvider.notifier).updateSearchQuery('');
  }
}

/// 材料分类选择Provider
@riverpod
class MaterialCategorySelection extends _$MaterialCategorySelection {
  @override
  String build() => '全部';
  
  /// 更新选择的分类
  void updateCategory(String category) {
    state = category;
    // 同时更新过滤条件
    ref.read(materialFilterCriteriaNotifierProvider.notifier).updateSelectedCategory(category);
  }
  
  /// 重置为全部分类
  void reset() {
    state = '全部';
    ref.read(materialFilterCriteriaNotifierProvider.notifier).updateSelectedCategory('全部');
  }
}

/// 材料分类列表Provider
@riverpod
Future<List<String>> materialCategories(MaterialCategoriesRef ref) async {
  final materials = await ref.watch(materialsNotifierProvider.future);
  
  // 提取所有唯一的分类
  final categories = materials
      .map((material) => material.category)
      .where((category) => category != null && category.isNotEmpty)
      .cast<String>()
      .toSet()
      .toList();
  
  // 排序并添加"全部"选项
  categories.sort();
  return ['全部', ...categories];
}

/// 搜索建议Provider
/// 基于现有材料提供搜索建议
@riverpod
Future<List<String>> searchSuggestions(SearchSuggestionsRef ref, String query) async {
  if (query.trim().isEmpty) return [];
  
  final materials = await ref.watch(materialsNotifierProvider.future);
  final suggestions = <String>{};
  
  final lowerQuery = query.toLowerCase();
  
  for (final material in materials) {
    // 添加名称建议
    if (material.name.toLowerCase().contains(lowerQuery)) {
      suggestions.add(material.name);
    }
    
    // 添加品牌建议
    if (material.brand != null && 
        material.brand!.toLowerCase().contains(lowerQuery)) {
      suggestions.add(material.brand!);
    }
    
    // 添加分类建议
    if (material.category != null && 
        material.category!.toLowerCase().contains(lowerQuery)) {
      suggestions.add(material.category!);
    }
  }
  
  return suggestions.take(10).toList(); // 限制建议数量
}

/// 材料统计Provider
/// 提供过滤后材料的统计信息
@riverpod
Future<MaterialFilterStats> materialFilterStats(MaterialFilterStatsRef ref) async {
  final allMaterials = await ref.watch(materialsNotifierProvider.future);
  final filteredMaterials = await ref.watch(filteredMaterialsByFilterProvider.future);
  
  return MaterialFilterStats(
    totalCount: allMaterials.length,
    filteredCount: filteredMaterials.length,
    isFiltered: filteredMaterials.length < allMaterials.length,
  );
}

/// 材料过滤统计信息
class MaterialFilterStats {
  final int totalCount;
  final int filteredCount;
  final bool isFiltered;
  
  const MaterialFilterStats({
    required this.totalCount,
    required this.filteredCount,
    required this.isFiltered,
  });
  
  /// 过滤百分比
  double get filterPercentage {
    if (totalCount == 0) return 0.0;
    return (filteredCount / totalCount) * 100;
  }
  
  /// 过滤状态描述
  String get statusDescription {
    if (!isFiltered) return '显示全部 $totalCount 个材料';
    return '显示 $filteredCount / $totalCount 个材料';
  }
}
