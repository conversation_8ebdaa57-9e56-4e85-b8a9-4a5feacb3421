import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/timeline.dart';
import '../repositories/timeline_repository.dart';

/// 获取项目时间轴用例
class GetProjectTimelineUseCase implements UseCase<Timeline, GetProjectTimelineParams> {
  final TimelineRepository repository;

  GetProjectTimelineUseCase(this.repository);

  @override
  Future<Either<Failure, Timeline>> call(GetProjectTimelineParams params) async {
    return await repository.getProjectTimeline(params.projectId);
  }
}

/// 获取项目时间轴参数
class GetProjectTimelineParams {
  final String projectId;

  GetProjectTimelineParams({required this.projectId});
}