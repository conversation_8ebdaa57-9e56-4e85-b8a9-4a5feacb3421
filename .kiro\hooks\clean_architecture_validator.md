# VanHub Clean Architecture Validator

## Hook Configuration
```yaml
name: "VanHub Clean Architecture Validator"
description: "验证VanHub项目是否遵循Clean Architecture原则"
trigger: "on_file_save"
file_patterns: ["lib/**/*.dart"]
auto_execute: true
```

## 综合验证规则

此hook整合了多个专门的验证器，提供全面的Clean Architecture合规性检查：

### 1. Widget业务逻辑检查
- ❌ Widget中不能包含业务逻辑
- ❌ Widget中不能直接调用数据源（如Supabase、HTTP请求）
- ✅ Widget只能调用Notifier的方法
- ✅ Widget应该处理UI渲染和用户交互

### 2. Riverpod状态管理验证
引用现有hook：#[[file:riverpod_state_validator.md]]
- ✅ 所有有状态的Widget都使用ConsumerWidget
- ✅ 状态管理通过Riverpod Notifier实现
- ❌ 不能在Widget中使用setState处理业务状态

### 3. Either类型返回检查
引用现有的either_type_enforcer hook
- ✅ 所有可能失败的操作必须返回`Either<Failure, Success>`
- ✅ Repository方法必须返回Either类型
- ✅ UseCase方法必须返回Either类型

### 4. Freezed实体验证
引用现有hook：#[[file:freezed_entity_validator.md]]
- ✅ Domain层实体必须使用freezed
- ✅ 实体必须是不可变的
- ✅ 必须包含正确的注解和part声明

### 5. 分层依赖关系检查
引用现有的dependency_layer_validator hook
- ✅ Domain层不能依赖Flutter或外部包
- ✅ Presentation层不能直接访问Data层
- ✅ Data层实现Repository接口
- ✅ 依赖方向：Presentation → Domain ← Data

## 执行动作
当检测到违规时：
1. 显示具体的违规信息
2. 提供修复建议
3. 可选择自动修复简单问题