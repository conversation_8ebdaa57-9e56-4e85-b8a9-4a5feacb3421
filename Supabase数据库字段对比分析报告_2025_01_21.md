# Supabase数据库字段对比分析报告

## 📅 分析日期：2025年1月21日

---

## 🎯 **分析概述**

基于对VanHub项目代码的深入分析，我发现了一个关键问题：**数据库表尚未在Supabase中创建**。项目中包含完整的SQL初始化脚本，但这些表在实际的Supabase实例中并不存在，这解释了为什么会出现大量的编译错误和功能缺失。

---

## 🔍 **数据库连接状态**

### **Supabase配置信息**
- **项目URL**: `https://zpxqphldtuzukvzxnozs.supabase.co`
- **连接状态**: ✅ 正常连接
- **API访问**: ✅ 可以访问REST API
- **表状态**: ❌ 核心表不存在

### **API测试结果**
```bash
# 测试结果：所有核心表都返回404错误
- projects: 404 未找到
- material_library: 404 未找到  
- bom_items: 404 未找到
- commits: 404 未找到
- material_categories: 404 未找到
```

---

## 📋 **预期数据库表结构 vs 实际状态**

### **1. projects 表**

#### **预期结构** (基于 `docs/database_schema.sql`)
```sql
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'planning',
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **代码中的字段映射** (基于 `ProjectModel`)
```dart
// lib/features/project/data/models/project_model.dart
{
  'id': String,
  'user_id': String,
  'title': String,        // ⚠️ 字段名不匹配：代码用title，数据库用name
  'vehicle_model': String,
  'description': String,
  'total_budget': double, // ⚠️ 字段名不匹配：代码用total_budget，数据库用budget
  'is_public': bool,      // ⚠️ 数据库schema中缺少此字段
  'created_at': String,
  'updated_at': String,
}
```

#### **实际状态**: ❌ **表不存在**

#### **字段映射问题**:
1. **title vs name**: 代码使用`title`，数据库定义为`name`
2. **total_budget vs budget**: 代码使用`total_budget`，数据库定义为`budget`
3. **is_public字段缺失**: 代码中使用但数据库schema中未定义
4. **vehicle_model字段缺失**: 代码中使用但数据库schema中未定义

---

### **2. material_library 表**

#### **预期结构** (基于代码推断)
```sql
CREATE TABLE IF NOT EXISTS material_library (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    item_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    brand VARCHAR(255),
    model VARCHAR(255),
    specification TEXT,
    reference_price DECIMAL(10,2),
    purchase_link TEXT,
    usage_count INTEGER DEFAULT 0,
    attributes JSONB,
    weight DECIMAL(8,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **代码中的字段映射** (基于 `MaterialRemoteDataSourceImpl`)
```dart
// 创建材料时的字段映射
{
  'user_id': userId,
  'item_name': request.name,
  'description': request.description,
  'category': request.category,
  'brand': request.brand,
  'model': request.model,
  'specification': request.specifications,
  'reference_price': request.price,
  'purchase_link': request.supplierUrl,
  'usage_count': 0,
  'attributes': request.metadata,
  'created_at': DateTime.now().toIso8601String(),
  'updated_at': DateTime.now().toIso8601String(),
}
```

#### **实际状态**: ❌ **表不存在**

---

### **3. bom_items 表**

#### **预期结构** (基于 `docs/database_schema.sql`)
```sql
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, material_id)
);
```

#### **代码中的实际使用** (基于 `BomRemoteDataSourceImpl`)
```dart
// 代码中实际使用的字段
{
  'project_id': projectId,
  'commit_id': commitId,        // ⚠️ 数据库schema中缺少此字段
  'item_name': materialName,    // ⚠️ 字段名不匹配
  'description': description,
  'quantity': quantity,
  'price': unitPrice,           // ⚠️ 字段名不匹配：代码用price，数据库用unit_price
  'category': category,
  'notes': notes,
  'status': status.code,
  'attributes': attributes,     // ⚠️ 数据库schema中缺少此字段
  'purchase_date': purchaseDate,// ⚠️ 数据库schema中缺少此字段
  'use_date': useDate,         // ⚠️ 数据库schema中缺少此字段
}
```

#### **实际状态**: ❌ **表不存在**

#### **关键问题**:
1. **commit_id字段**: 代码中大量使用但数据库schema中未定义
2. **字段名不匹配**: `price` vs `unit_price`, `item_name` vs 缺失
3. **缺少扩展字段**: `attributes`, `purchase_date`, `use_date`

---

### **4. commits 表**

#### **代码中的使用** (基于 `BomRemoteDataSourceImpl`)
```dart
// commits表在代码中被大量使用，但数据库schema中未定义
{
  'project_id': projectId,
  'message': message,
  'title': title,
  'description': description,
  'status': 'active',
}
```

#### **实际状态**: ❌ **表不存在且schema中未定义**

---

### **5. material_categories 表**

#### **预期结构** (基于 `docs/database_schema.sql`)
```sql
CREATE TABLE IF NOT EXISTS material_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **实际状态**: ❌ **表不存在**

---

## 🚨 **关键发现和问题**

### **1. 数据库表完全缺失**
- 所有核心表（projects, material_library, bom_items, commits, material_categories）都不存在
- 这是导致应用无法正常运行的根本原因

### **2. 字段映射不一致**
即使表存在，也存在严重的字段映射问题：

| 功能模块 | 代码字段 | 数据库字段 | 状态 |
|---------|---------|-----------|------|
| 项目管理 | title | name | ❌ 不匹配 |
| 项目管理 | total_budget | budget | ❌ 不匹配 |
| 项目管理 | is_public | - | ❌ 缺失 |
| 项目管理 | vehicle_model | - | ❌ 缺失 |
| BOM管理 | commit_id | - | ❌ 缺失 |
| BOM管理 | item_name | - | ❌ 缺失 |
| BOM管理 | price | unit_price | ❌ 不匹配 |
| BOM管理 | attributes | - | ❌ 缺失 |

### **3. 架构设计不一致**
- 代码中使用了`commits`表的概念，但数据库schema中未定义
- BOM系统依赖commit_id进行RLS策略验证，但相关表结构缺失

---

## 🔧 **修复建议**

### **立即行动项**

#### **1. 创建数据库表**
需要在Supabase SQL编辑器中执行以下脚本：

```sql
-- 基于代码实际使用情况的修正版本

-- 1. 项目表 (修正字段名)
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,           -- 修正：使用title而不是name
    vehicle_model VARCHAR(255),            -- 新增：车型字段
    description TEXT,
    total_budget DECIMAL(12,2) DEFAULT 0,  -- 修正：使用total_budget而不是budget
    is_public BOOLEAN DEFAULT false,       -- 新增：公开状态字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. commits表 (新增)
CREATE TABLE IF NOT EXISTS commits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 材料库表
CREATE TABLE IF NOT EXISTS material_library (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    item_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    brand VARCHAR(255),
    model VARCHAR(255),
    specification TEXT,
    reference_price DECIMAL(10,2),
    purchase_link TEXT,
    usage_count INTEGER DEFAULT 0,
    attributes JSONB,
    weight DECIMAL(8,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. BOM项表 (修正字段)
CREATE TABLE IF NOT EXISTS bom_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    commit_id UUID REFERENCES commits(id) ON DELETE CASCADE,  -- 新增：commit关联
    material_id UUID REFERENCES material_library(id) ON DELETE SET NULL,
    item_name VARCHAR(255),                -- 新增：项目名称
    description TEXT,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    price DECIMAL(10,2) DEFAULT 0,         -- 修正：使用price而不是unit_price
    category VARCHAR(100),
    notes TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    attributes JSONB,                      -- 新增：扩展属性
    purchase_date TIMESTAMP,              -- 新增：采购日期
    use_date TIMESTAMP,                   -- 新增：使用日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 材料分类表
CREATE TABLE IF NOT EXISTS material_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES material_categories(id),  -- 新增：支持层级分类
    icon VARCHAR(100),                     -- 新增：图标字段
    color VARCHAR(50),                     -- 新增：颜色字段
    sort_order INTEGER DEFAULT 0,         -- 新增：排序字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **2. 启用RLS策略**
```sql
-- 启用行级安全
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE commits ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 项目表策略
CREATE POLICY "Users can view public projects" ON projects
    FOR SELECT USING (is_public = true OR auth.uid() = user_id);

CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING (auth.uid() = user_id);

-- 其他表的策略...
```

#### **3. 插入默认数据**
```sql
-- 插入默认材料分类
INSERT INTO material_categories (name, description, icon, color, sort_order) VALUES
('电气系统', '电池、逆变器、充电器等电气设备', 'electrical', '#FF6B35', 1),
('水系统', '水泵、水箱、净水器等水系统设备', 'water', '#4ECDC4', 2),
('燃气系统', '燃气灶、热水器、燃气罐等燃气设备', 'gas', '#45B7D1', 3),
('家具', '床、桌椅、储物柜等家具', 'furniture', '#96CEB4', 4),
('装饰材料', '地板、墙面、窗帘等装饰材料', 'decoration', '#FFEAA7', 5),
('工具配件', '螺丝、胶水、线材等工具配件', 'tools', '#DDA0DD', 6),
('安全设备', '灭火器、烟雾报警器、急救包等', 'safety', '#FF7675', 7),
('通讯设备', '对讲机、GPS、网络设备等', 'communication', '#74B9FF', 8),
('储物系统', '储物箱、挂钩、收纳用品等', 'storage', '#A29BFE', 9),
('舒适设备', '空调、暖风机、座椅等', 'comfort', '#FD79A8', 10),
('外观改装', '贴纸、灯具、外观配件等', 'exterior', '#FDCB6E', 11)
ON CONFLICT DO NOTHING;
```

---

## 📊 **修复优先级**

### **🔴 紧急修复（今天完成）**
1. **创建核心数据库表** - 这是应用运行的基础
2. **配置RLS策略** - 确保数据安全
3. **插入默认数据** - 材料分类等基础数据

### **🟡 重要修复（本周完成）**
1. **验证字段映射** - 确保代码和数据库字段一致
2. **测试数据库连接** - 验证所有CRUD操作正常
3. **修复编译错误** - 解决因表缺失导致的错误

### **🟢 后续优化（下周完成）**
1. **性能优化** - 添加必要的索引
2. **数据迁移** - 如果有现有数据需要迁移
3. **监控和日志** - 添加数据库操作监控

---

## 📝 **总结**

VanHub项目的数据库问题是一个**系统性问题**：

1. **根本原因**: Supabase实例中缺少所有核心表
2. **影响范围**: 导致应用无法正常运行，所有数据操作都会失败
3. **解决方案**: 需要立即执行数据库初始化脚本
4. **预防措施**: 建立数据库版本管理和自动化部署流程

**建议立即执行数据库初始化脚本，这是解决当前所有问题的关键步骤。**
