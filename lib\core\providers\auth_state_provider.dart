import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../features/auth/presentation/providers/auth_provider.dart';

part 'auth_state_provider.g.dart';

/// 全局当前用户ID提供者
@riverpod
String? currentUserId(Ref ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.when(
    data: (user) => user?.id,
    loading: () => null,
    error: (_, __) => null,
  );
}

/// 全局当前用户提供者
@riverpod
Future<String> requireCurrentUserId(Ref ref) async {
  final userId = ref.watch(currentUserIdProvider);
  if (userId == null) {
    throw Exception('用户未登录，请先登录');
  }
  return userId;
}