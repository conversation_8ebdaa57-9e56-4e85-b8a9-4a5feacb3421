import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material_recommendation.dart';

/// 材料推荐服务接口
abstract class MaterialRecommendationService {
  /// 为项目推荐材料
  /// 
  /// 基于项目类型、系统和已有材料推荐相关材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForProject(
    String projectId, {
    int limit = 10,
  });
  
  /// 为特定系统推荐材料
  /// 
  /// 基于系统类型和项目信息推荐相关材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForSystem(
    String projectId,
    String systemType, {
    int limit = 10,
  });
  
  /// 推荐相似材料
  /// 
  /// 基于指定材料推荐相似的其他材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendSimilarMaterials(
    String materialId, {
    int limit = 10,
  });
  
  /// 推荐常用搭配材料
  /// 
  /// 基于指定材料推荐常一起使用的其他材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendComplementaryMaterials(
    String materialId, {
    int limit = 10,
  });
  
  /// 推荐热门材料
  /// 
  /// 基于使用频率推荐热门材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendPopularMaterials(
    String userId, {
    String? category, 
    int limit = 10,
  });
  
  /// 推荐性价比高的材料
  /// 
  /// 基于价格和评分推荐性价比高的材料
  Future<Either<Failure, List<MaterialRecommendation>>> recommendValueForMoneyMaterials(
    String userId, {
    String? category, 
    int limit = 10,
  });
}