import 'package:flutter/material.dart';

/// 数据表格样式
class VanHubDataTableStyle {
  final double columnSpacing;
  final double rowHeight;
  final double headerHeight;
  final double horizontalMargin;
  final double dividerThickness;
  final TextStyle? headerTextStyle;
  final TextStyle? cellTextStyle;

  const VanHubDataTableStyle({
    this.columnSpacing = 56.0,
    this.rowHeight = 48.0,
    this.headerHeight = 56.0,
    this.horizontalMargin = 24.0,
    this.dividerThickness = 1.0,
    this.headerTextStyle,
    this.cellTextStyle,
  });
}