import 'package:flutter/material.dart';

/// 材料库空状态组件
/// 
/// 特性：
/// - 游客和登录用户不同的空状态提示
/// - 友好的插图和文案
/// - 引导用户进行下一步操作
/// - 支持搜索结果为空的情况
class MaterialEmptyStateWidget extends StatelessWidget {
  final bool isGuestMode;
  final bool isSearchResult;
  final String? searchQuery;
  final VoidCallback? onAddMaterial;
  final VoidCallback? onLogin;
  final VoidCallback? onClearSearch;

  const MaterialEmptyStateWidget({
    super.key,
    this.isGuestMode = false,
    this.isSearchResult = false,
    this.searchQuery,
    this.onAddMaterial,
    this.onLogin,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    if (isSearchResult) {
      return _buildSearchEmptyState(context);
    }
    
    return isGuestMode 
        ? _buildGuestEmptyState(context)
        : _buildUserEmptyState(context);
  }
  
  Widget _buildGuestEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildEmptyIcon(context, Icons.explore_outlined),
            const SizedBox(height: 24),
            Text(
              '发现改装材料',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              '这里汇聚了来自全球改装爱好者的材料分享\n登录后可以收藏喜欢的材料，创建自己的材料库',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildFeatureHighlights(context),
            const SizedBox(height: 32),
            _buildActionButtons(context, [
              _buildPrimaryButton(
                context,
                label: '立即登录',
                icon: Icons.login,
                onPressed: onLogin,
              ),
              const SizedBox(height: 12),
              _buildSecondaryButton(
                context,
                label: '浏览公开材料',
                icon: Icons.visibility_outlined,
                onPressed: () {
                  // TODO: 加载公开材料
                },
              ),
            ]),
          ],
        ),
      ),
    );
  }
  
  Widget _buildUserEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildEmptyIcon(context, Icons.inventory_2_outlined),
            const SizedBox(height: 24),
            Text(
              '开始建立您的材料库',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              '添加您使用过的改装材料\n记录价格、供应商和使用心得\n为下次改装做好准备',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            _buildActionButtons(context, [
              _buildPrimaryButton(
                context,
                label: '添加第一个材料',
                icon: Icons.add,
                onPressed: onAddMaterial,
              ),
              const SizedBox(height: 12),
              _buildSecondaryButton(
                context,
                label: '浏览材料模板',
                icon: Icons.library_books_outlined,
                onPressed: () {
                  // TODO: 显示材料模板
                },
              ),
            ]),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSearchEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildEmptyIcon(context, Icons.search_off_outlined),
            const SizedBox(height: 24),
            Text(
              '未找到匹配的材料',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              searchQuery?.isNotEmpty == true
                  ? '没有找到包含"$searchQuery"的材料\n尝试使用其他关键词或调整筛选条件'
                  : '当前筛选条件下没有匹配的材料\n尝试调整筛选条件或清除筛选',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            _buildActionButtons(context, [
              _buildPrimaryButton(
                context,
                label: '清除搜索',
                icon: Icons.clear,
                onPressed: onClearSearch,
              ),
              if (!isGuestMode) ...[
                const SizedBox(height: 12),
                _buildSecondaryButton(
                  context,
                  label: '添加这个材料',
                  icon: Icons.add,
                  onPressed: onAddMaterial,
                ),
              ],
            ]),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEmptyIcon(BuildContext context, IconData icon) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        size: 64,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }
  
  Widget _buildActionButtons(BuildContext context, List<Widget> buttons) {
    return Column(
      children: buttons,
    );
  }
  
  Widget _buildPrimaryButton(
    BuildContext context, {
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSecondaryButton(
    BuildContext context, {
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureHighlights(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            '平台特色',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildFeatureItem(
                  context,
                  icon: Icons.inventory_2_outlined,
                  title: '丰富材料库',
                  subtitle: '10000+材料',
                ),
              ),
              Expanded(
                child: _buildFeatureItem(
                  context,
                  icon: Icons.people_outline,
                  title: '社区分享',
                  subtitle: '真实评价',
                ),
              ),
              Expanded(
                child: _buildFeatureItem(
                  context,
                  icon: Icons.smart_toy_outlined,
                  title: 'AI推荐',
                  subtitle: '智能匹配',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 32,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
