import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/project_stats.dart';

/// 项目统计服务接口
/// 遵循Clean Architecture原则，定义项目统计相关的业务逻辑接口
abstract class ProjectStatsService {
  /// 获取项目统计数据
  /// 
  /// [projectId] 项目ID
  /// 返回项目的完整统计信息，包括进度、预算、BOM项目等
  Future<Either<Failure, ProjectStats>> getProjectStats(String projectId);
  
  /// 计算项目进度百分比
  /// 
  /// [projectId] 项目ID
  /// 返回项目的完成百分比 (0-100)
  Future<Either<Failure, double>> calculateProjectProgress(String projectId);
  
  /// 获取BOM项目状态统计
  /// 
  /// [projectId] 项目ID
  /// 返回各状态的BOM项目数量统计
  Future<Either<Failure, Map<String, int>>> getBomItemStatusCounts(String projectId);
  
  /// 计算项目预算使用情况
  /// 
  /// [projectId] 项目ID
  /// 返回预算使用的详细分析
  Future<Either<Failure, BudgetAnalysis>> calculateBudgetAnalysis(String projectId);
  
  /// 获取项目成本分类分布
  /// 
  /// [projectId] 项目ID
  /// 返回按分类的成本分布
  Future<Either<Failure, Map<String, double>>> getCategoryCostDistribution(String projectId);
  
  /// 预测项目完成时间
  /// 
  /// [projectId] 项目ID
  /// 基于当前进度和历史数据预测完成时间
  Future<Either<Failure, DateTime?>> estimateCompletionDate(String projectId);
  
  /// 获取项目效率指标
  /// 
  /// [projectId] 项目ID
  /// 返回项目的各种效率指标
  Future<Either<Failure, ProjectEfficiencyMetrics>> getEfficiencyMetrics(String projectId);
  
  /// 比较项目与平均水平
  /// 
  /// [projectId] 项目ID
  /// 返回项目与同类项目的比较结果
  Future<Either<Failure, ProjectComparison>> compareWithAverage(String projectId);
  
  /// 获取项目风险评估
  /// 
  /// [projectId] 项目ID
  /// 返回项目的风险评估结果
  Future<Either<Failure, ProjectRiskAssessment>> assessProjectRisk(String projectId);
  
  /// 生成项目报告
  /// 
  /// [projectId] 项目ID
  /// [reportType] 报告类型
  /// 返回指定类型的项目报告
  Future<Either<Failure, ProjectReport>> generateProjectReport(
    String projectId,
    ProjectReportType reportType,
  );
}

/// 预算分析结果
class BudgetAnalysis {
  final double totalBudget;
  final double actualCost;
  final double remainingBudget;
  final double budgetUtilization;
  final bool isOverBudget;
  final double overrunAmount;
  final List<BudgetAlert> alerts;
  final Map<String, double> categoryBreakdown;
  
  const BudgetAnalysis({
    required this.totalBudget,
    required this.actualCost,
    required this.remainingBudget,
    required this.budgetUtilization,
    required this.isOverBudget,
    required this.overrunAmount,
    required this.alerts,
    required this.categoryBreakdown,
  });
}

/// 预算警告
class BudgetAlert {
  final BudgetAlertType type;
  final String message;
  final double amount;
  final String? category;
  
  const BudgetAlert({
    required this.type,
    required this.message,
    required this.amount,
    this.category,
  });
}

/// 预算警告类型
enum BudgetAlertType {
  warning,    // 警告
  critical,   // 危险
  overBudget, // 超支
}

/// 项目效率指标
class ProjectEfficiencyMetrics {
  final double costPerItem;           // 每项成本
  final double timePerItem;           // 每项时间
  final double completionVelocity;    // 完成速度
  final double budgetEfficiency;      // 预算效率
  final double resourceUtilization;   // 资源利用率
  final int daysActive;              // 活跃天数
  final double dailyProgress;        // 日均进度
  
  const ProjectEfficiencyMetrics({
    required this.costPerItem,
    required this.timePerItem,
    required this.completionVelocity,
    required this.budgetEfficiency,
    required this.resourceUtilization,
    required this.daysActive,
    required this.dailyProgress,
  });
}

/// 项目比较结果
class ProjectComparison {
  final double progressVsAverage;     // 进度对比
  final double costVsAverage;         // 成本对比
  final double timeVsAverage;         // 时间对比
  final double efficiencyVsAverage;   // 效率对比
  final ProjectPerformanceLevel performanceLevel;
  final List<String> strengths;       // 优势
  final List<String> improvements;    // 改进建议
  
  const ProjectComparison({
    required this.progressVsAverage,
    required this.costVsAverage,
    required this.timeVsAverage,
    required this.efficiencyVsAverage,
    required this.performanceLevel,
    required this.strengths,
    required this.improvements,
  });
}

/// 项目表现水平
enum ProjectPerformanceLevel {
  excellent,  // 优秀
  good,       // 良好
  average,    // 平均
  belowAverage, // 低于平均
  poor,       // 较差
}

/// 项目风险评估
class ProjectRiskAssessment {
  final ProjectRiskLevel overallRisk;
  final double riskScore;             // 风险评分 (0-100)
  final List<ProjectRisk> risks;      // 具体风险
  final List<String> recommendations; // 建议
  
  const ProjectRiskAssessment({
    required this.overallRisk,
    required this.riskScore,
    required this.risks,
    required this.recommendations,
  });
}

/// 项目风险等级
enum ProjectRiskLevel {
  low,      // 低风险
  medium,   // 中等风险
  high,     // 高风险
  critical, // 严重风险
}

/// 具体项目风险
class ProjectRisk {
  final ProjectRiskType type;
  final String description;
  final double probability;    // 概率 (0-1)
  final double impact;        // 影响 (0-1)
  final double riskValue;     // 风险值 (概率 × 影响)
  final String mitigation;    // 缓解措施
  
  const ProjectRisk({
    required this.type,
    required this.description,
    required this.probability,
    required this.impact,
    required this.riskValue,
    required this.mitigation,
  });
}

/// 项目风险类型
enum ProjectRiskType {
  budget,     // 预算风险
  schedule,   // 进度风险
  quality,    // 质量风险
  resource,   // 资源风险
  technical,  // 技术风险
}

/// 项目报告
class ProjectReport {
  final String projectId;
  final ProjectReportType type;
  final DateTime generatedAt;
  final Map<String, dynamic> data;
  final String summary;
  final List<String> keyInsights;
  final List<String> recommendations;
  
  const ProjectReport({
    required this.projectId,
    required this.type,
    required this.generatedAt,
    required this.data,
    required this.summary,
    required this.keyInsights,
    required this.recommendations,
  });
}

/// 项目报告类型
enum ProjectReportType {
  progress,     // 进度报告
  budget,       // 预算报告
  efficiency,   // 效率报告
  risk,         // 风险报告
  comprehensive, // 综合报告
}

/// 风险等级
enum RiskLevel {
  minimal,  // 最小风险
  low,      // 低风险
  medium,   // 中等风险
  high,     // 高风险
}

/// 行业平均值
class IndustryAverages {
  final double averageCostPerItem;
  final double averageTimePerItem;
  final double averageBudgetEfficiency;
  final int averageCompletionTime;
  final double averageProjectCost;

  const IndustryAverages({
    required this.averageCostPerItem,
    required this.averageTimePerItem,
    required this.averageBudgetEfficiency,
    required this.averageCompletionTime,
    required this.averageProjectCost,
  });
}
