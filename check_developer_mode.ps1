# Windows开发者模式检查脚本

Write-Host "🔍 检查Windows开发者模式状态..." -ForegroundColor Cyan

# 检查开发者模式注册表项
$devModeKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppModelUnlock"
$devModeValue = "AllowDevelopmentWithoutDevLicense"

try {
    $devModeEnabled = Get-ItemProperty -Path $devModeKey -Name $devModeValue -ErrorAction SilentlyContinue
    
    if ($devModeEnabled -and $devModeEnabled.$devModeValue -eq 1) {
        Write-Host "✅ 开发者模式已启用" -ForegroundColor Green
        
        # 测试符号链接创建权限
        Write-Host "🔗 测试符号链接权限..." -ForegroundColor Yellow
        
        $testFile = "test_target.txt"
        $testLink = "test_symlink"
        
        try {
            # 创建测试文件
            "test content" | Out-File -FilePath $testFile -Encoding UTF8
            
            # 尝试创建符号链接
            cmd /c "mklink `"$testLink`" `"$testFile`"" 2>$null
            
            if (Test-Path $testLink) {
                Write-Host "✅ 符号链接创建成功 - Flutter构建应该可以正常进行" -ForegroundColor Green
                
                # 清理测试文件
                Remove-Item $testFile -Force -ErrorAction SilentlyContinue
                Remove-Item $testLink -Force -ErrorAction SilentlyContinue
                
                Write-Host "`n🚀 建议执行以下命令继续构建:" -ForegroundColor Cyan
                Write-Host "flutter clean" -ForegroundColor White
                Write-Host "flutter pub get" -ForegroundColor White
                Write-Host "flutter build apk --release" -ForegroundColor White
                
            } else {
                Write-Host "❌ 符号链接创建失败 - 可能需要重启系统" -ForegroundColor Red
                Write-Host "💡 建议重启电脑后重试" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ 符号链接测试失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ 开发者模式未启用" -ForegroundColor Red
        Write-Host "💡 请执行以下步骤启用开发者模式:" -ForegroundColor Yellow
        Write-Host "1. 运行: Start-Process ms-settings:developers" -ForegroundColor White
        Write-Host "2. 在设置页面中启用'开发者模式'" -ForegroundColor White
        Write-Host "3. 重启电脑（如果系统要求）" -ForegroundColor White
        Write-Host "4. 重新运行此脚本验证" -ForegroundColor White
        
        # 自动打开开发者设置
        Write-Host "`n🔧 正在打开开发者设置页面..." -ForegroundColor Cyan
        Start-Process ms-settings:developers
    }
    
} catch {
    Write-Host "❌ 无法检查开发者模式状态: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 请手动检查开发者模式设置" -ForegroundColor Yellow
}

# 检查Flutter环境
Write-Host "`n📱 检查Flutter环境..." -ForegroundColor Cyan

try {
    $flutterVersion = flutter --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Flutter环境正常" -ForegroundColor Green
        if ($flutterVersion) {
            $versionLine = ($flutterVersion -split "`n")[0]
            Write-Host "   版本信息: $versionLine" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Flutter环境异常" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法检查Flutter环境" -ForegroundColor Red
}

# 检查Android SDK
Write-Host "`n🤖 检查Android SDK..." -ForegroundColor Cyan

$androidSdkPath = "D:\AndroidSDK"
if (Test-Path $androidSdkPath) {
    Write-Host "✅ Android SDK路径存在: $androidSdkPath" -ForegroundColor Green
    
    $adbPath = "$androidSdkPath\platform-tools\adb.exe"
    if (Test-Path $adbPath) {
        Write-Host "✅ ADB工具可用" -ForegroundColor Green
    } else {
        Write-Host "⚠️  ADB工具未找到" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Android SDK路径不存在" -ForegroundColor Red
}

Write-Host "`n" + "="*50
Write-Host "📋 检查完成" -ForegroundColor Green
