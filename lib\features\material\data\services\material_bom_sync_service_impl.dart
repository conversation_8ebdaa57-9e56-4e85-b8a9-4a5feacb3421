import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/material_usage_history.dart';
import '../../domain/services/material_bom_sync_service.dart';
import '../../domain/entities/material_usage_history.dart';
import '../../domain/repositories/material_repository.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/domain/repositories/bom_repository.dart';

/// 材料库与BOM双向同步服务实现
/// 遵循Clean Architecture原则，实现智能同步功能
class MaterialBomSyncServiceImpl implements MaterialBomSyncService {
  final MaterialRepository materialRepository;
  final BomRepository bomRepository;

  const MaterialBomSyncServiceImpl({
    required this.materialRepository,
    required this.bomRepository,
  });

  @override
  Future<Either<Failure, List<BomItem>>> syncMaterialUpdateToBom({
    required String materialId,
    required Map<String, dynamic> updatedFields,
    String syncStrategy = 'prompt',
  }) async {
    try {
      // 1. 获取与材料关联的所有BOM项目
      final linkedBomItemsResult = await getLinkedBomItems(
        materialId: materialId,
        includeCompleted: false, // 只同步未完成的项目
      );

      return linkedBomItemsResult.fold(
        (failure) => Left(failure),
        (bomItems) async {
          if (bomItems.isEmpty) {
            return const Right([]);
          }

          // 2. 根据同步策略处理
          switch (syncStrategy) {
            case 'auto':
              return await _autoSyncBomItems(bomItems, updatedFields);
            case 'skip':
              return const Right([]);
            case 'prompt':
            default:
              // 返回需要同步的BOM项目列表，由UI层处理用户选择
              return Right(bomItems);
          }
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步材料更新到BOM失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Material?>> syncBomStatusToMaterial({
    required String bomItemId,
    required String oldStatus,
    required String newStatus,
    required int quantity,
  }) async {
    try {
      // 1. 获取BOM项目信息
      final bomItemResult = await bomRepository.getBomItemById(bomItemId);
      
      return bomItemResult.fold(
        (failure) => Left(failure),
        (bomItem) async {
          // 2. 检查是否有关联的材料
          if (bomItem.materialId == null) {
            return const Right(null);
          }

          // 3. 根据状态变化更新材料统计
          if (_shouldUpdateUsageStats(oldStatus, newStatus)) {
            final updateResult = await updateMaterialUsageStats(
              materialId: bomItem.materialId!,
              incrementUsage: newStatus == 'completed',
            );

            return updateResult.fold(
              (failure) => Left(failure),
              (material) async {
                // 4. 创建使用历史记录
                await createUsageHistory(
                  materialId: bomItem.materialId!,
                  projectId: bomItem.projectId,
                  bomItemId: bomItemId,
                  quantity: quantity,
                  unitPrice: bomItem.unitPrice,
                  usageType: _mapStatusToUsageType(newStatus).name,
                );

                return Right(material);
              },
            );
          }

          return const Right(null);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步BOM状态到材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> syncMaterialPriceUpdate({
    required String materialId,
    required double oldPrice,
    required double newPrice,
    bool updateBomPrices = false,
  }) async {
    try {
      if (!updateBomPrices) {
        return const Right(0);
      }

      // 1. 获取关联的BOM项目
      final linkedBomItemsResult = await getLinkedBomItems(
        materialId: materialId,
        includeCompleted: false,
      );

      return linkedBomItemsResult.fold(
        (failure) => Left(failure),
        (bomItems) async {
          int updatedCount = 0;

          // 2. 更新每个BOM项目的价格
          for (final bomItem in bomItems) {
            final updateResult = await bomRepository.updateBomItemById(
              bomItem.id,
              {'price': newPrice},
            );

            updateResult.fold(
              (failure) => {}, // 忽略单个更新失败
              (updatedBomItem) => updatedCount++,
            );
          }

          return Right(updatedCount);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '同步材料价格更新失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> batchSyncUsageStats({
    String? projectId,
  }) async {
    try {
      // TODO: 实现批量同步使用统计
      // 1. 获取所有需要同步的材料
      // 2. 计算每个材料的实际使用统计
      // 3. 批量更新材料库中的统计信息
      
      return const Right(0);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '批量同步使用统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialUsageHistory>>> getMaterialUsageHistory({
    required String materialId,
    int limit = 50,
  }) async {
    try {
      // TODO: 实现获取材料使用历史
      // 从数据库查询材料使用历史记录
      
      return const Right([]);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取材料使用历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<BomItem>>> getLinkedBomItems({
    required String materialId,
    bool includeCompleted = true,
  }) async {
    try {
      // 使用现有的Repository方法获取关联的BOM项目
      return await bomRepository.getBomItemsByMaterialId(materialId);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取关联BOM项目失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> checkDataConsistency({
    required String materialId,
  }) async {
    try {
      // TODO: 实现数据一致性检查
      // 1. 获取材料信息
      // 2. 获取关联的BOM项目
      // 3. 比较字段差异
      
      return const Right([]);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '检查数据一致性失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> resolveDataConflict({
    required String materialId,
    required List<String> conflictFields,
    required String resolution,
  }) async {
    try {
      // TODO: 实现数据冲突解决
      // 根据解决策略更新数据
      
      return const Right(true);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '解决数据冲突失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialUsageHistory>> createUsageHistory({
    required String materialId,
    required String projectId,
    required String bomItemId,
    required int quantity,
    required double unitPrice,
    required String usageType,
  }) async {
    try {
      // TODO: 实现创建使用历史记录
      // 1. 创建MaterialUsageHistory实体
      // 2. 保存到数据库
      
      final history = MaterialUsageHistory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        materialId: materialId,
        projectId: projectId,
        bomItemId: bomItemId,
        userId: '', // TODO: 获取当前用户ID
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: quantity * unitPrice,
        usageType: _parseUsageType(usageType),
        status: UsageStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return Right(history);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '创建使用历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Material>> updateMaterialUsageStats({
    required String materialId,
    bool incrementUsage = true,
  }) async {
    try {
      // 使用现有的Repository方法更新材料统计
      final updates = <String, dynamic>{
        'last_used_at': DateTime.now().toIso8601String(),
      };

      if (incrementUsage) {
        // TODO: 实现使用次数增加逻辑
        // 可能需要使用数据库函数来原子性地增加计数
      }

      return await materialRepository.updateMaterial(materialId, updates);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '更新材料使用统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<DateTime, int>>> getMaterialUsageTrend({
    required String materialId,
    int days = 30,
  }) async {
    try {
      // TODO: 实现获取材料使用趋势
      return const Right({});
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取材料使用趋势失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialUsageStats>>> getProjectTopMaterials({
    required String projectId,
    int limit = 10,
  }) async {
    try {
      // TODO: 实现获取项目热门材料
      return const Right([]);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取项目热门材料失败: $e'));
    }
  }

  // 私有辅助方法

  /// 自动同步BOM项目
  Future<Either<Failure, List<BomItem>>> _autoSyncBomItems(
    List<BomItem> bomItems,
    Map<String, dynamic> updatedFields,
  ) async {
    final updatedBomItems = <BomItem>[];

    for (final bomItem in bomItems) {
      final updateResult = await bomRepository.updateBomItemById(
        bomItem.id,
        updatedFields,
      );

      updateResult.fold(
        (failure) {
          // 忽略单个更新失败，记录日志
          debugPrint('Failed to update BOM item ${bomItem.id}: $failure');
          return null;
        },
        (updatedBomItem) {
          updatedBomItems.add(updatedBomItem);
          return updatedBomItem;
        },
      );
    }

    return Right(updatedBomItems);
  }

  /// 判断是否应该更新使用统计
  bool _shouldUpdateUsageStats(String oldStatus, String newStatus) {
    // 当状态从非完成变为完成时，更新使用统计
    return oldStatus != 'completed' && newStatus == 'completed';
  }

  /// 将状态映射为使用类型
  UsageType _mapStatusToUsageType(String status) {
    switch (status) {
      case 'completed':
        return UsageType.used;
      case 'cancelled':
        return UsageType.cancelled;
      case 'purchased':
        return UsageType.purchased;
      default:
        return UsageType.planned;
    }
  }

  /// 解析使用类型字符串
  UsageType _parseUsageType(String usageType) {
    switch (usageType.toLowerCase()) {
      case 'planned':
        return UsageType.planned;
      case 'purchased':
        return UsageType.purchased;
      case 'used':
        return UsageType.used;
      case 'cancelled':
        return UsageType.cancelled;
      case 'returned':
        return UsageType.returned;
      default:
        return UsageType.planned;
    }
  }
}
