-- VanHub项目 Supabase RLS策略修复脚本
-- 基于Supabase提示文件分析结果
-- 执行日期：2025-01-21

-- =====================================================
-- 第一步：启用所有核心表的RLS
-- =====================================================

-- 启用核心业务表的RLS
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bom_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.timeline_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- 启用材料相关表的RLS
ALTER TABLE public.material_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_reviews ENABLE ROW LEVEL SECURITY;

-- 启用项目扩展表的RLS
ALTER TABLE public.project_discovery ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_nodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_node_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_forks ENABLE ROW LEVEL SECURITY;

-- 启用其他支持表的RLS
ALTER TABLE public.log_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 第二步：验证现有策略（根据提示文件，策略已存在）
-- =====================================================

-- 查看现有策略状态
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual 
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('projects', 'bom_items', 'commits', 'timeline_events', 'users')
ORDER BY tablename, policyname;

-- =====================================================
-- 第三步：为材料分类表创建公开访问策略
-- =====================================================

-- 材料分类表 - 允许所有人读取（公共数据）
DROP POLICY IF EXISTS "Allow public read for material categories" ON public.material_categories;
CREATE POLICY "Allow public read for material categories" ON public.material_categories
    FOR SELECT USING (true);

-- 材料分类表 - 只允许管理员修改（如果需要的话）
DROP POLICY IF EXISTS "Allow admin manage material categories" ON public.material_categories;
CREATE POLICY "Allow admin manage material categories" ON public.material_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid() 
            AND users.email LIKE '%@admin.vanhub.com'
        )
    );

-- =====================================================
-- 第四步：为材料库创建适当的策略
-- =====================================================

-- 材料库 - 允许所有人读取
DROP POLICY IF EXISTS "Allow public read for materials" ON public.material_library;
CREATE POLICY "Allow public read for materials" ON public.material_library
    FOR SELECT USING (true);

-- 材料库 - 允许认证用户管理自己的材料
DROP POLICY IF EXISTS "Allow users manage own materials" ON public.material_library;
CREATE POLICY "Allow users manage own materials" ON public.material_library
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- 第五步：为材料收藏创建策略
-- =====================================================

-- 材料收藏 - 只允许用户管理自己的收藏
DROP POLICY IF EXISTS "Allow users manage own favorites" ON public.material_favorites;
CREATE POLICY "Allow users manage own favorites" ON public.material_favorites
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- 第六步：为材料评价创建策略
-- =====================================================

-- 材料评价 - 允许所有人读取
DROP POLICY IF EXISTS "Allow public read for reviews" ON public.material_reviews;
CREATE POLICY "Allow public read for reviews" ON public.material_reviews
    FOR SELECT USING (true);

-- 材料评价 - 允许认证用户创建评价
DROP POLICY IF EXISTS "Allow users create reviews" ON public.material_reviews;
CREATE POLICY "Allow users create reviews" ON public.material_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 材料评价 - 允许用户修改自己的评价
DROP POLICY IF EXISTS "Allow users update own reviews" ON public.material_reviews;
CREATE POLICY "Allow users update own reviews" ON public.material_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- 第七步：为日志条目创建策略
-- =====================================================

-- 日志条目 - 允许读取公开项目的日志
DROP POLICY IF EXISTS "Allow read logs for public projects" ON public.log_entries;
CREATE POLICY "Allow read logs for public projects" ON public.log_entries
    FOR SELECT USING (
        project_id IS NULL OR
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = log_entries.project_id 
            AND projects.is_public = true
        )
    );

-- 日志条目 - 允许用户管理自己项目的日志
DROP POLICY IF EXISTS "Allow users manage own project logs" ON public.log_entries;
CREATE POLICY "Allow users manage own project logs" ON public.log_entries
    FOR ALL USING (
        project_id IS NULL OR
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE projects.id = log_entries.project_id 
            AND projects.user_id = auth.uid()
        )
    );

-- =====================================================
-- 第八步：为媒体文件创建策略
-- =====================================================

-- 媒体文件 - 允许读取公开项目的媒体
DROP POLICY IF EXISTS "Allow read media for public projects" ON public.media;
CREATE POLICY "Allow read media for public projects" ON public.media
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.commits 
            JOIN public.projects ON commits.project_id = projects.id
            WHERE commits.id = media.commit_id 
            AND projects.is_public = true
        )
    );

-- 媒体文件 - 允许用户管理自己项目的媒体
DROP POLICY IF EXISTS "Allow users manage own project media" ON public.media;
CREATE POLICY "Allow users manage own project media" ON public.media
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.commits 
            JOIN public.projects ON commits.project_id = projects.id
            WHERE commits.id = media.commit_id 
            AND projects.user_id = auth.uid()
        )
    );

-- =====================================================
-- 第九步：为评论创建策略
-- =====================================================

-- 评论 - 允许读取所有评论
DROP POLICY IF EXISTS "Allow public read comments" ON public.comments;
CREATE POLICY "Allow public read comments" ON public.comments
    FOR SELECT USING (true);

-- 评论 - 允许认证用户创建评论
DROP POLICY IF EXISTS "Allow users create comments" ON public.comments;
CREATE POLICY "Allow users create comments" ON public.comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 评论 - 允许用户修改自己的评论
DROP POLICY IF EXISTS "Allow users update own comments" ON public.comments;
CREATE POLICY "Allow users update own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- 第十步：为社交功能创建策略
-- =====================================================

-- 项目点赞 - 允许读取所有点赞
DROP POLICY IF EXISTS "Allow public read likes" ON public.project_likes;
CREATE POLICY "Allow public read likes" ON public.project_likes
    FOR SELECT USING (true);

-- 项目点赞 - 允许认证用户管理自己的点赞
DROP POLICY IF EXISTS "Allow users manage own likes" ON public.project_likes;
CREATE POLICY "Allow users manage own likes" ON public.project_likes
    FOR ALL USING (auth.uid() = user_id);

-- 项目关注 - 允许用户管理自己的关注
DROP POLICY IF EXISTS "Allow users manage own follows" ON public.project_follows;
CREATE POLICY "Allow users manage own follows" ON public.project_follows
    FOR ALL USING (auth.uid() = user_id);

-- 项目分叉 - 允许读取所有分叉信息
DROP POLICY IF EXISTS "Allow public read forks" ON public.project_forks;
CREATE POLICY "Allow public read forks" ON public.project_forks
    FOR SELECT USING (true);

-- 项目分叉 - 允许认证用户创建分叉
DROP POLICY IF EXISTS "Allow users create forks" ON public.project_forks;
CREATE POLICY "Allow users create forks" ON public.project_forks
    FOR INSERT WITH CHECK (auth.uid() = forked_by_user_id);

-- =====================================================
-- 第十一步：验证RLS启用状态
-- =====================================================

-- 检查所有表的RLS状态
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN (
    'projects', 'bom_items', 'commits', 'timeline_events', 'users',
    'material_library', 'material_categories', 'material_favorites', 'material_reviews',
    'project_discovery', 'project_nodes', 'project_node_dependencies',
    'log_entries', 'media', 'comments', 'project_likes', 'project_follows', 'project_forks'
)
ORDER BY tablename;

-- =====================================================
-- 第十二步：测试策略是否生效
-- =====================================================

-- 这些查询应该能够成功执行（游客模式测试）
-- SELECT * FROM public.projects WHERE is_public = true LIMIT 1;
-- SELECT * FROM public.material_categories LIMIT 5;
-- SELECT * FROM public.material_library LIMIT 5;

-- =====================================================
-- 完成提示
-- =====================================================

-- 执行完成后，请在应用中测试以下功能：
-- 1. 游客模式访问公开项目
-- 2. 游客模式浏览材料库和分类
-- 3. 认证用户管理自己的项目和数据
-- 4. 认证用户创建评论和点赞

-- 如果仍有问题，请检查：
-- 1. API密钥权限设置
-- 2. Supabase项目设置中的RLS配置
-- 3. 应用代码中的认证状态处理
