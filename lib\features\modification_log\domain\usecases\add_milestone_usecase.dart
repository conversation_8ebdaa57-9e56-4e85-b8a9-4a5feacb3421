import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/milestone.dart';
import '../repositories/timeline_repository.dart';

/// 添加里程碑用例
class AddMilestoneUseCase implements UseCase<Milestone, AddMilestoneParams> {
  final TimelineRepository repository;

  AddMilestoneUseCase(this.repository);

  @override
  Future<Either<Failure, Milestone>> call(AddMilestoneParams params) async {
    return await repository.addMilestone(params.milestone);
  }
}

/// 添加里程碑参数
class AddMilestoneParams {
  final Milestone milestone;

  AddMilestoneParams({required this.milestone});
}