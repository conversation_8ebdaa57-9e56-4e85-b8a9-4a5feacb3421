import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../entities/bom_statistics.dart';
import '../entities/create_bom_item_request.dart';
import '../../../project/domain/entities/project.dart';

abstract class BomRepository {
  /// 创建BOM项
  Future<Either<Failure, BomItem>> createBomItem(CreateBomItemRequest request);
  
  /// 获取项目的BOM列表
  Future<Either<Failure, List<BomItem>>> getProjectBomItems(String projectId);
  
  /// 根据状态获取BOM项
  Future<Either<Failure, List<BomItem>>> getBomItemsByStatus(
    String projectId, 
    BomItemStatus status,
  );
  
  /// 获取BOM项详情
  Future<Either<Failure, BomItem>> getBomItemById(String bomItemId);
  
  /// 更新BOM项（通过ID和更新字段）
  Future<Either<Failure, BomItem>> updateBomItemById(
    String bomItemId, 
    Map<String, dynamic> updates,
  );
  
  /// 删除BOM项
  Future<Either<Failure, void>> deleteBomItem(String bomItemId);
  
  /// 更新BOM项状态
  Future<Either<Failure, BomItem>> updateBomItemStatus(
    String bomItemId, 
    BomItemStatus status,
  );
  
  /// 批量更新BOM项状态
  Future<Either<Failure, List<BomItem>>> batchUpdateBomItemStatus(
    List<String> bomItemIds, 
    BomItemStatus status,
  );
  
  /// 获取项目BOM统计
  Future<Either<Failure, BomStatistics>> getProjectBomStatistics(String projectId);
  
  /// 搜索BOM项
  Future<Either<Failure, List<BomItem>>> searchBomItems(
    String projectId,
    String query, {
    BomItemStatus? status,
    String? category,
    double? minPrice,
    double? maxPrice,
    int limit = 20,
    int offset = 0,
  });
  
  /// 从材料库添加到BOM（智能联动功能）
  Future<Either<Failure, BomItem>> addMaterialToBom({
    required String projectId,
    required String materialId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  });
  
  /// 将BOM项保存到材料库（反向联动）
  Future<Either<Failure, String>> saveBomItemToMaterialLibrary(String bomItemId);
  
  /// 获取用户的所有BOM项目
  Future<Either<Failure, List<BomItem>>> getUserBomItems(String userId);
  
  /// 根据材料ID获取BOM项目
  Future<Either<Failure, List<BomItem>>> getBomItemsByMaterialId(String materialId);
  
  /// 获取用户的所有项目
  Future<Either<Failure, List<Project>>> getUserProjects(String userId);
  
  /// 根据项目ID获取BOM项目
  Future<Either<Failure, List<BomItem>>> getBomItemsByProjectId(String projectId);
  
  /// 更新BOM项目（接受BomItem对象）
  Future<Either<Failure, void>> updateBomItem(BomItem bomItem);
}

