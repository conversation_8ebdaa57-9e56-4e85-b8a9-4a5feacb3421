import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material.dart';
import '../entities/search_criteria.dart';

/// 材料搜索服务接口
abstract class MaterialSearchService {
  /// 多维度搜索材料
  /// 
  /// 支持按名称、品牌、型号、分类、价格范围等多维度搜索
  Future<Either<Failure, List<Material>>> searchMaterials(
    String userId,
    SearchCriteria criteria,
  );
  
  /// 高级搜索（支持更复杂的过滤条件）
  Future<Either<Failure, List<Material>>> advancedSearch(
    String userId,
    Map<String, dynamic> filters, {
    int limit = 20, 
    int offset = 0,
  });
  
  /// 按标签搜索
  Future<Either<Failure, List<Material>>> searchByTags(
    String userId,
    List<String> tags, {
    int limit = 20, 
    int offset = 0,
  });
  
  /// 按项目类型搜索相关材料
  Future<Either<Failure, List<Material>>> searchByProjectType(
    String userId,
    String vehicleBrand,
    String vehicleModel, {
    String? systemType, 
    int limit = 20, 
    int offset = 0,
  });
  
  /// 按系统类型搜索相关材料
  Future<Either<Failure, List<Material>>> searchBySystemType(
    String userId,
    String systemType, {
    int limit = 20, 
    int offset = 0,
  });
  
  /// 搜索建议（自动完成）
  Future<Either<Failure, List<String>>> getSuggestions(
    String userId,
    String query, {
    int limit = 10,
  });
  
  /// 获取热门搜索关键词
  Future<Either<Failure, List<String>>> getPopularSearchTerms({
    int limit = 10,
  });
}