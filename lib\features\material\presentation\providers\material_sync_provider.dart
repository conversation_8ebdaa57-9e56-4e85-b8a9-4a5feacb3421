import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/di/injection_container.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/material_usage_history.dart';
import '../../domain/services/material_bom_sync_service.dart';
import '../../data/services/material_bom_sync_service_impl.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/presentation/providers/bom_provider.dart';
import 'material_provider.dart';

part 'material_sync_provider.g.dart';

/// 材料同步服务Provider
@riverpod
MaterialBomSyncService materialBomSyncService(MaterialBomSyncServiceRef ref) {
  final materialRepository = ref.watch(materialRepositoryProvider);
  final bomRepository = ref.watch(bomRepositoryProvider);
  
  return MaterialBomSyncServiceImpl(
    materialRepository: materialRepository,
    bomRepository: bomRepository,
  );
}

/// 材料同步控制器Provider
@riverpod
class MaterialSyncController extends _$MaterialSyncController {
  @override
  AsyncValue<void> build() {
    return const AsyncData(null);
  }

  /// 同步材料更新到BOM
  Future<Either<Failure, List<BomItem>>> syncMaterialUpdateToBom({
    required String materialId,
    required Map<String, dynamic> updatedFields,
    String syncStrategy = 'prompt',
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).syncMaterialUpdateToBom(
        materialId: materialId,
        updatedFields: updatedFields,
        syncStrategy: syncStrategy,
      );

      state = const AsyncData(null);
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '同步材料更新到BOM失败: $e'));
    }
  }

  /// 同步BOM状态到材料
  Future<Either<Failure, Material?>> syncBomStatusToMaterial({
    required String bomItemId,
    required String oldStatus,
    required String newStatus,
    required int quantity,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).syncBomStatusToMaterial(
        bomItemId: bomItemId,
        oldStatus: oldStatus,
        newStatus: newStatus,
        quantity: quantity,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight()) {
        result.fold(
          (failure) => {},
          (material) {
            if (material != null) {
              ref.invalidate(userMaterialsProvider);
              ref.invalidate(materialDetailProvider(material.id));
            }
          },
        );
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '同步BOM状态到材料失败: $e'));
    }
  }

  /// 同步材料价格更新
  Future<Either<Failure, int>> syncMaterialPriceUpdate({
    required String materialId,
    required double oldPrice,
    required double newPrice,
    bool updateBomPrices = false,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).syncMaterialPriceUpdate(
        materialId: materialId,
        oldPrice: oldPrice,
        newPrice: newPrice,
        updateBomPrices: updateBomPrices,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight() && result.getOrElse((failure) => 0) > 0) {
        ref.invalidate(projectBomItemsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '同步材料价格更新失败: $e'));
    }
  }

  /// 获取关联的BOM项目
  Future<Either<Failure, List<BomItem>>> getLinkedBomItems({
    required String materialId,
    bool includeCompleted = true,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).getLinkedBomItems(
        materialId: materialId,
        includeCompleted: includeCompleted,
      );

      state = const AsyncData(null);
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '获取关联BOM项目失败: $e'));
    }
  }

  /// 批量同步使用统计
  Future<Either<Failure, int>> batchSyncUsageStats({
    String? projectId,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).batchSyncUsageStats(
        projectId: projectId,
      );

      state = const AsyncData(null);
      
      // 刷新相关Provider
      if (result.isRight() && result.getOrElse((failure) => 0) > 0) {
        ref.invalidate(userMaterialsProvider);
      }
      
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '批量同步使用统计失败: $e'));
    }
  }

  /// 获取材料使用历史
  Future<Either<Failure, List<MaterialUsageHistory>>> getMaterialUsageHistory({
    required String materialId,
    int limit = 50,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).getMaterialUsageHistory(
        materialId: materialId,
        limit: limit,
      );

      state = const AsyncData(null);
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '获取材料使用历史失败: $e'));
    }
  }

  /// 获取材料使用趋势
  Future<Either<Failure, Map<DateTime, int>>> getMaterialUsageTrend({
    required String materialId,
    int days = 30,
  }) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(materialBomSyncServiceProvider).getMaterialUsageTrend(
        materialId: materialId,
        days: days,
      );

      state = const AsyncData(null);
      return result;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      return Left(UnknownFailure(message: '获取材料使用趋势失败: $e'));
    }
  }
}

/// 关联BOM项目Provider
@riverpod
Future<List<BomItem>> linkedBomItems(LinkedBomItemsRef ref, String materialId) async {
  final result = await ref.watch(materialSyncControllerProvider.notifier)
      .getLinkedBomItems(materialId: materialId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
}

/// 材料使用历史Provider
@riverpod
Future<List<MaterialUsageHistory>> materialUsageHistory(
  MaterialUsageHistoryRef ref, 
  String materialId,
) async {
  final result = await ref.watch(materialSyncControllerProvider.notifier)
      .getMaterialUsageHistory(materialId: materialId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (history) => history,
  );
}

/// 材料使用趋势Provider
@riverpod
Future<Map<DateTime, int>> materialUsageTrend(
  MaterialUsageTrendRef ref, 
  String materialId,
) async {
  final result = await ref.watch(materialSyncControllerProvider.notifier)
      .getMaterialUsageTrend(materialId: materialId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (trend) => trend,
  );
}
