import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/log_repository.dart';

/// 删除日志条目用例
class DeleteLogEntryUseCase implements UseCase<void, DeleteLogEntryParams> {
  final LogRepository repository;

  DeleteLogEntryUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteLogEntryParams params) async {
    return await repository.deleteLogEntry(params.logId);
  }
}

/// 删除日志条目参数
class DeleteLogEntryParams {
  final String logId;

  DeleteLogEntryParams({required this.logId});
}