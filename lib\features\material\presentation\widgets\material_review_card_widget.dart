import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/material_review.dart';

/// 材料评价卡片组件
/// 显示单个材料评价的详细信息，帮助用户选择合适的物料
class MaterialReviewCardWidget extends ConsumerWidget {
  final MaterialReview review;
  final VoidCallback? onLike;
  final VoidCallback? onMarkHelpful;
  final VoidCallback? onReply;
  final VoidCallback? onReport;
  final bool showActions;
  final bool isCompact;

  const MaterialReviewCardWidget({
    super.key,
    required this.review,
    this.onLike,
    this.onMarkHelpful,
    this.onReply,
    this.onReport,
    this.showActions = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息和评分
            _buildUserHeader(context, colorScheme),
            
            const SizedBox(height: 12),
            
            // 评价内容
            _buildReviewContent(context),
            
            if (!isCompact) ...[
              const SizedBox(height: 12),
              
              // 专业评分维度
              _buildRatingDimensions(context, colorScheme),
              
              const SizedBox(height: 12),
              
              // 使用场景信息
              _buildUsageContext(context, colorScheme),
              
              // 优缺点和技巧
              if (review.pros.isNotEmpty || review.cons.isNotEmpty || review.tips.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildProsCons(context, colorScheme),
              ],
              
              // 图片展示
              if (review.imageUrls.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildImageGallery(context),
              ],
            ],
            
            const SizedBox(height: 12),
            
            // 操作按钮
            if (showActions) _buildActionButtons(context, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(BuildContext context, ColorScheme colorScheme) {
    return Row(
      children: [
        // 用户头像
        CircleAvatar(
          radius: 20,
          backgroundImage: review.userAvatarUrl != null
              ? NetworkImage(review.userAvatarUrl!)
              : null,
          child: review.userAvatarUrl == null
              ? Text(review.userName.isNotEmpty ? review.userName[0].toUpperCase() : 'U')
              : null,
        ),
        
        const SizedBox(width: 12),
        
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    review.userName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // 验证购买标识
                  if (review.isVerifiedPurchase)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.green, width: 1),
                      ),
                      child: Text(
                        '已验证购买',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Colors.green,
                          fontSize: 10,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              Row(
                children: [
                  // 总体评分
                  _buildStarRating(review.rating),
                  
                  const SizedBox(width: 8),
                  
                  Text(
                    review.rating.toStringAsFixed(1),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // 评价时间
                  Text(
                    DateFormat('yyyy-MM-dd').format(review.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReviewContent(BuildContext context) {
    return Text(
      review.content,
      style: Theme.of(context).textTheme.bodyMedium,
    );
  }

  Widget _buildRatingDimensions(BuildContext context, ColorScheme colorScheme) {
    final dimensions = [
      ('质量', review.qualityRating),
      ('性价比', review.valueRating),
      ('耐用性', review.durabilityRating),
      ('安装难度', review.installationRating),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '详细评分',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        ...dimensions.map((dimension) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              SizedBox(
                width: 60,
                child: Text(
                  dimension.$1,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              
              const SizedBox(width: 8),
              
              _buildStarRating(dimension.$2, size: 14),
              
              const SizedBox(width: 8),
              
              Text(
                dimension.$2.toStringAsFixed(1),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildUsageContext(BuildContext context, ColorScheme colorScheme) {
    final contextItems = <Widget>[];
    
    if (review.vehicleType != null) {
      contextItems.add(_buildContextChip('车型', review.vehicleType!, colorScheme));
    }
    
    if (review.systemType != null) {
      contextItems.add(_buildContextChip('系统', review.systemType!, colorScheme));
    }
    
    if (review.usageDuration != null) {
      contextItems.add(_buildContextChip('使用时长', review.usageDuration!, colorScheme));
    }

    if (contextItems.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '使用场景',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: contextItems,
        ),
      ],
    );
  }

  Widget _buildContextChip(String label, String value, ColorScheme colorScheme) {
    return Chip(
      label: Text('$label: $value'),
      backgroundColor: colorScheme.surfaceVariant,
      labelStyle: TextStyle(
        fontSize: 12,
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildProsCons(BuildContext context, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 优点
        if (review.pros.isNotEmpty) ...[
          Text(
            '优点',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
          
          const SizedBox(height: 4),
          
          ...review.pros.map((pro) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.add_circle,
                  size: 16,
                  color: Colors.green,
                ),
                
                const SizedBox(width: 8),
                
                Expanded(
                  child: Text(
                    pro,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          )),
          
          const SizedBox(height: 8),
        ],
        
        // 缺点
        if (review.cons.isNotEmpty) ...[
          Text(
            '缺点',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.orange,
            ),
          ),
          
          const SizedBox(height: 4),
          
          ...review.cons.map((con) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.remove_circle,
                  size: 16,
                  color: Colors.orange,
                ),
                
                const SizedBox(width: 8),
                
                Expanded(
                  child: Text(
                    con,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          )),
          
          const SizedBox(height: 8),
        ],
        
        // 使用技巧
        if (review.tips.isNotEmpty) ...[
          Text(
            '使用技巧',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
          
          const SizedBox(height: 4),
          
          ...review.tips.map((tip) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb,
                  size: 16,
                  color: Colors.blue,
                ),
                
                const SizedBox(width: 8),
                
                Expanded(
                  child: Text(
                    tip,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildImageGallery(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '图片展示',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: review.imageUrls.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    review.imageUrls[index],
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[300],
                        child: const Icon(Icons.error),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, ColorScheme colorScheme) {
    return Row(
      children: [
        // 有用按钮
        TextButton.icon(
          onPressed: onMarkHelpful,
          icon: Icon(
            Icons.thumb_up,
            size: 16,
            color: colorScheme.primary,
          ),
          label: Text(
            '有用 (${review.helpfulCount})',
            style: TextStyle(color: colorScheme.primary),
          ),
        ),
        
        // 点赞按钮
        TextButton.icon(
          onPressed: onLike,
          icon: Icon(
            Icons.favorite,
            size: 16,
            color: colorScheme.primary,
          ),
          label: Text(
            '点赞 (${review.likedByUserIds.length})',
            style: TextStyle(color: colorScheme.primary),
          ),
        ),
        
        const Spacer(),
        
        // 举报按钮
        TextButton(
          onPressed: onReport,
          child: Text(
            '举报',
            style: TextStyle(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStarRating(double rating, {double size = 16}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;
        return Icon(
          starValue <= rating
              ? Icons.star
              : starValue - 0.5 <= rating
                  ? Icons.star_half
                  : Icons.star_border,
          size: size,
          color: Colors.amber,
        );
      }),
    );
  }
}
