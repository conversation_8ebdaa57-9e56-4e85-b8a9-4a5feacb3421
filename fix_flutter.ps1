# Reset Flutter mirror settings
Write-Host "Resetting Flutter mirror settings..." -ForegroundColor Green

# Remove environment variables in current session
$env:PUB_HOSTED_URL = $null
$env:FLUTTER_STORAGE_BASE_URL = $null

# Remove system environment variables
[Environment]::SetEnvironmentVariable("PUB_HOSTED_URL", $null, "User")
[Environment]::SetEnvironmentVariable("FLUTTER_STORAGE_BASE_URL", $null, "User")

# Clean Flutter cache
Write-Host "Cleaning Flutter cache..." -ForegroundColor Green
flutter clean
flutter pub cache clean

# Get dependencies again
Write-Host "Getting dependencies again..." -ForegroundColor Green
flutter pub get

Write-Host "Done! If you still have issues, try these commands to set official sources:" -ForegroundColor Cyan
Write-Host '$env:PUB_HOSTED_URL=$null' -ForegroundColor Yellow
Write-Host '$env:FLUTTER_STORAGE_BASE_URL=$null' -ForegroundColor Yellow