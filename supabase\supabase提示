2025-07-24-10：36
errors
| name                       | title                      | level | facing   | categories   | description                                                                                                                                                                                         | detail                                                                                                                                                                                                                                                                                                                               | remediation                                                                                    | metadata                                                              | cache_key                                               |
| -------------------------- | -------------------------- | ----- | -------- | ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------- | ------------------------------------------------------- |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table.                                                                     | Table \`public.bom_items\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can delete bom_items of own projects","Users can insert bom_items to own projects","Users can update bom_items of own projects","Users can view bom_items of own projects","Users can view bom_items of public projects"}. | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"bom_items","type":"table","schema":"public"}                 | policy_exists_rls_disabled_public_bom_items             |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table.                                                                     | Table \`public.commits\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can delete commits of own projects","Users can insert commits to own projects","Users can update commits of own projects","Users can view commits of own projects","Users can view commits of public projects"}.             | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"commits","type":"table","schema":"public"}                   | policy_exists_rls_disabled_public_commits               |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table.                                                                     | Table \`public.projects\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can delete own projects","Users can insert own projects","Users can update own projects","Users can view own projects","Users can view public projects"}.                                                                   | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"projects","type":"table","schema":"public"}                  | policy_exists_rls_disabled_public_projects              |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table.                                                                     | Table \`public.timeline_events\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can delete timeline events","Users can insert timeline events","Users can update timeline events","Users can view timeline events"}.                                                                                 | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"timeline_events","type":"table","schema":"public"}           | policy_exists_rls_disabled_public_timeline_events       |
| policy_exists_rls_disabled | Policy Exists RLS Disabled | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) policies have been created, but RLS has not been enabled for the underlying table.                                                                     | Table \`public.users\` has RLS policies but RLS is not enabled on the table. Policies include {"Users can insert own profile","Users can update own profile","Users can view own profile"}.                                                                                                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0007_policy_exists_rls_disabled | {"name":"users","type":"table","schema":"public"}                     | policy_exists_rls_disabled_public_users                 |
| security_definer_view      | Security Definer View      | ERROR | EXTERNAL | ["SECURITY"] | Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user | View \`public.project_bom_summary\` is defined with the SECURITY DEFINER property                                                                                                                                                                                                                                                    | https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view      | {"name":"project_bom_summary","type":"view","schema":"public"}        | security_definer_view_public_project_bom_summary        |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.users\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                                      | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"users","type":"table","schema":"public"}                     | rls_disabled_in_public_public_users                     |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.projects\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                                   | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"projects","type":"table","schema":"public"}                  | rls_disabled_in_public_public_projects                  |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.commits\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                                    | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"commits","type":"table","schema":"public"}                   | rls_disabled_in_public_public_commits                   |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.project_discovery\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"project_discovery","type":"table","schema":"public"}         | rls_disabled_in_public_public_project_discovery         |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.timeline_events\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                            | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"timeline_events","type":"table","schema":"public"}           | rls_disabled_in_public_public_timeline_events           |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.project_node_dependencies\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                  | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"project_node_dependencies","type":"table","schema":"public"} | rls_disabled_in_public_public_project_node_dependencies |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.bom_items\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                                  | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"bom_items","type":"table","schema":"public"}                 | rls_disabled_in_public_public_bom_items                 |
| rls_disabled_in_public     | RLS Disabled in Public     | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST                                                                                         | Table \`public.project_nodes\` is public, but RLS has not been enabled.                                                                                                                                                                                                                                                              | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public     | {"name":"project_nodes","type":"table","schema":"public"}             | rls_disabled_in_public_public_project_nodes             |

warnings
| name                            | title                               | level | facing   | categories   | description                                                   | detail                                                                                                                                   | remediation                                                                                              | metadata                                                                      | cache_key                                                                                           |
| ------------------------------- | ----------------------------------- | ----- | -------- | ------------ | ------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.increment_project_like_count\` has a role mutable search_path                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"increment_project_like_count","type":"function","schema":"public"}   | function_search_path_mutable_public_increment_project_like_count_62e831a5b3cdf3df5c4dda272f1d49e4   |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.decrement_project_like_count\` has a role mutable search_path                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"decrement_project_like_count","type":"function","schema":"public"}   | function_search_path_mutable_public_decrement_project_like_count_62638abe3901781a977438bb2790d241   |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.update_material_rating\` has a role mutable search_path                                                                | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_material_rating","type":"function","schema":"public"}         | function_search_path_mutable_public_update_material_rating_8c29e1902c2eb64850ab8e099c28524f         |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.trigger_update_material_rating\` has a role mutable search_path                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"trigger_update_material_rating","type":"function","schema":"public"} | function_search_path_mutable_public_trigger_update_material_rating_955f9f9ade605b879a2503eeef775da2 |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.update_review_helpful_count\` has a role mutable search_path                                                           | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_review_helpful_count","type":"function","schema":"public"}    | function_search_path_mutable_public_update_review_helpful_count_5f1eb369a690a30598d3455f0b94efe4    |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.check_node_dependency_cycle\` has a role mutable search_path                                                           | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"check_node_dependency_cycle","type":"function","schema":"public"}    | function_search_path_mutable_public_check_node_dependency_cycle_bed7c3bb3b3d6c22c483c829a4cbe563    |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.handle_new_user\` has a role mutable search_path                                                                       | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"handle_new_user","type":"function","schema":"public"}                | function_search_path_mutable_public_handle_new_user_f4062a93ae477c8fa367f3df9a9d317f                |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.handle_user_update\` has a role mutable search_path                                                                    | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"handle_user_update","type":"function","schema":"public"}             | function_search_path_mutable_public_handle_user_update_1c75761ca0462fbb98e45c2b2e7cdfd7             |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.update_updated_at_column\` has a role mutable search_path                                                              | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_updated_at_column","type":"function","schema":"public"}       | function_search_path_mutable_public_update_updated_at_column_06bcf30ac3d0a7f279a54cbf228a7bec       |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.fork_project\` has a role mutable search_path                                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"fork_project","type":"function","schema":"public"}                   | function_search_path_mutable_public_fork_project_a096112b1b11ed4e5c462daad2217a4e                   |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.get_project_bom_summary\` has a role mutable search_path                                                               | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"get_project_bom_summary","type":"function","schema":"public"}        | function_search_path_mutable_public_get_project_bom_summary_0835e6189c1ef0eeab47520f9bee686d        |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.increment_material_usage\` has a role mutable search_path                                                              | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"increment_material_usage","type":"function","schema":"public"}       | function_search_path_mutable_public_increment_material_usage_19f03d34fd8044bddb05f88a63c8c0dc       |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.increment_project_view_count\` has a role mutable search_path                                                          | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"increment_project_view_count","type":"function","schema":"public"}   | function_search_path_mutable_public_increment_project_view_count_f99c0529d09f79d70d1de99465d838fd   |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set. | Function \`public.update_project_stats\` has a role mutable search_path                                                                  | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_project_stats","type":"function","schema":"public"}           | function_search_path_mutable_public_update_project_stats_655d2f34e251650ee4ad056d13df7344           |
| auth_leaked_password_protection | Leaked Password Protection Disabled | WARN  | EXTERNAL | ["SECURITY"] | Leaked password protection is currently disabled.             | Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security. | https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection | {"type":"auth","entity":"Auth"}                                               | auth_leaked_password_protection                                                                     |