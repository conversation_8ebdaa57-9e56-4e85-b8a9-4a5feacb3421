import 'package:intl/intl.dart';

/// VanHub通用格式化工具类
class VanHubFormatters {
  VanHubFormatters._();

  /// 格式化货币金额
  static String formatCurrency(double amount) {
    if (amount >= 10000) {
      return '${(amount / 10000).toStringAsFixed(1)}万';
    }
    final formatter = NumberFormat('#,##0', 'zh_CN');
    return formatter.format(amount);
  }

  /// 格式化日期为相对时间
  static String formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return DateFormat('MM-dd').format(date);
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化价格显示
  static String formatPrice(double price) {
    if (price >= 10000) {
      return '${(price / 10000).toStringAsFixed(1)}万';
    }
    final formatter = NumberFormat('#,##0', 'zh_CN');
    return formatter.format(price);
  }

  /// 格式化日期为简短格式
  static String formatShortDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 0) {
      return '${(-difference.inDays)}天后';
    } else {
      return '今天';
    }
  }

  /// 格式化完整日期
  static String formatFullDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}