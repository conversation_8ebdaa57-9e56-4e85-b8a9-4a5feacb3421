import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/material_favorite.dart';

/// 材料收藏Repository接口
/// 定义材料收藏功能的数据访问抽象
abstract class MaterialFavoriteRepository {
  
  /// 添加材料到收藏
  /// 
  /// [materialId] 材料ID
  /// [tags] 收藏标签（可选）
  /// [notes] 收藏备注（可选）
  /// [category] 收藏分类（可选）
  /// [priority] 优先级（1-5，默认3）
  /// 
  /// 返回创建的收藏记录
  Future<Either<Failure, MaterialFavorite>> addToFavorites({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  });

  /// 从收藏中移除材料
  /// 
  /// [materialId] 材料ID
  /// 
  /// 返回操作结果
  Future<Either<Failure, void>> removeFromFavorites(String materialId);

  /// 检查材料是否已收藏
  /// 
  /// [materialId] 材料ID
  /// 
  /// 返回是否已收藏
  Future<Either<Failure, bool>> isFavorited(String materialId);

  /// 获取用户的所有收藏材料
  /// 
  /// [limit] 返回数量限制（可选）
  /// [offset] 偏移量（可选）
  /// [sortBy] 排序方式（可选）
  /// [filterBy] 筛选条件（可选）
  /// 
  /// 返回收藏列表
  Future<Either<Failure, List<MaterialFavorite>>> getUserFavorites({
    int? limit,
    int? offset,
    String? sortBy,
    Map<String, dynamic>? filterBy,
  });

  /// 获取收藏详情
  /// 
  /// [favoriteId] 收藏记录ID
  /// 
  /// 返回收藏详情
  Future<Either<Failure, MaterialFavorite>> getFavoriteById(String favoriteId);

  /// 通过材料ID获取收藏记录
  /// 
  /// [materialId] 材料ID
  /// 
  /// 返回收藏记录（如果存在）
  Future<Either<Failure, MaterialFavorite?>> getFavoriteByMaterialId(String materialId);

  /// 更新收藏信息
  /// 
  /// [favoriteId] 收藏记录ID
  /// [updates] 更新数据
  /// 
  /// 返回更新后的收藏记录
  Future<Either<Failure, MaterialFavorite>> updateFavorite(
    String favoriteId,
    Map<String, dynamic> updates,
  );

  /// 批量更新收藏
  /// 
  /// [favoriteIds] 收藏记录ID列表
  /// [updates] 更新数据
  /// 
  /// 返回更新的记录数量
  Future<Either<Failure, int>> batchUpdateFavorites(
    List<String> favoriteIds,
    Map<String, dynamic> updates,
  );

  /// 切换收藏状态
  /// 
  /// [materialId] 材料ID
  /// [tags] 收藏标签（添加收藏时使用）
  /// [notes] 收藏备注（添加收藏时使用）
  /// [category] 收藏分类（添加收藏时使用）
  /// [priority] 优先级（添加收藏时使用）
  /// 
  /// 返回操作后的收藏状态（true=已收藏，false=未收藏）
  Future<Either<Failure, bool>> toggleFavorite({
    required String materialId,
    List<String>? tags,
    String? notes,
    String? category,
    int priority = 3,
  });

  /// 获取收藏统计信息
  /// 
  /// 返回收藏统计数据
  Future<Either<Failure, FavoriteStats>> getFavoriteStats();

  /// 按分类获取收藏
  /// 
  /// [category] 分类名称
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回该分类下的收藏列表
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByCategory(
    String category, {
    int? limit,
  });

  /// 按标签获取收藏
  /// 
  /// [tag] 标签名称
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回包含该标签的收藏列表
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByTag(
    String tag, {
    int? limit,
  });

  /// 按优先级获取收藏
  /// 
  /// [priority] 优先级（1-5）
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回该优先级的收藏列表
  Future<Either<Failure, List<MaterialFavorite>>> getFavoritesByPriority(
    int priority, {
    int? limit,
  });

  /// 获取置顶收藏
  /// 
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回置顶的收藏列表
  Future<Either<Failure, List<MaterialFavorite>>> getPinnedFavorites({
    int? limit,
  });

  /// 获取最近收藏
  /// 
  /// [days] 天数范围（默认7天）
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回最近收藏的列表
  Future<Either<Failure, List<MaterialFavorite>>> getRecentFavorites({
    int days = 7,
    int? limit,
  });

  /// 搜索收藏
  /// 
  /// [query] 搜索关键词
  /// [searchIn] 搜索范围（tags, notes, category等）
  /// [limit] 返回数量限制（可选）
  /// 
  /// 返回搜索结果
  Future<Either<Failure, List<MaterialFavorite>>> searchFavorites({
    required String query,
    List<String>? searchIn,
    int? limit,
  });

  /// 获取所有收藏标签
  /// 
  /// 返回标签列表（按使用频率排序）
  Future<Either<Failure, List<String>>> getAllTags();

  /// 获取所有收藏分类
  /// 
  /// 返回分类列表（按使用频率排序）
  Future<Either<Failure, List<String>>> getAllCategories();

  /// 清空用户的所有收藏
  /// 
  /// 返回删除的记录数量
  Future<Either<Failure, int>> clearAllFavorites();

  /// 导出收藏数据
  /// 
  /// [format] 导出格式（json, csv等）
  /// [includeMetadata] 是否包含元数据
  /// 
  /// 返回导出的数据
  Future<Either<Failure, Map<String, dynamic>>> exportFavorites({
    String format = 'json',
    bool includeMetadata = true,
  });

  /// 导入收藏数据
  /// 
  /// [data] 导入的数据
  /// [format] 数据格式（json, csv等）
  /// [mergeStrategy] 合并策略（replace, merge, skip）
  /// 
  /// 返回导入的记录数量
  Future<Either<Failure, int>> importFavorites({
    required Map<String, dynamic> data,
    String format = 'json',
    String mergeStrategy = 'merge',
  });
}

/// 收藏排序方式枚举
enum FavoriteSortBy {
  createdAt,      // 按创建时间
  updatedAt,      // 按更新时间
  priority,       // 按优先级
  materialName,   // 按材料名称
  category,       // 按分类
}

/// 收藏筛选条件
class FavoriteFilter {
  final String? category;
  final List<String>? tags;
  final int? minPriority;
  final int? maxPriority;
  final bool? isPinned;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const FavoriteFilter({
    this.category,
    this.tags,
    this.minPriority,
    this.maxPriority,
    this.isPinned,
    this.createdAfter,
    this.createdBefore,
  });

  Map<String, dynamic> toMap() {
    return {
      if (category != null) 'category': category,
      if (tags != null) 'tags': tags,
      if (minPriority != null) 'min_priority': minPriority,
      if (maxPriority != null) 'max_priority': maxPriority,
      if (isPinned != null) 'is_pinned': isPinned,
      if (createdAfter != null) 'created_after': createdAfter!.toIso8601String(),
      if (createdBefore != null) 'created_before': createdBefore!.toIso8601String(),
    };
  }
}
