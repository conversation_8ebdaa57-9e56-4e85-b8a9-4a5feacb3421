import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/material_recommendation.dart';
import '../../domain/services/material_recommendation_service.dart';
import '../../domain/repositories/material_repository.dart';

/// 材料推荐服务实现
/// 遵循Clean Architecture原则，实现智能材料推荐功能
class MaterialRecommendationServiceImpl implements MaterialRecommendationService {
  final MaterialRepository materialRepository;

  const MaterialRecommendationServiceImpl({
    required this.materialRepository,
  });

  /// 判断两个类别是否互补
  bool _isComplementaryCategory(String category1, String category2) {
    // 定义互补类别映射
    const complementaryMap = {
      '电池系统': ['充电系统', '电源管理', '逆变器'],
      '充电系统': ['电池系统', '电源管理'],
      '水电系统': ['储水设备', '净水设备', '管道配件'],
      '储水设备': ['水电系统', '净水设备'],
      '照明系统': ['电池系统', '开关控制'],
      '通风系统': ['温控设备', '空调系统'],
      '厨房设备': ['燃气系统', '储水设备'],
      '燃气系统': ['厨房设备', '通风系统'],
    };

    final complementaries = complementaryMap[category1];
    return complementaries?.contains(category2) ?? false;
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForProject(
    String projectId, {
    int limit = 10,
  }) async {
    try {
      // 实现基于项目的材料推荐逻辑
      // 1. 获取用户材料作为基础数据 (暂时使用空用户ID获取公共材料)
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 基于项目类型推荐热门材料
          final recommendations = materials
              .take(limit)
              .map((material) => MaterialRecommendation(
                    material: material,
                    relevanceScore: 80.0, // 基础推荐分数
                    reason: '基于项目类型推荐',
                    type: RecommendationType.projectBased,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为项目推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForSystem(
    String projectId,
    String systemType, {
    int limit = 10,
  }) async {
    try {
      // 实现基于系统类型的材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 基于系统类型过滤相关材料
          final filteredMaterials = materials.where((material) {
            // 简单的系统类型匹配逻辑
            final category = material.category.toLowerCase();
            final systemTypeLower = systemType.toLowerCase();

            return category.contains(systemTypeLower) ||
                   material.name.toLowerCase().contains(systemTypeLower);
          }).toList();

          // 3. 按相关性排序并生成推荐
          final recommendations = filteredMaterials
              .take(limit)
              .map((material) => MaterialRecommendation(
                    material: material,
                    relevanceScore: 90.0, // 系统匹配推荐分数更高
                    reason: '适用于$systemType系统',
                    type: RecommendationType.systemBased,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为系统推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendSimilarMaterials(
    String materialId, {
    int limit = 10,
  }) async {
    try {
      // 实现相似材料推荐逻辑
      // 1. 获取指定材料信息
      final targetMaterialResult = await materialRepository.getMaterialById(materialId);

      return targetMaterialResult.fold(
        (failure) => Left(failure),
        (targetMaterial) async {
          // 2. 获取用户材料进行相似性比较
          final allMaterialsResult = await materialRepository.getUserMaterials('');

          return allMaterialsResult.fold(
            (failure) => Left(failure),
            (allMaterials) {
              // 3. 基于属性相似性查找相似材料
              final similarMaterials = allMaterials
                  .where((material) => material.id != materialId) // 排除自己
                  .where((material) {
                    // 简单的相似性判断：同类别或同品牌
                    return material.category == targetMaterial.category ||
                           (material.brand != null &&
                            material.brand == targetMaterial.brand);
                  })
                  .take(limit)
                  .map((material) => MaterialRecommendation(
                        material: material,
                        relevanceScore: 70.0, // 相似材料推荐分数
                        reason: '与${targetMaterial.name}相似',
                        type: RecommendationType.similarMaterial,
                      ))
                  .toList();

              return Right(similarMaterials);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐相似材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendComplementaryMaterials(
    String materialId, {
    int limit = 10,
  }) async {
    try {
      // 实现搭配材料推荐逻辑
      // 1. 获取指定材料信息
      final targetMaterialResult = await materialRepository.getMaterialById(materialId);

      return targetMaterialResult.fold(
        (failure) => Left(failure),
        (targetMaterial) async {
          // 2. 获取用户材料进行搭配分析
          final allMaterialsResult = await materialRepository.getUserMaterials('');

          return allMaterialsResult.fold(
            (failure) => Left(failure),
            (allMaterials) {
              // 3. 基于类别互补性查找搭配材料
              final complementaryMaterials = allMaterials
                  .where((material) => material.id != materialId) // 排除自己
                  .where((material) {
                    // 简单的搭配逻辑：不同类别但相关的材料
                    return material.category != targetMaterial.category &&
                           _isComplementaryCategory(targetMaterial.category, material.category);
                  })
                  .take(limit)
                  .map((material) => MaterialRecommendation(
                        material: material,
                        relevanceScore: 60.0, // 搭配材料推荐分数
                        reason: '与${targetMaterial.name}搭配使用',
                        type: RecommendationType.complementary,
                      ))
                  .toList();

              return Right(complementaryMaterials);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐搭配材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendPopularMaterials(
    String userId, {
    String? category,
    int limit = 10,
  }) async {
    try {
      // 实现热门材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 按类别过滤（如果指定）
          var filteredMaterials = materials;
          if (category != null && category.isNotEmpty && category != '全部') {
            filteredMaterials = materials.where((material) =>
                material.category == category).toList();
          }

          // 3. 按使用频率排序（usageCount）
          filteredMaterials.sort((a, b) => b.usageCount.compareTo(a.usageCount));

          // 4. 生成热门材料推荐
          final recommendations = filteredMaterials
              .take(limit)
              .map((material) => MaterialRecommendation(
                    material: material,
                    relevanceScore: 80.0 + (material.usageCount * 1.0), // 基于使用次数的动态分数
                    reason: '热门材料 (使用${material.usageCount}次)',
                    type: RecommendationType.popular,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐热门材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendValueForMoneyMaterials(
    String userId, {
    String? category,
    int limit = 10,
  }) async {
    try {
      // 实现性价比材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 按类别过滤（如果指定）
          var filteredMaterials = materials;
          if (category != null && category.isNotEmpty && category != '全部') {
            filteredMaterials = materials.where((material) =>
                material.category == category).toList();
          }

          // 3. 过滤有价格的材料并计算性价比
          final materialsWithPrice = filteredMaterials
              .where((material) => material.price > 0)
              .toList();

          // 4. 按性价比排序（使用次数/价格比）
          materialsWithPrice.sort((a, b) {
            final ratioA = a.usageCount / a.price;
            final ratioB = b.usageCount / b.price;
            return ratioB.compareTo(ratioA);
          });

          // 5. 生成性价比推荐
          final recommendations = materialsWithPrice
              .take(limit)
              .map((material) {
                final ratio = material.usageCount / material.price;
                return MaterialRecommendation(
                  material: material,
                  relevanceScore: 70.0 + (ratio * 10.0).clamp(0.0, 30.0), // 基于性价比的动态分数
                  reason: '高性价比 (¥${material.price.toStringAsFixed(2)})',
                  type: RecommendationType.valueForMoney,
                );
              })
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐性价比材料失败: $e'));
    }
  }
}