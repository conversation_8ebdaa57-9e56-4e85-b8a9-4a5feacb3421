import 'package:flutter/material.dart';
import '../../foundation/colors/colors.dart';
import '../../foundation/typography/typography.dart';
import '../../foundation/spacing/spacing.dart';
import '../../foundation/animations/animations.dart';

/// VanHub按钮组件，符合Material 3设计规范
class VanHubButton extends StatelessWidget {
  const VanHubButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.variant = VanHubButtonVariant.primary,
    this.size = VanHubButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.width,
    this.height,
  });

  final String text;
  final VoidCallback? onPressed;
  final VanHubButtonVariant variant;
  final VanHubButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final IconData? icon;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = !isDisabled && !isLoading && onPressed != null;

    return AnimatedContainer(
      duration: VanHubAnimations.fast,
      curve: VanHubAnimations.easeInOut,
      width: width,
      height: height ?? _getButtonHeight(),
      child: _buildButton(context, theme, isEnabled),
    );
  }

  Widget _buildButton(BuildContext context, ThemeData theme, bool isEnabled) {
    switch (variant) {
      case VanHubButtonVariant.primary:
        return _buildPrimaryButton(theme, isEnabled);
      case VanHubButtonVariant.secondary:
        return _buildSecondaryButton(theme, isEnabled);
      case VanHubButtonVariant.outlined:
        return _buildOutlinedButton(theme, isEnabled);
      case VanHubButtonVariant.text:
        return _buildTextButton(theme, isEnabled);
      case VanHubButtonVariant.danger:
        return _buildDangerButton(theme, isEnabled);
    }
  }

  Widget _buildPrimaryButton(ThemeData theme, bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled ? VanHubColors.primary : VanHubColors.disabled,
        foregroundColor: VanHubColors.onPrimary,
        elevation: isEnabled ? 2 : 0,
        shadowColor: VanHubColors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubSpacing.xs),
        ),
        padding: _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSecondaryButton(ThemeData theme, bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled ? VanHubColors.secondary : VanHubColors.disabled,
        foregroundColor: VanHubColors.onSecondary,
        elevation: isEnabled ? 1 : 0,
        shadowColor: VanHubColors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubSpacing.xs),
        ),
        padding: _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlinedButton(ThemeData theme, bool isEnabled) {
    return OutlinedButton(
      onPressed: isEnabled ? onPressed : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: isEnabled ? VanHubColors.primary : VanHubColors.disabled,
        side: BorderSide(
          color: isEnabled ? VanHubColors.primary : VanHubColors.disabled,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubSpacing.xs),
        ),
        padding: _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton(ThemeData theme, bool isEnabled) {
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: isEnabled ? VanHubColors.primary : VanHubColors.disabled,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubSpacing.xs),
        ),
        padding: _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildDangerButton(ThemeData theme, bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled ? VanHubColors.error : VanHubColors.disabled,
        foregroundColor: VanHubColors.onError,
        elevation: isEnabled ? 2 : 0,
        shadowColor: VanHubColors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubSpacing.xs),
        ),
        padding: _getButtonPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            variant == VanHubButtonVariant.outlined || variant == VanHubButtonVariant.text
                ? VanHubColors.primary
                : VanHubColors.onPrimary,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: VanHubSpacing.xs),
          Text(
            text,
            style: _getTextStyle(),
          ),
        ],
      );
    }

    return Text(
      text,
      style: _getTextStyle(),
    );
  }

  double _getButtonHeight() {
    switch (size) {
      case VanHubButtonSize.small:
        return 32;
      case VanHubButtonSize.medium:
        return 40;
      case VanHubButtonSize.large:
        return 48;
    }
  }

  EdgeInsets _getButtonPadding() {
    switch (size) {
      case VanHubButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.sm,
          vertical: VanHubSpacing.xs,
        );
      case VanHubButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.md,
          vertical: VanHubSpacing.sm,
        );
      case VanHubButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: VanHubSpacing.lg,
          vertical: VanHubSpacing.md,
        );
    }
  }

  double _getIconSize() {
    switch (size) {
      case VanHubButtonSize.small:
        return 16;
      case VanHubButtonSize.medium:
        return 18;
      case VanHubButtonSize.large:
        return 20;
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case VanHubButtonSize.small:
        return VanHubTypography.bodySmall;
      case VanHubButtonSize.medium:
        return VanHubTypography.bodyMedium;
      case VanHubButtonSize.large:
        return VanHubTypography.bodyLarge;
    }
  }
}

/// 按钮变体枚举
enum VanHubButtonVariant {
  primary,
  secondary,
  outlined,
  text,
  danger,
}

/// 按钮尺寸枚举
enum VanHubButtonSize {
  small,
  medium,
  large,
}