import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/di/injection_container.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/create_bom_item_request.dart';
import '../../domain/entities/update_bom_item_request.dart';
import '../../domain/usecases/create_bom_item_usecase.dart';
import '../../domain/usecases/add_material_to_bom_usecase.dart';

/// BOM控制器 - 使用StateNotifier模式
class BomController extends StateNotifier<AsyncValue<void>> {
  final Ref ref;

  BomController(this.ref) : super(const AsyncValue.data(null));

  /// 创建BOM项目
  Future<Either<Failure, BomItem>> createBomItem(
    String projectId,
    CreateBomItemRequest request,
  ) async {
    state = const AsyncValue.loading();

    try {
      final result = await ref.read(createBomItemUseCaseProvider).call(
        CreateBomItemParams(
          projectId: projectId,
          request: request,
        ),
      );

      result.fold(
        (failure) => state = AsyncValue.error(failure, StackTrace.current),
        (bomItem) => state = const AsyncValue.data(null),
      );

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return Left(ServerFailure(message: '创建BOM项目失败: $e'));
    }
  }

  /// 更新BOM项目
  Future<Either<Failure, void>> updateBomItem(
    UpdateBomItemRequest request,
  ) async {
    state = const AsyncValue.loading();

    try {
      // 首先获取现有的BOM项目
      final getBomResult = await ref.read(bomRepositoryProvider).getBomItemById(request.id);
      
      return getBomResult.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
          return Left(failure);
        },
        (existingBomItem) async {
          // 使用请求数据更新BOM项目
          final updatedBomItem = existingBomItem.copyWith(
            materialName: request.name,
            description: request.description,
            quantity: request.quantity,
            unitPrice: request.unitPrice,
            category: request.category,
            status: request.status,
            brand: request.brand,
            model: request.model,
            specifications: request.specifications,
            supplier: request.supplier,
            supplierUrl: request.supplierUrl,
            imageUrl: request.imageUrl,
            notes: request.notes,
          );

          final result = await ref.read(bomRepositoryProvider).updateBomItem(updatedBomItem);

          result.fold(
            (failure) => state = AsyncValue.error(failure, StackTrace.current),
            (_) => state = const AsyncValue.data(null),
          );

          return result;
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return Left(ServerFailure(message: '更新BOM项目失败: $e'));
    }
  }

  /// 删除BOM项目
  Future<Either<Failure, void>> deleteBomItem(String bomItemId) async {
    state = const AsyncValue.loading();

    try {
      final result = await ref.read(bomRepositoryProvider).deleteBomItem(bomItemId);

      result.fold(
        (failure) => state = AsyncValue.error(failure, StackTrace.current),
        (_) => state = const AsyncValue.data(null),
      );

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return Left(ServerFailure(message: '删除BOM项目失败: $e'));
    }
  }

  /// 更新BOM项目状态
  Future<Either<Failure, void>> updateBomItemStatus(
    String bomItemId,
    BomItemStatus status,
  ) async {
    try {
      final result = await ref.read(bomRepositoryProvider).updateBomItemStatus(
        bomItemId,
        status,
      );
      return result;
    } catch (e) {
      return Left(ServerFailure(message: '更新BOM项目状态失败: $e'));
    }
  }

  /// 添加材料到BOM
  Future<Either<Failure, void>> addMaterialToBom({
    required String projectId,
    required String materialId,
    required int quantity,
    String? notes,
  }) async {
    state = const AsyncValue.loading();

    try {
      final result = await ref.read(addMaterialToBomUseCaseProvider).call(
        AddMaterialToBomParams(
          projectId: projectId,
          materialId: materialId,
          quantity: quantity,
          notes: notes,
        ),
      );

      result.fold(
        (failure) => state = AsyncValue.error(failure, StackTrace.current),
        (_) => state = const AsyncValue.data(null),
      );

      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return Left(ServerFailure(message: '添加材料到BOM失败: $e'));
    }
  }

  /// 保存BOM项目到材料库
  Future<Either<Failure, void>> saveBomItemToMaterialLibrary(String bomItemId) async {
    try {
      final result = await ref.read(bomRepositoryProvider).saveBomItemToMaterialLibrary(bomItemId);
      return result;
    } catch (e) {
      return Left(ServerFailure(message: '保存BOM项目到材料库失败: $e'));
    }
  }
}