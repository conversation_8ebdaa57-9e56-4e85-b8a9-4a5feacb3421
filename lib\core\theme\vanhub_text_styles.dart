// 兼容性文件 - 重新导出新的设计系统
import 'package:flutter/material.dart';
import '../design_system/vanhub_design_system.dart';

class VanHubTextStyles {
  // 标题样式
  static TextStyle get headlineLarge => TextStyle(
    fontSize: VanHubDesignSystem.fontSize4xl,
    fontWeight: VanHubDesignSystem.fontWeightBold,
    color: VanHubDesignSystem.neutralGray900,
  );
  
  static TextStyle get headlineMedium => TextStyle(
    fontSize: VanHubDesignSystem.fontSize3xl,
    fontWeight: VanHubDesignSystem.fontWeightSemiBold,
    color: VanHubDesignSystem.neutralGray900,
  );
  
  static TextStyle get headlineSmall => TextStyle(
    fontSize: VanHubDesignSystem.fontSize2xl,
    fontWeight: VanHubDesignSystem.fontWeightSemiBold,
    color: VanHubDesignSystem.neutralGray900,
  );
  
  // 正文样式
  static TextStyle get bodyLarge => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeLg,
    fontWeight: VanHubDesignSystem.fontWeightRegular,
    color: VanHubDesignSystem.neutralGray800,
  );
  
  static TextStyle get bodyMedium => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeBase,
    fontWeight: VanHubDesignSystem.fontWeightRegular,
    color: VanHubDesignSystem.neutralGray800,
  );
  
  static TextStyle get bodySmall => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeSm,
    fontWeight: VanHubDesignSystem.fontWeightRegular,
    color: VanHubDesignSystem.neutralGray700,
  );
  
  // 标签样式
  static TextStyle get labelLarge => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeBase,
    fontWeight: VanHubDesignSystem.fontWeightMedium,
    color: VanHubDesignSystem.neutralGray900,
  );
  
  static TextStyle get labelMedium => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeSm,
    fontWeight: VanHubDesignSystem.fontWeightMedium,
    color: VanHubDesignSystem.neutralGray800,
  );
  
  static TextStyle get labelSmall => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeXs,
    fontWeight: VanHubDesignSystem.fontWeightMedium,
    color: VanHubDesignSystem.neutralGray700,
  );
  
  // 兼容性样式 - 为了向后兼容旧代码
  static TextStyle get subtitle1 => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeLg,
    fontWeight: VanHubDesignSystem.fontWeightMedium,
    color: VanHubDesignSystem.neutralGray900,
  );

  static TextStyle get headline3 => TextStyle(
    fontSize: VanHubDesignSystem.fontSize2xl,
    fontWeight: VanHubDesignSystem.fontWeightBold,
    color: VanHubDesignSystem.neutralGray900,
  );

  static TextStyle get body1 => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeBase,
    fontWeight: VanHubDesignSystem.fontWeightRegular,
    color: VanHubDesignSystem.neutralGray800,
  );

  static TextStyle get caption => TextStyle(
    fontSize: VanHubDesignSystem.fontSizeXs,
    fontWeight: VanHubDesignSystem.fontWeightRegular,
    color: VanHubDesignSystem.neutralGray600,
  );

  // 字体权重常量
  static const weightLight = VanHubDesignSystem.fontWeightLight;
  static const weightRegular = VanHubDesignSystem.fontWeightRegular;
  static const weightMedium = VanHubDesignSystem.fontWeightMedium;
  static const weightSemiBold = VanHubDesignSystem.fontWeightSemiBold;
  static const weightBold = VanHubDesignSystem.fontWeightBold;
}