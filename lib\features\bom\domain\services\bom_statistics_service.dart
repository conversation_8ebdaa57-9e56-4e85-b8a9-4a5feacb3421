import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../entities/bom_statistics.dart';

/// BOM统计计算服务接口
abstract class BomStatisticsService {
  /// 计算BOM统计数据
  BomStatistics calculateStatistics(List<BomItem> bomItems);
  
  /// 计算成本趋势
  Map<String, double> calculateCostTrend(List<BomItem> bomItems);
  
  /// 查找逾期项目
  List<BomItem> findOverdueItems(List<BomItem> bomItems);
  
  /// 计算完成百分比
  double calculateCompletionPercentage(List<BomItem> bomItems);
  
  /// 计算项目预算分析
  Either<Failure, BomStatistics> calculateProjectBudgetAnalysis(
    List<BomItem> bomItems,
    double projectBudget,
  );
  
  /// 生成成本报告
  Either<Failure, Map<String, dynamic>> generateCostReport(List<BomItem> bomItems);
  
  /// 生成预算警告
  Either<Failure, List<String>> generateBudgetAlerts(
    List<BomItem> bomItems,
    double projectBudget,
  );
}

