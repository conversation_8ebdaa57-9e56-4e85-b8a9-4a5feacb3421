# 临时覆盖环境变量，使用官方源
$env:PUB_HOSTED_URL = $null
$env:FLUTTER_STORAGE_BASE_URL = $null

Write-Host "已临时设置为官方源，现在执行Flutter命令..." -ForegroundColor Green

# 执行用户指定的Flutter命令
$command = $args -join " "
if ([string]::IsNullOrEmpty($command)) {
    $command = "pub get"  # 默认命令
}

Write-Host "执行: flutter $command" -ForegroundColor Cyan
flutter $command

Write-Host "`n完成！" -ForegroundColor Green
Write-Host "注意：这只是临时修改，如果要永久修改，请在系统环境变量中删除以下变量：" -ForegroundColor Yellow
Write-Host "PUB_HOSTED_URL 和 FLUTTER_STORAGE_BASE_URL" -ForegroundColor Yellow