import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entities/material_review.dart';

part 'material_review_model.freezed.dart';
part 'material_review_model.g.dart';

/// 材料评价数据模型
/// 遵循Clean Architecture原则，负责数据传输和序列化
@freezed
class MaterialReviewModel with _$MaterialReviewModel {
  const factory MaterialReviewModel({
    required String id,
    @JsonKey(name: 'material_id') required String materialId,
    @JsonKey(name: 'user_id') required String userId,
    @<PERSON>son<PERSON><PERSON>(name: 'user_name') required String userName,
    @<PERSON>son<PERSON><PERSON>(name: 'user_avatar_url') String? userAvatarUrl,
    required String content,
    required double rating,
    @<PERSON>son<PERSON>ey(name: 'quality_rating') required double qualityRating,
    @JsonKey(name: 'value_rating') required double valueRating,
    @<PERSON>son<PERSON>ey(name: 'durability_rating') required double durabilityRating,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'installation_rating') required double installationRating,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'vehicle_type') String? vehicleType,
    @Json<PERSON>ey(name: 'system_type') String? systemType,
    @JsonKey(name: 'usage_duration') String? usageDuration,
    @Default([]) List<String> pros,
    @Default([]) List<String> cons,
    @Default([]) List<String> tips,
    @JsonKey(name: 'is_verified_purchase') @Default(false) bool isVerifiedPurchase,
    @JsonKey(name: 'purchase_date') DateTime? purchaseDate,
    @JsonKey(name: 'purchase_proof_url') String? purchaseProofUrl,
    @JsonKey(name: 'image_urls') @Default([]) List<String> imageUrls,
    @JsonKey(name: 'video_urls') @Default([]) List<String> videoUrls,
    @JsonKey(name: 'liked_by_user_ids') @Default([]) List<String> likedByUserIds,
    @JsonKey(name: 'helpful_user_ids') @Default([]) List<String> helpfulUserIds,
    @JsonKey(name: 'helpful_count') @Default(0) int helpfulCount,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _MaterialReviewModel;

  const MaterialReviewModel._();

  factory MaterialReviewModel.fromJson(Map<String, dynamic> json) =>
      _$MaterialReviewModelFromJson(json);

  /// 从Domain实体转换为Data模型
  factory MaterialReviewModel.fromEntity(MaterialReview entity) {
    return MaterialReviewModel(
      id: entity.id,
      materialId: entity.materialId,
      userId: entity.userId,
      userName: entity.userName,
      userAvatarUrl: entity.userAvatarUrl,
      content: entity.content,
      rating: entity.rating,
      qualityRating: entity.qualityRating,
      valueRating: entity.valueRating,
      durabilityRating: entity.durabilityRating,
      installationRating: entity.installationRating,
      vehicleType: entity.vehicleType,
      systemType: entity.systemType,
      usageDuration: entity.usageDuration,
      pros: entity.pros,
      cons: entity.cons,
      tips: entity.tips,
      isVerifiedPurchase: entity.isVerifiedPurchase,
      purchaseDate: entity.purchaseDate,
      purchaseProofUrl: entity.purchaseProofUrl,
      imageUrls: entity.imageUrls,
      videoUrls: entity.videoUrls,
      likedByUserIds: entity.likedByUserIds,
      helpfulUserIds: entity.helpfulUserIds,
      helpfulCount: entity.helpfulCount,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// 转换为Domain实体
  MaterialReview toEntity() {
    return MaterialReview(
      id: id,
      materialId: materialId,
      userId: userId,
      userName: userName,
      userAvatarUrl: userAvatarUrl,
      content: content,
      rating: rating,
      qualityRating: qualityRating,
      valueRating: valueRating,
      durabilityRating: durabilityRating,
      installationRating: installationRating,
      vehicleType: vehicleType,
      systemType: systemType,
      usageDuration: usageDuration,
      pros: pros,
      cons: cons,
      tips: tips,
      isVerifiedPurchase: isVerifiedPurchase,
      purchaseDate: purchaseDate,
      purchaseProofUrl: purchaseProofUrl,
      imageUrls: imageUrls,
      videoUrls: videoUrls,
      likedByUserIds: likedByUserIds,
      helpfulUserIds: helpfulUserIds,
      helpfulCount: helpfulCount,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// 转换为数据库插入格式
  Map<String, dynamic> toInsertMap() {
    return {
      'id': id,
      'material_id': materialId,
      'user_id': userId,
      'user_name': userName,
      'user_avatar_url': userAvatarUrl,
      'content': content,
      'rating': rating,
      'quality_rating': qualityRating,
      'value_rating': valueRating,
      'durability_rating': durabilityRating,
      'installation_rating': installationRating,
      'vehicle_type': vehicleType,
      'system_type': systemType,
      'usage_duration': usageDuration,
      'pros': pros,
      'cons': cons,
      'tips': tips,
      'is_verified_purchase': isVerifiedPurchase,
      'purchase_date': purchaseDate?.toIso8601String(),
      'purchase_proof_url': purchaseProofUrl,
      'image_urls': imageUrls,
      'video_urls': videoUrls,
      'liked_by_user_ids': likedByUserIds,
      'helpful_user_ids': helpfulUserIds,
      'helpful_count': helpfulCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// 转换为数据库更新格式
  Map<String, dynamic> toUpdateMap() {
    return {
      'content': content,
      'rating': rating,
      'quality_rating': qualityRating,
      'value_rating': valueRating,
      'durability_rating': durabilityRating,
      'installation_rating': installationRating,
      'vehicle_type': vehicleType,
      'system_type': systemType,
      'usage_duration': usageDuration,
      'pros': pros,
      'cons': cons,
      'tips': tips,
      'is_verified_purchase': isVerifiedPurchase,
      'purchase_date': purchaseDate?.toIso8601String(),
      'purchase_proof_url': purchaseProofUrl,
      'image_urls': imageUrls,
      'video_urls': videoUrls,
      'updated_at': DateTime.now().toIso8601String(),
    };
  }
}

/// 材料评价摘要数据模型
@freezed
class MaterialReviewSummaryModel with _$MaterialReviewSummaryModel {
  const factory MaterialReviewSummaryModel({
    @JsonKey(name: 'material_id') required String materialId,
    @JsonKey(name: 'total_reviews') required int totalReviews,
    @JsonKey(name: 'average_rating') required double averageRating,
    @JsonKey(name: 'quality_average') required double qualityAverage,
    @JsonKey(name: 'value_average') required double valueAverage,
    @JsonKey(name: 'durability_average') required double durabilityAverage,
    @JsonKey(name: 'installation_average') required double installationAverage,
    @JsonKey(name: 'rating_distribution') required Map<String, int> ratingDistribution,
    @JsonKey(name: 'verified_purchase_count') required int verifiedPurchaseCount,
    @JsonKey(name: 'top_pros') @Default([]) List<String> topPros,
    @JsonKey(name: 'top_cons') @Default([]) List<String> topCons,
    @JsonKey(name: 'common_tips') @Default([]) List<String> commonTips,
    @JsonKey(name: 'latest_review_date') DateTime? latestReviewDate,
    @JsonKey(name: 'recommendation_score') required double recommendationScore,
  }) = _MaterialReviewSummaryModel;

  const MaterialReviewSummaryModel._();

  factory MaterialReviewSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$MaterialReviewSummaryModelFromJson(json);

  /// 转换为Domain实体
  MaterialReviewSummary toEntity() {
    // 转换评分分布的键从String到int
    final Map<int, int> intRatingDistribution = {};
    ratingDistribution.forEach((key, value) {
      final intKey = int.tryParse(key) ?? 0;
      intRatingDistribution[intKey] = value;
    });

    return MaterialReviewSummary(
      materialId: materialId,
      totalReviews: totalReviews,
      averageRating: averageRating,
      qualityAverage: qualityAverage,
      valueAverage: valueAverage,
      durabilityAverage: durabilityAverage,
      installationAverage: installationAverage,
      ratingDistribution: intRatingDistribution,
      verifiedPurchaseCount: verifiedPurchaseCount,
      topPros: topPros,
      topCons: topCons,
      commonTips: commonTips,
      latestReviewDate: latestReviewDate,
      recommendationScore: recommendationScore,
    );
  }
}
