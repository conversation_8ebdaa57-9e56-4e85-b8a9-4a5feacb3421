import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'interaction_event.freezed.dart';
part 'interaction_event.g.dart';

/// 交互事件实体
/// 代表用户与改装日志的交互行为
@freezed
class InteractionEvent with _$InteractionEvent {
  const factory InteractionEvent({
    /// 交互事件唯一标识
    required String id,
    
    /// 交互类型
    required InteractionType interactionType,
    
    /// 目标对象ID（可能是日志条目ID或时间轴事件ID）
    required String targetId,
    
    /// 目标对象类型
    required String targetType, // 'log_entry', 'timeline_event', 'comment'
    
    /// 交互用户ID
    required String userId,
    
    /// 交互用户名称
    String? userName,
    
    /// 交互内容（评论内容、分享文本等）
    String? content,
    
    /// 交互时间
    required DateTime interactionTime,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 交互状态
    @Default(LogStatus.published) LogStatus status,
    
    /// 关联的媒体ID列表（用于评论中的图片等）
    @Default([]) List<String> mediaIds,
    
    /// 父级交互ID（用于回复评论）
    String? parentInteractionId,
    
    /// 提及的用户ID列表
    @Default([]) List<String> mentionedUserIds,
    
    /// 标签列表
    @Default([]) List<String> tags,
    
    /// 是否为置顶
    @Default(false) bool isPinned,
    
    /// 是否为精华
    @Default(false) bool isFeatured,
    
    /// 点赞用户ID列表
    @Default([]) List<String> likedByUserIds,
    
    /// 回复数量
    @Default(0) int replyCount,
    
    /// 评分（用于建议类交互）
    double? rating,
    
    /// 元数据
    Map<String, dynamic>? metadata,
  }) = _InteractionEvent;

  factory InteractionEvent.fromJson(Map<String, dynamic> json) =>
      _$InteractionEventFromJson(json);
}

/// InteractionEvent扩展方法
extension InteractionEventX on InteractionEvent {
  /// 获取交互类型显示文本
  String get interactionTypeDisplayText => interactionType.displayName;
  
  /// 获取状态显示文本
  String get statusDisplayText => status.displayName;
  
  /// 获取交互内容摘要
  String get contentSummary {
    if (content == null || content!.isEmpty) {
      return interactionTypeDisplayText;
    }
    
    if (content!.length <= 100) {
      return content!;
    } else {
      return '${content!.substring(0, 97)}...';
    }
  }
  
  /// 是否为评论类型
  bool get isComment => interactionType == InteractionType.comment;
  
  /// 是否为点赞类型
  bool get isLike => interactionType == InteractionType.like;
  
  /// 是否为收藏类型
  bool get isBookmark => interactionType == InteractionType.bookmark;
  
  /// 是否为分享类型
  bool get isShare => interactionType == InteractionType.share;
  
  /// 是否为引用类型
  bool get isReference => interactionType == InteractionType.reference;
  
  /// 是否为提问类型
  bool get isQuestion => interactionType == InteractionType.question;
  
  /// 是否为建议类型
  bool get isSuggestion => interactionType == InteractionType.suggestion;
  
  /// 是否为回复
  bool get isReply => parentInteractionId != null;
  
  /// 是否为顶级交互
  bool get isTopLevel => parentInteractionId == null;
  
  /// 获取交互图标
  String get interactionIcon {
    switch (interactionType) {
      case InteractionType.like:
        return '👍';
      case InteractionType.bookmark:
        return '⭐';
      case InteractionType.comment:
        return '💬';
      case InteractionType.share:
        return '🔗';
      case InteractionType.reference:
        return '📎';
      case InteractionType.question:
        return '❓';
      case InteractionType.suggestion:
        return '💡';
    }
  }
  
  /// 获取交互颜色
  String get interactionColor {
    switch (interactionType) {
      case InteractionType.like:
        return '#2196F3'; // 蓝色
      case InteractionType.bookmark:
        return '#FF9800'; // 橙色
      case InteractionType.comment:
        return '#4CAF50'; // 绿色
      case InteractionType.share:
        return '#9C27B0'; // 紫色
      case InteractionType.reference:
        return '#607D8B'; // 蓝灰色
      case InteractionType.question:
        return '#F44336'; // 红色
      case InteractionType.suggestion:
        return '#FF5722'; // 深橙色
    }
  }
  
  /// 获取时间描述
  String get timeDescription {
    final now = DateTime.now();
    final difference = now.difference(interactionTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  /// 获取关联媒体数量
  int get mediaCount => mediaIds.length;
  
  /// 获取点赞数量
  int get likeCount => likedByUserIds.length;
  
  /// 获取提及用户数量
  int get mentionCount => mentionedUserIds.length;
  
  /// 获取评分显示文本
  String get ratingDisplayText {
    if (rating == null) return '';
    return '${rating!.toStringAsFixed(1)}星';
  }
  
  /// 是否有评分
  bool get hasRating => rating != null;
  
  /// 是否有内容
  bool get hasContent => content != null && content!.isNotEmpty;
  
  /// 是否有媒体
  bool get hasMedia => mediaIds.isNotEmpty;
  
  /// 是否有提及
  bool get hasMentions => mentionedUserIds.isNotEmpty;
  
  /// 是否有回复
  bool get hasReplies => replyCount > 0;
  
  /// 获取目标类型显示文本
  String get targetTypeDisplayText {
    switch (targetType) {
      case 'log_entry':
        return '日志条目';
      case 'timeline_event':
        return '时间轴事件';
      case 'comment':
        return '评论';
      default:
        return '未知';
    }
  }
}
