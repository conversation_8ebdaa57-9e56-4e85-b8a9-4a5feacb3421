# BOM树形结构功能文档

## 📋 功能概述

BOM树形结构功能是VanHub改装宝的核心功能之一，它将BOM（Bill of Materials）项目按材料分类组织成直观的树形结构，为用户提供清晰的项目材料管理视图。

## ✅ 实现状态

**功能状态**: ✅ **已完成并测试通过**
- **BOM保存功能**: ✅ 已修复并正常工作
- **树形结构展示**: ✅ 已实现并测试通过
- **分类管理**: ✅ 支持11个专业分类
- **用户交互**: ✅ 完整的CRUD操作支持
- **数据统计**: ✅ 实时分类统计和汇总

## 🎯 核心特性

### 1. 树形结构展示 ✅
- **分类组织**: 按材料分类自动组织BOM项目（已实现）
- **多级展示**: 支持分类-材料的两级树形结构（已实现）
- **展开/折叠**: 可自由展开或折叠分类节点（已实现）
- **视觉层次**: 清晰的视觉层次和缩进显示（已实现）
- **分类图标**: 每个分类有专属的Material Design图标
- **颜色编码**: 11种分类颜色，便于快速识别

### 2. 实时统计信息 ✅
- **总项目统计**: 显示BOM项目总数量（已实现）
- **价值统计**: 计算并显示总价值（已实现）
- **完成率**: 实时显示项目完成百分比（已实现）
- **分类统计**: 每个分类的详细统计信息（已实现）
- **分类汇总**: 分类级别显示"X项 总价值: ¥XXX"
- **动态更新**: 数据变更时自动更新统计信息

### 3. 智能搜索功能
- **全文搜索**: 支持材料名称、品牌、型号搜索
- **实时高亮**: 搜索结果实时高亮显示
- **模糊匹配**: 智能模糊匹配算法
- **搜索历史**: 保存搜索历史记录

### 4. 多维度过滤
- **状态过滤**: 按BOM项目状态过滤显示
- **价格过滤**: 按价格范围过滤项目
- **分类过滤**: 按材料分类过滤显示
- **组合过滤**: 支持多个过滤条件组合

### 5. 拖拽操作
- **节点拖拽**: 支持树节点拖拽重组
- **可视化反馈**: 拖拽过程中的视觉反馈
- **智能检测**: 自动检测有效的拖拽目标
- **操作撤销**: 支持拖拽操作的撤销

## 🏗️ 技术架构

### Clean Architecture 实现

#### Domain Layer (领域层)
```
lib/features/bom/domain/
├── entities/
│   ├── bom_tree_state.dart          # BOM树状态实体
│   └── bom_item.dart                # BOM项目实体
├── services/
│   ├── bom_tree_service.dart        # BOM树服务接口
│   └── bom_tree_service_impl.dart   # BOM树服务实现
└── repositories/
    └── bom_repository.dart          # BOM数据仓库接口
```

#### Data Layer (数据层)
```
lib/features/bom/data/
├── repositories/
│   └── bom_repository_impl.dart     # BOM数据仓库实现
├── datasources/
│   └── bom_remote_datasource.dart   # BOM远程数据源
└── models/
    └── bom_item_model.dart          # BOM项目数据模型
```

#### Presentation Layer (表现层)
```
lib/features/bom/presentation/
├── providers/
│   └── bom_tree_provider.dart       # BOM树状态管理
├── widgets/
│   └── bom_tree_widget.dart         # BOM树UI组件
└── pages/
    └── bom_tree_page.dart           # BOM树页面
```

### 核心服务类

#### BomTreeService
```dart
abstract class BomTreeService {
  // 构建树形结构
  Either<Failure, List<TreeNode>> buildTreeFromBomItems(
    List<BomItem> bomItems, {
    TreeViewSettings? settings,
  });
  
  // 搜索功能
  Either<Failure, List<String>> searchInBomTree(
    List<TreeNode> tree,
    String query, {
    TreeViewSettings? settings,
  });
  
  // 统计计算
  Either<Failure, Map<String, BomCategoryStats>> calculateBomCategoryStatistics(
    List<BomItem> bomItems,
  );
  
  // 过滤功能
  Either<Failure, List<TreeNode>> filterBomTreeByStatus(
    List<TreeNode> tree,
    List<BomItemStatus> statuses,
  );
  
  // 排序功能
  Either<Failure, List<TreeNode>> sortBomTree(
    List<TreeNode> tree,
    BomTreeSortMode sortMode,
  );
}
```

#### BomTreeState
```dart
@freezed
class BomTreeState with _$BomTreeState {
  const factory BomTreeState({
    @Default([]) List<TreeNode> tree,
    @Default(false) bool isLoading,
    String? errorMessage,
    @Default('') String searchQuery,
    @Default([]) List<String> searchResults,
    @Default(false) bool isSearchActive,
    String? selectedNodeId,
    TreeViewSettings? settings,
    @Default({}) Map<String, BomCategoryStats> categoryStats,
    DateTime? lastUpdated,
    @Default(0) int totalBomItemCount,
    @Default([]) List<BomItemStatus> filteredStatuses,
    PriceRange? priceRange,
    BomTreeSortMode? sortMode,
    @Default(true) bool showStatistics,
    @Default(false) bool isDragEnabled,
    String? draggingNodeId,
    String? dropTargetNodeId,
  }) = _BomTreeState;
}
```

## 🎨 用户界面设计

### 工具栏组件
- **标题区域**: 显示"BOM树形结构"标题和图标
- **功能按钮**: 统计切换、拖拽开关、刷新按钮
- **状态指示**: 加载状态和错误提示

### 统计面板
- **总体统计卡片**: 总项目、总价值、完成率、分类数
- **状态分布**: 各状态的项目数量和占比
- **可折叠设计**: 支持显示/隐藏统计面板

### 树形结构区域
- **分类节点**: 显示分类名称、项目数量、统计信息
- **材料节点**: 显示材料详情、价格、状态等
- **交互操作**: 点击选择、展开折叠、右键菜单

## 📊 数据流程

### 初始化流程
1. **数据获取**: 从Supabase获取BOM项目数据
2. **树构建**: 调用BomTreeService构建树形结构
3. **统计计算**: 计算各分类的统计信息
4. **UI渲染**: 渲染树形结构和统计面板

### 搜索流程
1. **输入监听**: 监听搜索框输入变化
2. **搜索执行**: 调用搜索服务进行匹配
3. **结果高亮**: 高亮显示匹配的节点
4. **状态更新**: 更新搜索状态和结果

### 过滤流程
1. **条件设置**: 用户设置过滤条件
2. **过滤执行**: 调用过滤服务筛选节点
3. **树重构**: 重新构建过滤后的树结构
4. **统计更新**: 更新过滤后的统计信息

## 🧪 测试策略

### 单元测试
- **服务层测试**: BomTreeService各方法的单元测试
- **状态管理测试**: BomTreeNotifier状态变更测试
- **工具函数测试**: 辅助函数和算法测试

### 集成测试
- **数据流测试**: 端到端数据流程测试
- **UI交互测试**: 用户交互场景测试
- **性能测试**: 大数据量下的性能测试

### E2E测试 (Playwright)
- **页面加载测试**: 验证页面正常加载
- **搜索功能测试**: 验证搜索功能正常工作
- **过滤功能测试**: 验证各种过滤条件
- **拖拽操作测试**: 验证拖拽功能
- **响应式测试**: 验证不同屏幕尺寸下的表现

## 🎨 支持的分类系统

### 11个专业分类 ✅
| 分类名称 | 图标 | 颜色 | 描述 |
|---------|------|------|------|
| 电力系统 | ⚡ electrical_services | 琥珀色 | 电池、逆变器、LED灯等 |
| 水路系统 | 💧 water_drop | 蓝色 | 水泵、水箱、净水器等 |
| 内饰改装 | 🪑 chair | 棕色 | 座椅、内饰板、地板等 |
| 外观改装 | 🚗 directions_car | 红色 | 车身贴纸、保险杠等 |
| 储物方案 | 📦 storage | 绿色 | 储物柜、挂钩、收纳盒等 |
| 床铺设计 | 🛏️ bed | 紫色 | 床垫、床架、床品等 |
| 厨房改装 | 🍳 kitchen | 橙色 | 炉具、冰箱、厨具等 |
| 卫浴改装 | 🚿 bathroom | 青色 | 马桶、洗手盆、淋浴等 |
| 安全设备 | 🛡️ security | 深橙色 | 灭火器、报警器、锁具等 |
| 通讯设备 | 📶 wifi | 靛蓝色 | 路由器、天线、对讲机等 |
| 娱乐设备 | 📺 tv | 粉色 | 电视、音响、游戏设备等 |

### 自动分类识别 ✅
- **智能匹配**: 根据材料名称和描述自动识别分类
- **未分类处理**: 无法识别的材料自动归入"未分类"
- **手动调整**: 用户可以手动修改材料分类

## 🚀 性能优化

### 渲染优化 ✅
- **ExpansionTile优化**: 使用Flutter原生ExpansionTile实现流畅动画
- **按需渲染**: 只渲染可见的分类节点
- **缓存策略**: 缓存分类统计结果

### 数据优化 ✅
- **分组算法**: 高效的Map分组算法
- **排序优化**: 分类名称字母排序
- **统计计算**: 实时计算分类统计信息

### 内存优化 ✅
- **Widget复用**: 复用BomItemCardWidget
- **状态管理**: 使用Riverpod进行高效状态管理
- **垃圾回收**: 及时释放不需要的对象

## 🔮 未来规划

### 短期计划
- **导出功能**: 支持树形结构数据导出
- **打印功能**: 支持树形结构打印
- **快捷键**: 添加键盘快捷键支持
- **主题定制**: 支持树形结构主题定制

### 长期计划
- **AI智能**: AI辅助的树形结构优化建议
- **协作功能**: 多人协作编辑树形结构
- **版本控制**: 树形结构变更历史记录
- **插件系统**: 支持第三方插件扩展

## 📚 相关文档

- [Clean Architecture验证规则](../hooks/clean_architecture_validator.md)
- [Either类型使用规范](../hooks/either_type_enforcer.md)
- [设计文档](design.md)
- [需求文档](requirements.md)
- [任务清单](tasks.md)
