# Either Type Enforcer Hook

## Hook Configuration
```yaml
name: "Either Type Enforcer"
description: "确保所有可能失败的操作使用Either<Failure, Success>类型"
trigger: "on_file_save"
file_patterns: ["lib/features/**/repositories/*.dart", "lib/features/**/usecases/*.dart"]
auto_execute: true
```

## Either Type Rules

### Repository Methods
所有Repository方法必须返回Either类型：
```dart
// ✅ 正确
Future<Either<Failure, User>> getUser(String id);
Future<Either<Failure, List<Project>>> getProjects();
Future<Either<Failure, void>> deleteProject(String id);

// ❌ 错误
Future<User> getUser(String id);
Future<List<Project>> getProjects();
Future<void> deleteProject(String id);
```

### UseCase Methods
所有UseCase的call方法必须返回Either类型：
```dart
// ✅ 正确
Future<Either<Failure, User>> call(LoginParams params);

// ❌ 错误
Future<User> call(LoginParams params);
```

### Exception Handling
必须将异常转换为Failure：
```dart
// ✅ 正确
try {
  final result = await dataSource.getData();
  return Right(result);
} catch (e) {
  return Left(ServerFailure(message: e.toString()));
}

// ❌ 错误
final result = await dataSource.getData();
return result;
```

## 执行动作
1. 扫描方法签名
2. 检查返回类型是否为Either
3. 提供自动修复建议
4. 生成标准的异常处理代码