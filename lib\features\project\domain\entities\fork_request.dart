import 'package:freezed_annotation/freezed_annotation.dart';

part 'fork_request.freezed.dart';
part 'fork_request.g.dart';

@freezed
class ForkRequest with _$ForkRequest {
  const factory ForkRequest({
    required String sourceProjectId,
    required String newProjectTitle,
    String? description,
    @Default(true) bool copyBomItems,
    @Default(true) bool copySystems,
    @Default(false) bool copyImages,
  }) = _ForkRequest;

  factory ForkRequest.fromJson(Map<String, dynamic> json) =>
      _$ForkRequestFromJson(json);
}