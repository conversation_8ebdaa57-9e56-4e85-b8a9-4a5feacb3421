import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/project.dart' as domain;
import '../../domain/entities/fork_request.dart';
import '../../domain/entities/create_project_request.dart';
import '../../domain/usecases/create_project_usecase.dart';
import '../../domain/usecases/get_projects_usecase.dart';
import '../../domain/usecases/fork_project_usecase.dart';
import '../../../../core/di/injection_container.dart';

part 'project_provider.g.dart';

// Use Cases Providers
@riverpod
CreateProjectUseCase createProjectUseCase(Ref ref) {
  return CreateProjectUseCase(ref.read(projectRepositoryProvider));
}

@riverpod
GetProjectsUseCase getProjectsUseCase(Ref ref) {
  return GetProjectsUseCase(ref.read(projectRepositoryProvider));
}

@riverpod
ForkProjectUseCase forkProjectUseCase(Ref ref) {
  return ForkProjectUseCase(ref.read(projectRepositoryProvider));
}

// Project List Providers
@riverpod
Future<List<domain.Project>> userProjects(Ref ref, String userId) async {
  final result = await ref.read(getProjectsUseCaseProvider).getUserProjects(userId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (projects) => projects,
  );
}

@riverpod
Future<List<domain.Project>> publicProjects(Ref ref, {int limit = 20, int offset = 0}) async {
  final result = await ref.read(getProjectsUseCaseProvider).getPublicProjects(
    limit: limit,
    offset: offset,
  );
  return result.fold(
    (failure) => throw Exception(failure.message),
    (projects) => projects,
  );
}

@riverpod
Future<domain.Project> projectDetail(Ref ref, String projectId) async {
  final result = await ref.read(projectRepositoryProvider).getProjectById(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (project) => project,
  );
}

@riverpod
Future<bool> projectLikeStatus(Ref ref, String projectId) async {
  final result = await ref.read(projectRepositoryProvider).getLikeStatus(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (isLiked) => isLiked,
  );
}

// Project Controller Notifier
@riverpod
class ProjectController extends _$ProjectController {
  @override
  FutureOr<void> build() {
    // 初始化状态
  }

  Future<Either<Failure, domain.Project>> createProject(CreateProjectRequest request) async {
    // 避免重复设置loading状态
    if (!state.isLoading) {
      state = const AsyncLoading();
    }

    try {
      final result = await ref.read(createProjectUseCaseProvider).call(request);

      // 使用更安全的状态管理，避免竞争条件
      return result.fold(
        (failure) {
          // 设置错误状态
          state = AsyncError(failure, StackTrace.current);
          return Left(failure);
        },
        (project) {
          // 设置成功状态
          state = const AsyncData(null);

          // 异步刷新Provider，不等待完成，避免阻塞主流程
          _scheduleProviderRefresh();

          return Right(project);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '创建项目失败: $e'));
    }
  }

  /// 安全地刷新相关Provider
  void _scheduleProviderRefresh() {
    // 使用microtask避免阻塞主线程
    Future.microtask(() async {
      try {
        // 添加小延迟确保状态稳定
        await Future.delayed(const Duration(milliseconds: 50));

        ref.invalidate(publicProjectsProvider);
        // 如果有用户项目Provider，也刷新它
        final userId = ref.read(currentUserIdProvider);
        if (userId != null) {
          ref.invalidate(userProjectsProvider(userId));
        }
      } catch (e) {
        // 静默处理刷新错误，不影响主流程
        debugPrint('Provider刷新警告: $e');
      }
    });
  }

  Future<Either<Failure, void>> likeProject(String projectId) async {
    final result = await ref.read(projectRepositoryProvider).likeProject(projectId);
    
    result.fold(
      (failure) => null,
      (_) {
        // 刷新相关状态
        ref.invalidate(projectLikeStatusProvider(projectId));
        ref.invalidate(projectDetailProvider(projectId));
      },
    );
    
    return result;
  }

  Future<Either<Failure, void>> unlikeProject(String projectId) async {
    final result = await ref.read(projectRepositoryProvider).unlikeProject(projectId);
    
    result.fold(
      (failure) => null,
      (_) {
        // 刷新相关状态
        ref.invalidate(projectLikeStatusProvider(projectId));
        ref.invalidate(projectDetailProvider(projectId));
      },
    );
    
    return result;
  }

  Future<Either<Failure, ForkResult>> forkProject({
    required String sourceProjectId,
    required String newProjectTitle,
    String? description,
    bool copyBomItems = true,
    bool copySystems = true,
    bool copyImages = false,
  }) async {
    // 避免重复设置loading状态
    if (!state.isLoading) {
      state = const AsyncLoading();
    }

    // 获取当前用户ID
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = AsyncError(ValidationFailure(message: '用户未登录'), StackTrace.current);
      return Left(ValidationFailure(message: '用户未登录'));
    }
    
    final forkRequest = ForkRequest(
      sourceProjectId: sourceProjectId,
      newProjectTitle: newProjectTitle,
      description: description,
      copyBomItems: copyBomItems,
      copySystems: copySystems,
      copyImages: copyImages,
    );

    final params = ForkProjectParams(
      sourceProjectId: sourceProjectId,
      forkRequest: forkRequest,
    );

    try {
      final result = await ref.read(forkProjectUseCaseProvider).call(params);

      // 简化状态管理，避免在fold中重复设置状态
      result.fold(
        (failure) => state = AsyncError(failure, StackTrace.current),
        (project) => state = const AsyncData(null),
      );

      // 延迟刷新，避免状态竞争
      if (result.isRight()) {
        await Future.delayed(const Duration(milliseconds: 100));
        try {
          ref.invalidate(userProjectsProvider);
          ref.invalidate(publicProjectsProvider);
        } catch (e) {
          // 忽略Provider刷新错误，避免影响主流程
        }
      }

      return result.fold(
        (failure) => Left(failure),
        (project) {
          // 根据Clean Architecture原则，将Domain实体转换为适当的返回类型
          final forkResult = ForkResult(
            newProjectId: project.id,
            sourceProjectId: sourceProjectId,
            sourceProjectTitle: project.title,
            sourceAuthorId: project.authorId,
            sourceAuthorName: project.authorName ?? '未知用户',
            copiedSystems: 0, // TODO: 实现实际的复制统计
            copiedBomItems: 0, // TODO: 实现实际的复制统计
            copiedImages: 0, // TODO: 实现实际的复制统计
            forkedAt: DateTime.now(),
          );

          return Right(forkResult);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '复刻项目失败: $e'));
    }
  }

  Future<Either<Failure, void>> deleteProject(String projectId) async {
    final result = await ref.read(projectRepositoryProvider).deleteProject(projectId);
    
    result.fold(
      (failure) => null,
      (_) {
        // 刷新项目列表
        ref.invalidate(userProjectsProvider);
        ref.invalidate(publicProjectsProvider);
      },
    );
    
    return result;
  }
}