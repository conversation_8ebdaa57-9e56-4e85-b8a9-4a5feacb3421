import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/dashboard_data.dart';
import '../providers/dashboard_provider.dart';

/// 项目概览卡片组件
class ProjectOverviewCard extends ConsumerWidget {
  final String projectId;

  const ProjectOverviewCard({
    Key? key,
    required this.projectId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final overviewAsync = ref.watch(projectOverviewProvider(projectId));

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: overviewAsync.when(
          data: (overview) => _buildOverviewContent(context, overview),
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 8),
                Text('加载失败: $error'),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => ref.refresh(projectOverviewProvider(projectId)),
                  child: const Text('重试'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewContent(BuildContext context, ProjectOverview overview) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            const Icon(Icons.dashboard, color: Colors.blue, size: 28),
            const SizedBox(width: 12),
            const Text(
              '项目概览',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            _buildStatusChip(overview.status),
          ],
        ),
        const SizedBox(height: 24),

        // 主要指标行
        Row(
          children: [
            // 进度环形图
            Expanded(
              flex: 2,
              child: _buildProgressChart(overview),
            ),
            const SizedBox(width: 20),
            // 关键指标
            Expanded(
              flex: 3,
              child: _buildKeyMetrics(overview),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // 预算使用情况
        _buildBudgetProgress(overview),
      ],
    );
  }

  Widget _buildStatusChip(ProjectStatus status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case ProjectStatus.planning:
        color = Colors.orange;
        text = '计划中';
        icon = Icons.schedule;
        break;
      case ProjectStatus.inProgress:
        color = Colors.blue;
        text = '进行中';
        icon = Icons.play_arrow;
        break;
      case ProjectStatus.onHold:
        color = Colors.amber;
        text = '暂停';
        icon = Icons.pause;
        break;
      case ProjectStatus.completed:
        color = Colors.green;
        text = '已完成';
        icon = Icons.check_circle;
        break;
      case ProjectStatus.cancelled:
        color = Colors.red;
        text = '已取消';
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressChart(ProjectOverview overview) {
    return SizedBox(
      height: 120,
      child: Stack(
        alignment: Alignment.center,
        children: [
          PieChart(
            PieChartData(
              startDegreeOffset: -90,
              sectionsSpace: 2,
              centerSpaceRadius: 35,
              sections: [
                PieChartSectionData(
                  value: overview.overallProgress * 100,
                  color: Colors.blue,
                  radius: 15,
                  showTitle: false,
                ),
                PieChartSectionData(
                  value: (1 - overview.overallProgress) * 100,
                  color: Colors.grey.withOpacity(0.2),
                  radius: 15,
                  showTitle: false,
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${(overview.overallProgress * 100).toStringAsFixed(0)}%',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const Text(
                '总体进度',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeyMetrics(ProjectOverview overview) {
    return Column(
      children: [
        _buildMetricRow(
          icon: Icons.task_alt,
          label: '任务完成',
          value: '${overview.completedTasks}/${overview.totalTasks}',
          color: Colors.green,
        ),
        const SizedBox(height: 12),
        _buildMetricRow(
          icon: Icons.access_time,
          label: '总工时',
          value: '${(overview.totalTimeSpent / 60).toStringAsFixed(1)}h',
          color: Colors.orange,
        ),
        const SizedBox(height: 12),
        _buildMetricRow(
          icon: Icons.schedule,
          label: '预计剩余',
          value: '${(overview.estimatedTimeRemaining / 60).toStringAsFixed(1)}h',
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildMetricRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetProgress(ProjectOverview overview) {
    final budgetUsageRatio = overview.totalBudget > 0 
        ? overview.budgetUsed / overview.totalBudget 
        : 0.0;
    
    Color progressColor;
    if (budgetUsageRatio <= 0.7) {
      progressColor = Colors.green;
    } else if (budgetUsageRatio <= 0.9) {
      progressColor = Colors.orange;
    } else {
      progressColor = Colors.red;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '预算使用情况',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '¥${overview.budgetUsed.toStringAsFixed(0)} / ¥${overview.totalBudget.toStringAsFixed(0)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: budgetUsageRatio.clamp(0.0, 1.0),
          backgroundColor: Colors.grey.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
          minHeight: 8,
        ),
        const SizedBox(height: 4),
        Text(
          '${(budgetUsageRatio * 100).toStringAsFixed(1)}% 已使用',
          style: TextStyle(
            fontSize: 11,
            color: progressColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
