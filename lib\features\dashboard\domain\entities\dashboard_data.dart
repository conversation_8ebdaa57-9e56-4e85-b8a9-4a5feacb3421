import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_data.freezed.dart';
part 'dashboard_data.g.dart';

/// 仪表盘数据实体
@freezed
class DashboardData with _$DashboardData {
  const factory DashboardData({
    required String projectId,
    required ProjectOverview projectOverview,
    required CostAnalysis costAnalysis,
    required ProgressTracking progressTracking,
    required List<SmartInsight> smartInsights,
    @Default([]) List<RecentActivity> recentActivities,
    DateTime? lastUpdated,
  }) = _DashboardData;

  factory DashboardData.fromJson(Map<String, dynamic> json) =>
      _$DashboardDataFromJson(json);
}

/// 项目概览数据
@freezed
class ProjectOverview with _$ProjectOverview {
  const factory ProjectOverview({
    required double overallProgress, // 总体进度 0.0-1.0
    required double budgetUsed, // 已使用预算
    required double totalBudget, // 总预算
    required int totalTasks, // 总任务数
    required int completedTasks, // 已完成任务数
    required int totalTimeSpent, // 总耗时(分钟)
    required int estimatedTimeRemaining, // 预计剩余时间(分钟)
    required ProjectStatus status, // 项目状态
    DateTime? startDate,
    DateTime? expectedEndDate,
  }) = _ProjectOverview;

  factory ProjectOverview.fromJson(Map<String, dynamic> json) =>
      _$ProjectOverviewFromJson(json);
}

/// 成本分析数据
@freezed
class CostAnalysis with _$CostAnalysis {
  const factory CostAnalysis({
    required double totalCost, // 总成本
    required double plannedCost, // 计划成本
    required double actualCost, // 实际成本
    required List<CategoryCost> categoryCosts, // 分类成本
    required List<MonthlySpending> monthlySpending, // 月度支出
    required List<CostTrend> costTrends, // 成本趋势
    @Default([]) List<CostAlert> costAlerts, // 成本预警
  }) = _CostAnalysis;

  factory CostAnalysis.fromJson(Map<String, dynamic> json) =>
      _$CostAnalysisFromJson(json);
}

/// 进度跟踪数据
@freezed
class ProgressTracking with _$ProgressTracking {
  const factory ProgressTracking({
    required List<MilestoneProgress> milestones, // 里程碑进度
    required List<SystemProgress> systemProgress, // 系统进度
    required List<TimelineEvent> recentEvents, // 最近事件
    required WorkTimeAnalysis workTimeAnalysis, // 工时分析
  }) = _ProgressTracking;

  factory ProgressTracking.fromJson(Map<String, dynamic> json) =>
      _$ProgressTrackingFromJson(json);
}

/// 分类成本
@freezed
class CategoryCost with _$CategoryCost {
  const factory CategoryCost({
    required String categoryId,
    required String categoryName,
    required double amount,
    required double percentage,
    required String colorHex,
  }) = _CategoryCost;

  factory CategoryCost.fromJson(Map<String, dynamic> json) =>
      _$CategoryCostFromJson(json);
}

/// 月度支出
@freezed
class MonthlySpending with _$MonthlySpending {
  const factory MonthlySpending({
    required String month, // YYYY-MM
    required double amount,
    required double budgetAmount,
  }) = _MonthlySpending;

  factory MonthlySpending.fromJson(Map<String, dynamic> json) =>
      _$MonthlySpendingFromJson(json);
}

/// 成本趋势
@freezed
class CostTrend with _$CostTrend {
  const factory CostTrend({
    required DateTime date,
    required double cumulativeCost,
    required double plannedCost,
  }) = _CostTrend;

  factory CostTrend.fromJson(Map<String, dynamic> json) =>
      _$CostTrendFromJson(json);
}

/// 成本预警
@freezed
class CostAlert with _$CostAlert {
  const factory CostAlert({
    required String id,
    required String title,
    required String description,
    required AlertSeverity severity,
    required double threshold,
    required double currentValue,
    DateTime? createdAt,
  }) = _CostAlert;

  factory CostAlert.fromJson(Map<String, dynamic> json) =>
      _$CostAlertFromJson(json);
}

/// 里程碑进度
@freezed
class MilestoneProgress with _$MilestoneProgress {
  const factory MilestoneProgress({
    required String id,
    required String title,
    required double progress, // 0.0-1.0
    required MilestoneStatus status,
    DateTime? dueDate,
    DateTime? completedDate,
  }) = _MilestoneProgress;

  factory MilestoneProgress.fromJson(Map<String, dynamic> json) =>
      _$MilestoneProgressFromJson(json);
}

/// 系统进度
@freezed
class SystemProgress with _$SystemProgress {
  const factory SystemProgress({
    required String systemId,
    required String systemName,
    required double progress, // 0.0-1.0
    required int totalTasks,
    required int completedTasks,
    required String colorHex,
  }) = _SystemProgress;

  factory SystemProgress.fromJson(Map<String, dynamic> json) =>
      _$SystemProgressFromJson(json);
}

/// 时间轴事件
@freezed
class TimelineEvent with _$TimelineEvent {
  const factory TimelineEvent({
    required String id,
    required String title,
    required String description,
    required DateTime date,
    required EventType type,
    String? systemId,
    String? systemName,
  }) = _TimelineEvent;

  factory TimelineEvent.fromJson(Map<String, dynamic> json) =>
      _$TimelineEventFromJson(json);
}

/// 工时分析
@freezed
class WorkTimeAnalysis with _$WorkTimeAnalysis {
  const factory WorkTimeAnalysis({
    required int totalHours,
    required int thisWeekHours,
    required int thisMonthHours,
    required double averageHoursPerDay,
    required List<DailyWorkTime> dailyWorkTime,
  }) = _WorkTimeAnalysis;

  factory WorkTimeAnalysis.fromJson(Map<String, dynamic> json) =>
      _$WorkTimeAnalysisFromJson(json);
}

/// 每日工时
@freezed
class DailyWorkTime with _$DailyWorkTime {
  const factory DailyWorkTime({
    required DateTime date,
    required int minutes,
  }) = _DailyWorkTime;

  factory DailyWorkTime.fromJson(Map<String, dynamic> json) =>
      _$DailyWorkTimeFromJson(json);
}

/// 智能洞察
@freezed
class SmartInsight with _$SmartInsight {
  const factory SmartInsight({
    required String id,
    required String title,
    required String description,
    required InsightType type,
    required InsightPriority priority,
    String? actionText,
    String? actionRoute,
    DateTime? createdAt,
  }) = _SmartInsight;

  factory SmartInsight.fromJson(Map<String, dynamic> json) =>
      _$SmartInsightFromJson(json);
}

/// 最近活动
@freezed
class RecentActivity with _$RecentActivity {
  const factory RecentActivity({
    required String id,
    required String title,
    required String description,
    required DateTime timestamp,
    required ActivityType type,
    String? userId,
    String? userName,
  }) = _RecentActivity;

  factory RecentActivity.fromJson(Map<String, dynamic> json) =>
      _$RecentActivityFromJson(json);
}

/// 枚举定义
enum ProjectStatus {
  planning,
  inProgress,
  onHold,
  completed,
  cancelled,
}

enum AlertSeverity {
  low,
  medium,
  high,
  critical,
}

enum MilestoneStatus {
  planned,
  inProgress,
  completed,
  overdue,
}

enum EventType {
  milestone,
  task,
  note,
  media,
  checkpoint,
}

enum InsightType {
  cost,
  progress,
  time,
  quality,
  recommendation,
}

enum InsightPriority {
  low,
  medium,
  high,
}

enum ActivityType {
  logCreated,
  bomUpdated,
  milestoneCompleted,
  materialAdded,
  costAlert,
}
