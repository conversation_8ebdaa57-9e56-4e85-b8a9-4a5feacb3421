# VanHub 组件使用指南

> 详细的组件使用说明和最佳实践

## 📋 目录

- [响应式工具](#响应式工具)
- [材料库组件](#材料库组件)
- [搜索和筛选](#搜索和筛选)
- [表单组件](#表单组件)
- [导航组件](#导航组件)
- [最佳实践](#最佳实践)

## 🔧 响应式工具

### VanHubResponsiveUtils

核心响应式工具类，提供断点判断和响应式值获取。

#### 基础用法

```dart
// 获取当前断点
final breakpoint = VanHubResponsiveUtils.getBreakpoint(context);

// 设备类型判断
if (VanHubResponsiveUtils.isMobile(context)) {
  // 移动端逻辑
}

// 响应式值获取
final padding = VanHubResponsiveUtils.getValue<double>(
  context,
  xs: 8,
  sm: 12,
  md: 16,
  lg: 20,
  xl: 24,
  defaultValue: 16,
);
```

#### 扩展方法使用

```dart
// 使用扩展方法更简洁
final isMobile = context.isMobile;
final isTablet = context.isTablet;
final isDesktop = context.isDesktop;

// 响应式值获取
final fontSize = context.responsiveValue(
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  defaultValue: 16,
);

// 简化设备类型值获取
final columns = context.simpleResponsiveValue(
  mobile: 1,
  tablet: 2,
  desktop: 4,
);
```

#### 响应式构建器

```dart
VanHubResponsiveBuilder(
  mobile: MobileLayout(),
  tablet: TabletLayout(),
  desktop: DesktopLayout(),
)

// 或使用构建器函数
VanHubResponsiveBuilder(
  builder: (context, breakpoint) {
    switch (breakpoint) {
      case VanHubBreakpoint.xs:
      case VanHubBreakpoint.sm:
        return MobileLayout();
      case VanHubBreakpoint.md:
        return TabletLayout();
      case VanHubBreakpoint.lg:
      case VanHubBreakpoint.xl:
        return DesktopLayout();
    }
  },
)
```

## 📚 材料库组件

### MaterialLibraryPageUnified

统一的材料库页面组件，支持登录用户和游客模式。

#### 基础用法

```dart
MaterialLibraryPageUnified(
  userId: currentUser?.id, // null 表示游客模式
)
```

#### 特性

- **响应式布局**: 自动适配不同设备
- **智能筛选**: 多维度筛选系统
- **搜索功能**: 实时搜索和历史记录
- **用户体验一致性**: 登录和游客模式体验一致

### MaterialCardUnifiedWidget

统一的材料卡片组件，支持网格和列表两种布局。

#### 基础用法

```dart
MaterialCardUnifiedWidget(
  material: materialData,
  isGuestMode: isGuest,
  isListView: false, // true 为列表视图
  showActions: true,
  onTap: () => _showMaterialDetails(material),
  onEdit: () => _editMaterial(material),
  onDelete: () => _deleteMaterial(material),
  onAddToBom: () => _addToBom(material),
  onFavorite: () => _toggleFavorite(material),
  onShare: () => _shareMaterial(material),
)
```

#### 响应式特性

- **自适应布局**: 根据设备自动调整卡片大小
- **智能内容**: 移动端隐藏次要信息
- **触摸优化**: 移动端优化的触摸目标

### MaterialEmptyStateWidget

材料库空状态组件，提供友好的空状态提示。

#### 基础用法

```dart
MaterialEmptyStateWidget(
  isGuestMode: isGuest,
  isSearchResult: hasSearchQuery,
  searchQuery: currentSearchQuery,
  onAddMaterial: _showCreateDialog,
  onLogin: _showLoginDialog,
  onClearSearch: _clearSearch,
)
```

#### 特性

- **智能提示**: 根据用户状态显示不同提示
- **操作引导**: 提供明确的下一步操作
- **特色展示**: 展示平台核心功能

## 🔍 搜索和筛选

### MaterialSearchBarWidget

高级搜索栏组件，支持实时搜索、历史记录和智能建议。

#### 基础用法

```dart
MaterialSearchBarWidget(
  onSearchChanged: (query) {
    setState(() {
      searchQuery = query;
    });
  },
  onFilterTap: () {
    setState(() {
      isFilterExpanded = !isFilterExpanded;
    });
  },
  hintText: '搜索材料名称、品牌、型号...',
)
```

#### 高级功能

- **防抖搜索**: 300ms 防抖，避免频繁请求
- **搜索历史**: 自动保存和管理搜索历史
- **智能建议**: 基于历史和热门搜索的建议
- **响应式设计**: 不同设备显示不同提示文本

### MaterialFilterWidget

多维度筛选组件，支持分类、价格、品牌、状态筛选。

#### 基础用法

```dart
MaterialFilterWidget(
  selectedCategory: selectedCategory,
  onCategoryChanged: (category) {
    setState(() {
      selectedCategory = category;
    });
  },
  selectedBrands: selectedBrands,
  onBrandsChanged: (brands) {
    setState(() {
      selectedBrands = brands;
    });
  },
  selectedStatuses: selectedStatuses,
  onStatusesChanged: (statuses) {
    setState(() {
      selectedStatuses = statuses;
    });
  },
  minPrice: minPrice,
  maxPrice: maxPrice,
  onPriceRangeChanged: (min, max) {
    setState(() {
      minPrice = min;
      maxPrice = max;
    });
  },
  totalCount: totalMaterialCount,
  filteredCount: filteredMaterialCount,
)
```

#### 筛选维度

- **分类筛选**: 11 个专业分类
- **价格筛选**: 范围滑块选择
- **品牌筛选**: 多选品牌筛选
- **状态筛选**: 材料状态筛选
- **结果统计**: 实时显示筛选结果

### SmartFilterSuggestionsWidget

智能筛选建议组件，提供预设筛选组合和快速筛选。

#### 基础用法

```dart
SmartFilterSuggestionsWidget(
  onApplyFilter: (filters) {
    // 应用筛选条件
    _applyFilters(filters);
  },
  currentFilters: currentFilterState,
)
```

#### 智能建议

- **快速筛选**: 价格、状态、品牌快速筛选
- **推荐组合**: 6 个场景化筛选组合
- **历史记录**: 筛选历史功能预留
- **智能描述**: 自动生成筛选条件描述

## 📝 表单组件

### CreateMaterialDialogWidget

创建材料对话框组件，支持完整的材料信息录入。

#### 基础用法

```dart
showDialog(
  context: context,
  builder: (context) => CreateMaterialDialogWidget(
    initialCategory: selectedCategory,
    initialName: materialName,
  ),
).then((result) {
  if (result == true) {
    // 材料创建成功
    _refreshMaterialList();
  }
});
```

#### 表单功能

- **分类选择**: 下拉选择材料分类
- **基础信息**: 名称、品牌、型号录入
- **价格信息**: 数字输入和验证
- **描述信息**: 多行文本输入
- **表单验证**: 必填字段验证

## 🧭 导航组件

### 响应式导航

根据设备类型显示不同的导航模式：

#### 移动端导航

```dart
// 底部导航栏
BottomNavigationBar(
  currentIndex: currentIndex,
  onTap: (index) => _onNavigationTap(index),
  items: [
    BottomNavigationBarItem(
      icon: Icon(Icons.home),
      label: '首页',
    ),
    // 更多导航项...
  ],
)
```

#### 桌面端导航

```dart
// 侧边导航栏
NavigationRail(
  selectedIndex: currentIndex,
  onDestinationSelected: (index) => _onNavigationTap(index),
  destinations: [
    NavigationRailDestination(
      icon: Icon(Icons.home),
      label: Text('首页'),
    ),
    // 更多导航项...
  ],
)
```

## 💡 最佳实践

### 响应式设计

1. **移动优先**: 先设计移动端，再扩展到大屏
2. **断点使用**: 合理使用 5 个断点，避免过度复杂
3. **内容优先**: 根据内容重要性决定显示策略

```dart
// 好的做法
Widget build(BuildContext context) {
  return Column(
    children: [
      // 核心内容始终显示
      CoreContent(),
      
      // 次要内容在大屏显示
      if (!context.isMobile) SecondaryContent(),
      
      // 响应式间距
      SizedBox(height: context.responsiveValue(
        xs: 8,
        md: 16,
        xl: 24,
        defaultValue: 16,
      )),
    ],
  );
}
```

### 性能优化

1. **懒加载**: 大列表使用懒加载
2. **缓存**: 合理使用缓存减少重建
3. **异步**: 耗时操作使用异步处理

```dart
// 懒加载示例
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return MaterialCardUnifiedWidget(
      material: items[index],
      // 其他属性...
    );
  },
)
```

### 用户体验

1. **加载状态**: 提供明确的加载反馈
2. **错误处理**: 友好的错误提示和恢复
3. **空状态**: 有意义的空状态引导

```dart
// 状态管理示例
Widget build(BuildContext context) {
  return materialsAsync.when(
    data: (materials) => MaterialGrid(materials: materials),
    loading: () => LoadingShimmer(),
    error: (error, stack) => ErrorState(
      error: error,
      onRetry: () => ref.refresh(materialsProvider),
    ),
  );
}
```

### 无障碍支持

1. **语义化**: 为所有交互元素添加语义化标签
2. **键盘导航**: 支持完整的键盘导航
3. **对比度**: 确保足够的颜色对比度

```dart
// 无障碍示例
Semantics(
  label: '添加材料到BOM',
  hint: '将当前材料添加到项目BOM清单',
  button: true,
  child: IconButton(
    icon: Icon(Icons.add_shopping_cart),
    onPressed: onAddToBom,
  ),
)
```

### 国际化

1. **文本外化**: 所有用户可见文本使用国际化
2. **布局适配**: 考虑不同语言的文本长度
3. **文化适配**: 考虑不同文化的使用习惯

```dart
// 国际化示例
Text(
  AppLocalizations.of(context).addMaterial,
  style: Theme.of(context).textTheme.labelLarge,
)
```

## 🔧 调试技巧

### 响应式调试

```dart
// 显示当前断点信息
Widget build(BuildContext context) {
  return Column(
    children: [
      if (kDebugMode)
        Container(
          color: Colors.red,
          child: Text(
            'Breakpoint: ${context.breakpoint.name}',
            style: TextStyle(color: Colors.white),
          ),
        ),
      // 实际内容...
    ],
  );
}
```

### 性能监控

```dart
// 性能监控
class PerformanceMonitor extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return PerformanceOverlay.allEnabled();
  }
}
```

---

**记住**: 始终以用户体验为中心，保持设计的一致性和可访问性。
