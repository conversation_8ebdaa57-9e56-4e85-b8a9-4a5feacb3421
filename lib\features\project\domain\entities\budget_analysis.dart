/// 预算分析实体
class BudgetAnalysis {
  final double totalBudget;
  final double actualCost;
  final double remainingBudget;
  final double budgetUtilization;
  final BudgetHealthStatus healthStatus;
  final List<BudgetCategory> categoryBreakdown;
  final DateTime lastUpdated;

  const BudgetAnalysis({
    required this.totalBudget,
    required this.actualCost,
    required this.remainingBudget,
    required this.budgetUtilization,
    required this.healthStatus,
    required this.categoryBreakdown,
    required this.lastUpdated,
  });

  /// 是否超预算
  bool get isOverBudget => actualCost > totalBudget;

  /// 超支金额
  double get overrunAmount => isOverBudget ? actualCost - totalBudget : 0.0;

  /// 预算使用率百分比
  double get utilizationPercentage => (actualCost / totalBudget) * 100;

  factory BudgetAnalysis.fromJson(Map<String, dynamic> json) {
    return BudgetAnalysis(
      totalBudget: (json['total_budget'] as num).toDouble(),
      actualCost: (json['actual_cost'] as num).toDouble(),
      remainingBudget: (json['remaining_budget'] as num).toDouble(),
      budgetUtilization: (json['budget_utilization'] as num).toDouble(),
      healthStatus: BudgetHealthStatus.values.firstWhere(
        (status) => status.name == json['health_status'],
        orElse: () => BudgetHealthStatus.healthy,
      ),
      categoryBreakdown: (json['category_breakdown'] as List<dynamic>)
          .map((item) => BudgetCategory.fromJson(item))
          .toList(),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_budget': totalBudget,
      'actual_cost': actualCost,
      'remaining_budget': remainingBudget,
      'budget_utilization': budgetUtilization,
      'health_status': healthStatus.name,
      'category_breakdown': categoryBreakdown.map((cat) => cat.toJson()).toList(),
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// 预算健康状态
enum BudgetHealthStatus {
  healthy,    // 健康
  warning,    // 警告
  critical,   // 危险
  overBudget, // 超预算
}

/// 预算分类
class BudgetCategory {
  final String name;
  final double budgetAmount;
  final double actualAmount;
  final double percentage;

  const BudgetCategory({
    required this.name,
    required this.budgetAmount,
    required this.actualAmount,
    required this.percentage,
  });

  factory BudgetCategory.fromJson(Map<String, dynamic> json) {
    return BudgetCategory(
      name: json['name'],
      budgetAmount: (json['budget_amount'] as num).toDouble(),
      actualAmount: (json['actual_amount'] as num).toDouble(),
      percentage: (json['percentage'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'budget_amount': budgetAmount,
      'actual_amount': actualAmount,
      'percentage': percentage,
    };
  }
}