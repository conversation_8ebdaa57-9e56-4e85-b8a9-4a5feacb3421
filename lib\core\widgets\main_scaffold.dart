import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/widgets/user_avatar_widget.dart';

class MainScaffold extends ConsumerStatefulWidget {
  final Widget child;

  const MainScaffold({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends ConsumerState<MainScaffold> {
  int _selectedIndex = 0;

  // 底部导航栏项目
  static const List<BottomNavigationBarItem> _bottomNavItems = [
    BottomNavigationBarItem(
      icon: Icon(Icons.explore),
      label: '发现',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.folder),
      label: '我的项目',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.inventory),
      label: '材料库',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: '设置',
    ),
  ];

  // 对应的路由路径
  static const List<String> _routes = [
    '/',
    '/my-projects',
    '/materials',
    '/settings',
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final location = GoRouterState.of(context).uri.toString();
    final index = _routes.indexOf(location);
    if (index != -1 && index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _onItemTapped(int index) {
    if (index != _selectedIndex) {
      context.go(_routes[index]);
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // 用户头像/登录按钮
          const UserAvatarWidget(),
          const SizedBox(width: 8),
        ],
      ),
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        items: _bottomNavItems,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  String _getAppBarTitle() {
    switch (_selectedIndex) {
      case 0:
        return 'VanHub改装宝';
      case 1:
        return '我的项目';
      case 2:
        return '材料库';
      case 3:
        return '设置';
      default:
        return 'VanHub改装宝';
    }
  }

  Widget? _buildFloatingActionButton() {
    // 根据当前页面显示不同的浮动按钮
    switch (_selectedIndex) {
      case 1: // 我的项目页面
        return FloatingActionButton(
          heroTag: "main_scaffold_project_fab",  // 修复Hero Widget冲突
          onPressed: () {
            // TODO: 创建新项目
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('创建项目功能即将上线'),
              ),
            );
          },
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          child: const Icon(Icons.add),
        );
      case 2: // 材料库页面
        return FloatingActionButton(
          heroTag: "main_scaffold_material_fab",  // 修复Hero Widget冲突
          onPressed: () {
            // TODO: 添加新材料
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('添加材料功能即将上线'),
              ),
            );
          },
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          child: const Icon(Icons.add),
        );
      default:
        return null;
    }
  }
}
