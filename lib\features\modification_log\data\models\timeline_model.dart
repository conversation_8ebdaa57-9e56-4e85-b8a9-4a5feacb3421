import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/timeline.dart';
import '../../domain/entities/enums.dart';
import 'milestone_model.dart';

part 'timeline_model.freezed.dart';
part 'timeline_model.g.dart';

/// 时间轴数据模型
@freezed
class TimelineModel with _$TimelineModel {
  const factory TimelineModel({
    @JsonKey(name: 'project_id') required String projectId,
    required List<TimelineItemModel> items,
    @JsonKey(name: 'start_date') required String startDate,
    @JsonKey(name: 'end_date') required String endDate,
    TimelineStatus? status,
    List<MilestoneModel>? milestones,
    String? createdBy,
    String? updatedBy,
    Map<String, dynamic>? metadata,
  }) = _TimelineModel;

  factory TimelineModel.fromJson(Map<String, dynamic> json) => _$TimelineModelFromJson(json);

}

/// TimelineModel扩展方法
extension TimelineModelExtension on TimelineModel {
  /// 转换为领域实体
  Timeline toEntity() {
    return Timeline(
      projectId: projectId,
      items: items.map((item) => item.toEntity()).toList(),
      startDate: DateTime.parse(startDate),
      endDate: DateTime.parse(endDate),
    );
  }
}

/// 从领域实体创建TimelineModel的工厂方法
extension TimelineEntityExtension on Timeline {
  TimelineModel toModel() {
    return TimelineModel(
      projectId: projectId,
      items: items.map((item) => item.toModel()).toList(),
      startDate: startDate.toIso8601String(),
      endDate: endDate.toIso8601String(),
    );
  }
}

/// 时间轴项目数据模型
@freezed
class TimelineItemModel with _$TimelineItemModel {
  const factory TimelineItemModel({
    required String id,
    required String title,
    required String date,
    @JsonKey(name: 'system_id') String? systemId,
    @JsonKey(name: 'system_name') String? systemName,
    String? description,
    String? type,
    String? status,
    @JsonKey(name: 'icon_name') String? iconName,
    @JsonKey(name: 'color_hex') String? colorHex,
  }) = _TimelineItemModel;

  factory TimelineItemModel.fromJson(Map<String, dynamic> json) => _$TimelineItemModelFromJson(json);

}
/// TimelineItemModel扩展方法
extension TimelineItemModelExtension on TimelineItemModel {
  /// 转换为领域实体
  TimelineItem toEntity() {
    if (type == 'log_entry') {
      return TimelineItem.logEntry(
        id: id,
        title: title,
        date: DateTime.parse(date),
        systemId: systemId ?? '',
        systemName: systemName,
        description: description,
        iconName: iconName,
        colorHex: colorHex,
      );
    } else {
      return TimelineItem.milestone(
        id: id,
        title: title,
        date: DateTime.parse(date),
        systemId: systemId,
        systemName: systemName,
        description: description,
        status: _parseMilestoneStatus(status ?? 'planned'),
        iconName: iconName,
        colorHex: colorHex,
      );
    }
  }
}

/// 从领域实体创建TimelineItemModel的扩展方法
extension TimelineItemEntityExtension on TimelineItem {
  TimelineItemModel toModel() {
    return map(
      logEntry: (logEntry) => TimelineItemModel(
        id: logEntry.id,
        title: logEntry.title,
        date: logEntry.date.toIso8601String(),
        systemId: logEntry.systemId,
        systemName: logEntry.systemName,
        description: logEntry.description,
        type: 'log_entry',
        iconName: logEntry.iconName,
        colorHex: logEntry.colorHex,
      ),
      milestone: (milestone) => TimelineItemModel(
        id: milestone.id,
        title: milestone.title,
        date: milestone.date.toIso8601String(),
        systemId: milestone.systemId,
        systemName: milestone.systemName,
        description: milestone.description,
        type: 'milestone',
        status: _milestoneStatusToString(milestone.status),
        iconName: milestone.iconName,
        colorHex: milestone.colorHex,
      ),
    );
  }
}

/// 解析里程碑状态的辅助函数
MilestoneStatus _parseMilestoneStatus(String status) {
  switch (status) {
    case 'in_progress':
      return MilestoneStatus.inProgress;
    case 'completed':
      return MilestoneStatus.completed;
    case 'delayed':
    case 'overdue':
      return MilestoneStatus.overdue;
    case 'cancelled':
      return MilestoneStatus.cancelled;
    case 'planned':
    default:
      return MilestoneStatus.planned;
  }
}

/// 里程碑状态转字符串的辅助函数
String _milestoneStatusToString(MilestoneStatus status) {
  switch (status) {
    case MilestoneStatus.inProgress:
      return 'in_progress';
    case MilestoneStatus.completed:
      return 'completed';
    case MilestoneStatus.overdue:
      return 'overdue';
    case MilestoneStatus.cancelled:
      return 'cancelled';
    case MilestoneStatus.planned:
      return 'planned';
  }
}