// 暂时禁用图表组件，等待修复
/*
import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 预算饼图组件
class BudgetPieChart extends StatelessWidget {
  final double totalBudget;
  final double spentAmount;
  final double remainingBudget;

  const BudgetPieChart({
    super.key,
    required this.totalBudget,
    required this.spentAmount,
    required this.remainingBudget,
  });

  @override
  Widget build(BuildContext context) {
    if (totalBudget <= 0) {
      return const Center(
        child: Text(
          '未设置预算',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    final spentPercentage = (spentAmount / totalBudget * 100).clamp(0, 100);
    final remainingPercentage = (remainingBudget / totalBudget * 100).clamp(0, 100);

    return Column(
      children: [
        // 饼图
        SizedBox(
          width: 200,
          height: 200,
          child: CustomPaint(
            painter: PieChartPainter(
              spentPercentage: spentPercentage,
              remainingPercentage: remainingPercentage,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // 图例
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildLegendItem(
              color: Colors.red,
              label: '已花费',
              value: '¥${spentAmount.toStringAsFixed(2)}',
              percentage: spentPercentage,
            ),
            _buildLegendItem(
              color: Colors.green,
              label: '剩余',
              value: '¥${remainingBudget.toStringAsFixed(2)}',
              percentage: remainingPercentage,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required String value,
    required double percentage,
  }) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}

/// 饼图绘制器
class PieChartPainter extends CustomPainter {
  final double spentPercentage;
  final double remainingPercentage;

  PieChartPainter({
    required this.spentPercentage,
    required this.remainingPercentage,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 10;

    // 已花费部分
    final spentPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;

    final spentAngle = (spentPercentage / 100) * 2 * math.pi;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      spentAngle,
      true,
      spentPaint,
    );

    // 剩余部分
    final remainingPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;

    final remainingAngle = (remainingPercentage / 100) * 2 * math.pi;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2 + spentAngle,
      remainingAngle,
      true,
      remainingPaint,
    );

    // 超支部分（如果有）
    if (spentPercentage + remainingPercentage < 100) {
      final overBudgetPaint = Paint()
        ..color = Colors.grey.shade300
        ..style = PaintingStyle.fill;

      final overBudgetAngle = ((100 - spentPercentage - remainingPercentage) / 100) * 2 * math.pi;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2 + spentAngle + remainingAngle,
        overBudgetAngle,
        true,
        overBudgetPaint,
      );
    }

    // 中心圆
    final centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.4, centerPaint);

    // 中心文字
    final textPainter = TextPainter(
      text: TextSpan(
        text: '总预算\n¥${(spentAmount + remainingBudget).toStringAsFixed(0)}',
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 进度条图表
class ProgressChart extends StatelessWidget {
  final double progress;
  final String title;
  final Color color;

  const ProgressChart({
    super.key,
    required this.progress,
    required this.title,
    this.color = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${progress.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress / 100,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
      ],
    );
  }
}

/// 分类统计条形图
class CategoryBarChart extends StatelessWidget {
  final Map<String, int> categoryData;
  final Map<String, Color> categoryColors;

  const CategoryBarChart({
    super.key,
    required this.categoryData,
    required this.categoryColors,
  });

  @override
  Widget build(BuildContext context) {
    if (categoryData.isEmpty) {
      return const Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    final maxValue = categoryData.values.reduce(math.max);
    final sortedEntries = categoryData.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Column(
      children: sortedEntries.map((entry) {
        final percentage = maxValue > 0 ? entry.value / maxValue : 0.0;
        final color = categoryColors[entry.key] ?? Colors.blue;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  entry.key,
                  style: const TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Stack(
                  children: [
                    Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    FractionallySizedBox(
                      widthFactor: percentage,
                      child: Container(
                        height: 20,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 30,
                child: Text(
                  '${entry.value}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
*/
