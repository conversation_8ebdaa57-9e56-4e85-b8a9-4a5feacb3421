import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_entry.dart';
import '../repositories/log_repository.dart';

/// 获取项目日志条目用例
class GetProjectLogsUseCase implements UseCase<List<LogEntry>, GetProjectLogsParams> {
  final LogRepository repository;

  GetProjectLogsUseCase(this.repository);

  @override
  Future<Either<Failure, List<LogEntry>>> call(GetProjectLogsParams params) async {
    return await repository.getProjectLogs(params.projectId);
  }
}

/// 获取项目日志条目参数
class GetProjectLogsParams {
  final String projectId;

  GetProjectLogsParams({required this.projectId});
}