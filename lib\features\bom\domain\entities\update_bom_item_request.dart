import 'package:freezed_annotation/freezed_annotation.dart';
import 'bom_item.dart';

part 'update_bom_item_request.freezed.dart';
part 'update_bom_item_request.g.dart';

/// 更新BOM项目请求实体
/// 严格遵循Clean Architecture原则，Domain层纯Dart实体
@freezed
class UpdateBomItemRequest with _$UpdateBomItemRequest {
  const factory UpdateBomItemRequest({
    required String id,
    required String name,
    required String description,
    required String category,
    required int quantity,
    required double unitPrice,
    required BomItemStatus status,
    String? brand,
    String? model,
    String? specifications,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? plannedDate,
    String? notes,
    Map<String, dynamic>? metadata,
  }) = _UpdateBomItemRequest;

  factory UpdateBomItemRequest.fromJson(Map<String, dynamic> json) => 
      _$UpdateBomItemRequestFromJson(json);
}
