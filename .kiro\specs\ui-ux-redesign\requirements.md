# Requirements Document

## Introduction

VanHub需要一个专门为房车改装项目设计的用户界面，核心目标是清晰、直观地展示改装项目的层次结构。用户需要能够轻松查看和管理车辆的不同改装系统（如电路系统、水路系统、储物系统等），每个系统下的具体物料，以及相关的费用信息。界面设计应该让用户一目了然地理解项目的整体结构和成本分布。

## Requirements

### Requirement 1: 改装项目层次结构展示

**User Story:** 作为房车改装爱好者，我希望能够清晰地查看改装项目的完整层次结构，以便理解车辆的各个改装系统和具体物料的组织关系。

#### Acceptance Criteria

1. WHEN 用户查看项目 THEN 系统 SHALL 以三级层次结构展示：车辆 → 改装系统 → 具体物料
2. WHEN 显示改装系统 THEN 系统 SHALL 包含常见分类：电路系统、水路系统、储物系统、床铺系统、厨房系统、卫浴系统、外观改装、底盘改装
3. WHEN 显示每个改装系统 THEN 系统 SHALL 显示该系统的总费用、物料数量、完成状态
4. WHEN 用户点击改装系统 THEN 系统 SHALL 展开显示该系统下的所有物料清单
5. WHEN 显示物料信息 THEN 系统 SHALL 包含：物料名称、规格型号、数量、单价、总价、购买状态、安装状态
6. WHEN 计算费用 THEN 系统 SHALL 自动汇总各系统费用到项目总费用

### Requirement 2: 项目概览卡片设计

**User Story:** 作为用户，我希望通过项目卡片快速了解改装项目的核心信息和进度，以便高效地浏览和管理多个项目。

#### Acceptance Criteria

1. WHEN 显示项目卡片 THEN 系统 SHALL 包含车辆主图、项目标题、车型信息（品牌+型号）
2. WHEN 显示项目卡片 THEN 系统 SHALL 显示关键统计数据：总费用、改装系统数量、物料总数
3. WHEN 显示项目卡片 THEN 系统 SHALL 包含整体进度条显示项目完成百分比
4. WHEN 显示项目卡片 THEN 系统 SHALL 显示费用分布概览（如：电路系统 ¥15,000、水路系统 ¥8,000）
5. WHEN 显示项目卡片 THEN 系统 SHALL 显示最后更新时间和最近活动
6. WHEN 用户点击项目卡片 THEN 系统 SHALL 导航到项目详情页面

### Requirement 3: 费用统计和可视化展示

**User Story:** 作为改装项目管理者，我希望能够清晰地查看项目的费用分布和统计信息，以便更好地控制预算和了解成本结构。

#### Acceptance Criteria

1. WHEN 显示项目费用 THEN 系统 SHALL 提供多维度费用统计：按改装系统分类、按物料类型分类、按时间分布
2. WHEN 显示费用图表 THEN 系统 SHALL 使用饼图展示各改装系统的费用占比
3. WHEN 显示费用图表 THEN 系统 SHALL 使用柱状图展示月度费用支出趋势
4. WHEN 用户点击图表区域 THEN 系统 SHALL 显示该部分的详细费用明细
5. WHEN 显示费用明细 THEN 系统 SHALL 包含：预算费用、实际费用、差额、完成度
6. WHEN 费用超出预算 THEN 系统 SHALL 显示警告提示和超支金额

### Requirement 4: 改装系统管理界面

**User Story:** 作为改装项目管理者，我希望能够方便地管理各个改装系统，包括添加、编辑、删除系统和调整系统优先级，以便灵活地组织项目结构。

#### Acceptance Criteria

1. WHEN 用户查看项目详情 THEN 系统 SHALL 提供改装系统管理入口
2. WHEN 显示改装系统列表 THEN 系统 SHALL 支持拖拽排序调整系统优先级
3. WHEN 用户添加新系统 THEN 系统 SHALL 提供预设系统模板：电路、水路、储物、床铺、厨房、卫浴、外观、底盘
4. WHEN 用户添加自定义系统 THEN 系统 SHALL 允许输入系统名称、描述、预算金额
5. WHEN 显示系统详情 THEN 系统 SHALL 显示系统进度、费用统计、物料数量、完成状态
6. WHEN 用户编辑系统 THEN 系统 SHALL 支持修改系统名称、描述、预算、状态

### Requirement 5: 物料管理和智能联动

**User Story:** 作为用户，我希望能够高效地管理改装物料，并且系统能够智能地从材料库中推荐和添加物料，以便减少重复输入和提高效率。

#### Acceptance Criteria

1. WHEN 用户在改装系统中添加物料 THEN 系统 SHALL 提供材料库搜索和推荐功能
2. WHEN 显示物料推荐 THEN 系统 SHALL 基于改装系统类型智能筛选相关材料
3. WHEN 用户选择材料库物料 THEN 系统 SHALL 自动填充物料基本信息：名称、规格、参考价格
4. WHEN 用户添加物料 THEN 系统 SHALL 支持设置：数量、实际价格、购买状态、安装状态、备注
5. WHEN 物料状态变更 THEN 系统 SHALL 自动更新改装系统和项目的整体进度
6. WHEN 显示物料列表 THEN 系统 SHALL 支持按状态、价格、类型进行筛选和排序

### Requirement 6: 项目详情页面核心设计

**User Story:** 作为用户，我希望在项目详情页面获得完整的项目信息和丰富的交互功能，以便深入了解项目并进行相关操作。

#### Acceptance Criteria

1. WHEN 用户进入项目详情页 THEN 系统 SHALL 显示项目主图作为背景的头部区域
2. WHEN 显示头部区域 THEN 系统 SHALL 包含返回按钮、项目标题、作者信息（头像+用户名）
3. WHEN 显示头部区域 THEN 系统 SHALL 提供核心操作按钮：复刻(Fork)、关注/收藏、分享
4. WHEN 用户点击作者信息 THEN 系统 SHALL 导航到作者的个人主页
5. WHEN 用户点击复刻按钮 THEN 系统 SHALL 启动项目复刻流程
6. WHEN 用户点击关注按钮 THEN 系统 SHALL 切换关注状态并更新UI反馈

### Requirement 7: 项目详情标签页系统

**User Story:** 作为用户，我希望通过标签页系统访问项目的不同维度信息，以便根据需要查看概览、BOM、时间轴或改装日志。

#### Acceptance Criteria

1. WHEN 显示项目详情页 THEN 系统 SHALL 在头部区域下方提供可滑动的标签页
2. WHEN 显示标签页 THEN 系统 SHALL 包含4个标签：概览(Overview)、物料清单(BOM)、时间轴(Timeline)、改装日志(Commits)
3. WHEN 用户点击或滑动标签 THEN 系统 SHALL 切换到对应内容页面
4. WHEN 显示概览标签 THEN 系统 SHALL 使用flutter_markdown渲染项目README内容
5. WHEN 显示BOM标签 THEN 系统 SHALL 在顶部显示fl_chart绘制的成本分布饼图
6. WHEN 显示BOM标签 THEN 系统 SHALL 在下方显示可滚动的物料列表包含名称、分类、状态、数量、单价
7. WHEN 显示时间轴标签 THEN 系统 SHALL 使用垂直时间轴视图展示项目里程碑和任务
8. WHEN 显示改装日志标签 THEN 系统 SHALL 按时间倒序显示所有Commit记录

### Requirement 8: 项目树视图功能

**User Story:** 作为用户，我希望能够以树状结构查看项目的组织架构，以便更好地理解项目的层次结构和组件关系。

#### Acceptance Criteria

1. WHEN 用户在概览或时间轴标签页 THEN 系统 SHALL 在右上角显示"视图切换"图标
2. WHEN 用户点击视图切换图标 THEN 系统 SHALL 将当前页面切换为可展开/折叠的树状列表视图
3. WHEN 显示树状视图 THEN 系统 SHALL 以项目结构（如"电路系统"、"水路系统"）为父节点
4. WHEN 显示树状视图 THEN 系统 SHALL 以下属的Commit或Task为子节点
5. WHEN 用户点击父节点 THEN 系统 SHALL 展开或折叠对应的子节点
6. WHEN 用户点击子节点 THEN 系统 SHALL 导航到对应的详细信息页面

### Requirement 9: 响应式设计和视觉一致性

**User Story:** 作为用户，我希望在不同设备上都能获得一致且优秀的视觉体验，以便在任何情况下都能舒适地使用应用。

#### Acceptance Criteria

1. WHEN 应用在不同屏幕尺寸设备上运行 THEN 系统 SHALL 自动适配布局保持最佳显示效果
2. WHEN 显示任何UI组件 THEN 系统 SHALL 遵循Material Design 3设计规范
3. WHEN 显示文本内容 THEN 系统 SHALL 使用一致的字体层级和颜色方案
4. WHEN 显示交互元素 THEN 系统 SHALL 提供清晰的视觉反馈和状态指示
5. WHEN 用户执行操作 THEN 系统 SHALL 提供适当的动画过渡效果
6. WHEN 显示加载状态 THEN 系统 SHALL 使用统一的加载指示器和骨架屏

### Requirement 10: 性能和用户体验优化

**User Story:** 作为用户，我希望应用响应迅速且操作流畅，以便获得高效愉悦的使用体验。

#### Acceptance Criteria

1. WHEN 用户切换页面或标签 THEN 系统 SHALL 在300ms内完成页面切换
2. WHEN 加载图片内容 THEN 系统 SHALL 实现懒加载和渐进式显示
3. WHEN 显示长列表 THEN 系统 SHALL 实现虚拟化滚动优化性能
4. WHEN 网络请求失败 THEN 系统 SHALL 显示友好的错误提示和重试选项
5. WHEN 用户离线 THEN 系统 SHALL 显示缓存内容并提示网络状态
6. WHEN 用户执行关键操作 THEN 系统 SHALL 提供操作确认和撤销机制