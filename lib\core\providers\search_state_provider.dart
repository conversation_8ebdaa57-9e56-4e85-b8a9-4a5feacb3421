import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_state_provider.g.dart';

/// 搜索状态数据类
class SearchState {
  final String query;
  final String selectedCategory;
  final Map<String, dynamic> filters;

  const SearchState({
    this.query = '',
    this.selectedCategory = '全部',
    this.filters = const {},
  });

  SearchState copyWith({
    String? query,
    String? selectedCategory,
    Map<String, dynamic>? filters,
  }) {
    return SearchState(
      query: query ?? this.query,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      filters: filters ?? this.filters,
    );
  }
}

/// 通用搜索状态管理Provider
@riverpod
class SearchStateNotifier extends _$SearchStateNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  void updateCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void updateFilter(String key, dynamic value) {
    final newFilters = Map<String, dynamic>.from(state.filters);
    newFilters[key] = value;
    state = state.copyWith(filters: newFilters);
  }

  void clearFilters() {
    state = state.copyWith(
      query: '',
      selectedCategory: '全部',
      filters: {},
    );
  }
}

/// 材料库搜索状态Provider
@riverpod
class MaterialSearchNotifier extends _$MaterialSearchNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  void updateCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void updatePriceRange(double? minPrice, double? maxPrice) {
    final newFilters = Map<String, dynamic>.from(state.filters);
    if (minPrice != null) newFilters['minPrice'] = minPrice;
    if (maxPrice != null) newFilters['maxPrice'] = maxPrice;
    state = state.copyWith(filters: newFilters);
  }

  void clearFilters() {
    state = state.copyWith(
      query: '',
      selectedCategory: '全部',
      filters: {},
    );
  }
}

/// BOM搜索状态Provider
@riverpod
class BomSearchNotifier extends _$BomSearchNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  void updateStatus(String status) {
    final newFilters = Map<String, dynamic>.from(state.filters);
    newFilters['status'] = status;
    state = state.copyWith(filters: newFilters);
  }

  void updateCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void clearFilters() {
    state = state.copyWith(
      query: '',
      selectedCategory: '全部',
      filters: {},
    );
  }
}

/// 项目搜索状态Provider
@riverpod
class ProjectSearchNotifier extends _$ProjectSearchNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  void updateCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void updateStatus(String status) {
    final newFilters = Map<String, dynamic>.from(state.filters);
    newFilters['status'] = status;
    state = state.copyWith(filters: newFilters);
  }

  void clearFilters() {
    state = state.copyWith(
      query: '',
      selectedCategory: '全部',
      filters: {},
    );
  }
}