import 'package:flutter/material.dart';
import '../error/ui_failure.dart';
import '../design_system/vanhub_design_system.dart';

/// 简化的错误展示组件
class ErrorDisplayWidget extends StatelessWidget {
  const ErrorDisplayWidget({
    super.key,
    required this.failure,
    this.onRetry,
    this.showDetails = false,
  });

  final UIFailure failure;
  final VoidCallback? onRetry;
  final bool showDetails;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubDesignSystem.spacing6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 错误图标
            Icon(
              _getErrorIcon(),
              size: 64,
              color: VanHubDesignSystem.semanticError,
            ),
            
            SizedBox(height: VanHubDesignSystem.spacing4),
            
            // 错误标题
            Text(
              _getErrorTitle(),
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSize2xl,
                fontWeight: VanHubDesignSystem.fontWeightSemiBold,
                color: VanHubDesignSystem.semanticError,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: VanHubDesignSystem.spacing2),
            
            // 错误描述
            Text(
              _getErrorMessage(),
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeBase,
                color: VanHubDesignSystem.neutralGray600,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (onRetry != null && failure.canRetry) ...[
              SizedBox(height: VanHubDesignSystem.spacing6),
              
              // 重试按钮
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('重试'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: VanHubDesignSystem.brandPrimary,
                  foregroundColor: VanHubDesignSystem.neutralWhite,
                ),
              ),
            ],
            
            if (showDetails) ...[
              SizedBox(height: VanHubDesignSystem.spacing4),
              
              // 错误详情
              Container(
                padding: EdgeInsets.all(VanHubDesignSystem.spacing3),
                decoration: BoxDecoration(
                  color: VanHubDesignSystem.neutralGray100,
                  borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
                ),
                child: Text(
                  failure.when(
                    network: (message, details, statusCode, code) => details ?? '',
                    validation: (field, message, details, code) => details ?? '',
                    permission: (action, message, details, code) => details ?? '',
                    storage: (operation, message, details, code) => details ?? '',
                    authentication: (message, details, code) => details ?? '',
                    business: (message, details, code) => details ?? '',
                    server: (message, details, statusCode, code) => details ?? '',
                    unknown: (message, details, code) => details ?? '',
                    timeout: (message, details, timeoutSeconds, code) => details ?? '',
                    format: (message, details, expectedFormat, code) => details ?? '',
                  ),
                  style: TextStyle(
                    fontSize: VanHubDesignSystem.fontSizeSm,
                    color: VanHubDesignSystem.neutralGray600,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getErrorIcon() {
    return failure.when(
      network: (message, details, statusCode, code) => Icons.wifi_off,
      validation: (field, message, details, code) => Icons.warning_amber,
      permission: (action, message, details, code) => Icons.lock_outline,
      storage: (operation, message, details, code) => Icons.storage,
      authentication: (message, details, code) => Icons.person_off,
      business: (message, details, code) => Icons.warning,
      server: (message, details, statusCode, code) => Icons.cloud_off,
      unknown: (message, details, code) => Icons.help_outline,
      timeout: (message, details, timeoutSeconds, code) => Icons.schedule,
      format: (message, details, expectedFormat, code) => Icons.data_usage,
    );
  }

  String _getErrorTitle() {
    return failure.userFriendlyTitle;
  }

  String _getErrorMessage() {
    return failure.userFriendlyMessage;
  }
}