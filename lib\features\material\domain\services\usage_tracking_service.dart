import 'package:fpdart/fpdart.dart';
import '../../../../core/error/failures.dart';
import '../entities/material_usage.dart';

/// Material usage tracking service interface
/// Follows Clean Architecture principles - Domain layer service
abstract class UsageTrackingService {
  /// Record material usage in a project
  Future<Either<Failure, void>> recordMaterialUsage({
    required String materialId,
    required String projectId,
    required String userId,
    required double quantity,
    required String unit,
    String? notes,
    Map<String, dynamic>? metadata,
  });

  /// Get material usage history for a specific material
  Future<Either<Failure, List<MaterialUsage>>> getMaterialUsageHistory(
    String materialId, {
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });

  /// Get material usage statistics for a user
  Future<Either<Failure, MaterialUsageStatistics>> getUserUsageStatistics(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get material usage statistics for a specific material
  Future<Either<Failure, MaterialUsageStatistics>> getMaterialUsageStatistics(
    String materialId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get trending materials based on usage
  Future<Either<Failure, List<TrendingMaterial>>> getTrendingMaterials({
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
    String? category,
  });

  /// Get material usage recommendations based on user history
  Future<Either<Failure, List<MaterialRecommendation>>> getUsageBasedRecommendations(
    String userId, {
    String? projectType,
    String? category,
    int limit = 10,
  });

  /// Get materials frequently used together
  Future<Either<Failure, List<MaterialCombination>>> getFrequentlyUsedTogether(
    String materialId, {
    int limit = 5,
    double minConfidence = 0.3,
  });

  /// Update material usage record
  Future<Either<Failure, void>> updateMaterialUsage({
    required String usageId,
    double? quantity,
    String? unit,
    String? notes,
    Map<String, dynamic>? metadata,
  });

  /// Delete material usage record
  Future<Either<Failure, void>> deleteMaterialUsage(String usageId);

  /// Get material usage analytics for dashboard
  Future<Either<Failure, UsageAnalytics>> getUsageAnalytics({
    String? userId,
    String? materialId,
    String? projectId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Export material usage data
  Future<Either<Failure, String>> exportUsageData({
    String? userId,
    String? materialId,
    DateTime? startDate,
    DateTime? endDate,
    String format = 'csv', // csv, json, excel
  });

  /// Get material usage patterns
  Future<Either<Failure, List<UsagePattern>>> getUsagePatterns(
    String userId, {
    String? category,
    int minOccurrences = 3,
  });

  /// Predict material needs based on usage history
  Future<Either<Failure, List<MaterialPrediction>>> predictMaterialNeeds(
    String userId, {
    String? projectType,
    DateTime? targetDate,
    int limit = 10,
  });

  /// Get material waste analysis
  Future<Either<Failure, WasteAnalysis>> getWasteAnalysis({
    String? userId,
    String? materialId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Record material feedback (quality, performance, etc.)
  Future<Either<Failure, void>> recordMaterialFeedback({
    required String materialId,
    required String userId,
    required String projectId,
    required double qualityRating,
    required double performanceRating,
    String? feedback,
    Map<String, dynamic>? metadata,
  });

  /// Get material performance metrics
  Future<Either<Failure, MaterialPerformanceMetrics>> getMaterialPerformanceMetrics(
    String materialId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Batch record material usage (for bulk operations)
  Future<Either<Failure, void>> batchRecordUsage(
    List<MaterialUsageRequest> usageRequests,
  );

  /// Get usage comparison between materials
  Future<Either<Failure, UsageComparison>> compareUsage(
    List<String> materialIds, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get seasonal usage patterns
  Future<Either<Failure, List<SeasonalPattern>>> getSeasonalPatterns({
    String? materialId,
    String? category,
    int years = 2,
  });

  /// Get cost-effectiveness analysis based on usage
  Future<Either<Failure, CostEffectivenessAnalysis>> getCostEffectivenessAnalysis(
    String materialId, {
    DateTime? startDate,
    DateTime? endDate,
  });
}

/// Material usage request for batch operations
class MaterialUsageRequest {
  final String materialId;
  final String projectId;
  final String userId;
  final double quantity;
  final String unit;
  final String? notes;
  final Map<String, dynamic>? metadata;
  final DateTime? usageDate;

  const MaterialUsageRequest({
    required this.materialId,
    required this.projectId,
    required this.userId,
    required this.quantity,
    required this.unit,
    this.notes,
    this.metadata,
    this.usageDate,
  });
}

/// Usage analytics data structure
class UsageAnalytics {
  final int totalUsages;
  final double totalQuantity;
  final int uniqueMaterials;
  final int uniqueProjects;
  final Map<String, int> categoryBreakdown;
  final Map<String, double> monthlyTrends;
  final List<TopUsedMaterial> topMaterials;
  final double averageUsagePerProject;
  final double totalCost;

  const UsageAnalytics({
    required this.totalUsages,
    required this.totalQuantity,
    required this.uniqueMaterials,
    required this.uniqueProjects,
    required this.categoryBreakdown,
    required this.monthlyTrends,
    required this.topMaterials,
    required this.averageUsagePerProject,
    required this.totalCost,
  });
}

/// Top used material data
class TopUsedMaterial {
  final String materialId;
  final String materialName;
  final int usageCount;
  final double totalQuantity;
  final String unit;
  final double totalCost;

  const TopUsedMaterial({
    required this.materialId,
    required this.materialName,
    required this.usageCount,
    required this.totalQuantity,
    required this.unit,
    required this.totalCost,
  });
}

/// Usage pattern data
class UsagePattern {
  final String patternId;
  final String name;
  final List<String> materialIds;
  final int occurrences;
  final double confidence;
  final String description;
  final Map<String, dynamic> metadata;

  const UsagePattern({
    required this.patternId,
    required this.name,
    required this.materialIds,
    required this.occurrences,
    required this.confidence,
    required this.description,
    required this.metadata,
  });
}

/// Material prediction data
class MaterialPrediction {
  final String materialId;
  final String materialName;
  final double predictedQuantity;
  final String unit;
  final double confidence;
  final String reasoning;
  final DateTime predictedDate;

  const MaterialPrediction({
    required this.materialId,
    required this.materialName,
    required this.predictedQuantity,
    required this.unit,
    required this.confidence,
    required this.reasoning,
    required this.predictedDate,
  });
}

/// Waste analysis data
class WasteAnalysis {
  final double totalWaste;
  final double wastePercentage;
  final Map<String, double> wasteByCategory;
  final List<WasteItem> topWasteItems;
  final double estimatedWasteCost;
  final List<WasteReduction> recommendations;

  const WasteAnalysis({
    required this.totalWaste,
    required this.wastePercentage,
    required this.wasteByCategory,
    required this.topWasteItems,
    required this.estimatedWasteCost,
    required this.recommendations,
  });
}

/// Waste item data
class WasteItem {
  final String materialId;
  final String materialName;
  final double wasteQuantity;
  final String unit;
  final double wasteCost;
  final String reason;

  const WasteItem({
    required this.materialId,
    required this.materialName,
    required this.wasteQuantity,
    required this.unit,
    required this.wasteCost,
    required this.reason,
  });
}

/// Waste reduction recommendation
class WasteReduction {
  final String recommendation;
  final double potentialSavings;
  final String category;
  final int priority;

  const WasteReduction({
    required this.recommendation,
    required this.potentialSavings,
    required this.category,
    required this.priority,
  });
}

/// Material performance metrics
class MaterialPerformanceMetrics {
  final String materialId;
  final double averageQualityRating;
  final double averagePerformanceRating;
  final int totalFeedbacks;
  final Map<String, double> ratingDistribution;
  final List<String> commonFeedback;
  final double recommendationScore;

  const MaterialPerformanceMetrics({
    required this.materialId,
    required this.averageQualityRating,
    required this.averagePerformanceRating,
    required this.totalFeedbacks,
    required this.ratingDistribution,
    required this.commonFeedback,
    required this.recommendationScore,
  });
}

/// Usage comparison data
class UsageComparison {
  final List<String> materialIds;
  final Map<String, UsageMetrics> metrics;
  final String winner;
  final String criteria;
  final List<ComparisonInsight> insights;

  const UsageComparison({
    required this.materialIds,
    required this.metrics,
    required this.winner,
    required this.criteria,
    required this.insights,
  });
}

/// Usage metrics for comparison
class UsageMetrics {
  final int usageCount;
  final double totalQuantity;
  final double averageQuantity;
  final double totalCost;
  final double averageCost;
  final double efficiency;

  const UsageMetrics({
    required this.usageCount,
    required this.totalQuantity,
    required this.averageQuantity,
    required this.totalCost,
    required this.averageCost,
    required this.efficiency,
  });
}

/// Comparison insight
class ComparisonInsight {
  final String insight;
  final String category;
  final double impact;

  const ComparisonInsight({
    required this.insight,
    required this.category,
    required this.impact,
  });
}

/// Seasonal pattern data
class SeasonalPattern {
  final String season;
  final String materialId;
  final String materialName;
  final double usageMultiplier;
  final String trend;
  final List<String> reasons;

  const SeasonalPattern({
    required this.season,
    required this.materialId,
    required this.materialName,
    required this.usageMultiplier,
    required this.trend,
    required this.reasons,
  });
}

/// Cost-effectiveness analysis
class CostEffectivenessAnalysis {
  final String materialId;
  final double costPerUnit;
  final double costPerProject;
  final double efficiency;
  final double roi;
  final String recommendation;
  final List<String> alternatives;

  const CostEffectivenessAnalysis({
    required this.materialId,
    required this.costPerUnit,
    required this.costPerProject,
    required this.efficiency,
    required this.roi,
    required this.recommendation,
    required this.alternatives,
  });
}
