import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/api/supabase_config.dart';
import '../core/utils/performance_monitor.dart';
import '../core/utils/error_handler.dart';
import '../core/utils/loading_manager.dart';
import '../core/errors/app_error.dart';

/// 材料库管理页面
class MaterialLibraryPage extends ConsumerStatefulWidget {
  const MaterialLibraryPage({super.key});

  @override
  ConsumerState<MaterialLibraryPage> createState() => _MaterialLibraryPageState();
}

class _MaterialLibraryPageState extends ConsumerState<MaterialLibraryPage> {
  List<Map<String, dynamic>> materials = [];
  List<Map<String, dynamic>> categories = [];
  bool isLoading = false;
  String? errorMessage;
  String? selectedCategoryId;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    await Future.wait([
      _loadCategories(),
      _loadMaterials(),
    ]);
  }

  /// 加载材料分类
  Future<void> _loadCategories() async {
    try {
      // 从material_library表中获取所有不同的分类
      final response = await SupabaseConfig.client
          .from('material_library')
          .select('category')
          .not('category', 'is', null);

      // 提取唯一分类
      final uniqueCategories = <String>{};
      for (final item in response) {
        final category = item['category'];
        if (category != null && category.toString().isNotEmpty) {
          uniqueCategories.add(category.toString());
        }
      }

      // 转换为分类列表格式
      final categoryList = uniqueCategories.map((name) => {
        'id': name,
        'name': name,
      }).toList();

      // 按名称排序
      categoryList.sort((a, b) => (a['name'] as String).compareTo(b['name'] as String));

      if (mounted) {
        setState(() {
          categories = categoryList;
        });
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('加载材料分类失败', details: e.toString()),
          customMessage: '无法加载材料分类',
        );
      }
    }
  }

  /// 加载材料列表
  Future<void> _loadMaterials() async {
    const loadingKey = 'load_materials';
    
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      globalLoadingManager.startLoading(loadingKey, message: '加载材料库...');
      
      final result = await PerformanceMonitor.monitor('load_materials', () async {
        // 使用material_library表而不是materials表
        var query = SupabaseConfig.client
            .from('material_library')
            .select('*');

        // 按分类筛选
        if (selectedCategoryId != null) {
          query = query.eq('category', selectedCategoryId!);
        }

        // 搜索筛选 - 暂时移除，后续可以添加
        // if (searchQuery.isNotEmpty) {
        //   query = query.or('item_name.ilike.%$searchQuery%,description.ilike.%$searchQuery%');
        // }

        return await query.order('created_at', ascending: false);
      });

      // 客户端搜索过滤
      List<Map<String, dynamic>> filteredResult = result.cast<Map<String, dynamic>>();
      final searchText = _searchController.text.trim();
      if (searchText.isNotEmpty) {
        filteredResult = filteredResult.where((material) {
          final name = material['name']?.toString().toLowerCase() ?? '';
          final description = material['description']?.toString().toLowerCase() ?? '';
          final searchLower = searchText.toLowerCase();
          return name.contains(searchLower) || description.contains(searchLower);
        }).toList();
      }
      
      globalLoadingManager.completeLoading(loadingKey, message: '材料库加载完成');
      
      if (mounted) {
        setState(() {
          materials = filteredResult;
          isLoading = false;
        });
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '材料库加载失败');
      
      if (mounted) {
        setState(() {
          errorMessage = e.toString();
          isLoading = false;
        });

        ErrorHandler.handle(
          context,
          AppError.network('加载材料库失败', details: e.toString()),
          customMessage: '无法加载材料库，请检查网络连接',
        );
      }
    }
  }

  /// 创建新材料
  Future<void> _createMaterial() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => MaterialCreateDialog(categories: categories),
    );

    if (result != null) {
      await _saveMaterial(result);
    }
  }

  /// 保存材料
  Future<void> _saveMaterial(Map<String, dynamic> materialData) async {
    const loadingKey = 'save_material';
    
    try {
      globalLoadingManager.startLoading(loadingKey, message: '保存材料...');
      
      await PerformanceMonitor.monitor('save_material', () async {
        await SupabaseConfig.client.from('materials').insert({
          ...materialData,
          'user_id': SupabaseConfig.client.auth.currentUser?.id,
          'created_at': DateTime.now().toIso8601String(),
        });
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: '材料保存成功');
      
      // 重新加载材料列表
      await _loadMaterials();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('材料添加成功！'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '材料保存失败');
      
      if (mounted) {
        ErrorHandler.handle(
          context,
          AppError.database('保存材料失败', details: e.toString()),
          customMessage: '无法保存材料，请重试',
        );
      }
    }
  }

  /// 删除材料
  Future<void> _deleteMaterial(String materialId, String materialName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除材料"$materialName"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      const loadingKey = 'delete_material';
      
      try {
        globalLoadingManager.startLoading(loadingKey, message: '删除材料...');
        
        await PerformanceMonitor.monitor('delete_material', () async {
          await SupabaseConfig.client
              .from('materials')
              .delete()
              .eq('id', materialId);
        });
        
        globalLoadingManager.completeLoading(loadingKey, message: '材料删除成功');
        
        // 重新加载材料列表
        await _loadMaterials();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.delete, color: Colors.white),
                  SizedBox(width: 8),
                  Text('材料删除成功！'),
                ],
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        globalLoadingManager.failLoading(loadingKey, message: '材料删除失败');
        
        if (mounted) {
          ErrorHandler.handle(
            context,
            AppError.database('删除材料失败', details: e.toString()),
            customMessage: '无法删除材料，请重试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('材料库'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMaterials,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(child: _buildBody()),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createMaterial,
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade50,
      child: Column(
        children: [
          // 搜索框
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索材料名称或描述...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _loadMaterials();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onSubmitted: (_) => _loadMaterials(),
          ),
          const SizedBox(height: 12),
          // 分类筛选
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('全部'),
                  selected: selectedCategoryId == null,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        selectedCategoryId = null;
                      });
                      _loadMaterials();
                    }
                  },
                ),
                const SizedBox(width: 8),
                ...categories.map((category) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category['name']),
                    selected: selectedCategoryId == category['id'],
                    onSelected: (selected) {
                      setState(() {
                        selectedCategoryId = selected ? category['id'] : null;
                      });
                      _loadMaterials();
                    },
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('加载材料库中...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMaterials,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (materials.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无材料',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              '点击右下角的 + 按钮添加第一个材料',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: materials.length,
      itemBuilder: (context, index) {
        final material = materials[index];
        return _buildMaterialCard(material);
      },
    );
  }

  Widget _buildMaterialCard(Map<String, dynamic> material) {
    final categoryName = material['material_categories']?['name'] ?? '未分类';
    final price = material['price'] ?? 0.0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    material['name'] ?? '未命名材料',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green),
                  ),
                  child: Text(
                    categoryName,
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            if (material['description'] != null) ...[
              const SizedBox(height: 8),
              Text(
                material['description'],
                style: const TextStyle(color: Colors.grey),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.attach_money, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                Text(
                  '¥${price.toStringAsFixed(2)}/${material['unit'] ?? '个'}',
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
                const SizedBox(width: 16),
                if (material['supplier'] != null) ...[
                  Icon(Icons.business, size: 16, color: Colors.blue),
                  const SizedBox(width: 4),
                  Text(
                    material['supplier'],
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ],
            ),
            if (material['model'] != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.label, size: 16, color: Colors.orange),
                  const SizedBox(width: 4),
                  Text(
                    '型号: ${material['model']}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _editMaterial(material),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('编辑'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteMaterial(
                    material['id'].toString(),
                    material['name'] ?? '未命名材料',
                  ),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('删除'),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _editMaterial(Map<String, dynamic> material) {
    // TODO: 实现编辑功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('编辑功能即将推出'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// 材料创建对话框
class MaterialCreateDialog extends StatefulWidget {
  final List<Map<String, dynamic>> categories;

  const MaterialCreateDialog({super.key, required this.categories});

  @override
  State<MaterialCreateDialog> createState() => _MaterialCreateDialogState();
}

class _MaterialCreateDialogState extends State<MaterialCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _unitController = TextEditingController(text: '个');
  final _supplierController = TextEditingController();
  final _modelController = TextEditingController();
  String? _selectedCategoryId;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _unitController.dispose();
    _supplierController.dispose();
    _modelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('添加新材料'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: '材料名称',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入材料名称';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: '材料描述',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedCategoryId,
                  decoration: const InputDecoration(
                    labelText: '材料分类',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category['id'],
                      child: Text(category['name']),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategoryId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: '单价',
                          border: OutlineInputBorder(),
                          prefixText: '¥ ',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (double.tryParse(value) == null) {
                              return '请输入有效的价格';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _unitController,
                        decoration: const InputDecoration(
                          labelText: '单位',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _supplierController,
                  decoration: const InputDecoration(
                    labelText: '供应商',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _modelController,
                  decoration: const InputDecoration(
                    labelText: '型号',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop({
                'name': _nameController.text.trim(),
                'description': _descriptionController.text.trim(),
                'category_id': _selectedCategoryId,
                'price': double.tryParse(_priceController.text.trim()) ?? 0.0,
                'unit': _unitController.text.trim(),
                'supplier': _supplierController.text.trim(),
                'model': _modelController.text.trim(),
              });
            }
          },
          child: const Text('添加'),
        ),
      ],
    );
  }
}
