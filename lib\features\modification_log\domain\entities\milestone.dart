import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'milestone.freezed.dart';
part 'milestone.g.dart';

/// 里程碑实体
/// 代表项目中的重要节点或阶段
@freezed
class Milestone with _$Milestone {
  const factory Milestone({
    /// 里程碑唯一标识
    required String id,
    
    /// 关联的项目ID
    required String projectId,
    
    /// 里程碑标题
    required String title,
    
    /// 里程碑描述
    String? description,
    
    /// 里程碑日期
    required DateTime date,
    
    /// 里程碑状态
    @Default(MilestoneStatus.planned) MilestoneStatus status,
    
    /// 关联的系统ID（可选）
    String? systemId,
    
    /// 关联的日志ID列表
    @Default([]) List<String> relatedLogIds,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
    
    /// 创建者ID
    required String createdBy,
    
    /// 里程碑图标
    String? iconName,
    
    /// 里程碑颜色
    String? colorHex,
    
    /// 里程碑优先级
    @Default(MilestonePriority.medium) MilestonePriority priority,
  }) = _Milestone;

  factory Milestone.fromJson(Map<String, dynamic> json) =>
      _$MilestoneFromJson(json);
}

/// Milestone扩展方法
extension MilestoneX on Milestone {
  /// 获取状态显示文本
  String get statusDisplayText => status.displayName;
  
  /// 获取优先级显示文本
  String get priorityDisplayText => priority.displayName;
  
  /// 是否已完成
  bool get isCompleted => status == MilestoneStatus.completed;
  
  /// 是否已延期
  bool get isDelayed {
    if (status == MilestoneStatus.overdue) return true;
    if (status == MilestoneStatus.completed) return false;
    
    return DateTime.now().isAfter(date) && status != MilestoneStatus.completed;
  }
  
  /// 获取日期显示文本
  String get dateDisplayText {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final milestoneDate = DateTime(date.year, date.month, date.day);
    
    if (milestoneDate == today) {
      return '今天';
    } else if (milestoneDate == today.add(const Duration(days: 1))) {
      return '明天';
    } else if (milestoneDate == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else {
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }
  
  /// 获取剩余天数
  int? get daysRemaining {
    if (isCompleted) return null;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final milestoneDate = DateTime(date.year, date.month, date.day);
    
    return milestoneDate.difference(today).inDays;
  }
  
  /// 获取剩余天数显示文本
  String? get daysRemainingDisplayText {
    final days = daysRemaining;
    if (days == null) return null;
    
    if (days < 0) {
      return '已逾期 ${days.abs()} 天';
    } else if (days == 0) {
      return '今天到期';
    } else if (days == 1) {
      return '明天到期';
    } else {
      return '还剩 $days 天';
    }
  }
}