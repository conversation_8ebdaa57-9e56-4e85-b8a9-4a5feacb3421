import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'form_state_provider.g.dart';

/// 表单字段状态
class FormFieldState {
  final String value;
  final String? error;
  final bool isValid;

  const FormFieldState({
    this.value = '',
    this.error,
    this.isValid = true,
  });

  FormFieldState copyWith({
    String? value,
    String? error,
    bool? isValid,
  }) {
    return FormFieldState(
      value: value ?? this.value,
      error: error,
      isValid: isValid ?? this.isValid,
    );
  }
}

/// 表单状态
class FormState {
  final Map<String, FormFieldState> fields;
  final bool isLoading;
  final bool isValid;

  const FormState({
    this.fields = const {},
    this.isLoading = false,
    this.isValid = true,
  });

  FormState copyWith({
    Map<String, FormFieldState>? fields,
    bool? isLoading,
    bool? isValid,
  }) {
    return FormState(
      fields: fields ?? this.fields,
      isLoading: isLoading ?? this.isLoading,
      isValid: isValid ?? this.isValid,
    );
  }

  FormFieldState getField(String key) {
    return fields[key] ?? const FormFieldState();
  }
}

/// 通用表单状态管理Provider
@riverpod
class FormStateNotifier extends _$FormStateNotifier {
  @override
  FormState build() {
    return const FormState();
  }

  void updateField(String key, String value, {String? error}) {
    final newFields = Map<String, FormFieldState>.from(state.fields);
    newFields[key] = FormFieldState(
      value: value,
      error: error,
      isValid: error == null,
    );
    
    final isFormValid = newFields.values.every((field) => field.isValid);
    
    state = state.copyWith(
      fields: newFields,
      isValid: isFormValid,
    );
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void validateField(String key, String? Function(String) validator) {
    final field = state.getField(key);
    final error = validator(field.value);
    updateField(key, field.value, error: error);
  }

  void clearForm() {
    state = const FormState();
  }

  void setFieldError(String key, String error) {
    final field = state.getField(key);
    updateField(key, field.value, error: error);
  }

  String getFieldValue(String key) {
    return state.getField(key).value;
  }

  bool hasFieldError(String key) {
    return state.getField(key).error != null;
  }

  String? getFieldError(String key) {
    return state.getField(key).error;
  }
}

/// 创建材料表单Provider
@riverpod
class CreateMaterialFormNotifier extends _$CreateMaterialFormNotifier {
  @override
  FormState build() {
    return const FormState();
  }

  void updateName(String value) {
    final error = value.trim().isEmpty ? '请输入材料名称' : null;
    updateField('name', value, error: error);
  }

  void updateCategory(String value) {
    updateField('category', value);
  }

  void updateBrand(String value) {
    updateField('brand', value);
  }

  void updateModel(String value) {
    updateField('model', value);
  }

  void updatePrice(String value) {
    String? error;
    if (value.trim().isEmpty) {
      error = '请输入价格';
    } else {
      final price = double.tryParse(value);
      if (price == null || price < 0) {
        error = '请输入有效的价格';
      }
    }
    updateField('price', value, error: error);
  }

  void updateSupplier(String value) {
    updateField('supplier', value);
  }

  void updateNotes(String value) {
    updateField('notes', value);
  }

  void updateField(String key, String value, {String? error}) {
    final newFields = Map<String, FormFieldState>.from(state.fields);
    newFields[key] = FormFieldState(
      value: value,
      error: error,
      isValid: error == null,
    );
    
    final isFormValid = newFields.values.every((field) => field.isValid);
    
    state = state.copyWith(
      fields: newFields,
      isValid: isFormValid,
    );
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void clearForm() {
    state = const FormState();
  }
}

/// 创建BOM项表单Provider
@riverpod
class CreateBomItemFormNotifier extends _$CreateBomItemFormNotifier {
  @override
  FormState build() {
    return const FormState();
  }

  void updateName(String value) {
    final error = value.trim().isEmpty ? '请输入物料名称' : null;
    updateField('name', value, error: error);
  }

  void updateQuantity(String value) {
    String? error;
    if (value.trim().isEmpty) {
      error = '请输入数量';
    } else {
      final quantity = int.tryParse(value);
      if (quantity == null || quantity <= 0) {
        error = '请输入有效数量';
      }
    }
    updateField('quantity', value, error: error);
  }

  void updateUnitPrice(String value) {
    if (value.trim().isNotEmpty) {
      final price = double.tryParse(value);
      final error = (price == null || price < 0) ? '请输入有效价格' : null;
      updateField('unitPrice', value, error: error);
    } else {
      updateField('unitPrice', value);
    }
  }

  void updateCategory(String value) {
    updateField('category', value);
  }

  void updateBrand(String value) {
    updateField('brand', value);
  }

  void updateModel(String value) {
    updateField('model', value);
  }

  void updateDescription(String value) {
    updateField('description', value);
  }

  void updateField(String key, String value, {String? error}) {
    final newFields = Map<String, FormFieldState>.from(state.fields);
    newFields[key] = FormFieldState(
      value: value,
      error: error,
      isValid: error == null,
    );
    
    final isFormValid = newFields.values.every((field) => field.isValid);
    
    state = state.copyWith(
      fields: newFields,
      isValid: isFormValid,
    );
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void clearForm() {
    state = const FormState();
  }
}