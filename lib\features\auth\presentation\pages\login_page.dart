import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/login_request.dart';
import '../providers/auth_provider.dart';
import 'register_page.dart';

class LoginPage extends ConsumerWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('登录'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const LoginForm(),
    );
  }
}

class LoginForm extends ConsumerStatefulWidget {
  const LoginForm({super.key});

  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo或标题
            const Icon(
              Icons.directions_car,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            const Text(
              'VanHub改装宝',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 40),

            // 邮箱输入框
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: '邮箱',
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入邮箱';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return '邮箱格式不正确';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 密码输入框
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: '密码',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入密码';
                }
                if (value.length < 6) {
                  return '密码长度不能少于6位';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // 登录按钮
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: _handleLogin,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('登录', style: TextStyle(fontSize: 16)),
              ),
            ),
            const SizedBox(height: 16),

            // 注册和忘记密码链接
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const RegisterPage(),
                      ),
                    );
                  },
                  child: const Text('还没有账号？注册'),
                ),
                TextButton(
                  onPressed: _handleForgotPassword,
                  child: const Text('忘记密码？'),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 游客模式按钮
            OutlinedButton(
              onPressed: _handleGuestMode,
              child: const Text('以游客身份浏览'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = LoginRequest(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );

    debugPrint('🔍 [LoginForm] 开始登录...');
    final success = await ref.read(authNotifierProvider.notifier).login(request);

    if (mounted) {
      if (success) {
        debugPrint('🚀 [LoginForm] 登录成功，执行导航');
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/',
          (route) => false,
        );
      } else {
        // 从AsyncError状态中获取错误信息
        final authState = ref.read(authNotifierProvider);
        if (authState.hasError) {
          _showError(authState.error.toString());
        } else {
          _showError('登录失败，请重试');
        }
      }
    }
  }

  Future<void> _handleForgotPassword() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) {
      _showError('请先输入邮箱地址');
      return;
    }

    final success = await ref.read(authNotifierProvider.notifier).resetPassword(email);

    if (mounted) {
      if (success) {
        _showSuccess('密码重置邮件已发送，请检查您的邮箱');
      } else {
        _showError('密码重置失败，请重试');
      }
    }
  }

  Future<void> _handleGuestMode() async {
    debugPrint('🔍 [LoginForm] 开始游客登录...');
    final success = await ref.read(authNotifierProvider.notifier).signInAsGuest();

    if (mounted) {
      if (success) {
        debugPrint('🚀 [LoginForm] 游客登录成功，执行导航');
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/',
          (route) => false,
        );
      } else {
        _showError('游客登录失败，请重试');
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}
