import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../domain/entities/bom_item.dart' as domain;
import '../providers/bom_provider.dart';
import '../widgets/edit_bom_item_dialog_widget.dart';

/// BOM项目详情页面 - 严格遵循Clean Architecture
class BomItemDetailPage extends ConsumerStatefulWidget {
  final String bomItemId;
  final String projectId;

  const BomItemDetailPage({
    super.key,
    required this.bomItemId,
    required this.projectId,
  });

  @override
  ConsumerState<BomItemDetailPage> createState() => _BomItemDetailPageState();
}

class _BomItemDetailPageState extends ConsumerState<BomItemDetailPage> {
  @override
  Widget build(BuildContext context) {
    final bomItemAsync = ref.watch(bomItemDetailProvider(widget.bomItemId));

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('BOM项目详情'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          // 编辑按钮
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditDialog(),
            tooltip: '编辑BOM项',
          ),
          // 更多操作
          PopupMenuButton<String>(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 18),
                    SizedBox(width: 8),
                    Text('复制项目'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'save_to_library',
                child: Row(
                  children: [
                    Icon(Icons.library_add, size: 18),
                    SizedBox(width: 8),
                    Text('保存到材料库'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 18, color: Colors.red),
                    SizedBox(width: 8),
                    Text('删除项目', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
            onSelected: (value) => _handleMenuAction(value),
          ),
        ],
      ),
      body: bomItemAsync.when(
        data: (bomItem) => _buildDetailContent(bomItem),
        loading: () => const LoadingWidget(message: '加载BOM项详情...'),
        error: (error, stack) => ErrorDisplayWidget(
          message: '加载BOM项详情失败: ${error.toString()}',
          onRetry: () {
            ref.invalidate(bomItemDetailProvider(widget.bomItemId));
          },
        ),
      ),
    );
  }

  Widget _buildDetailContent(domain.BomItem bomItem) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息卡片
          _buildBasicInfoCard(bomItem),
          const SizedBox(height: 16),
          
          // 价格和数量信息
          _buildPriceQuantityCard(bomItem),
          const SizedBox(height: 16),
          
          // 状态和进度信息
          _buildStatusCard(bomItem),
          const SizedBox(height: 16),
          
          // 详细信息
          _buildDetailInfoCard(bomItem),
          const SizedBox(height: 16),
          
          // 操作按钮
          _buildActionButtons(bomItem),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(domain.BomItem bomItem) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory_2,
                  color: Colors.indigo[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 物料名称
            _buildInfoRow('物料名称', bomItem.materialName),
            const SizedBox(height: 12),
            
            // 分类
            if (bomItem.category != null)
              _buildInfoRow('分类', bomItem.category!),
            if (bomItem.category != null) const SizedBox(height: 12),

            // 描述
            if (bomItem.description.isNotEmpty)
              _buildInfoRow('描述', bomItem.description),
            if (bomItem.description.isNotEmpty)
              const SizedBox(height: 12),
            
            // 创建时间
            _buildInfoRow(
              '创建时间',
              DateFormat('yyyy-MM-dd HH:mm').format(bomItem.createdAt),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceQuantityCard(domain.BomItem bomItem) {
    final totalPrice = bomItem.unitPrice * bomItem.quantity;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  color: Colors.green[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  '价格信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildPriceItem(
                    '数量',
                    '${bomItem.quantity}',
                    Colors.blue[600]!,
                  ),
                ),
                Expanded(
                  child: _buildPriceItem(
                    '单价',
                    '¥${bomItem.unitPrice.toStringAsFixed(2)}',
                    Colors.orange[600]!,
                  ),
                ),
                Expanded(
                  child: _buildPriceItem(
                    '总价',
                    '¥${totalPrice.toStringAsFixed(2)}',
                    Colors.green[600]!,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(domain.BomItem bomItem) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: Colors.purple[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  '状态信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 当前状态
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(bomItem.status),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getStatusIcon(bomItem.status),
                        size: 16,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        _getStatusDisplayName(bomItem.status),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                // 状态更新按钮
                _buildStatusUpdateButton(bomItem),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailInfoCard(domain.BomItem bomItem) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.teal[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  '详细信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 备注
            if (bomItem.notes != null && bomItem.notes!.isNotEmpty) ...[
              _buildInfoRow('备注', bomItem.notes!),
              const SizedBox(height: 12),
            ],
            
            // 更新时间
            _buildInfoRow(
              '最后更新',
              DateFormat('yyyy-MM-dd HH:mm').format(bomItem.updatedAt),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(domain.BomItem bomItem) {
    return Column(
      children: [
        // 主要操作按钮
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showEditDialog(),
                icon: const Icon(Icons.edit),
                label: const Text('编辑项目'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _duplicateItem(bomItem),
                icon: const Icon(Icons.copy),
                label: const Text('复制项目'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 次要操作按钮
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _saveToLibrary(bomItem),
                icon: const Icon(Icons.library_add),
                label: const Text('保存到材料库'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _deleteItem(bomItem),
                icon: const Icon(Icons.delete),
                label: const Text('删除项目'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusUpdateButton(domain.BomItem bomItem) {
    final nextStatus = _getNextStatus(bomItem.status);
    if (nextStatus == null) return const SizedBox.shrink();

    return ElevatedButton.icon(
      onPressed: () => _updateStatus(bomItem, nextStatus),
      icon: Icon(_getStatusIcon(nextStatus), size: 16),
      label: Text(_getStatusDisplayName(nextStatus)),
      style: ElevatedButton.styleFrom(
        backgroundColor: _getStatusColor(nextStatus),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  // 状态相关方法
  Color _getStatusColor(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return Colors.orange;
      case domain.BomItemStatus.ordered:
        return Colors.blue;
      case domain.BomItemStatus.received:
        return Colors.green;
      case domain.BomItemStatus.installed:
        return Colors.purple;
      case domain.BomItemStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return Icons.schedule;
      case domain.BomItemStatus.ordered:
        return Icons.shopping_cart;
      case domain.BomItemStatus.received:
        return Icons.inventory;
      case domain.BomItemStatus.installed:
        return Icons.check_circle;
      case domain.BomItemStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusDisplayName(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return '待采购';
      case domain.BomItemStatus.ordered:
        return '已下单';
      case domain.BomItemStatus.received:
        return '已收货';
      case domain.BomItemStatus.installed:
        return '已安装';
      case domain.BomItemStatus.cancelled:
        return '已取消';
    }
  }

  domain.BomItemStatus? _getNextStatus(domain.BomItemStatus currentStatus) {
    switch (currentStatus) {
      case domain.BomItemStatus.pending:
        return domain.BomItemStatus.ordered;
      case domain.BomItemStatus.ordered:
        return domain.BomItemStatus.received;
      case domain.BomItemStatus.received:
        return domain.BomItemStatus.installed;
      case domain.BomItemStatus.installed:
      case domain.BomItemStatus.cancelled:
        return null;
    }
  }

  // 事件处理方法
  void _showEditDialog() {
    final bomItemAsync = ref.read(bomItemDetailProvider(widget.bomItemId));
    bomItemAsync.whenData((bomItem) async {
      final result = await showDialog<domain.BomItem>(
        context: context,
        builder: (context) => EditBomItemDialogWidget(
          bomItem: bomItem,
        ),
      );

      // 如果编辑成功，刷新BOM详情数据
      if (result != null) {
        ref.invalidate(bomItemDetailProvider(widget.bomItemId));
      }
    });
  }

  void _handleMenuAction(String action) {
    final bomItemAsync = ref.read(bomItemDetailProvider(widget.bomItemId));
    bomItemAsync.whenData((bomItem) {
      switch (action) {
        case 'duplicate':
          _duplicateItem(bomItem);
          break;
        case 'save_to_library':
          _saveToLibrary(bomItem);
          break;
        case 'delete':
          _deleteItem(bomItem);
          break;
      }
    });
  }

  void _updateStatus(domain.BomItem bomItem, domain.BomItemStatus newStatus) async {
    try {
      // 调用BOM Provider更新状态 - 严格遵循Clean Architecture原则
      final result = await ref.read(bomControllerProvider.notifier).updateBomItemStatus(
        bomItem.id,
        newStatus,
      );

      if (mounted) {
        result.fold(
          (failure) {
            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('状态更新失败: ${failure.message}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: () => _updateStatus(bomItem, newStatus),
                ),
              ),
            );
          },
          (updatedBomItem) {
            // 显示成功信息
            // Provider刷新已在BomController中处理，这里不需要重复刷新
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('状态已更新为: ${_getStatusDisplayName(newStatus)}'),
                backgroundColor: Colors.green,
              ),
            );
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('状态更新时发生错误: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _duplicateItem(domain.BomItem bomItem) {
    // TODO: 实现复制逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('复制功能开发中...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _saveToLibrary(domain.BomItem bomItem) {
    // TODO: 实现保存到材料库逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('保存到材料库功能开发中...'),
        backgroundColor: Colors.teal,
      ),
    );
  }

  void _deleteItem(domain.BomItem bomItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除"${bomItem.materialName}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现删除逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('删除功能开发中...'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
