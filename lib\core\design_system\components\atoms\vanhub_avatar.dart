import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../vanhub_design_system.dart';

/// VanHub头像组件
/// 
/// 支持多种尺寸、在线状态指示器、图片加载失败处理
/// 遵循Material Design 3规范
class VanHubAvatar extends StatelessWidget {
  /// 头像图片URL
  final String? imageUrl;
  
  /// 用户名称（用于生成首字母）
  final String? name;
  
  /// 头像尺寸
  final VanHubAvatarSize size;
  
  /// 是否显示在线状态
  final bool showOnlineStatus;
  
  /// 在线状态
  final bool isOnline;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 自定义边框颜色
  final Color? borderColor;
  
  /// 边框宽度
  final double borderWidth;
  
  /// 是否显示阴影
  final bool showShadow;
  
  /// 背景颜色（当没有图片时）
  final Color? backgroundColor;
  
  /// 文字颜色（首字母）
  final Color? textColor;

  const VanHubAvatar({
    super.key,
    this.imageUrl,
    this.name,
    this.size = VanHubAvatarSize.md,
    this.showOnlineStatus = false,
    this.isOnline = false,
    this.onTap,
    this.borderColor,
    this.borderWidth = 0,
    this.showShadow = false,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final avatarSize = _getAvatarSize();
    final statusSize = _getStatusSize();
    
    Widget avatar = Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: borderWidth > 0
            ? Border.all(
                color: borderColor ?? Theme.of(context).colorScheme.outline,
                width: borderWidth,
              )
            : null,
        boxShadow: showShadow
            ? [VanHubDesignSystem.shadowBase]
            : null,
      ),
      child: ClipOval(
        child: _buildAvatarContent(context),
      ),
    );

    // 添加点击效果
    if (onTap != null) {
      avatar = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(avatarSize / 2),
          child: avatar,
        ),
      );
    }

    // 添加在线状态指示器
    if (showOnlineStatus) {
      avatar = Stack(
        children: [
          avatar,
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: statusSize,
              height: statusSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isOnline 
                    ? VanHubDesignSystem.semanticSuccess
                    : VanHubDesignSystem.neutralGray400,
                border: Border.all(
                  color: Theme.of(context).colorScheme.surface,
                  width: 2,
                ),
              ),
            ),
          ),
        ],
      );
    }

    return avatar;
  }

  Widget _buildAvatarContent(BuildContext context) {
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return _buildNetworkImage(context);
    } else {
      return _buildFallbackAvatar(context);
    }
  }

  Widget _buildNetworkImage(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl!,
      fit: BoxFit.cover,
      placeholder: (context, url) => _buildLoadingPlaceholder(context),
      errorWidget: (context, url, error) => _buildFallbackAvatar(context),
      fadeInDuration: VanHubDesignSystem.durationFast,
      fadeOutDuration: VanHubDesignSystem.durationFast,
    );
  }

  Widget _buildLoadingPlaceholder(BuildContext context) {
    return Container(
      color: backgroundColor ?? Theme.of(context).colorScheme.surfaceVariant,
      child: Center(
        child: SizedBox(
          width: _getAvatarSize() * 0.3,
          height: _getAvatarSize() * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFallbackAvatar(BuildContext context) {
    final initials = _getInitials();
    final fontSize = _getFontSize();
    
    return Container(
      color: backgroundColor ?? _generateBackgroundColor(context),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: VanHubDesignSystem.fontWeightMedium,
            color: textColor ?? _getContrastColor(context),
          ),
        ),
      ),
    );
  }

  String _getInitials() {
    if (name == null || name!.isEmpty) {
      return '?';
    }
    
    final words = name!.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return name![0].toUpperCase();
    }
  }

  Color _generateBackgroundColor(BuildContext context) {
    if (name == null || name!.isEmpty) {
      return Theme.of(context).colorScheme.surfaceVariant;
    }
    
    // 基于名称生成一致的颜色
    final hash = name!.hashCode;
    final colors = [
      VanHubDesignSystem.brandPrimary,
      VanHubDesignSystem.brandSecondary,
      VanHubDesignSystem.brandAccent,
      VanHubDesignSystem.semanticInfo,
      VanHubDesignSystem.semanticWarning,
      const Color(0xFF8B5CF6), // 紫色
      const Color(0xFFEC4899), // 粉色
      const Color(0xFF06B6D4), // 青色
    ];
    
    return colors[hash.abs() % colors.length];
  }

  Color _getContrastColor(BuildContext context) {
    final bgColor = backgroundColor ?? _generateBackgroundColor(context);
    final luminance = bgColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  double _getAvatarSize() {
    switch (size) {
      case VanHubAvatarSize.xs:
        return 24;
      case VanHubAvatarSize.sm:
        return 32;
      case VanHubAvatarSize.md:
        return 40;
      case VanHubAvatarSize.lg:
        return 48;
      case VanHubAvatarSize.xl:
        return 56;
      case VanHubAvatarSize.xxl:
        return 64;
    }
  }

  double _getStatusSize() {
    switch (size) {
      case VanHubAvatarSize.xs:
        return 8;
      case VanHubAvatarSize.sm:
        return 10;
      case VanHubAvatarSize.md:
        return 12;
      case VanHubAvatarSize.lg:
        return 14;
      case VanHubAvatarSize.xl:
        return 16;
      case VanHubAvatarSize.xxl:
        return 18;
    }
  }

  double _getFontSize() {
    switch (size) {
      case VanHubAvatarSize.xs:
        return 10;
      case VanHubAvatarSize.sm:
        return 12;
      case VanHubAvatarSize.md:
        return 14;
      case VanHubAvatarSize.lg:
        return 16;
      case VanHubAvatarSize.xl:
        return 18;
      case VanHubAvatarSize.xxl:
        return 20;
    }
  }
}

/// 头像尺寸枚举
enum VanHubAvatarSize {
  xs,   // 24px
  sm,   // 32px
  md,   // 40px
  lg,   // 48px
  xl,   // 56px
  xxl,  // 64px
}
