import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_criteria.freezed.dart';
part 'search_criteria.g.dart';

/// 材料搜索条件
@freezed
class SearchCriteria with _$SearchCriteria {
  const factory SearchCriteria({
    /// 搜索关键词（名称、品牌、型号等）
    String? query,
    
    /// 分类
    String? category,
    
    /// 品牌
    String? brand,
    
    /// 最低价格
    double? minPrice,
    
    /// 最高价格
    double? maxPrice,
    
    /// 标签列表
    List<String>? tags,
    
    /// 供应商
    String? supplier,
    
    /// 排序字段
    @Default('created_at') String sortBy,
    
    /// 排序方向（升序/降序）
    @Default(false) bool ascending,
    
    /// 结果数量限制
    @Default(20) int limit,
    
    /// 结果偏移量（用于分页）
    @Default(0) int offset,
    
    /// 是否包含已停产材料
    @Default(false) bool includeDiscontinued,
    
    /// 是否只显示有库存的材料
    @Default(false) bool onlyInStock,
    
    /// 是否只显示有图片的材料
    @Default(false) bool onlyWithImages,
    
    /// 最小使用次数
    int? minUsageCount,
    
    /// 最近使用时间范围（天数）
    int? recentlyUsedDays,
    
    /// 自定义过滤条件
    Map<String, dynamic>? customFilters,
  }) = _SearchCriteria;

  factory SearchCriteria.fromJson(Map<String, dynamic> json) => _$SearchCriteriaFromJson(json);
}

extension SearchCriteriaX on SearchCriteria {
  /// 检查搜索条件是否为空
  bool get isEmpty => 
    query == null && 
    category == null && 
    brand == null && 
    minPrice == null && 
    maxPrice == null && 
    (tags == null || tags!.isEmpty) && 
    supplier == null &&
    !includeDiscontinued &&
    !onlyInStock &&
    !onlyWithImages &&
    minUsageCount == null &&
    recentlyUsedDays == null &&
    (customFilters == null || customFilters!.isEmpty);
  
  /// 检查搜索条件是否不为空
  bool get isNotEmpty => !isEmpty;
  
  /// 转换为查询参数
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (query != null && query!.isNotEmpty) params['query'] = query;
    if (category != null && category!.isNotEmpty) params['category'] = category;
    if (brand != null && brand!.isNotEmpty) params['brand'] = brand;
    if (minPrice != null) params['min_price'] = minPrice;
    if (maxPrice != null) params['max_price'] = maxPrice;
    if (tags != null && tags!.isNotEmpty) params['tags'] = tags;
    if (supplier != null && supplier!.isNotEmpty) params['supplier'] = supplier;
    
    params['sort_by'] = sortBy;
    params['ascending'] = ascending;
    params['limit'] = limit;
    params['offset'] = offset;
    params['include_discontinued'] = includeDiscontinued;
    params['only_in_stock'] = onlyInStock;
    params['only_with_images'] = onlyWithImages;
    
    if (minUsageCount != null) params['min_usage_count'] = minUsageCount;
    if (recentlyUsedDays != null) params['recently_used_days'] = recentlyUsedDays;
    
    if (customFilters != null && customFilters!.isNotEmpty) {
      params.addAll(customFilters!);
    }
    
    return params;
  }
}