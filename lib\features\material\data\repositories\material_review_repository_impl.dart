import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/material_review.dart';
import '../../domain/repositories/material_review_repository.dart';
import '../datasources/material_review_remote_datasource.dart';
import '../models/material_review_model.dart';

/// 材料评价仓储实现
/// 遵循Clean Architecture原则，实现材料评价相关的数据操作
class MaterialReviewRepositoryImpl implements MaterialReviewRepository {
  final MaterialReviewRemoteDataSource remoteDataSource;

  const MaterialReviewRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, MaterialReview>> createReview(MaterialReview review) async {
    try {
      final model = MaterialReviewModel.fromEntity(review);
      final result = await remoteDataSource.createReview(model);
      return Right(result.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '创建评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialReview>> updateReview(MaterialReview review) async {
    try {
      final model = MaterialReviewModel.fromEntity(review);
      final result = await remoteDataSource.updateReview(model);
      return Right(result.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '更新评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteReview(String reviewId) async {
    try {
      await remoteDataSource.deleteReview(reviewId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '删除评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialReview>> getReview(String reviewId) async {
    try {
      final result = await remoteDataSource.getReview(reviewId);
      return Right(result.toEntity());
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '获取评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getMaterialReviews(
    String materialId, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) async {
    try {
      final results = await remoteDataSource.getMaterialReviews(
        materialId,
        filterCriteria: filterCriteria,
        limit: limit,
        offset: offset,
      );
      return Right(results.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '获取材料评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getUserReviews(
    String userId, {
    int? limit,
    int? offset,
  }) async {
    try {
      final results = await remoteDataSource.getUserReviews(
        userId,
        limit: limit,
        offset: offset,
      );
      return Right(results.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '获取用户评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, MaterialReviewSummary>> getMaterialReviewSummary(
    String materialId,
  ) async {
    try {
      final result = await remoteDataSource.getMaterialReviewSummary(materialId);
      return Right(result.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '获取评价摘要失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> markReviewAsHelpful(
    String reviewId,
    String userId,
  ) async {
    try {
      await remoteDataSource.markReviewAsHelpful(reviewId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '标记有用失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> unmarkReviewAsHelpful(
    String reviewId,
    String userId,
  ) async {
    try {
      await remoteDataSource.unmarkReviewAsHelpful(reviewId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '取消标记有用失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> likeReview(
    String reviewId,
    String userId,
  ) async {
    try {
      await remoteDataSource.likeReview(reviewId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '点赞失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> unlikeReview(
    String reviewId,
    String userId,
  ) async {
    try {
      await remoteDataSource.unlikeReview(reviewId, userId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '取消点赞失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasUserReviewedMaterial(
    String materialId,
    String userId,
  ) async {
    try {
      final result = await remoteDataSource.hasUserReviewedMaterial(materialId, userId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '检查评价状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> getPopularReviews({
    int limit = 10,
    int timeRange = 30,
  }) async {
    try {
      final results = await remoteDataSource.getPopularReviews(
        limit: limit,
        timeRange: timeRange,
      );
      return Right(results.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '获取热门评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialReview>>> searchReviews(
    String query, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) async {
    try {
      final results = await remoteDataSource.searchReviews(
        query,
        filterCriteria: filterCriteria,
        limit: limit,
        offset: offset,
      );
      return Right(results.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '搜索评价失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, MaterialReviewSummary>>> getBatchReviewSummaries(
    List<String> materialIds,
  ) async {
    try {
      final results = await remoteDataSource.getBatchReviewSummaries(materialIds);
      final Map<String, MaterialReviewSummary> summaries = {};
      
      results.forEach((materialId, summaryModel) {
        summaries[materialId] = summaryModel.toEntity();
      });
      
      return Right(summaries);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '批量获取评价摘要失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> reportReview(
    String reviewId,
    String reporterId,
    String reason,
  ) async {
    try {
      await remoteDataSource.reportReview(reviewId, reporterId, reason);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: '举报评价失败: $e'));
    }
  }
}
