{"enabled": true, "name": "Riverpod Presentation Validator", "description": "Validates correct Riverpod usage in Presentation layer files: checks Widget types (ConsumerWidget/ConsumerStatefulWidget), ensures no business logic in Widgets, validates Provider usage (ref.watch/read/listen), checks Notifier implementation with @riverpod annotation, and ensures state updates through Notifier methods instead of setState", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/features/**/presentation/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "当保存Presentation层文件时，验证Riverpod状态管理的正确使用：\n1. 检查Widget类型 - 有状态的Widget必须继承ConsumerWidget或ConsumerStatefulWidget\n2. 检查业务逻辑位置 - Widget中不能包含业务逻辑，必须通过Notifier处理\n3. 检查Provider使用 - 必须正确使用ref.watch、ref.read和ref.listen\n4. 检查Notifier实现 - 状态管理必须通过@riverpod注解的Notifier类\n5. 检查状态更新 - 状态更新必须通过Notifier方法，不能在Widget中使用setState\n如果发现违规：\n- 指出业务逻辑泄露到UI层的位置\n- 建议正确的Riverpod使用方式\n- 提供Notifier重构建议"}}