import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/vanhub_design_system.dart';

/// 简化的VanHub主题配置
class VanHubTheme {
  VanHubTheme._();

  /// 浅色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // 颜色方案
      colorScheme: ColorScheme.fromSeed(
        seedColor: VanHubDesignSystem.brandPrimary,
        brightness: Brightness.light,
      ),
      
      // 字体主题
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSize3xl,
          fontWeight: VanHubDesignSystem.fontWeightBold,
          color: VanHubDesignSystem.neutralGray900,
        ),
        headlineMedium: TextStyle(
          fontSize: VanHubDesignSystem.fontSize2xl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray900,
        ),
        headlineSmall: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeXl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray900,
        ),
        bodyLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeBase,
          fontWeight: VanHubDesignSystem.fontWeightRegular,
          color: VanHubDesignSystem.neutralGray800,
        ),
        bodyMedium: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeSm,
          fontWeight: VanHubDesignSystem.fontWeightRegular,
          color: VanHubDesignSystem.neutralGray800,
        ),
        labelLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeBase,
          fontWeight: VanHubDesignSystem.fontWeightMedium,
          color: VanHubDesignSystem.neutralGray900,
        ),
      ),
      
      // AppBar主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: VanHubDesignSystem.neutralWhite,
        foregroundColor: VanHubDesignSystem.neutralGray900,
        titleTextStyle: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeXl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray900,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VanHubDesignSystem.brandPrimary,
          foregroundColor: VanHubDesignSystem.neutralWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
          ),
          textStyle: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeBase,
            fontWeight: VanHubDesignSystem.fontWeightMedium,
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
        contentPadding: EdgeInsets.all(VanHubDesignSystem.spacing4),
      ),
    );
  }

  /// 深色主题
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // 颜色方案
      colorScheme: ColorScheme.fromSeed(
        seedColor: VanHubDesignSystem.brandPrimary,
        brightness: Brightness.dark,
      ),
      
      // 字体主题
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSize3xl,
          fontWeight: VanHubDesignSystem.fontWeightBold,
          color: VanHubDesignSystem.neutralGray100,
        ),
        headlineMedium: TextStyle(
          fontSize: VanHubDesignSystem.fontSize2xl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray100,
        ),
        headlineSmall: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeXl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray100,
        ),
        bodyLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeBase,
          fontWeight: VanHubDesignSystem.fontWeightRegular,
          color: VanHubDesignSystem.neutralGray200,
        ),
        bodyMedium: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeSm,
          fontWeight: VanHubDesignSystem.fontWeightRegular,
          color: VanHubDesignSystem.neutralGray200,
        ),
        labelLarge: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeBase,
          fontWeight: VanHubDesignSystem.fontWeightMedium,
          color: VanHubDesignSystem.neutralGray100,
        ),
      ),
      
      // AppBar主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: VanHubDesignSystem.neutralGray900,
        foregroundColor: VanHubDesignSystem.neutralGray100,
        titleTextStyle: TextStyle(
          fontSize: VanHubDesignSystem.fontSizeXl,
          fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          color: VanHubDesignSystem.neutralGray100,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VanHubDesignSystem.brandPrimary,
          foregroundColor: VanHubDesignSystem.neutralWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
          ),
          textStyle: TextStyle(
            fontSize: VanHubDesignSystem.fontSizeBase,
            fontWeight: VanHubDesignSystem.fontWeightMedium,
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
        contentPadding: EdgeInsets.all(VanHubDesignSystem.spacing4),
      ),
    );
  }
}