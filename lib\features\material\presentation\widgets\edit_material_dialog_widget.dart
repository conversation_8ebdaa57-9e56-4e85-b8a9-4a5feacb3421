import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/update_material_request.dart';
import '../providers/material_provider.dart';

/// 编辑材料对话框
/// 严格遵循Clean Architecture原则，只负责UI展示和用户交互
/// 通过MaterialProvider调用Domain层的UseCase
class EditMaterialDialogWidget extends ConsumerStatefulWidget {
  final domain.Material material;

  const EditMaterialDialogWidget({
    super.key,
    required this.material,
  });

  @override
  ConsumerState<EditMaterialDialogWidget> createState() => _EditMaterialDialogWidgetState();
}

class _EditMaterialDialogWidgetState extends ConsumerState<EditMaterialDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _brandController;
  late final TextEditingController _modelController;
  late final TextEditingController _specificationsController;
  late final TextEditingController _priceController;
  late final TextEditingController _supplierController;
  late final TextEditingController _notesController;
  
  late String _selectedCategory;
  DateTime? _purchaseDate;
  bool _isLoading = false;
  
  // 房车改装专业分类
  final List<String> _categories = [
    '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装',
    '车顶改装', '底盘改装', '其他配件'
  ];

  @override
  void initState() {
    super.initState();
    // 初始化控制器和数据
    _nameController = TextEditingController(text: widget.material.name);
    _brandController = TextEditingController(text: widget.material.brand ?? '');
    _modelController = TextEditingController(text: widget.material.model ?? '');
    _specificationsController = TextEditingController(text: widget.material.specifications ?? '');
    _priceController = TextEditingController(text: widget.material.price?.toString() ?? '');
    _supplierController = TextEditingController(text: widget.material.supplier ?? '');
    _notesController = TextEditingController(text: widget.material.description ?? '');
    _selectedCategory = widget.material.category;
    _purchaseDate = widget.material.purchaseDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _specificationsController.dispose();
    _priceController.dispose();
    _supplierController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Icon(
                  Icons.edit,
                  color: Colors.teal,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  '编辑材料信息',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildSpecificationSection(),
                      const SizedBox(height: 24),
                      _buildPurchaseInfoSection(),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息部分
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 材料名称
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: '材料名称 *',
            hintText: '例如：锂电池、水泵、LED灯带',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.inventory_2),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入材料名称';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // 分类选择
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: '材料分类 *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCategory = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // 品牌和型号
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _brandController,
                decoration: const InputDecoration(
                  labelText: '品牌',
                  hintText: '例如：比亚迪、格力',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: '型号',
                  hintText: '例如：BYD-100AH',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建规格信息部分
  Widget _buildSpecificationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '规格信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 规格描述
        TextFormField(
          controller: _specificationsController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: '规格描述',
            hintText: '详细描述材料的技术规格、尺寸、性能参数等',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
        ),
        const SizedBox(height: 16),
        
        // 价格
        TextFormField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: '价格（元）',
            hintText: '例如：299.99',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.attach_money),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              final price = double.tryParse(value.trim());
              if (price == null || price < 0) {
                return '请输入有效的价格';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建采购信息部分
  Widget _buildPurchaseInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '采购信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 供应商
        TextFormField(
          controller: _supplierController,
          decoration: const InputDecoration(
            labelText: '供应商',
            hintText: '例如：京东、淘宝、线下门店',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.store),
          ),
        ),
        const SizedBox(height: 16),
        
        // 采购日期
        InkWell(
          onTap: _selectPurchaseDate,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: '采购日期',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.calendar_today),
            ),
            child: Text(
              _purchaseDate != null
                  ? '${_purchaseDate!.year}-${_purchaseDate!.month.toString().padLeft(2, '0')}-${_purchaseDate!.day.toString().padLeft(2, '0')}'
                  : '选择采购日期',
              style: TextStyle(
                color: _purchaseDate != null ? Colors.black87 : Colors.grey[600],
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // 备注
        TextFormField(
          controller: _notesController,
          maxLines: 2,
          decoration: const InputDecoration(
            labelText: '备注',
            hintText: '其他需要记录的信息...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _updateMaterial,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('保存修改'),
          ),
        ),
      ],
    );
  }

  /// 选择采购日期
  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _purchaseDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() {
        _purchaseDate = date;
      });
    }
  }

  /// 更新材料 - 严格遵循Clean Architecture原则
  Future<void> _updateMaterial() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 构建UpdateMaterialRequest - Domain层实体
      final request = UpdateMaterialRequest(
        id: widget.material.id,
        name: _nameController.text.trim(),
        category: _selectedCategory,
        brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
        model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
        specifications: _specificationsController.text.trim().isEmpty ? null : _specificationsController.text.trim(),
        price: _priceController.text.trim().isEmpty ? null : double.tryParse(_priceController.text.trim()),
        supplier: _supplierController.text.trim().isEmpty ? null : _supplierController.text.trim(),
        purchaseDate: _purchaseDate,
        description: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      // 通过Provider调用Domain层UseCase
      final result = await ref.read(materialControllerProvider.notifier).updateMaterial(request);

      if (mounted) {
        result.fold(
          (failure) {
            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('更新材料失败: ${failure.message}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: _updateMaterial,
                ),
              ),
            );
          },
          (material) {
            // 更新成功
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('材料 "${material.name}" 更新成功！'),
                backgroundColor: Colors.green,
              ),
            );
            
            // 关闭对话框
            Navigator.of(context).pop(material);
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新材料时发生错误: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
