import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/project_stats.dart';
import '../../domain/repositories/project_repository.dart';
import '../../domain/services/project_stats_service.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/domain/repositories/bom_repository.dart';

/// 项目统计服务实现
/// 遵循Clean Architecture原则，实现项目统计的具体业务逻辑
class ProjectStatsServiceImpl implements ProjectStatsService {
  final ProjectRepository _projectRepository;
  final BomRepository _bomRepository;
  
  const ProjectStatsServiceImpl(
    this._projectRepository,
    this._bomRepository,
  );
  
  @override
  Future<Either<Failure, ProjectStats>> getProjectStats(String projectId) async {
    try {
      // 获取项目基本信息
      final projectResult = await _projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(projectResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final project = projectResult.fold((l) => throw Exception(), (r) => r);
      
      // 获取BOM项目列表
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      // 计算统计数据
      final stats = _calculateProjectStats(project, bomItems);
      
      return Right(stats);
    } catch (e) {
      return Left(ServerFailure(message: '获取项目统计失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, double>> calculateProjectProgress(String projectId) async {
    try {
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      if (bomItems.isEmpty) return const Right(0.0);
      
      final completedItems = bomItems.where((item) =>
        item.status == BomItemStatus.installed
      ).length;
      
      final progressPercentage = (completedItems / bomItems.length) * 100;
      
      return Right(progressPercentage);
    } catch (e) {
      return Left(ServerFailure(message: '计算项目进度失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, Map<String, int>>> getBomItemStatusCounts(String projectId) async {
    try {
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      final statusCounts = <String, int>{};
      
      for (final status in BomItemStatus.values) {
        final count = bomItems.where((item) => item.status == status).length;
        statusCounts[status.name] = count;
      }
      
      return Right(statusCounts);
    } catch (e) {
      return Left(ServerFailure(message: '获取BOM状态统计失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, BudgetAnalysis>> calculateBudgetAnalysis(String projectId) async {
    try {
      final projectResult = await _projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(projectResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final project = projectResult.fold((l) => throw Exception(), (r) => r);
      final totalBudget = project.budget ?? 0.0;
      
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      // 计算实际成本
      final actualCost = bomItems.fold<double>(0, (sum, item) => 
        sum + ((item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity));
      
      // 计算预算分析
      final remainingBudget = totalBudget - actualCost;
      final budgetUtilization = totalBudget > 0 ? (actualCost / totalBudget) * 100 : 0.0;
      final isOverBudget = actualCost > totalBudget;
      final overrunAmount = isOverBudget ? actualCost - totalBudget : 0.0;
      
      // 生成预算警告
      final alerts = <BudgetAlert>[];
      if (budgetUtilization >= 90 && !isOverBudget) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.critical,
          message: '预算使用率已达${budgetUtilization.toStringAsFixed(1)}%，接近预算上限',
          amount: remainingBudget,
        ));
      } else if (budgetUtilization >= 75) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.warning,
          message: '预算使用率已达${budgetUtilization.toStringAsFixed(1)}%，请注意控制成本',
          amount: remainingBudget,
        ));
      }
      
      if (isOverBudget) {
        alerts.add(BudgetAlert(
          type: BudgetAlertType.overBudget,
          message: '项目已超出预算¥${overrunAmount.toStringAsFixed(2)}',
          amount: overrunAmount,
        ));
      }
      
      // 计算分类成本分布
      final categoryBreakdown = <String, double>{};
      for (final item in bomItems) {
        final category = item.category ?? '未分类';
        final cost = (item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity;
        categoryBreakdown[category] = (categoryBreakdown[category] ?? 0.0) + cost;
      }
      
      final budgetAnalysis = BudgetAnalysis(
        totalBudget: totalBudget,
        actualCost: actualCost,
        remainingBudget: remainingBudget,
        budgetUtilization: budgetUtilization,
        isOverBudget: isOverBudget,
        overrunAmount: overrunAmount,
        alerts: alerts,
        categoryBreakdown: categoryBreakdown,
      );
      
      return Right(budgetAnalysis);
    } catch (e) {
      return Left(ServerFailure(message: '计算预算分析失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, Map<String, double>>> getCategoryCostDistribution(String projectId) async {
    try {
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      final categoryDistribution = <String, double>{};
      
      for (final item in bomItems) {
        final category = item.category ?? '未分类';
        final cost = (item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity;
        categoryDistribution[category] = (categoryDistribution[category] ?? 0.0) + cost;
      }
      
      return Right(categoryDistribution);
    } catch (e) {
      return Left(ServerFailure(message: '获取分类成本分布失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, DateTime?>> estimateCompletionDate(String projectId) async {
    try {
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }
      
      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);
      
      // 找出所有有计划日期的未完成项目
      final pendingItems = bomItems.where((item) =>
        item.status != BomItemStatus.installed &&
        item.plannedDate != null
      ).toList();
      
      if (pendingItems.isEmpty) return const Right(null);
      
      // 找出最晚的计划日期作为预计完成日期
      final latestDate = pendingItems
          .map((item) => item.plannedDate!)
          .reduce((a, b) => a.isAfter(b) ? a : b);
      
      return Right(latestDate);
    } catch (e) {
      return Left(ServerFailure(message: '预测完成时间失败: $e'));
    }
  }
  
  /// 计算项目统计数据的私有方法
  ProjectStats _calculateProjectStats(Project project, List<BomItem> bomItems) {
    final totalItems = bomItems.length;
    final completedItems = bomItems.where((item) =>
      item.status == BomItemStatus.installed
    ).length;
    final plannedItems = bomItems.where((item) => item.status == BomItemStatus.pending).length;
    final purchasedItems = bomItems.where((item) => item.status == BomItemStatus.ordered).length;
    final inUseItems = bomItems.where((item) => item.status == BomItemStatus.received).length;
    final installedItems = bomItems.where((item) => item.status == BomItemStatus.installed).length;
    
    // 计算成本
    final totalBudget = project.budget ?? 0.0;
    final actualCost = bomItems.fold<double>(0, (sum, item) => 
      sum + ((item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity));
    final remainingBudget = totalBudget - actualCost;
    final budgetUtilization = totalBudget > 0 ? (actualCost / totalBudget) * 100 : 0.0;
    
    // 计算进度
    final progressPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0.0;
    
    // 状态分布
    final statusDistribution = <String, int>{};
    for (final status in BomItemStatus.values) {
      final count = bomItems.where((item) => item.status == status).length;
      statusDistribution[status.name] = count;
    }
    
    // 分类成本分布
    final categoryCostDistribution = <String, double>{};
    for (final item in bomItems) {
      final category = item.category ?? '未分类';
      final cost = (item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity;
      categoryCostDistribution[category] = (categoryCostDistribution[category] ?? 0.0) + cost;
    }
    
    // 预计完成时间
    final pendingItems = bomItems.where((item) =>
      item.status != BomItemStatus.installed &&
      item.plannedDate != null
    ).toList();
    
    DateTime? estimatedCompletionDate;
    if (pendingItems.isNotEmpty) {
      estimatedCompletionDate = pendingItems
          .map((item) => item.plannedDate!)
          .reduce((a, b) => a.isAfter(b) ? a : b);
    }
    
    return ProjectStats(
      projectId: project.id,
      progressPercentage: progressPercentage,
      totalBudget: totalBudget,
      actualCost: actualCost,
      remainingBudget: remainingBudget,
      budgetUtilization: budgetUtilization,
      totalBomItems: totalItems,
      completedBomItems: completedItems,
      plannedBomItems: plannedItems,
      purchasedBomItems: purchasedItems,
      inUseBomItems: inUseItems,
      installedBomItems: installedItems,
      lastUpdated: DateTime.now(),
      projectCreatedAt: project.createdAt,
      estimatedCompletionDate: estimatedCompletionDate,
      statusDistribution: statusDistribution,
      categoryCostDistribution: categoryCostDistribution,
    );
  }
  
  @override
  Future<Either<Failure, ProjectEfficiencyMetrics>> getEfficiencyMetrics(String projectId) async {
    try {
      // 获取项目基本信息
      final projectResult = await _projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(projectResult.fold((l) => l, (r) => throw Exception()));
      }

      final project = projectResult.fold((l) => throw Exception(), (r) => r);

      // 获取BOM项目列表
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }

      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);

      if (bomItems.isEmpty) {
        return Right(ProjectEfficiencyMetrics(
          costPerItem: 0.0,
          timePerItem: 0.0,
          completionVelocity: 0.0,
          budgetEfficiency: 0.0,
          resourceUtilization: 0.0,
          daysActive: 0,
          dailyProgress: 0.0,
        ));
      }

      // 计算项目活跃天数
      final now = DateTime.now();
      final daysActive = now.difference(project.createdAt).inDays + 1;

      // 计算总成本和完成项目数
      final totalCost = bomItems.fold<double>(0, (sum, item) =>
        sum + ((item.actualPrice ?? item.estimatedPrice ?? 0.0) * item.quantity));
      final completedItems = bomItems.where((item) =>
        item.status == BomItemStatus.installed).length;
      final totalItems = bomItems.length;

      // 计算效率指标
      final costPerItem = totalItems > 0 ? totalCost / totalItems : 0.0;
      final timePerItem = completedItems > 0 ? daysActive / completedItems : 0.0;
      final completionVelocity = daysActive > 0 ? completedItems / daysActive : 0.0;
      final progressPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0.0;
      final dailyProgress = daysActive > 0 ? progressPercentage / daysActive : 0.0;

      // 计算预算效率 (完成度 / 预算使用率)
      final budgetUsed = project.budget != null && project.budget! > 0
        ? (totalCost / project.budget!) * 100 : 0.0;
      final budgetEfficiency = budgetUsed > 0 ? progressPercentage / budgetUsed : 0.0;

      // 计算资源利用率 (基于计划vs实际时间)
      final resourceUtilization = _calculateResourceUtilization(bomItems, daysActive);

      return Right(ProjectEfficiencyMetrics(
        costPerItem: costPerItem,
        timePerItem: timePerItem,
        completionVelocity: completionVelocity,
        budgetEfficiency: budgetEfficiency,
        resourceUtilization: resourceUtilization,
        daysActive: daysActive,
        dailyProgress: dailyProgress,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '获取效率指标失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, ProjectComparison>> compareWithAverage(String projectId) async {
    try {
      // 获取当前项目的效率指标
      final metricsResult = await getEfficiencyMetrics(projectId);
      if (metricsResult.isLeft()) {
        return Left(metricsResult.fold((l) => l, (r) => throw Exception()));
      }

      final currentMetrics = metricsResult.fold((l) => throw Exception(), (r) => r);

      // 获取项目基本信息
      final projectResult = await _projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(projectResult.fold((l) => l, (r) => throw Exception()));
      }

      final project = projectResult.fold((l) => throw Exception(), (r) => r);

      // 计算行业平均值 (基于经验数据和同类项目)
      final industryAverages = _calculateIndustryAverages(project);

      // 计算比较结果
      final progressVsAverage = currentMetrics.completionVelocity / 0.5; // 假设行业平均完成速度为0.5
      final costVsAverage = industryAverages.averageCostPerItem > 0
        ? currentMetrics.costPerItem / industryAverages.averageCostPerItem : 1.0;
      final timeVsAverage = industryAverages.averageTimePerItem > 0
        ? currentMetrics.timePerItem / industryAverages.averageTimePerItem : 1.0;
      final efficiencyVsAverage = industryAverages.averageBudgetEfficiency > 0
        ? currentMetrics.budgetEfficiency / industryAverages.averageBudgetEfficiency : 1.0;

      // 计算表现水平
      final performanceLevel = _calculatePerformanceLevel(progressVsAverage, costVsAverage, timeVsAverage, efficiencyVsAverage);

      // 生成优势和改进建议
      final strengths = _generateStrengths(progressVsAverage, costVsAverage, timeVsAverage, efficiencyVsAverage);
      final improvements = _generateImprovements(progressVsAverage, costVsAverage, timeVsAverage, efficiencyVsAverage);

      return Right(ProjectComparison(
        progressVsAverage: progressVsAverage,
        costVsAverage: costVsAverage,
        timeVsAverage: timeVsAverage,
        efficiencyVsAverage: efficiencyVsAverage,
        performanceLevel: performanceLevel,
        strengths: strengths,
        improvements: improvements,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '项目比较失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, ProjectRiskAssessment>> assessProjectRisk(String projectId) async {
    try {
      // 获取项目统计数据
      final statsResult = await getProjectStats(projectId);
      if (statsResult.isLeft()) {
        return Left(statsResult.fold((l) => l, (r) => throw Exception()));
      }

      final stats = statsResult.fold((l) => throw Exception(), (r) => r);

      // 获取BOM项目列表
      final bomItemsResult = await _bomRepository.getProjectBomItems(projectId);
      if (bomItemsResult.isLeft()) {
        return Left(bomItemsResult.fold((l) => l, (r) => throw Exception()));
      }

      final bomItems = bomItemsResult.fold((l) => throw Exception(), (r) => r);

      // 计算各种风险因子
      final budgetRisk = _calculateBudgetRisk(stats);
      final scheduleRisk = _calculateScheduleRisk(bomItems);
      final qualityRisk = _calculateQualityRisk(bomItems);
      final resourceRisk = _calculateResourceRisk(stats, bomItems);

      // 计算综合风险等级
      final overallRisk = _calculateOverallProjectRisk(budgetRisk, scheduleRisk, qualityRisk, resourceRisk);

      // 生成具体风险列表
      final risks = _generateProjectRisks(budgetRisk, scheduleRisk, qualityRisk, resourceRisk, bomItems);

      // 生成风险建议
      final recommendations = _generateRiskRecommendations(
        budgetRisk, scheduleRisk, qualityRisk, resourceRisk, overallRisk
      );

      // 计算风险分数
      final riskScore = _calculateRiskScore(budgetRisk, scheduleRisk, qualityRisk, resourceRisk);

      return Right(ProjectRiskAssessment(
        overallRisk: overallRisk,
        riskScore: riskScore,
        risks: risks,
        recommendations: recommendations,
      ));
    } catch (e) {
      return Left(ServerFailure(message: '风险评估失败: $e'));
    }
  }
  
  @override
  Future<Either<Failure, ProjectReport>> generateProjectReport(
    String projectId,
    ProjectReportType reportType,
  ) async {
    try {
      // 获取项目基本信息
      final projectResult = await _projectRepository.getProjectById(projectId);
      if (projectResult.isLeft()) {
        return Left(projectResult.fold((l) => l, (r) => throw Exception()));
      }

      final project = projectResult.fold((l) => throw Exception(), (r) => r);

      // 根据报告类型生成不同的报告内容
      Map<String, dynamic> reportData;
      List<String> sections;

      switch (reportType) {
        case ProjectReportType.budget:
          reportData = await _generateCostReport(projectId);
          sections = ['成本概览', '预算分析', '分类成本', '成本趋势'];
          break;
        case ProjectReportType.progress:
          reportData = await _generateProgressReport(projectId);
          sections = ['进度概览', '完成状态', '时间分析', '里程碑'];
          break;
        case ProjectReportType.comprehensive:
          reportData = await _generateCompleteReport(projectId);
          sections = ['项目概览', '成本分析', '进度分析', '效率指标', '风险评估'];
          break;
        case ProjectReportType.efficiency:
          reportData = await _generateEfficiencyReport(projectId);
          sections = ['效率概览', '性能指标', '对比分析', '优化建议'];
          break;
        case ProjectReportType.risk:
          reportData = await _generateRiskReport(projectId);
          sections = ['风险概览', '风险评估', '缓解措施', '监控建议'];
          break;
      }

      return Right(ProjectReport(
        projectId: projectId,
        type: reportType,
        generatedAt: DateTime.now(),
        data: reportData,
        summary: _generateReportSummary(reportType, reportData),
        keyInsights: sections,
        recommendations: _generateReportRecommendations(reportType, reportData),
      ));
    } catch (e) {
      return Left(ServerFailure(message: '生成报告失败: $e'));
    }
  }

  /// 计算资源利用率的私有方法
  double _calculateResourceUtilization(List<BomItem> bomItems, int daysActive) {
    if (bomItems.isEmpty || daysActive <= 0) return 0.0;

    // 基于计划日期vs实际完成时间计算资源利用率
    final itemsWithPlannedDate = bomItems.where((item) => item.plannedDate != null).toList();
    if (itemsWithPlannedDate.isEmpty) return 0.8; // 默认80%利用率

    double totalUtilization = 0.0;
    int validItems = 0;

    for (final item in itemsWithPlannedDate) {
      if (item.status == BomItemStatus.installed && item.usedDate != null) {
        final plannedDays = item.plannedDate!.difference(DateTime.now()).inDays.abs();
        final actualDays = item.usedDate!.difference(DateTime.now()).inDays.abs();

        if (plannedDays > 0) {
          final utilization = actualDays <= plannedDays ? 1.0 : plannedDays / actualDays;
          totalUtilization += utilization;
          validItems++;
        }
      }
    }

    return validItems > 0 ? totalUtilization / validItems : 0.8;
  }

  /// 计算行业平均值的私有方法
  IndustryAverages _calculateIndustryAverages(Project project) {
    // 基于项目类型和改装系统计算行业平均值
    // 这里使用经验数据，实际应用中可以从数据库获取
    final systemCount = project.tags?.length ?? 1; // 使用tags作为系统数量的替代

    return IndustryAverages(
      averageCostPerItem: 2000.0 + (systemCount * 500.0), // 基础成本 + 系统复杂度
      averageTimePerItem: 3.0 + (systemCount * 0.5), // 基础时间 + 系统复杂度
      averageBudgetEfficiency: 0.85, // 85%的预算效率
      averageCompletionTime: 30 + (systemCount * 10), // 基础30天 + 系统复杂度
      averageProjectCost: 50000.0 + (systemCount * 15000.0), // 基础成本 + 系统成本
    );
  }

  /// 计算表现水平的私有方法
  ProjectPerformanceLevel _calculatePerformanceLevel(
    double progressVsAverage,
    double costVsAverage,
    double timeVsAverage,
    double efficiencyVsAverage,
  ) {
    final averageRatio = (progressVsAverage + (2.0 - costVsAverage) + (2.0 - timeVsAverage) + efficiencyVsAverage) / 4;

    if (averageRatio >= 1.3) return ProjectPerformanceLevel.excellent;
    if (averageRatio >= 1.1) return ProjectPerformanceLevel.good;
    if (averageRatio >= 0.9) return ProjectPerformanceLevel.average;
    if (averageRatio >= 0.7) return ProjectPerformanceLevel.belowAverage;
    return ProjectPerformanceLevel.poor;
  }

  /// 生成优势的私有方法
  List<String> _generateStrengths(
    double progressVsAverage,
    double costVsAverage,
    double timeVsAverage,
    double efficiencyVsAverage,
  ) {
    final strengths = <String>[];

    if (progressVsAverage > 1.2) {
      strengths.add('项目进度超前，执行效率高');
    }
    if (costVsAverage < 0.8) {
      strengths.add('成本控制优秀，预算管理到位');
    }
    if (timeVsAverage < 0.8) {
      strengths.add('时间管理高效，工期控制良好');
    }
    if (efficiencyVsAverage > 1.2) {
      strengths.add('整体效率优秀，资源利用充分');
    }

    if (strengths.isEmpty) {
      strengths.add('项目执行稳定，各项指标均衡');
    }

    return strengths;
  }

  /// 生成改进建议的私有方法
  List<String> _generateImprovements(
    double progressVsAverage,
    double costVsAverage,
    double timeVsAverage,
    double efficiencyVsAverage,
  ) {
    final improvements = <String>[];

    if (progressVsAverage < 0.8) {
      improvements.add('加快项目进度，优化工作流程');
    }
    if (costVsAverage > 1.2) {
      improvements.add('控制项目成本，寻找更优惠的供应商');
    }
    if (timeVsAverage > 1.2) {
      improvements.add('提高时间效率，合理安排工作计划');
    }
    if (efficiencyVsAverage < 0.8) {
      improvements.add('提升整体效率，优化资源配置');
    }

    if (improvements.isEmpty) {
      improvements.add('继续保持当前水平，可考虑进一步优化');
    }

    return improvements;
  }

  /// 计算预算风险的私有方法
  RiskLevel _calculateBudgetRisk(ProjectStats stats) {
    if (stats.budgetUtilization > 100) return RiskLevel.high;
    if (stats.budgetUtilization > 90) return RiskLevel.medium;
    if (stats.budgetUtilization > 70) return RiskLevel.low;
    return RiskLevel.minimal;
  }

  /// 计算进度风险的私有方法
  RiskLevel _calculateScheduleRisk(List<BomItem> bomItems) {
    final now = DateTime.now();
    final overdueItems = bomItems.where((item) =>
      item.plannedDate != null &&
      item.plannedDate!.isBefore(now) &&
      item.status != BomItemStatus.installed
    ).length;

    final totalItems = bomItems.length;
    if (totalItems == 0) return RiskLevel.minimal;

    final overdueRatio = overdueItems / totalItems;
    if (overdueRatio > 0.3) return RiskLevel.high;
    if (overdueRatio > 0.15) return RiskLevel.medium;
    if (overdueRatio > 0.05) return RiskLevel.low;
    return RiskLevel.minimal;
  }

  /// 计算质量风险的私有方法
  RiskLevel _calculateQualityRisk(List<BomItem> bomItems) {
    // 基于材料品质、价格异常等因素评估质量风险
    final lowPriceItems = bomItems.where((item) {
      final price = item.actualPrice ?? item.estimatedPrice ?? 0.0;
      return price > 0 && price < 100; // 价格过低可能存在质量风险
    }).length;

    final totalItems = bomItems.length;
    if (totalItems == 0) return RiskLevel.minimal;

    final lowPriceRatio = lowPriceItems / totalItems;
    if (lowPriceRatio > 0.4) return RiskLevel.high;
    if (lowPriceRatio > 0.2) return RiskLevel.medium;
    if (lowPriceRatio > 0.1) return RiskLevel.low;
    return RiskLevel.minimal;
  }

  /// 计算资源风险的私有方法
  RiskLevel _calculateResourceRisk(ProjectStats stats, List<BomItem> bomItems) {
    // 基于资源利用率和项目复杂度评估资源风险
    final complexItems = bomItems.where((item) =>
      (item.actualPrice ?? item.estimatedPrice ?? 0.0) > 5000
    ).length;

    final totalItems = bomItems.length;
    if (totalItems == 0) return RiskLevel.minimal;

    final complexityRatio = complexItems / totalItems;
    if (complexityRatio > 0.5) return RiskLevel.high;
    if (complexityRatio > 0.3) return RiskLevel.medium;
    if (complexityRatio > 0.1) return RiskLevel.low;
    return RiskLevel.minimal;
  }

  /// 计算综合风险等级的私有方法
  ProjectRiskLevel _calculateOverallProjectRisk(
    RiskLevel budgetRisk,
    RiskLevel scheduleRisk,
    RiskLevel qualityRisk,
    RiskLevel resourceRisk,
  ) {
    final risks = [budgetRisk, scheduleRisk, qualityRisk, resourceRisk];
    final riskScores = risks.map((risk) => risk.index).toList();
    final averageScore = riskScores.reduce((a, b) => a + b) / riskScores.length;

    if (averageScore >= 3) return ProjectRiskLevel.critical;
    if (averageScore >= 2.5) return ProjectRiskLevel.high;
    if (averageScore >= 1.5) return ProjectRiskLevel.medium;
    return ProjectRiskLevel.low;
  }

  /// 生成具体风险列表的私有方法
  List<ProjectRisk> _generateProjectRisks(
    RiskLevel budgetRisk,
    RiskLevel scheduleRisk,
    RiskLevel qualityRisk,
    RiskLevel resourceRisk,
    List<BomItem> bomItems,
  ) {
    final risks = <ProjectRisk>[];

    if (budgetRisk != RiskLevel.minimal) {
      risks.add(ProjectRisk(
        type: ProjectRiskType.budget,
        description: '预算超支风险',
        probability: budgetRisk.index / 4.0,
        impact: 0.8,
        riskValue: (budgetRisk.index / 4.0) * 0.8,
        mitigation: '加强成本控制，定期审查预算使用情况',
      ));
    }

    if (scheduleRisk != RiskLevel.minimal) {
      risks.add(ProjectRisk(
        type: ProjectRiskType.schedule,
        description: '进度延期风险',
        probability: scheduleRisk.index / 4.0,
        impact: 0.7,
        riskValue: (scheduleRisk.index / 4.0) * 0.7,
        mitigation: '优化工作流程，合理安排资源',
      ));
    }

    if (qualityRisk != RiskLevel.minimal) {
      risks.add(ProjectRisk(
        type: ProjectRiskType.quality,
        description: '质量问题风险',
        probability: qualityRisk.index / 4.0,
        impact: 0.9,
        riskValue: (qualityRisk.index / 4.0) * 0.9,
        mitigation: '加强质量检查，选择可靠供应商',
      ));
    }

    if (resourceRisk != RiskLevel.minimal) {
      risks.add(ProjectRisk(
        type: ProjectRiskType.resource,
        description: '资源不足风险',
        probability: resourceRisk.index / 4.0,
        impact: 0.6,
        riskValue: (resourceRisk.index / 4.0) * 0.6,
        mitigation: '提前规划资源需求，建立备用方案',
      ));
    }

    return risks;
  }

  /// 生成风险建议的私有方法
  List<String> _generateRiskRecommendations(
    RiskLevel budgetRisk,
    RiskLevel scheduleRisk,
    RiskLevel qualityRisk,
    RiskLevel resourceRisk,
    ProjectRiskLevel overallRisk,
  ) {
    final recommendations = <String>[];

    if (budgetRisk == RiskLevel.high) {
      recommendations.add('预算风险较高，建议立即审查成本控制措施');
    }
    if (scheduleRisk == RiskLevel.high) {
      recommendations.add('进度风险较高，建议重新评估项目时间线');
    }
    if (qualityRisk == RiskLevel.high) {
      recommendations.add('质量风险较高，建议加强材料质量检查');
    }
    if (resourceRisk == RiskLevel.high) {
      recommendations.add('资源风险较高，建议优化资源配置');
    }

    if (overallRisk == ProjectRiskLevel.critical) {
      recommendations.add('项目风险严重，建议立即召开紧急风险评估会议');
    } else if (overallRisk == ProjectRiskLevel.high) {
      recommendations.add('项目整体风险较高，建议召开风险评估会议');
    } else if (overallRisk == ProjectRiskLevel.low) {
      recommendations.add('项目风险控制良好，继续保持当前管理水平');
    }

    return recommendations;
  }

  /// 识别关键风险项目的私有方法
  List<String> _identifyCriticalItems(List<BomItem> bomItems) {
    final criticalItems = <String>[];
    final now = DateTime.now();

    for (final item in bomItems) {
      // 高价值项目
      final price = (item.actualPrice ?? item.unitPrice) * item.quantity;
      if (price > 10000) {
        criticalItems.add('${item.materialName} - 高价值项目 (¥${price.toStringAsFixed(2)})');
      }

      // 逾期项目
      if (item.plannedDate != null &&
          item.plannedDate!.isBefore(now) &&
          item.status != BomItemStatus.installed) {
        final overdueDays = now.difference(item.plannedDate!).inDays;
        criticalItems.add('${item.materialName} - 逾期 $overdueDays 天');
      }

      // 长期未更新项目
      final daysSinceUpdate = now.difference(item.updatedAt).inDays;
      if (daysSinceUpdate > 30 && item.status != BomItemStatus.installed) {
        criticalItems.add('${item.materialName} - $daysSinceUpdate 天未更新');
      }
    }

    return criticalItems;
  }

  /// 计算风险分数的私有方法
  double _calculateRiskScore(
    RiskLevel budgetRisk,
    RiskLevel scheduleRisk,
    RiskLevel qualityRisk,
    RiskLevel resourceRisk,
  ) {
    final totalScore = budgetRisk.index + scheduleRisk.index + qualityRisk.index + resourceRisk.index;
    return (totalScore / 16.0) * 100; // 转换为0-100分
  }

  /// 生成成本报告的私有方法
  Future<Map<String, dynamic>> _generateCostReport(String projectId) async {
    final budgetResult = await calculateBudgetAnalysis(projectId);
    final categoryResult = await getCategoryCostDistribution(projectId);

    return {
      'budgetAnalysis': budgetResult.fold((l) => null, (r) => r),
      'categoryDistribution': categoryResult.fold((l) => {}, (r) => r),
      'costTrends': {}, // 可以后续添加成本趋势数据
    };
  }

  /// 生成进度报告的私有方法
  Future<Map<String, dynamic>> _generateProgressReport(String projectId) async {
    final progressResult = await calculateProjectProgress(projectId);
    final statusResult = await getBomItemStatusCounts(projectId);
    final completionResult = await estimateCompletionDate(projectId);

    return {
      'progressPercentage': progressResult.fold((l) => 0.0, (r) => r),
      'statusCounts': statusResult.fold((l) => {}, (r) => r),
      'estimatedCompletion': completionResult.fold((l) => null, (r) => r?.toIso8601String()),
    };
  }

  /// 生成完整报告的私有方法
  Future<Map<String, dynamic>> _generateCompleteReport(String projectId) async {
    final statsResult = await getProjectStats(projectId);
    final metricsResult = await getEfficiencyMetrics(projectId);
    final riskResult = await assessProjectRisk(projectId);

    return {
      'projectStats': statsResult.fold((l) => null, (r) => r),
      'efficiencyMetrics': metricsResult.fold((l) => null, (r) => r),
      'riskAssessment': riskResult.fold((l) => null, (r) => r),
    };
  }

  /// 生成效率报告的私有方法
  Future<Map<String, dynamic>> _generateEfficiencyReport(String projectId) async {
    final metricsResult = await getEfficiencyMetrics(projectId);
    final comparisonResult = await compareWithAverage(projectId);

    return {
      'efficiencyMetrics': metricsResult.fold((l) => null, (r) => r),
      'comparison': comparisonResult.fold((l) => null, (r) => r),
    };
  }

  /// 生成风险报告的私有方法
  Future<Map<String, dynamic>> _generateRiskReport(String projectId) async {
    final riskResult = await assessProjectRisk(projectId);

    return {
      'riskAssessment': riskResult.fold((l) => null, (r) => r),
    };
  }

  /// 生成报告摘要的私有方法
  String _generateReportSummary(ProjectReportType reportType, Map<String, dynamic> data) {
    switch (reportType) {
      case ProjectReportType.budget:
        return '成本报告显示了项目的预算使用情况和成本分布';
      case ProjectReportType.progress:
        return '进度报告显示了项目的完成状态和时间安排';
      case ProjectReportType.comprehensive:
        return '完整报告提供了项目的全面分析和评估';
      case ProjectReportType.efficiency:
        return '效率报告分析了项目的性能指标和优化建议';
      case ProjectReportType.risk:
        return '风险报告评估了项目的潜在风险和缓解措施';
    }
  }

  /// 生成报告建议的私有方法
  List<String> _generateReportRecommendations(ProjectReportType reportType, Map<String, dynamic> data) {
    final recommendations = <String>[];

    switch (reportType) {
      case ProjectReportType.budget:
        recommendations.add('定期监控预算使用情况');
        recommendations.add('优化高成本分类的材料选择');
        break;
      case ProjectReportType.progress:
        recommendations.add('关注逾期项目的处理');
        recommendations.add('合理安排后续工作计划');
        break;
      case ProjectReportType.comprehensive:
        recommendations.add('综合考虑成本、进度和质量平衡');
        recommendations.add('建立项目管理最佳实践');
        break;
      case ProjectReportType.efficiency:
        recommendations.add('持续优化工作流程');
        recommendations.add('学习行业最佳实践');
        break;
      case ProjectReportType.risk:
        recommendations.add('建立风险监控机制');
        recommendations.add('制定应急预案');
        break;
    }

    return recommendations;
  }
}
