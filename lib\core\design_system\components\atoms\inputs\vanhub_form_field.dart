import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../foundation/colors/colors.dart';
import '../../../foundation/spacing/spacing.dart';
import '../../../foundation/typography/typography.dart';

/// 表单字段类型枚举
enum VanHubFormFieldType {
  /// 文本输入
  text,
  /// 密码输入
  password,
  /// 邮箱输入
  email,
  /// 电话输入
  phone,
  /// 数字输入
  number,
  /// 多行文本输入
  multiline,
  /// 搜索输入
  search,
  /// URL输入
  url,
}

/// VanHub表单字段组件
/// 提供统一的表单输入体验，支持多种输入类型和验证
class VanHubFormField extends StatefulWidget {
  /// 字段类型
  final VanHubFormFieldType type;
  
  /// 标签文本
  final String? label;
  
  /// 占位符文本
  final String? placeholder;
  
  /// 帮助文本
  final String? helperText;
  
  /// 错误文本
  final String? errorText;
  
  /// 初始值
  final String? initialValue;
  
  /// 控制器
  final TextEditingController? controller;
  
  /// 值变化回调
  final ValueChanged<String>? onChanged;
  
  /// 提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 验证函数
  final String? Function(String?)? validator;
  
  /// 是否必填
  final bool required;
  
  /// 是否启用
  final bool enabled;
  
  /// 是否只读
  final bool readOnly;
  
  /// 是否自动聚焦
  final bool autofocus;
  
  /// 最大行数
  final int? maxLines;
  
  /// 最小行数
  final int? minLines;
  
  /// 最大长度
  final int? maxLength;
  
  /// 前缀图标
  final IconData? prefixIcon;
  
  /// 后缀图标
  final IconData? suffixIcon;
  
  /// 后缀图标点击回调
  final VoidCallback? onSuffixIconTap;
  
  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;
  
  /// 键盘类型
  final TextInputType? keyboardType;
  
  /// 文本输入动作
  final TextInputAction? textInputAction;
  
  /// 焦点节点
  final FocusNode? focusNode;

  const VanHubFormField({
    super.key,
    this.type = VanHubFormFieldType.text,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.required = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLines,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.inputFormatters,
    this.keyboardType,
    this.textInputAction,
    this.focusNode,
  });

  /// 创建文本输入字段
  const VanHubFormField.text({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.required = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.inputFormatters,
    this.textInputAction,
    this.focusNode,
  }) : type = VanHubFormFieldType.text,
       maxLines = 1,
       minLines = null,
       keyboardType = null;

  /// 创建密码输入字段
  const VanHubFormField.password({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.required = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLength,
    this.prefixIcon,
    this.inputFormatters,
    this.textInputAction,
    this.focusNode,
  }) : type = VanHubFormFieldType.password,
       maxLines = 1,
       minLines = null,
       suffixIcon = null,
       onSuffixIconTap = null,
       keyboardType = null;

  /// 创建邮箱输入字段
  const VanHubFormField.email({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.required = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.inputFormatters,
    this.textInputAction,
    this.focusNode,
  }) : type = VanHubFormFieldType.email,
       maxLines = 1,
       minLines = null,
       keyboardType = TextInputType.emailAddress;

  /// 创建多行文本输入字段
  const VanHubFormField.multiline({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.required = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLines,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.inputFormatters,
    this.textInputAction,
    this.focusNode,
  }) : type = VanHubFormFieldType.multiline,
       keyboardType = TextInputType.multiline;

  @override
  State<VanHubFormField> createState() => _VanHubFormFieldState();
}

class _VanHubFormFieldState extends State<VanHubFormField> {
  late TextEditingController _controller;
  bool _obscureText = false;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _obscureText = widget.type == VanHubFormFieldType.password;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          _buildLabel(),
          VanHubSpacing.vXs,
        ],
        _buildTextField(),
        if (widget.helperText != null || widget.errorText != null) ...[
          VanHubSpacing.vXs,
          _buildHelperText(),
        ],
      ],
    );
  }

  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        text: widget.label!,
        style: VanHubTypography.labelMedium.copyWith(
          color: VanHubColors.onSurface,
        ),
        children: [
          if (widget.required)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: VanHubColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField() {
    return Focus(
      onFocusChange: (hasFocus) {
        setState(() {
          _hasFocus = hasFocus;
        });
      },
      child: TextFormField(
        controller: _controller,
        focusNode: widget.focusNode,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        autofocus: widget.autofocus,
        obscureText: _obscureText,
        maxLines: widget.type == VanHubFormFieldType.multiline ? (widget.maxLines ?? 3) : 1,
        minLines: widget.minLines,
        maxLength: widget.maxLength,
        keyboardType: _getKeyboardType(),
        textInputAction: widget.textInputAction,
        inputFormatters: widget.inputFormatters,
        validator: widget.validator,
        onChanged: widget.onChanged,
        onFieldSubmitted: widget.onSubmitted,
        decoration: InputDecoration(
          hintText: widget.placeholder,
          prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
          suffixIcon: _buildSuffixIcon(),
          errorText: widget.errorText,
          border: _buildBorder(),
          enabledBorder: _buildBorder(),
          focusedBorder: _buildBorder(focused: true),
          errorBorder: _buildBorder(error: true),
          focusedErrorBorder: _buildBorder(error: true, focused: true),
          filled: true,
          fillColor: widget.enabled ? VanHubColors.surface : VanHubColors.surfaceVariant,
        ),
        style: VanHubTypography.bodyMedium,
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.type == VanHubFormFieldType.password) {
      return IconButton(
        icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }

    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(widget.suffixIcon),
        onPressed: widget.onSuffixIconTap,
      );
    }

    return null;
  }

  Widget _buildHelperText() {
    final text = widget.errorText ?? widget.helperText;
    final isError = widget.errorText != null;

    return Text(
      text!,
      style: VanHubTypography.bodySmall.copyWith(
        color: isError ? VanHubColors.error : VanHubColors.onSurfaceVariant,
      ),
    );
  }

  OutlineInputBorder _buildBorder({bool focused = false, bool error = false}) {
    Color borderColor;
    double borderWidth = 1.0;

    if (error) {
      borderColor = VanHubColors.error;
      borderWidth = 2.0;
    } else if (focused) {
      borderColor = VanHubColors.primary;
      borderWidth = 2.0;
    } else {
      borderColor = VanHubColors.outline;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  TextInputType? _getKeyboardType() {
    if (widget.keyboardType != null) return widget.keyboardType;

    switch (widget.type) {
      case VanHubFormFieldType.email:
        return TextInputType.emailAddress;
      case VanHubFormFieldType.phone:
        return TextInputType.phone;
      case VanHubFormFieldType.number:
        return TextInputType.number;
      case VanHubFormFieldType.multiline:
        return TextInputType.multiline;
      case VanHubFormFieldType.url:
        return TextInputType.url;
      default:
        return TextInputType.text;
    }
  }
}
