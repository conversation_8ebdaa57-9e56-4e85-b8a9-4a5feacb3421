import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// VanHub状态组件
/// 提供加载状态、错误状态、空状态和成功状态的统一展示
class VanHubStates {
  /// 创建加载状态
  static Widget loading({
    String? message,
    double size = 40.0,
    Color? color,
    bool useShimmer = false,
    Widget? customIndicator,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (useShimmer)
              _buildShimmerLoading(size: size, color: color)
            else if (customIndicator != null)
              customIndicator
            else
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  strokeWidth: 4.0,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    color ?? Colors.blue,
                  ),
                ),
              ),
            if (message != null) ...[
              const SizedBox(height: 16.0),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16.0,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 创建骨架屏加载
  static Widget shimmerLoading({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration period = const Duration(milliseconds: 1500),
  }) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? Colors.grey[300]!,
      highlightColor: highlightColor ?? Colors.grey[100]!,
      period: period,
      child: child,
    );
  }

  /// 创建骨架屏矩形
  static Widget shimmerRect({
    double? width,
    double? height = 16.0,
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(4.0)),
    Color color = Colors.white,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color,
        borderRadius: borderRadius,
      ),
    );
  }

  /// 创建骨架屏圆形
  static Widget shimmerCircle({
    double size = 40.0,
    Color color = Colors.white,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  /// 创建错误状态
  static Widget error({
    required String message,
    String? title,
    VoidCallback? onRetry,
    String retryText = '重试',
    IconData icon = Icons.error_outline,
    Color? iconColor,
    double iconSize = 64.0,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    Widget? customIcon,
  }) {
    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            customIcon ??
                Icon(
                  icon,
                  size: iconSize,
                  color: iconColor ?? Colors.red,
                ),
            const SizedBox(height: 16.0),
            if (title != null) ...[
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8.0),
            ],
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.black87,
              ),
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24.0),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryText),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 创建空状态
  static Widget empty({
    String message = '暂无数据',
    String? description,
    IconData icon = Icons.inbox_outlined,
    Color? iconColor,
    double iconSize = 64.0,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    Widget? action,
    Widget? customIcon,
  }) {
    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            customIcon ??
                Icon(
                  icon,
                  size: iconSize,
                  color: iconColor ?? Colors.grey[400],
                ),
            const SizedBox(height: 16.0),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (description != null) ...[
              const SizedBox(height: 8.0),
              Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14.0,
                  color: Colors.grey[600],
                ),
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24.0),
              action,
            ],
          ],
        ),
      ),
    );
  }

  /// 创建成功状态
  static Widget success({
    required String message,
    String? title,
    VoidCallback? onAction,
    String actionText = '确定',
    IconData icon = Icons.check_circle_outline,
    Color? iconColor,
    double iconSize = 64.0,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    Widget? customIcon,
    Duration autoHideDuration = const Duration(seconds: 3),
    VoidCallback? onAutoHide,
  }) {
    if (onAutoHide != null) {
      Future.delayed(autoHideDuration, onAutoHide);
    }

    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            customIcon ??
                Icon(
                  icon,
                  size: iconSize,
                  color: iconColor ?? Colors.green,
                ),
            const SizedBox(height: 16.0),
            if (title != null) ...[
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8.0),
            ],
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16.0,
                color: Colors.black87,
              ),
            ),
            if (onAction != null) ...[
              const SizedBox(height: 24.0),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionText),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 创建线性进度指示器
  static Widget linearProgress({
    double? value,
    double height = 4.0,
    Color? backgroundColor,
    Color? valueColor,
    String? label,
    TextStyle? labelStyle,
    bool showPercentage = false,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(vertical: 8.0),
  }) {
    final percentage = value != null ? (value * 100).toStringAsFixed(0) : null;
    
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (label != null || showPercentage) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (label != null)
                  Text(
                    label,
                    style: labelStyle ?? const TextStyle(fontSize: 14.0),
                  ),
                if (showPercentage && percentage != null)
                  Text(
                    '$percentage%',
                    style: labelStyle ?? const TextStyle(fontSize: 14.0),
                  ),
              ],
            ),
            const SizedBox(height: 4.0),
          ],
          LinearProgressIndicator(
            value: value,
            minHeight: height,
            backgroundColor: backgroundColor,
            valueColor: valueColor != null
                ? AlwaysStoppedAnimation<Color>(valueColor)
                : null,
          ),
        ],
      ),
    );
  }

  /// 创建网络错误状态
  static Widget networkError({
    String message = '网络连接错误',
    String description = '请检查您的网络连接并重试',
    VoidCallback? onRetry,
    String retryText = '重试',
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return error(
      message: message,
      title: description,
      onRetry: onRetry,
      retryText: retryText,
      icon: Icons.wifi_off,
      iconColor: Colors.orange,
      padding: padding,
    );
  }

  /// 创建权限错误状态
  static Widget permissionError({
    String message = '权限不足',
    String description = '您没有权限访问此内容',
    VoidCallback? onAction,
    String actionText = '返回',
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return error(
      message: description,
      title: message,
      onRetry: onAction,
      retryText: actionText,
      icon: Icons.lock_outline,
      iconColor: Colors.red[700],
      padding: padding,
    );
  }

  /// 创建维护状态
  static Widget maintenance({
    String message = '系统维护中',
    String description = '我们正在进行系统维护，请稍后再试',
    VoidCallback? onAction,
    String actionText = '刷新',
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return error(
      message: description,
      title: message,
      onRetry: onAction,
      retryText: actionText,
      icon: Icons.engineering,
      iconColor: Colors.amber[700],
      padding: padding,
    );
  }

  /// 创建骨架屏列表
  static Widget shimmerList({
    int itemCount = 5,
    double itemHeight = 80.0,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(vertical: 8.0),
    Color? baseColor,
    Color? highlightColor,
  }) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? Colors.grey[300]!,
      highlightColor: highlightColor ?? Colors.grey[100]!,
      child: ListView.builder(
        itemCount: itemCount,
        padding: padding,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            child: Container(
              height: itemHeight,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 创建骨架屏网格
  static Widget shimmerGrid({
    int crossAxisCount = 2,
    int itemCount = 10,
    double mainAxisSpacing = 16.0,
    double crossAxisSpacing = 16.0,
    double childAspectRatio = 1.0,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    Color? baseColor,
    Color? highlightColor,
  }) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? Colors.grey[300]!,
      highlightColor: highlightColor ?? Colors.grey[100]!,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: mainAxisSpacing,
          crossAxisSpacing: crossAxisSpacing,
          childAspectRatio: childAspectRatio,
        ),
        padding: padding,
        itemCount: itemCount,
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.0),
            ),
          );
        },
      ),
    );
  }

  /// 创建骨架屏卡片
  static Widget shimmerCard({
    double? width,
    double height = 200.0,
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(8.0)),
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    Color? baseColor,
    Color? highlightColor,
  }) {
    return Padding(
      padding: padding,
      child: Shimmer.fromColors(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: highlightColor ?? Colors.grey[100]!,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: borderRadius,
          ),
        ),
      ),
    );
  }

  /// 创建骨架屏详情页
  static Widget shimmerDetails({
    Color? baseColor,
    Color? highlightColor,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return Padding(
      padding: padding,
      child: Shimmer.fromColors(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: highlightColor ?? Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部
            Container(
              height: 200.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            const SizedBox(height: 16.0),
            
            // 标题
            Container(
              height: 24.0,
              width: 200.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
            const SizedBox(height: 8.0),
            
            // 副标题
            Container(
              height: 16.0,
              width: 150.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
            const SizedBox(height: 16.0),
            
            // 内容段落
            for (int i = 0; i < 5; i++) ...[
              Container(
                height: 16.0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              const SizedBox(height: 8.0),
            ],
            
            // 短段落
            Container(
              height: 16.0,
              width: 250.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
            const SizedBox(height: 16.0),
            
            // 按钮
            Container(
              height: 40.0,
              width: 120.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 创建骨架屏表单
  static Widget shimmerForm({
    int fieldCount = 4,
    Color? baseColor,
    Color? highlightColor,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
  }) {
    return Padding(
      padding: padding,
      child: Shimmer.fromColors(
        baseColor: baseColor ?? Colors.grey[300]!,
        highlightColor: highlightColor ?? Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < fieldCount; i++) ...[
              // 标签
              Container(
                height: 16.0,
                width: 100.0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              const SizedBox(height: 8.0),
              
              // 输入框
              Container(
                height: 48.0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              const SizedBox(height: 16.0),
            ],
            
            // 提交按钮
            Container(
              height: 48.0,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 私有方法
  static Widget _buildShimmerLoading({
    required double size,
    Color? color,
  }) {
    return Shimmer.fromColors(
      baseColor: color ?? Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}