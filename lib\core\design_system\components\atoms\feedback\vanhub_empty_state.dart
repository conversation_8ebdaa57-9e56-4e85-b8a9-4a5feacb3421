import 'package:flutter/material.dart';
import '../../../foundation/colors/colors.dart';
import '../../../foundation/spacing/spacing.dart';
import '../../../foundation/typography/typography.dart';

/// 空状态类型枚举
enum VanHubEmptyType {
  /// 搜索无结果
  search,
  /// 列表为空
  list,
  /// 收藏为空
  favorites,
  /// 项目为空
  projects,
  /// 材料为空
  materials,
  /// 通用空状态
  generic,
}

/// VanHub空状态组件
/// 提供统一的空状态显示，支持多种空状态类型和自定义操作
class VanHubEmptyState extends StatelessWidget {
  /// 空状态类型
  final VanHubEmptyType type;
  
  /// 标题
  final String? title;
  
  /// 描述信息
  final String? description;
  
  /// 图标
  final IconData? icon;
  
  /// 插图组件
  final Widget? illustration;
  
  /// 操作按钮文本
  final String? actionText;
  
  /// 操作回调
  final VoidCallback? onAction;
  
  /// 次要操作按钮文本
  final String? secondaryActionText;
  
  /// 次要操作回调
  final VoidCallback? onSecondaryAction;
  
  /// 是否显示图标/插图
  final bool showIllustration;
  
  /// 是否居中显示
  final bool centered;
  
  /// 内边距
  final EdgeInsets? padding;
  
  /// 最大宽度
  final double? maxWidth;

  const VanHubEmptyState({
    super.key,
    this.type = VanHubEmptyType.generic,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  });

  /// 创建搜索无结果状态
  const VanHubEmptyState.search({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubEmptyType.search;

  /// 创建列表为空状态
  const VanHubEmptyState.list({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubEmptyType.list;

  /// 创建收藏为空状态
  const VanHubEmptyState.favorites({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubEmptyType.favorites;

  /// 创建项目为空状态
  const VanHubEmptyState.projects({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubEmptyType.projects;

  /// 创建材料为空状态
  const VanHubEmptyState.materials({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.illustration,
    this.actionText,
    this.onAction,
    this.secondaryActionText,
    this.onSecondaryAction,
    this.showIllustration = true,
    this.centered = true,
    this.padding,
    this.maxWidth,
  }) : type = VanHubEmptyType.materials;

  @override
  Widget build(BuildContext context) {
    final content = ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? 400,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIllustration) ...[
            _buildIllustration(),
            VanHubSpacing.vLg,
          ],
          Text(
            _getTitle(),
            style: VanHubTypography.headlineSmall.copyWith(
              color: VanHubColors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          VanHubSpacing.vMd,
          Text(
            _getDescription(),
            style: VanHubTypography.bodyMedium.copyWith(
              color: VanHubColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          VanHubSpacing.vLg,
          _buildActions(context),
        ],
      ),
    );

    final paddedContent = padding != null
        ? Padding(padding: padding!, child: content)
        : content;

    if (centered) {
      return Center(child: paddedContent);
    }

    return paddedContent;
  }

  Widget _buildIllustration() {
    if (illustration != null) {
      return illustration!;
    }

    return Icon(
      _getIcon(),
      size: 80,
      color: VanHubColors.onSurfaceVariant.withOpacity(0.6),
    );
  }

  Widget _buildActions(BuildContext context) {
    final actions = <Widget>[];

    if (onAction != null) {
      actions.add(
        ElevatedButton(
          onPressed: onAction,
          child: Text(actionText ?? '开始'),
        ),
      );
    }

    if (onSecondaryAction != null) {
      actions.add(
        TextButton(
          onPressed: onSecondaryAction,
          child: Text(secondaryActionText ?? '了解更多'),
        ),
      );
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    if (actions.length == 1) {
      return actions.first;
    }

    return Column(
      children: [
        actions.first,
        VanHubSpacing.vSm,
        actions.last,
      ],
    );
  }

  IconData _getIcon() {
    if (icon != null) return icon!;

    switch (type) {
      case VanHubEmptyType.search:
        return Icons.search_off;
      case VanHubEmptyType.list:
        return Icons.list_alt;
      case VanHubEmptyType.favorites:
        return Icons.favorite_border;
      case VanHubEmptyType.projects:
        return Icons.folder_open;
      case VanHubEmptyType.materials:
        return Icons.inventory_2_outlined;
      case VanHubEmptyType.generic:
        return Icons.inbox_outlined;
    }
  }

  String _getTitle() {
    if (title != null) return title!;

    switch (type) {
      case VanHubEmptyType.search:
        return '没有找到相关内容';
      case VanHubEmptyType.list:
        return '列表为空';
      case VanHubEmptyType.favorites:
        return '还没有收藏';
      case VanHubEmptyType.projects:
        return '还没有项目';
      case VanHubEmptyType.materials:
        return '还没有材料';
      case VanHubEmptyType.generic:
        return '暂无内容';
    }
  }

  String _getDescription() {
    if (description != null) return description!;

    switch (type) {
      case VanHubEmptyType.search:
        return '尝试调整搜索条件或关键词';
      case VanHubEmptyType.list:
        return '当前列表中没有任何项目';
      case VanHubEmptyType.favorites:
        return '收藏喜欢的内容，方便下次查看';
      case VanHubEmptyType.projects:
        return '创建您的第一个改装项目';
      case VanHubEmptyType.materials:
        return '添加材料到您的库存中';
      case VanHubEmptyType.generic:
        return '这里还没有任何内容';
    }
  }
}

/// VanHub空状态工厂类
class VanHubEmptyStateFactory {
  VanHubEmptyStateFactory._();

  /// 创建搜索无结果状态
  static Widget searchEmpty({VoidCallback? onClearSearch}) {
    return VanHubEmptyState.search(
      onAction: onClearSearch,
      actionText: '清除搜索',
    );
  }

  /// 创建项目列表为空状态
  static Widget projectsEmpty({VoidCallback? onCreateProject}) {
    return VanHubEmptyState.projects(
      onAction: onCreateProject,
      actionText: '创建项目',
    );
  }

  /// 创建材料库为空状态
  static Widget materialsEmpty({VoidCallback? onAddMaterial}) {
    return VanHubEmptyState.materials(
      onAction: onAddMaterial,
      actionText: '添加材料',
    );
  }

  /// 创建收藏为空状态
  static Widget favoritesEmpty({VoidCallback? onExplore}) {
    return VanHubEmptyState.favorites(
      onAction: onExplore,
      actionText: '去探索',
    );
  }
}
