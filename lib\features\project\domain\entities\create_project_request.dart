import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_project_request.freezed.dart';
part 'create_project_request.g.dart';

@freezed
class CreateProjectRequest with _$CreateProjectRequest {
  const factory CreateProjectRequest({
    required String title,
    required String description,
    required String vehicleType,
    required String vehicleModel,
    String? vehicleBrand,
    @Default(0.0) double budget,
    @Default(true) bool isPublic,
    @Default([]) List<String> tags,
  }) = _CreateProjectRequest;

  factory CreateProjectRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateProjectRequestFromJson(json);
}