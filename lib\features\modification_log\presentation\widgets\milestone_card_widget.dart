import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/milestone.dart';
import '../../domain/entities/enums.dart';

/// 里程碑卡片组件
class MilestoneCardWidget extends ConsumerWidget {
  final Milestone milestone;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStatusChange;
  final bool showActions;
  final bool isCompact;

  const MilestoneCardWidget({
    super.key,
    required this.milestone,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
    this.showActions = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isCompact) {
      return _buildCompactCard(context);
    }
    return _buildFullCard(context);
  }

  /// 构建完整卡片
  Widget _buildFullCard(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getMilestoneColor().withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              _buildHeader(context),
              _buildContent(context),
              if (showActions) _buildActionBar(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建紧凑卡片
  Widget _buildCompactCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getMilestoneColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getMilestoneColor().withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getMilestoneColor(),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getMilestoneIcon(),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    _buildStatusChip(),
                    const SizedBox(width: 8),
                    Text(
                      _formatDate(milestone.date),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (showActions)
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder: (context) => _buildMenuItems(),
              child: const Icon(Icons.more_vert, size: 20),
            ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getMilestoneColor(),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(6)),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getMilestoneIcon(),
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildStatusChip(isWhite: true),
                    const SizedBox(width: 8),
                    _buildPriorityChip(isWhite: true),
                  ],
                ),
              ],
            ),
          ),
          if (showActions)
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder: (context) => _buildMenuItems(),
              icon: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 描述
          if (milestone.description?.isNotEmpty == true) ...[
            Text(
              milestone.description!,
              style: const TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // 日期和进度信息
          _buildDateAndProgressInfo(context),
          
          const SizedBox(height: 16),
          
          // 关联日志
          _buildRelatedLogsInfo(context),
        ],
      ),
    );
  }

  /// 构建日期和进度信息
  Widget _buildDateAndProgressInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildInfoItem(
              icon: Icons.calendar_today,
              label: '目标日期',
              value: _formatDate(milestone.date),
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildInfoItem(
              icon: Icons.access_time,
              label: milestone.isDelayed ? '已延期' : '状态正常',
              value: _getTimeStatus(),
              color: milestone.isDelayed ? Colors.red : Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建关联日志信息
  Widget _buildRelatedLogsInfo(BuildContext context) {
    final logCount = milestone.relatedLogIds.length;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit_note,
            color: Colors.blue.shade700,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              logCount > 0 ? '关联 $logCount 个改装日志' : '暂无关联日志',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (logCount > 0)
            TextButton(
              onPressed: () {
                // TODO: 显示关联日志列表
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                '查看详情',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建操作栏
  Widget _buildActionBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 状态切换按钮
          if (!milestone.isCompleted)
            ElevatedButton.icon(
              onPressed: onStatusChange,
              icon: const Icon(Icons.check_circle, size: 16),
              label: const Text('标记完成'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          
          const Spacer(),
          
          // 编辑按钮
          TextButton.icon(
            onPressed: onEdit,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('编辑'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建状态芯片
  Widget _buildStatusChip({bool isWhite = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isWhite 
            ? Colors.white.withValues(alpha: 0.2)
            : _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isWhite 
              ? Colors.white.withValues(alpha: 0.5)
              : _getStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        milestone.statusDisplayText,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: isWhite ? Colors.white : _getStatusColor(),
        ),
      ),
    );
  }

  /// 构建优先级芯片
  Widget _buildPriorityChip({bool isWhite = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isWhite 
            ? Colors.white.withValues(alpha: 0.2)
            : _getPriorityColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isWhite 
              ? Colors.white.withValues(alpha: 0.5)
              : _getPriorityColor().withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        milestone.priorityDisplayText,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: isWhite ? Colors.white : _getPriorityColor(),
        ),
      ),
    );
  }

  /// 构建菜单项
  List<PopupMenuEntry<String>> _buildMenuItems() {
    return [
      const PopupMenuItem(
        value: 'edit',
        child: Row(
          children: [
            Icon(Icons.edit, size: 16),
            SizedBox(width: 8),
            Text('编辑'),
          ],
        ),
      ),
      if (!milestone.isCompleted)
        const PopupMenuItem(
          value: 'complete',
          child: Row(
            children: [
              Icon(Icons.check_circle, size: 16),
              SizedBox(width: 8),
              Text('标记完成'),
            ],
          ),
        ),
      const PopupMenuItem(
        value: 'delete',
        child: Row(
          children: [
            Icon(Icons.delete, size: 16, color: Colors.red),
            SizedBox(width: 8),
            Text('删除', style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    ];
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        onEdit?.call();
        break;
      case 'complete':
        onStatusChange?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  // 辅助方法
  Color _getMilestoneColor() {
    if (milestone.colorHex != null) {
      try {
        return Color(int.parse('0xFF${milestone.colorHex!.substring(1)}'));
      } catch (e) {
        // 如果颜色解析失败，使用默认颜色
      }
    }
    return _getStatusColor();
  }

  IconData _getMilestoneIcon() {
    if (milestone.iconName != null) {
      final iconMap = {
        'rocket_launch': Icons.rocket_launch,
        'electrical_services': Icons.electrical_services,
        'water_drop': Icons.water_drop,
        'local_fire_department': Icons.local_fire_department,
        'home_repair_service': Icons.home_repair_service,
        'build': Icons.build,
        'check_circle': Icons.check_circle,
        'flag': Icons.flag,
        'star': Icons.star,
        'celebration': Icons.celebration,
      };
      return iconMap[milestone.iconName] ?? Icons.flag;
    }
    return Icons.flag;
  }

  Color _getStatusColor() {
    switch (milestone.status) {
      case MilestoneStatus.planned:
        return Colors.grey;
      case MilestoneStatus.inProgress:
        return Colors.blue;
      case MilestoneStatus.completed:
        return Colors.green;
      case MilestoneStatus.cancelled:
        return Colors.red;
      case MilestoneStatus.overdue:
        return Colors.orange;
    }
  }

  Color _getPriorityColor() {
    switch (milestone.priority) {
      case MilestonePriority.low:
        return Colors.green;
      case MilestonePriority.medium:
        return Colors.orange;
      case MilestonePriority.high:
        return Colors.red;
      case MilestonePriority.critical:
        return Colors.purple;
    }
  }

  String _getTimeStatus() {
    final now = DateTime.now();
    final difference = milestone.date.difference(now).inDays;
    
    if (milestone.isCompleted) {
      return '已完成';
    } else if (difference < 0) {
      return '延期 ${-difference} 天';
    } else if (difference == 0) {
      return '今天到期';
    } else if (difference <= 7) {
      return '还有 $difference 天';
    } else {
      return '还有 $difference 天';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
