import 'package:freezed_annotation/freezed_annotation.dart';
import 'bom_item.dart';

part 'bom_statistics.freezed.dart';
part 'bom_statistics.g.dart';

@freezed
class BomStatistics with _$BomStatistics {
  const factory BomStatistics({
    required double totalBudget,
    required double actualCost,
    required double remainingBudget,
    required int totalItems,
    required int completedItems,
    required int pendingItems,
    required Map<String, double> costByCategory,
    required Map<BomItemStatus, int> itemsByStatus,
    required List<BomItem> overdueItems,
    required double completionPercentage,
    required double averageItemCost,
    BomItem? mostExpensiveItem,
    required double budgetUtilization,
    DateTime? estimatedCompletionDate,
  }) = _BomStatistics;

  factory BomStatistics.fromJson(Map<String, dynamic> json) => _$BomStatisticsFromJson(json);
}

extension BomStatisticsX on BomStatistics {
  /// 是否超预算
  bool get isOverBudget => actualCost > totalBudget;

  /// 超预算金额
  double get overBudgetAmount => isOverBudget ? actualCost - totalBudget : 0.0;

  /// 已花费金额（向后兼容）
  double get spentAmount => actualCost;

  /// 进度状态
  BomProgressStatus get progressStatus {
    if (completionPercentage >= 100) return BomProgressStatus.completed;
    if (completionPercentage >= 75) return BomProgressStatus.nearCompletion;
    if (completionPercentage >= 25) return BomProgressStatus.inProgress;
    return BomProgressStatus.justStarted;
  }

  /// 预算状态
  BomBudgetStatus get budgetStatus {
    if (isOverBudget) return BomBudgetStatus.overBudget;
    if (budgetUtilization >= 90) return BomBudgetStatus.nearLimit;
    if (budgetUtilization >= 50) return BomBudgetStatus.onTrack;
    return BomBudgetStatus.underUtilized;
  }
}

enum BomProgressStatus {
  justStarted,    // 刚开始
  inProgress,     // 进行中
  nearCompletion, // 接近完成
  completed,      // 已完成
}

enum BomBudgetStatus {
  underUtilized,  // 预算使用不足
  onTrack,        // 预算正常
  nearLimit,      // 接近预算上限
  overBudget,     // 超出预算
}

extension BomProgressStatusX on BomProgressStatus {
  String get displayName {
    switch (this) {
      case BomProgressStatus.justStarted:
        return '刚开始';
      case BomProgressStatus.inProgress:
        return '进行中';
      case BomProgressStatus.nearCompletion:
        return '接近完成';
      case BomProgressStatus.completed:
        return '已完成';
    }
  }

  String get color {
    switch (this) {
      case BomProgressStatus.justStarted:
        return '#FF7043'; // 橙红色
      case BomProgressStatus.inProgress:
        return '#42A5F5'; // 蓝色
      case BomProgressStatus.nearCompletion:
        return '#66BB6A'; // 绿色
      case BomProgressStatus.completed:
        return '#4CAF50'; // 深绿色
    }
  }
}

extension BomBudgetStatusX on BomBudgetStatus {
  String get displayName {
    switch (this) {
      case BomBudgetStatus.underUtilized:
        return '预算充足';
      case BomBudgetStatus.onTrack:
        return '预算正常';
      case BomBudgetStatus.nearLimit:
        return '接近上限';
      case BomBudgetStatus.overBudget:
        return '超出预算';
    }
  }

  String get color {
    switch (this) {
      case BomBudgetStatus.underUtilized:
        return '#4CAF50'; // 绿色
      case BomBudgetStatus.onTrack:
        return '#2196F3'; // 蓝色
      case BomBudgetStatus.nearLimit:
        return '#FF9800'; // 橙色
      case BomBudgetStatus.overBudget:
        return '#F44336'; // 红色
    }
  }
}