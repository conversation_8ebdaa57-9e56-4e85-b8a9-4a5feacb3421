# Design Document

## Overview

VanHub核心功能设计专注于实现房车改装项目管理的关键业务逻辑，包括BOM管理、项目复刻、智能联动等核心特性。设计遵循Clean Architecture原则，确保业务逻辑与UI分离，数据流清晰，易于测试和维护。

核心设计理念：
1. **业务驱动** - 以实际改装项目管理需求为导向
2. **数据一致性** - 确保材料库与BOM数据的同步和一致
3. **用户体验** - 简化操作流程，提供智能化辅助功能

## Architecture

### 核心业务架构

```
VanHub核心功能架构
├── BOM管理模块
│   ├── BOM项目CRUD操作
│   ├── 状态流转管理
│   ├── 成本统计计算
│   └── 导入导出功能
├── 项目复刻模块
│   ├── 项目复制逻辑
│   ├── 权限验证
│   ├── 关联关系管理
│   └── 复刻历史追踪
├── 智能联动模块
│   ├── 材料库搜索引擎
│   ├── 智能推荐算法
│   ├── 数据同步机制
│   └── 使用统计分析
└── 协作分享模块
    ├── 权限管理系统
    ├── 实时协作引擎
    ├── 通知推送服务
    └── 分享链接生成
```

### 数据流设计

```mermaid
graph TD
    A[用户操作] --> B[业务用例层]
    B --> C[领域服务层]
    C --> D[数据仓储层]
    
    E[材料库] --> F[智能推荐引擎]
    F --> G[BOM管理]
    G --> H[项目统计]
    
    I[项目复刻] --> J[数据复制服务]
    J --> K[权限验证]
    K --> L[新项目创建]
    
    M[协作编辑] --> N[实时同步]
    N --> O[冲突解决]
    O --> P[状态广播]
```

## Components and Interfaces

### 1. BOM管理核心组件

#### BomItem 领域实体
```dart
@freezed
class BomItem with _$BomItem {
  const factory BomItem({
    required String id,
    required String projectId,
    required String materialId,
    required String name,
    required String specification,
    required String category,
    required int quantity,
    required double unitPrice,
    required double totalPrice,
    required BomItemStatus status,
    String? supplier,
    String? notes,
    DateTime? plannedDate,
    DateTime? actualDate,
    String? imageUrl,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _BomItem;
  
  // 业务方法
  const BomItem._();
  
  double get totalCost => quantity * unitPrice;
  bool get isCompleted => status == BomItemStatus.installed;
  bool get isOverdue => plannedDate != null && 
    DateTime.now().isAfter(plannedDate!) && 
    !isCompleted;
}

enum BomItemStatus {
  pending,    // 待采购
  ordered,    // 已下单
  received,   // 已收货
  installed,  // 已安装
  cancelled,  // 已取消
}
```

#### BOM统计服务
```dart
@freezed
class BomStatistics with _$BomStatistics {
  const factory BomStatistics({
    required double totalBudget,
    required double actualCost,
    required double remainingBudget,
    required int totalItems,
    required int completedItems,
    required int pendingItems,
    required Map<String, double> costByCategory,
    required Map<BomItemStatus, int> itemsByStatus,
    required List<BomItem> overdueItems,
    required double completionPercentage,
  }) = _BomStatistics;
}

abstract class BomStatisticsService {
  BomStatistics calculateStatistics(List<BomItem> bomItems);
  Map<String, double> calculateCostTrend(List<BomItem> bomItems);
  List<BomItem> findOverdueItems(List<BomItem> bomItems);
  double calculateCompletionPercentage(List<BomItem> bomItems);
}
```

### 2. 项目复刻核心组件

#### 项目复刻服务
```dart
@freezed
class ForkRequest with _$ForkRequest {
  const factory ForkRequest({
    required String sourceProjectId,
    required String newProjectTitle,
    String? description,
    @Default(true) bool copyBomItems,
    @Default(true) bool copySystems,
    @Default(false) bool copyImages,
  }) = _ForkRequest;
}

@freezed
class ForkResult with _$ForkResult {
  const factory ForkResult({
    required String newProjectId,
    required String sourceProjectId,
    required int copiedSystems,
    required int copiedBomItems,
    required DateTime forkedAt,
  }) = _ForkResult;
}

abstract class ProjectForkService {
  Future<Either<Failure, ForkResult>> forkProject(ForkRequest request);
  Future<Either<Failure, List<Project>>> getForkHistory(String projectId);
  Future<Either<Failure, Project?>> getSourceProject(String forkedProjectId);
}
```

#### 复刻权限验证
```dart
abstract class ForkPermissionService {
  Future<Either<Failure, bool>> canForkProject(String projectId, String userId);
  Future<Either<Failure, bool>> isProjectPublic(String projectId);
  Future<Either<Failure, bool>> hasUserAccess(String projectId, String userId);
}
```

### 3. 智能联动核心组件

#### 材料推荐引擎
```dart
@freezed
class MaterialRecommendation with _$MaterialRecommendation {
  const factory MaterialRecommendation({
    required Material material,
    required double relevanceScore,
    required String reason,
    required int usageCount,
    double? averagePrice,
    List<String>? similarProjects,
  }) = _MaterialRecommendation;
}

abstract class MaterialRecommendationService {
  Future<Either<Failure, List<MaterialRecommendation>>> 
    recommendForSystem(String systemType, String projectId);
  
  Future<Either<Failure, List<MaterialRecommendation>>> 
    recommendForProject(String projectId);
    
  Future<Either<Failure, List<Material>>> 
    searchMaterials(String query, {String? category});
}
```

#### 数据同步服务
```dart
@freezed
class SyncOperation with _$SyncOperation {
  const factory SyncOperation({
    required String id,
    required SyncOperationType type,
    required String sourceId,
    required String targetId,
    required Map<String, dynamic> data,
    required DateTime timestamp,
    @Default(SyncStatus.pending) SyncStatus status,
  }) = _SyncOperation;
}

enum SyncOperationType {
  materialToBom,
  bomToMaterial,
  priceUpdate,
  statusUpdate,
}

enum SyncStatus {
  pending,
  completed,
  failed,
  cancelled,
}

abstract class DataSyncService {
  Future<Either<Failure, void>> syncMaterialToBom(String materialId, String bomItemId);
  Future<Either<Failure, void>> syncBomToMaterial(String bomItemId);
  Future<Either<Failure, void>> syncPriceUpdates(String materialId);
  Stream<SyncOperation> watchSyncOperations();
}
```

### 4. 协作分享核心组件

#### 项目协作管理
```dart
@freezed
class ProjectCollaborator with _$ProjectCollaborator {
  const factory ProjectCollaborator({
    required String userId,
    required String projectId,
    required CollaboratorRole role,
    required DateTime invitedAt,
    DateTime? acceptedAt,
    @Default(CollaboratorStatus.pending) CollaboratorStatus status,
  }) = _ProjectCollaborator;
}

enum CollaboratorRole {
  viewer,     // 只读
  editor,     // 编辑
  admin,      // 管理员
}

enum CollaboratorStatus {
  pending,    // 待接受
  active,     // 活跃
  inactive,   // 非活跃
}

abstract class CollaborationService {
  Future<Either<Failure, void>> inviteCollaborator(
    String projectId, String userEmail, CollaboratorRole role);
  
  Future<Either<Failure, void>> updateCollaboratorRole(
    String projectId, String userId, CollaboratorRole newRole);
    
  Future<Either<Failure, List<ProjectCollaborator>>> 
    getProjectCollaborators(String projectId);
    
  Future<Either<Failure, bool>> hasPermission(
    String projectId, String userId, String action);
}
```

#### 实时协作引擎
```dart
@freezed
class CollaborationEvent with _$CollaborationEvent {
  const factory CollaborationEvent({
    required String id,
    required String projectId,
    required String userId,
    required String userName,
    required CollaborationEventType type,
    required Map<String, dynamic> data,
    required DateTime timestamp,
  }) = _CollaborationEvent;
}

enum CollaborationEventType {
  bomItemAdded,
  bomItemUpdated,
  bomItemDeleted,
  systemAdded,
  systemUpdated,
  projectUpdated,
  userJoined,
  userLeft,
}

abstract class RealtimeCollaborationService {
  Stream<CollaborationEvent> watchProjectEvents(String projectId);
  Future<Either<Failure, void>> broadcastEvent(CollaborationEvent event);
  Future<Either<Failure, void>> resolveConflict(String projectId, Map<String, dynamic> conflictData);
}
```

## Data Models

### 核心领域模型扩展

```dart
// 扩展现有Project模型
extension ProjectExtensions on Project {
  bool get isFork => sourceProjectId != null;
  bool get isPublic => visibility == ProjectVisibility.public;
  bool get allowsFork => forkSettings.allowFork;
  
  double get budgetUtilization => 
    totalBudget > 0 ? (actualCost / totalBudget) : 0.0;
    
  ProjectStatus get calculatedStatus {
    if (completionPercentage >= 100) return ProjectStatus.completed;
    if (completionPercentage > 0) return ProjectStatus.inProgress;
    return ProjectStatus.planning;
  }
}

@freezed
class ForkSettings with _$ForkSettings {
  const factory ForkSettings({
    @Default(true) bool allowFork,
    @Default(true) bool copyBomItems,
    @Default(true) bool copySystems,
    @Default(false) bool copyImages,
    @Default(false) bool requireApproval,
  }) = _ForkSettings;
}

enum ProjectVisibility {
  private,      // 私有
  friendsOnly,  // 好友可见
  public,       // 公开
}

// BOM项目扩展模型
@freezed
class BomItemExtended with _$BomItemExtended {
  const factory BomItemExtended({
    required BomItem bomItem,
    Material? linkedMaterial,
    List<String>? alternativeOptions,
    PriceHistory? priceHistory,
    List<String>? supplierOptions,
  }) = _BomItemExtended;
}

@freezed
class PriceHistory with _$PriceHistory {
  const factory PriceHistory({
    required List<PricePoint> pricePoints,
    required double averagePrice,
    required double lowestPrice,
    required double highestPrice,
    required DateTime lastUpdated,
  }) = _PriceHistory;
}

@freezed
class PricePoint with _$PricePoint {
  const factory PricePoint({
    required double price,
    required DateTime date,
    String? supplier,
    String? source,
  }) = _PricePoint;
}
```

## Error Handling

### 业务错误类型定义

```dart
@freezed
class CoreFailure with _$CoreFailure {
  const factory CoreFailure.bomNotFound({
    required String bomItemId,
  }) = BomNotFoundFailure;
  
  const factory CoreFailure.forkPermissionDenied({
    required String projectId,
    required String reason,
  }) = ForkPermissionDeniedFailure;
  
  const factory CoreFailure.materialNotFound({
    required String materialId,
  }) = MaterialNotFoundFailure;
  
  const factory CoreFailure.syncConflict({
    required String resourceId,
    required String conflictType,
    required Map<String, dynamic> conflictData,
  }) = SyncConflictFailure;
  
  const factory CoreFailure.collaborationError({
    required String operation,
    required String reason,
  }) = CollaborationErrorFailure;
  
  const factory CoreFailure.dataIntegrityError({
    required String entity,
    required String constraint,
  }) = DataIntegrityErrorFailure;
}
```

## Testing Strategy

### 1. 业务逻辑单元测试

```dart
group('BOM Management Tests', () {
  late BomRepository mockBomRepository;
  late MaterialRepository mockMaterialRepository;
  late CreateBomItemUseCase createBomItemUseCase;
  
  setUp(() {
    mockBomRepository = MockBomRepository();
    mockMaterialRepository = MockMaterialRepository();
    createBomItemUseCase = CreateBomItemUseCase(
      bomRepository: mockBomRepository,
      materialRepository: mockMaterialRepository,
    );
  });
  
  test('should create BOM item with material data', () async {
    // Arrange
    final material = Material(id: '1', name: 'Test Material', price: 100.0);
    final request = CreateBomItemRequest(
      projectId: 'project1',
      materialId: '1',
      quantity: 2,
    );
    
    when(() => mockMaterialRepository.getMaterial('1'))
        .thenAnswer((_) async => Right(material));
    when(() => mockBomRepository.createBomItem(any()))
        .thenAnswer((_) async => Right('bomItem1'));
    
    // Act
    final result = await createBomItemUseCase(request);
    
    // Assert
    expect(result.isRight(), true);
    verify(() => mockMaterialRepository.getMaterial('1')).called(1);
    verify(() => mockBomRepository.createBomItem(any())).called(1);
  });
});

group('Project Fork Tests', () {
  late ProjectRepository mockProjectRepository;
  late ForkProjectUseCase forkProjectUseCase;
  
  test('should fork project with all systems and BOM items', () async {
    // Test implementation
  });
  
  test('should maintain source project reference', () async {
    // Test implementation
  });
});
```

### 2. 集成测试策略

```dart
group('Material-BOM Integration Tests', () {
  testWidgets('should sync material changes to BOM items', (tester) async {
    // 1. Create a project with BOM items linked to materials
    // 2. Update material price in material library
    // 3. Verify BOM items receive price update notification
    // 4. Verify user can choose to sync or keep current price
  });
  
  testWidgets('should recommend materials based on project type', (tester) async {
    // 1. Create a project with specific vehicle type and systems
    // 2. Add BOM items to different systems
    // 3. Verify material recommendations are relevant
    // 4. Verify recommendation scores are calculated correctly
  });
});

group('Collaboration Integration Tests', () {
  testWidgets('should handle concurrent BOM edits', (tester) async {
    // 1. Simulate two users editing same BOM item
    // 2. Verify conflict detection and resolution
    // 3. Verify all collaborators receive updates
  });
});
```

### 3. 性能测试

```dart
group('Performance Tests', () {
  test('should handle large BOM lists efficiently', () async {
    // Create project with 1000+ BOM items
    // Measure loading and calculation performance
    // Verify memory usage stays within limits
  });
  
  test('should sync data efficiently', () async {
    // Test bulk sync operations
    // Measure network usage and response times
    // Verify incremental sync works correctly
  });
});
```

## 实施优先级

### 第一阶段：核心BOM功能
1. **BOM CRUD操作** - 创建、读取、更新、删除BOM项目
2. **状态管理** - BOM项目状态流转和进度跟踪
3. **成本计算** - 自动计算总成本和分类统计
4. **材料库联动** - 从材料库添加物料到BOM

### 第二阶段：项目复刻和分享
1. **项目复刻** - 完整的项目复制功能
2. **权限管理** - 复刻权限验证和控制
3. **分享功能** - 项目分享链接和可见性设置
4. **基础协作** - 邀请协作者和权限管理

### 第三阶段：智能化和高级功能
1. **智能推荐** - 基于项目类型的材料推荐
2. **数据同步** - 材料库与BOM的双向同步
3. **实时协作** - 多用户实时编辑和冲突解决
4. **高级统计** - 项目分析和趋势预测

### 第四阶段：移动端和扩展功能
1. **移动端优化** - 响应式设计和触控优化
2. **离线支持** - 离线数据缓存和同步
3. **导入导出** - 多格式数据导入导出
4. **通知系统** - 智能提醒和通知推送

这个设计文档为VanHub的核心功能提供了完整的技术架构和实施指南，确保功能的完整性、可维护性和用户体验。