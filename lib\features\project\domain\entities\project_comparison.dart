/// 项目比较实体
class ProjectComparison {
  final String projectId;
  final double progressVsAverage;
  final double costVsAverage;
  final double timeVsAverage;
  final ComparisonResult overallComparison;
  final List<ComparisonMetric> metrics;
  final DateTime lastUpdated;

  const ProjectComparison({
    required this.projectId,
    required this.progressVsAverage,
    required this.costVsAverage,
    required this.timeVsAverage,
    required this.overallComparison,
    required this.metrics,
    required this.lastUpdated,
  });

  factory ProjectComparison.fromJson(Map<String, dynamic> json) {
    return ProjectComparison(
      projectId: json['project_id'],
      progressVsAverage: (json['progress_vs_average'] as num).toDouble(),
      costVsAverage: (json['cost_vs_average'] as num).toDouble(),
      timeVsAverage: (json['time_vs_average'] as num).toDouble(),
      overallComparison: ComparisonResult.values.firstWhere(
        (result) => result.name == json['overall_comparison'],
        orElse: () => ComparisonResult.average,
      ),
      metrics: (json['metrics'] as List<dynamic>)
          .map((item) => ComparisonMetric.fromJson(item))
          .toList(),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'project_id': projectId,
      'progress_vs_average': progressVsAverage,
      'cost_vs_average': costVsAverage,
      'time_vs_average': timeVsAverage,
      'overall_comparison': overallComparison.name,
      'metrics': metrics.map((metric) => metric.toJson()).toList(),
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// 比较结果枚举
enum ComparisonResult {
  muchBetter,  // 远超平均
  better,      // 优于平均
  average,     // 接近平均
  worse,       // 低于平均
  muchWorse,   // 远低于平均
}

/// 比较指标
class ComparisonMetric {
  final String name;
  final double value;
  final double averageValue;
  final double difference;
  final ComparisonResult result;

  const ComparisonMetric({
    required this.name,
    required this.value,
    required this.averageValue,
    required this.difference,
    required this.result,
  });

  factory ComparisonMetric.fromJson(Map<String, dynamic> json) {
    return ComparisonMetric(
      name: json['name'],
      value: (json['value'] as num).toDouble(),
      averageValue: (json['average_value'] as num).toDouble(),
      difference: (json['difference'] as num).toDouble(),
      result: ComparisonResult.values.firstWhere(
        (result) => result.name == json['result'],
        orElse: () => ComparisonResult.average,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'average_value': averageValue,
      'difference': difference,
      'result': result.name,
    };
  }
}