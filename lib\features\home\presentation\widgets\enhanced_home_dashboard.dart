import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../dashboard/presentation/widgets/project_overview_card.dart';
import '../../../dashboard/presentation/widgets/cost_analysis_chart.dart';
import '../../../project/presentation/pages/test_create_project_page.dart';
import '../../../project/presentation/widgets/create_project_dialog_widget.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../../../core/di/injection_container.dart';

/// 增强版首页仪表盘
/// 集成数据可视化功能
class EnhancedHomeDashboard extends ConsumerStatefulWidget {
  const EnhancedHomeDashboard({Key? key}) : super(key: key);

  @override
  ConsumerState<EnhancedHomeDashboard> createState() => _EnhancedHomeDashboardState();
}

class _EnhancedHomeDashboardState extends ConsumerState<EnhancedHomeDashboard> {
  String? _selectedProjectId;

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    
    if (currentUserId == null) {
      return _buildGuestDashboard();
    }

    return _buildUserDashboard(currentUserId);
  }

  Widget _buildGuestDashboard() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VanHub改装宝'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.dashboard, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '欢迎使用VanHub改装宝',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              '请登录以查看您的项目仪表盘',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDashboard(String userId) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('项目仪表盘'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // 刷新数据
              setState(() {
                _selectedProjectId = null;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目选择器
            _buildProjectSelector(userId),
            const SizedBox(height: 20),

            // 数据可视化区域
            if (_selectedProjectId != null) ...[
              // 项目概览卡片
              ProjectOverviewCard(projectId: _selectedProjectId!),
              const SizedBox(height: 20),

              // 成本分析图表
              CostAnalysisChart(projectId: _selectedProjectId!),
              const SizedBox(height: 20),

              // 快速操作区域
              _buildQuickActions(),
            ] else ...[
              _buildNoProjectSelected(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectSelector(String userId) {
    // 临时移除项目加载逻辑
    // final projectsAsync = ref.watch(projectRepositoryProvider).getProjectsByUserId(userId);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.folder, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '选择项目',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        _showCreateProjectDialog(context);
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('新建项目'),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TestCreateProjectPage(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.bug_report),
                      label: const Text('测试'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 临时简化版本 - 手动输入项目ID
            TextFormField(
              decoration: const InputDecoration(
                labelText: '输入项目ID (临时)',
                border: OutlineInputBorder(),
                hintText: '请输入要查看的项目ID',
              ),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    _selectedProjectId = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoProjectSelected() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(40),
        child: const Column(
          children: [
            Icon(Icons.analytics, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '选择项目查看数据',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '请在上方选择一个项目来查看详细的数据可视化分析',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.flash_on, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  '快速操作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 3,
              children: [
                _buildQuickActionButton(
                  icon: Icons.edit_note,
                  label: '添加日志',
                  color: Colors.blue,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('添加改装日志功能待实现')),
                    );
                  },
                ),
                _buildQuickActionButton(
                  icon: Icons.inventory,
                  label: '管理BOM',
                  color: Colors.green,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('BOM管理功能待实现')),
                    );
                  },
                ),
                _buildQuickActionButton(
                  icon: Icons.timeline,
                  label: '查看时间轴',
                  color: Colors.purple,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('时间轴功能待实现')),
                    );
                  },
                ),
                _buildQuickActionButton(
                  icon: Icons.library_books,
                  label: '材料库',
                  color: Colors.teal,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('材料库功能待实现')),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示创建项目对话框
  void _showCreateProjectDialog(BuildContext context) {
    // 调试：检查用户认证状态
    final currentUserId = ref.read(currentUserIdProvider);
    debugPrint('🔍 调试：当前用户ID = $currentUserId');

    if (currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先登录后再创建项目'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    debugPrint('🔍 调试：准备显示创建项目对话框');
    showDialog(
      context: context,
      barrierDismissible: false, // 防止意外关闭
      builder: (context) => const CreateProjectDialogWidget(),
    ).then((result) {
      debugPrint('🔍 调试：对话框返回结果 = $result');
      // 如果创建成功，显示成功消息并刷新项目列表
      if (result != null) {
        debugPrint('🔍 调试：项目创建成功，项目ID = ${result.id}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('项目 "${result.title}" 创建成功！'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                // 设置选中的项目ID
                setState(() {
                  _selectedProjectId = result.id;
                });
              },
            ),
          ),
        );
      } else {
        debugPrint('🔍 调试：对话框被取消或返回null');
      }
    }).catchError((error) {
      debugPrint('🔍 调试：对话框发生错误 = $error');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('创建项目失败: $error'),
          backgroundColor: Colors.red,
        ),
      );
    });
  }
}
