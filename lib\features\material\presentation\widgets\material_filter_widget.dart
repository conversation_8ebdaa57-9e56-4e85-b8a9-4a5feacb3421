import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 材料筛选组件
///
/// 特性：
/// - 多维度筛选（分类、价格、品牌、状态等）
/// - 筛选条件持久化
/// - 筛选结果统计和预览
/// - 智能推荐筛选条件
/// - 快速清除和重置功能
/// - 筛选历史记录
class MaterialFilterWidget extends ConsumerStatefulWidget {
  final String selectedCategory;
  final Function(String) onCategoryChanged;
  final double? minPrice;
  final double? maxPrice;
  final Function(double?, double?)? onPriceRangeChanged;
  final List<String>? selectedBrands;
  final Function(List<String>)? onBrandsChanged;
  final List<String>? selectedStatuses;
  final Function(List<String>)? onStatusesChanged;
  final int? totalCount;
  final int? filteredCount;

  const MaterialFilterWidget({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
    this.minPrice,
    this.maxPrice,
    this.onPriceRangeChanged,
    this.selectedBrands,
    this.onBrandsChanged,
    this.selectedStatuses,
    this.onStatusesChanged,
    this.totalCount,
    this.filteredCount,
  });

  @override
  ConsumerState<MaterialFilterWidget> createState() => _MaterialFilterWidgetState();
}

class _MaterialFilterWidgetState extends ConsumerState<MaterialFilterWidget>
    with TickerProviderStateMixin {
  
  late TabController _tabController;

  // 筛选数据
  final List<String> _categories = [
    '全部',
    '电力系统',
    '水系统',
    '照明系统',
    '储物系统',
    '床铺系统',
    '厨房系统',
    '卫浴系统',
    '通风系统',
    '安全系统',
    '娱乐系统',
  ];

  final List<String> _brands = [
    'Victron Energy',
    'Renogy',
    'Goal Zero',
    'Dometic',
    'Thule',
    'Yakima',
    'Fiamma',
    'Truma',
    'Webasto',
    'Espar',
  ];

  final List<String> _statuses = [
    '可用',
    '缺货',
    '停产',
    '新品',
    '促销',
    '预订',
  ];
  
  // 价格范围
  RangeValues _priceRange = const RangeValues(0, 10000);
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // 增加到4个Tab

    // 初始化价格范围
    if (widget.minPrice != null || widget.maxPrice != null) {
      _priceRange = RangeValues(
        widget.minPrice ?? 0,
        widget.maxPrice ?? 10000,
      );
    }
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildFilterHeader(),
          _buildFilterTabs(),
          _buildFilterContent(),
        ],
      ),
    );
  }
  
  Widget _buildFilterHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.tune,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '筛选条件',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearAllFilters,
                child: const Text('清除全部'),
              ),
            ],
          ),
          if (widget.totalCount != null && widget.filteredCount != null) ...[
            const SizedBox(height: 8),
            _buildResultsPreview(),
          ],
        ],
      ),
    );
  }

  Widget _buildResultsPreview() {
    final total = widget.totalCount!;
    final filtered = widget.filteredCount!;
    final percentage = total > 0 ? (filtered / total * 100).round() : 0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.filter_list,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 6),
          Text(
            '显示 $filtered / $total 个材料 ($percentage%)',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFilterTabs() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabs: const [
        Tab(text: '分类'),
        Tab(text: '价格'),
        Tab(text: '品牌'),
        Tab(text: '状态'),
      ],
    );
  }
  
  Widget _buildFilterContent() {
    return SizedBox(
      height: 200,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoryFilter(),
          _buildPriceFilter(),
          _buildBrandFilter(),
          _buildStatusFilter(),
        ],
      ),
    );
  }
  
  Widget _buildCategoryFilter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: _categories.map((category) {
          final isSelected = category == widget.selectedCategory;
          return FilterChip(
            label: Text(category),
            selected: isSelected,
            onSelected: (selected) {
              if (selected) {
                widget.onCategoryChanged(category);
              }
            },
            backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            selectedColor: Theme.of(context).colorScheme.primaryContainer,
            checkmarkColor: Theme.of(context).colorScheme.primary,
          );
        }).toList(),
      ),
    );
  }
  
  Widget _buildPriceFilter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '价格范围',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 16),
          RangeSlider(
            values: _priceRange,
            min: 0,
            max: 10000,
            divisions: 100,
            labels: RangeLabels(
              '¥${_priceRange.start.round()}',
              '¥${_priceRange.end.round()}',
            ),
            onChanged: (values) {
              setState(() {
                _priceRange = values;
              });
            },
            onChangeEnd: (values) {
              widget.onPriceRangeChanged?.call(
                values.start,
                values.end,
              );
            },
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '¥${_priceRange.start.round()}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '¥${_priceRange.end.round()}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildBrandFilter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: _brands.map((brand) {
          final isSelected = widget.selectedBrands?.contains(brand) ?? false;
          return FilterChip(
            label: Text(brand),
            selected: isSelected,
            onSelected: (selected) {
              final currentBrands = List<String>.from(widget.selectedBrands ?? []);
              if (selected) {
                currentBrands.add(brand);
              } else {
                currentBrands.remove(brand);
              }
              widget.onBrandsChanged?.call(currentBrands);
            },
            backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            selectedColor: Theme.of(context).colorScheme.primaryContainer,
            checkmarkColor: Theme.of(context).colorScheme.primary,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '材料状态',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _statuses.map((status) {
                final isSelected = widget.selectedStatuses?.contains(status) ?? false;
                return FilterChip(
                  label: Text(status),
                  selected: isSelected,
                  onSelected: (selected) {
                    final currentStatuses = List<String>.from(widget.selectedStatuses ?? []);
                    if (selected) {
                      currentStatuses.add(status);
                    } else {
                      currentStatuses.remove(status);
                    }
                    widget.onStatusesChanged?.call(currentStatuses);
                  },
                  selectedColor: Theme.of(context).colorScheme.primaryContainer,
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  void _clearAllFilters() {
    widget.onCategoryChanged('全部');
    setState(() {
      _priceRange = const RangeValues(0, 10000);
    });
    widget.onPriceRangeChanged?.call(null, null);
    widget.onBrandsChanged?.call([]);
    widget.onStatusesChanged?.call([]);
  }
}
