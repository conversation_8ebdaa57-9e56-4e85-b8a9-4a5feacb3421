import 'dart:io';

/// 测试运行脚本
void main(List<String> args) async {
  print('🧪 开始运行VanHub测试套件...\n');

  // 解析命令行参数
  final testType = args.isNotEmpty ? args[0] : 'all';
  final coverage = args.contains('--coverage');
  final verbose = args.contains('--verbose');

  try {
    switch (testType) {
      case 'unit':
        await runUnitTests(coverage: coverage, verbose: verbose);
        break;
      case 'widget':
        await runWidgetTests(coverage: coverage, verbose: verbose);
        break;
      case 'integration':
        await runIntegrationTests(coverage: coverage, verbose: verbose);
        break;
      case 'performance':
        await runPerformanceTests(coverage: coverage, verbose: verbose);
        break;
      case 'accessibility':
        await runAccessibilityTests(coverage: coverage, verbose: verbose);
        break;
      case 'all':
      default:
        await runAllTests(coverage: coverage, verbose: verbose);
        break;
    }

    print('\n✅ 所有测试完成！');
    
    if (coverage) {
      print('\n📊 生成测试覆盖率报告...');
      await generateCoverageReport();
    }

  } catch (e) {
    print('\n❌ 测试运行失败: $e');
    exit(1);
  }
}

/// 运行单元测试
Future<void> runUnitTests({bool coverage = false, bool verbose = false}) async {
  print('🔬 运行单元测试...');
  
  final args = [
    'test',
    'test/core/',
    if (coverage) '--coverage',
    if (verbose) '--verbose',
  ];

  final result = await Process.run('flutter', args);
  
  if (result.exitCode != 0) {
    throw Exception('单元测试失败:\n${result.stderr}');
  }
  
  print('✅ 单元测试通过');
}

/// 运行Widget测试
Future<void> runWidgetTests({bool coverage = false, bool verbose = false}) async {
  print('🎨 运行Widget测试...');
  
  final args = [
    'test',
    'test/core/design_system/',
    if (coverage) '--coverage',
    if (verbose) '--verbose',
  ];

  final result = await Process.run('flutter', args);
  
  if (result.exitCode != 0) {
    throw Exception('Widget测试失败:\n${result.stderr}');
  }
  
  print('✅ Widget测试通过');
}

/// 运行集成测试
Future<void> runIntegrationTests({bool coverage = false, bool verbose = false}) async {
  print('🔗 运行集成测试...');
  
  final args = [
    'test',
    'test/integration/',
    if (coverage) '--coverage',
    if (verbose) '--verbose',
  ];

  final result = await Process.run('flutter', args);
  
  if (result.exitCode != 0) {
    throw Exception('集成测试失败:\n${result.stderr}');
  }
  
  print('✅ 集成测试通过');
}

/// 运行性能测试
Future<void> runPerformanceTests({bool coverage = false, bool verbose = false}) async {
  print('⚡ 运行性能测试...');
  
  final args = [
    'test',
    'test/core/performance/',
    if (coverage) '--coverage',
    if (verbose) '--verbose',
  ];

  final result = await Process.run('flutter', args);
  
  if (result.exitCode != 0) {
    throw Exception('性能测试失败:\n${result.stderr}');
  }
  
  print('✅ 性能测试通过');
}

/// 运行无障碍测试
Future<void> runAccessibilityTests({bool coverage = false, bool verbose = false}) async {
  print('♿ 运行无障碍测试...');
  
  final args = [
    'test',
    'test/core/accessibility/',
    if (coverage) '--coverage',
    if (verbose) '--verbose',
  ];

  final result = await Process.run('flutter', args);
  
  if (result.exitCode != 0) {
    throw Exception('无障碍测试失败:\n${result.stderr}');
  }
  
  print('✅ 无障碍测试通过');
}

/// 运行所有测试
Future<void> runAllTests({bool coverage = false, bool verbose = false}) async {
  print('🚀 运行所有测试...\n');
  
  await runUnitTests(coverage: coverage, verbose: verbose);
  await runWidgetTests(coverage: coverage, verbose: verbose);
  await runIntegrationTests(coverage: coverage, verbose: verbose);
  await runPerformanceTests(coverage: coverage, verbose: verbose);
  await runAccessibilityTests(coverage: coverage, verbose: verbose);
}

/// 生成覆盖率报告
Future<void> generateCoverageReport() async {
  // 检查是否安装了lcov
  final lcovCheck = await Process.run('which', ['lcov']);
  if (lcovCheck.exitCode != 0) {
    print('⚠️  未找到lcov，跳过HTML报告生成');
    return;
  }

  // 生成HTML报告
  final genHtmlResult = await Process.run('genhtml', [
    'coverage/lcov.info',
    '-o',
    'coverage/html',
    '--title',
    'VanHub测试覆盖率报告',
  ]);

  if (genHtmlResult.exitCode == 0) {
    print('📊 覆盖率报告已生成: coverage/html/index.html');
  } else {
    print('⚠️  生成HTML报告失败: ${genHtmlResult.stderr}');
  }

  // 显示覆盖率摘要
  await showCoverageSummary();
}

/// 显示覆盖率摘要
Future<void> showCoverageSummary() async {
  final file = File('coverage/lcov.info');
  if (!await file.exists()) {
    print('⚠️  未找到覆盖率文件');
    return;
  }

  final content = await file.readAsString();
  final lines = content.split('\n');
  
  int totalLines = 0;
  int coveredLines = 0;
  
  for (final line in lines) {
    if (line.startsWith('LF:')) {
      totalLines += int.parse(line.substring(3));
    } else if (line.startsWith('LH:')) {
      coveredLines += int.parse(line.substring(3));
    }
  }
  
  if (totalLines > 0) {
    final coverage = (coveredLines / totalLines * 100).toStringAsFixed(1);
    print('\n📊 测试覆盖率摘要:');
    print('   总行数: $totalLines');
    print('   覆盖行数: $coveredLines');
    print('   覆盖率: $coverage%');
    
    if (double.parse(coverage) >= 90) {
      print('✅ 覆盖率达标 (≥90%)');
    } else {
      print('⚠️  覆盖率未达标 (<90%)');
    }
  }
}

/// 检查测试环境
Future<void> checkTestEnvironment() async {
  print('🔍 检查测试环境...');
  
  // 检查Flutter版本
  final flutterResult = await Process.run('flutter', ['--version']);
  if (flutterResult.exitCode != 0) {
    throw Exception('Flutter未正确安装');
  }
  
  // 检查依赖
  final pubResult = await Process.run('flutter', ['pub', 'deps']);
  if (pubResult.exitCode != 0) {
    throw Exception('依赖检查失败，请运行 flutter pub get');
  }
  
  print('✅ 测试环境检查通过');
}

/// 清理测试缓存
Future<void> cleanTestCache() async {
  print('🧹 清理测试缓存...');
  
  final directories = [
    'coverage/',
    '.dart_tool/test/',
    'build/test/',
  ];
  
  for (final dir in directories) {
    final directory = Directory(dir);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }
  
  print('✅ 测试缓存已清理');
}