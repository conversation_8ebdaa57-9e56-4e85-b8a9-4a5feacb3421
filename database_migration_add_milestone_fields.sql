-- VanHub改装宝 - 添加里程碑字段到log_entries表
-- 执行日期：2025年1月28日
-- 目的：支持日志驱动的里程碑功能

-- =====================================================
-- 第一步：为log_entries表添加里程碑相关字段
-- =====================================================

-- 添加里程碑标识字段
ALTER TABLE public.log_entries 
ADD COLUMN IF NOT EXISTS is_milestone BOOLEAN DEFAULT FALSE;

-- 添加里程碑图标名称字段
ALTER TABLE public.log_entries 
ADD COLUMN IF NOT EXISTS milestone_icon_name TEXT;

-- 添加里程碑颜色字段
ALTER TABLE public.log_entries 
ADD COLUMN IF NOT EXISTS milestone_color_hex TEXT;

-- 添加里程碑优先级字段
ALTER TABLE public.log_entries 
ADD COLUMN IF NOT EXISTS milestone_priority TEXT CHECK (
    milestone_priority IS NULL OR 
    milestone_priority IN ('low', 'medium', 'high', 'critical')
);

-- =====================================================
-- 第二步：添加索引以提高查询性能
-- =====================================================

-- 为里程碑查询添加索引
CREATE INDEX IF NOT EXISTS idx_log_entries_is_milestone 
ON public.log_entries(is_milestone) 
WHERE is_milestone = TRUE;

-- 为项目里程碑查询添加复合索引
CREATE INDEX IF NOT EXISTS idx_log_entries_project_milestone 
ON public.log_entries(project_id, is_milestone) 
WHERE is_milestone = TRUE;

-- =====================================================
-- 第三步：添加注释说明
-- =====================================================

-- 为新字段添加注释
COMMENT ON COLUMN public.log_entries.is_milestone IS '标识该日志是否为里程碑';
COMMENT ON COLUMN public.log_entries.milestone_icon_name IS '里程碑图标名称（当is_milestone为true时使用）';
COMMENT ON COLUMN public.log_entries.milestone_color_hex IS '里程碑颜色十六进制值（当is_milestone为true时使用）';
COMMENT ON COLUMN public.log_entries.milestone_priority IS '里程碑优先级：low, medium, high, critical';

-- =====================================================
-- 第四步：验证字段添加结果
-- =====================================================

-- 查看log_entries表的完整结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'log_entries'
AND column_name IN ('is_milestone', 'milestone_icon_name', 'milestone_color_hex', 'milestone_priority')
ORDER BY ordinal_position;

-- 查看新创建的索引
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'log_entries' 
AND indexname LIKE '%milestone%';

-- =====================================================
-- 第五步：测试数据插入（可选）
-- =====================================================

-- 测试插入一条里程碑日志（注释掉，仅供参考）
/*
INSERT INTO public.log_entries (
    id,
    project_id,
    system_id,
    title,
    content,
    log_date,
    author_id,
    created_at,
    updated_at,
    status,
    difficulty,
    time_spent_minutes,
    total_cost,
    is_milestone,
    milestone_icon_name,
    milestone_color_hex,
    milestone_priority
) VALUES (
    gen_random_uuid(),
    'test-project-id',
    'test-system-id',
    '测试里程碑日志',
    '这是一个测试里程碑日志的内容',
    NOW(),
    'test-user-id',
    NOW(),
    NOW(),
    'completed',
    'medium',
    120,
    0.0,
    TRUE,
    'flag',
    '#FF9800',
    'high'
);
*/

-- =====================================================
-- 第六步：更新现有数据（如果需要）
-- =====================================================

-- 如果需要将某些现有日志标记为里程碑，可以使用以下查询
-- 例如：将标题包含"完成"的日志标记为里程碑
/*
UPDATE public.log_entries 
SET 
    is_milestone = TRUE,
    milestone_icon_name = 'check_circle',
    milestone_color_hex = '#4CAF50',
    milestone_priority = 'medium',
    updated_at = NOW()
WHERE 
    title ILIKE '%完成%' 
    AND is_milestone = FALSE;
*/

-- =====================================================
-- 第七步：验证迁移成功
-- =====================================================

-- 检查字段是否成功添加
DO $$
DECLARE
    field_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO field_count
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'log_entries'
    AND column_name IN ('is_milestone', 'milestone_icon_name', 'milestone_color_hex', 'milestone_priority');
    
    IF field_count = 4 THEN
        RAISE NOTICE '✅ 里程碑字段迁移成功！所有4个字段都已添加到log_entries表。';
    ELSE
        RAISE NOTICE '❌ 里程碑字段迁移失败！只添加了 % 个字段，期望4个字段。', field_count;
    END IF;
END $$;

-- 显示迁移完成信息
SELECT 
    '🎯 VanHub里程碑功能数据库迁移完成' as status,
    NOW() as completed_at,
    'log_entries表已成功添加里程碑相关字段' as description;
