import 'dart:io';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/log_media.dart';
import '../entities/enums.dart';
import '../repositories/media_repository.dart';

/// 上传媒体用例
class UploadMediaUseCase implements UseCase<LogMedia, UploadMediaParams> {
  final MediaRepository repository;

  UploadMediaUseCase(this.repository);

  @override
  Future<Either<Failure, LogMedia>> call(UploadMediaParams params) async {
    return await repository.uploadMedia(
      file: params.file,
      logId: params.logId,
      type: params.type,
      uploadedBy: params.uploadedBy,
      metadata: params.metadata,
    );
  }
}

/// 上传媒体参数
class UploadMediaParams {
  final File file;
  final String logId;
  final MediaType type;
  final String uploadedBy;
  final MediaMetadata? metadata;

  UploadMediaParams({
    required this.file,
    required this.logId,
    required this.type,
    required this.uploadedBy,
    this.metadata,
  });
}