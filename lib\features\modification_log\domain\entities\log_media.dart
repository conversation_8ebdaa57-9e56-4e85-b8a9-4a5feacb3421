import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'log_media.freezed.dart';
part 'log_media.g.dart';

/// 日志媒体实体
/// 代表日志条目中的多媒体内容
@freezed
class LogMedia with _$LogMedia {
  const factory LogMedia({
    /// 媒体唯一标识
    required String id,
    
    /// 关联的日志ID
    required String logId,
    
    /// 媒体类型
    required MediaType type,
    
    /// 媒体URL
    required String url,
    
    /// 文件名
    required String filename,
    
    /// 媒体说明
    String? caption,
    
    /// 排序顺序
    @Default(0) int sortOrder,
    
    /// 上传时间
    required DateTime uploadedAt,
    
    /// 上传者ID
    required String uploadedBy,
    
    /// 元数据
    Map<String, dynamic>? metadata,
    
    /// 缩略图URL（如果有）
    String? thumbnailUrl,
    
    /// 文件大小（字节）
    int? fileSize,
    
    /// 媒体宽度（像素，适用于图片和视频）
    int? width,
    
    /// 媒体高度（像素，适用于图片和视频）
    int? height,
    
    /// 媒体时长（秒，适用于视频和音频）
    int? duration,
  }) = _LogMedia;

  factory LogMedia.fromJson(Map<String, dynamic> json) =>
      _$LogMediaFromJson(json);
}

/// LogMedia扩展方法
extension LogMediaX on LogMedia {
  /// 获取媒体类型显示文本
  String get typeDisplayText => type.displayName;
  
  /// 获取文件大小显示文本
  String? get fileSizeDisplayText {
    if (fileSize == null) return null;
    
    if (fileSize! < 1024) {
      return '$fileSize B';
    } else if (fileSize! < 1024 * 1024) {
      final kb = (fileSize! / 1024).toStringAsFixed(1);
      return '$kb KB';
    } else if (fileSize! < 1024 * 1024 * 1024) {
      final mb = (fileSize! / (1024 * 1024)).toStringAsFixed(1);
      return '$mb MB';
    } else {
      final gb = (fileSize! / (1024 * 1024 * 1024)).toStringAsFixed(1);
      return '$gb GB';
    }
  }
  
  /// 获取媒体尺寸显示文本
  String? get dimensionsDisplayText {
    if (width == null || height == null) return null;
    return '$width × $height';
  }
  
  /// 获取媒体时长显示文本
  String? get durationDisplayText {
    if (duration == null) return null;
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
  
  /// 是否为图片
  bool get isImage => type == MediaType.image;
  
  /// 是否为视频
  bool get isVideo => type == MediaType.video;
  
  /// 是否为文档
  bool get isDocument => type == MediaType.document;
  
  /// 是否为CAD文件
  bool get isCad => type == MediaType.cad;
  
  /// 是否为全景图
  bool get isPanorama => type == MediaType.panorama;
  
  /// 是否为音频
  bool get isAudio => type == MediaType.audio;
  
  /// 获取文件扩展名
  String? get fileExtension {
    final parts = filename.split('.');
    if (parts.length > 1) {
      return parts.last.toLowerCase();
    }
    return null;
  }
  
  /// 获取预览URL（如果有缩略图则返回缩略图，否则返回原始URL）
  String get previewUrl => thumbnailUrl ?? url;
}