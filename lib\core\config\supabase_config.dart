import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase配置类
/// 提供全局的Supabase客户端实例和配置管理
class SupabaseConfig {
  SupabaseConfig._();

  /// Supabase客户端实例
  static SupabaseClient get client => Supabase.instance.client;

  /// 初始化Supabase
  /// 
  /// [url] Supabase项目URL
  /// [anonKey] Supabase匿名密钥
  /// [debug] 是否启用调试模式
  static Future<void> initialize({
    required String url,
    required String anonKey,
    bool debug = false,
  }) async {
    await Supabase.initialize(
      url: url,
      anonKey: anonKey,
      debug: debug,
    );
  }

  /// 获取当前用户
  static User? get currentUser => client.auth.currentUser;

  /// 获取当前用户ID
  static String? get currentUserId => currentUser?.id;

  /// 检查用户是否已登录
  static bool get isLoggedIn => currentUser != null;

  /// 获取认证状态流
  static Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  /// 登出
  static Future<void> signOut() async {
    await client.auth.signOut();
  }

  /// 获取数据库表引用
  static PostgrestQueryBuilder from(String table) {
    return client.from(table);
  }

  /// 获取存储桶引用
  static SupabaseStorageClient get storage => client.storage;

  /// 获取实时订阅
  static RealtimeChannel channel(String name) {
    return client.channel(name);
  }

  /// 获取RPC调用
  static PostgrestFilterBuilder rpc(String fn, {Map<String, dynamic>? params}) {
    return client.rpc(fn, params: params);
  }

  /// 环境配置
  static const String _prodUrl = 'https://your-project.supabase.co';
  static const String _prodAnonKey = 'your-anon-key';
  
  static const String _devUrl = 'https://your-dev-project.supabase.co';
  static const String _devAnonKey = 'your-dev-anon-key';

  /// 获取环境配置
  static Map<String, String> getEnvironmentConfig({bool isProduction = false}) {
    return {
      'url': isProduction ? _prodUrl : _devUrl,
      'anonKey': isProduction ? _prodAnonKey : _devAnonKey,
    };
  }

  /// 初始化默认配置
  static Future<void> initializeDefault({bool isProduction = false}) async {
    final config = getEnvironmentConfig(isProduction: isProduction);
    await initialize(
      url: config['url']!,
      anonKey: config['anonKey']!,
      debug: !isProduction,
    );
  }
}
