import 'package:flutter/material.dart';

/// VanHub动画配置
/// 定义动画持续时间、缓动曲线等配置
class VanHubAnimations {
  VanHubAnimations._();

  // ============================================================================
  // 动画持续时间 (Animation Durations)
  // ============================================================================
  
  /// 超快动画 (100ms) - 用于微交互
  static const Duration ultraFast = Duration(milliseconds: 100);

  /// 快速动画 (200ms) - 用于简单过渡
  static const Duration fast = Duration(milliseconds: 200);

  /// 中等动画 (300ms) - 用于一般过渡
  static const Duration medium = Duration(milliseconds: 300);

  /// 慢速动画 (500ms) - 用于复杂过渡
  static const Duration slow = Duration(milliseconds: 500);

  /// 超慢动画 (800ms) - 用于特殊效果
  static const Duration ultraSlow = Duration(milliseconds: 800);

  // ============================================================================
  // 缓动曲线 (Easing Curves)
  // ============================================================================
  
  /// 标准缓动 - 用于一般动画
  static const Curve standard = Curves.easeInOut;

  /// 强调缓动 - 用于重要动画
  static const Curve emphasized = Curves.easeInOutCubic;

  /// 减速缓动 - 用于进入动画
  static const Curve decelerate = Curves.easeOut;

  /// 加速缓动 - 用于退出动画
  static const Curve accelerate = Curves.easeIn;

  /// 线性缓动 - 用于匀速动画
  static const Curve linear = Curves.linear;

  /// 弹性缓动 - 用于弹性效果
  static const Curve elastic = Curves.elasticOut;

  /// 反弹缓动 - 用于反弹效果
  static const Curve bounce = Curves.bounceOut;

  /// 自定义缓动曲线
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;

  // ============================================================================
  // 页面过渡动画 (Page Transition Animations)
  // ============================================================================
  
  /// 淡入淡出过渡
  static const Duration pageTransitionDuration = medium;
  static const Curve pageTransitionCurve = standard;

  /// 滑动过渡
  static const Duration slideTransitionDuration = medium;
  static const Curve slideTransitionCurve = emphasized;

  /// 缩放过渡
  static const Duration scaleTransitionDuration = fast;
  static const Curve scaleTransitionCurve = standard;

  // ============================================================================
  // 组件动画配置 (Component Animation Configs)
  // ============================================================================
  
  /// 按钮动画配置
  static const Duration buttonAnimationDuration = ultraFast;
  static const Curve buttonAnimationCurve = standard;

  /// 卡片动画配置
  static const Duration cardAnimationDuration = fast;
  static const Curve cardAnimationCurve = standard;

  /// 模态框动画配置
  static const Duration modalAnimationDuration = medium;
  static const Curve modalAnimationCurve = emphasized;

  /// 抽屉动画配置
  static const Duration drawerAnimationDuration = medium;
  static const Curve drawerAnimationCurve = standard;

  /// 底部表单动画配置
  static const Duration bottomSheetAnimationDuration = medium;
  static const Curve bottomSheetAnimationCurve = emphasized;

  /// 对话框动画配置
  static const Duration dialogAnimationDuration = fast;
  static const Curve dialogAnimationCurve = standard;

  /// 提示条动画配置
  static const Duration snackBarAnimationDuration = fast;
  static const Curve snackBarAnimationCurve = standard;

  /// 工具提示动画配置
  static const Duration tooltipAnimationDuration = ultraFast;
  static const Curve tooltipAnimationCurve = standard;

  /// 列表项动画配置
  static const Duration listItemAnimationDuration = fast;
  static const Curve listItemAnimationCurve = standard;

  /// 展开/折叠动画配置
  static const Duration expansionAnimationDuration = medium;
  static const Curve expansionAnimationCurve = standard;

  /// 加载动画配置
  static const Duration loadingAnimationDuration = slow;
  static const Curve loadingAnimationCurve = linear;

  // ============================================================================
  // 微交互动画 (Micro-interaction Animations)
  // ============================================================================
  
  /// 悬停效果动画
  static const Duration hoverAnimationDuration = ultraFast;
  static const Curve hoverAnimationCurve = standard;

  /// 点击效果动画
  static const Duration tapAnimationDuration = ultraFast;
  static const Curve tapAnimationCurve = standard;

  /// 焦点效果动画
  static const Duration focusAnimationDuration = ultraFast;
  static const Curve focusAnimationCurve = standard;

  /// 选中效果动画
  static const Duration selectionAnimationDuration = fast;
  static const Curve selectionAnimationCurve = standard;

  // ============================================================================
  // 动画构建器 (Animation Builders)
  // ============================================================================
  
  /// 创建淡入淡出动画
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// 创建滑动动画
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: slideTransitionCurve,
      )),
      child: child,
    );
  }

  /// 创建缩放动画
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: scaleTransitionCurve,
      )),
      child: child,
    );
  }

  /// 创建旋转动画
  static Widget rotationTransition({
    required Widget child,
    required Animation<double> animation,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return RotationTransition(
      turns: Tween<double>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: standard,
      )),
      child: child,
    );
  }

  /// 创建尺寸动画
  static Widget sizeTransition({
    required Widget child,
    required Animation<double> animation,
    Axis axis = Axis.vertical,
  }) {
    return SizeTransition(
      sizeFactor: CurvedAnimation(
        parent: animation,
        curve: standard,
      ),
      axis: axis,
      child: child,
    );
  }

  // ============================================================================
  // 页面路由动画 (Page Route Animations)
  // ============================================================================
  
  /// 创建淡入淡出页面路由
  static PageRouteBuilder<T> fadePageRoute<T>({
    required Widget page,
    RouteSettings? settings,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: pageTransitionDuration,
      reverseTransitionDuration: pageTransitionDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: pageTransitionCurve,
          ),
          child: child,
        );
      },
    );
  }

  /// 创建滑动页面路由
  static PageRouteBuilder<T> slidePageRoute<T>({
    required Widget page,
    RouteSettings? settings,
    Offset begin = const Offset(1.0, 0.0),
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: slideTransitionDuration,
      reverseTransitionDuration: slideTransitionDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: begin,
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: slideTransitionCurve,
          )),
          child: child,
        );
      },
    );
  }

  /// 创建缩放页面路由
  static PageRouteBuilder<T> scalePageRoute<T>({
    required Widget page,
    RouteSettings? settings,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: scaleTransitionDuration,
      reverseTransitionDuration: scaleTransitionDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: scaleTransitionCurve,
          )),
          child: child,
        );
      },
    );
  }

  // ============================================================================
  // 动画控制器工厂 (Animation Controller Factory)
  // ============================================================================
  
  /// 创建标准动画控制器
  static AnimationController createController({
    required TickerProvider vsync,
    Duration duration = medium,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }

  /// 创建重复动画控制器
  static AnimationController createRepeatingController({
    required TickerProvider vsync,
    Duration duration = medium,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
    );
    controller.repeat();
    return controller;
  }

  /// 创建反向重复动画控制器
  static AnimationController createReverseRepeatingController({
    required TickerProvider vsync,
    Duration duration = medium,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
    );
    controller.repeat(reverse: true);
    return controller;
  }

  // ============================================================================
  // 动画工具方法 (Animation Utility Methods)
  // ============================================================================
  
  /// 创建曲线动画
  static Animation<double> createCurvedAnimation({
    required Animation<double> parent,
    Curve curve = standard,
    Curve? reverseCurve,
  }) {
    return CurvedAnimation(
      parent: parent,
      curve: curve,
      reverseCurve: reverseCurve,
    );
  }

  /// 创建补间动画
  static Animation<T> createTweenAnimation<T>({
    required Animation<double> parent,
    required T begin,
    required T end,
    Curve curve = standard,
  }) {
    return Tween<T>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: parent,
      curve: curve,
    ));
  }

  /// 创建颜色补间动画
  static Animation<Color?> createColorTweenAnimation({
    required Animation<double> parent,
    required Color begin,
    required Color end,
    Curve curve = standard,
  }) {
    return ColorTween(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: parent,
      curve: curve,
    ));
  }

  /// 延迟执行动画
  static Future<void> delayedAnimation(
    Duration delay,
    VoidCallback callback,
  ) async {
    await Future.delayed(delay);
    callback();
  }

  /// 交错动画间隔
  static Duration getStaggeredDelay(int index, {Duration interval = ultraFast}) {
    return Duration(milliseconds: interval.inMilliseconds * index);
  }
}
