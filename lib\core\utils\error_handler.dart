import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../error/ui_failure.dart';

/// 错误处理工具类
/// 用于将各种异常转换为UIFailure
class ErrorHandler {
  // 私有构造函数，防止实例化
  ErrorHandler._();

  /// 将异常转换为UIFailure
  static UIFailure handleError(dynamic error, [StackTrace? stackTrace]) {
    // 如果已经是UIFailure，直接返回
    if (error is UIFailure) {
      return error;
    }

    // 网络相关错误
    if (error is SocketException) {
      return UIFailure.network(
        message: '网络连接失败',
        details: error.message,
        code: 'socket_exception',
      );
    }

    if (error is HttpException) {
      return UIFailure.network(
        message: 'HTTP请求失败',
        details: error.message,
        code: 'http_exception',
      );
    }

    // 格式相关错误
    if (error is FormatException) {
      return UIFailure.format(
        message: '数据格式错误',
        details: error.message,
        code: 'format_exception',
      );
    }

    // 参数相关错误
    if (error is ArgumentError) {
      return UIFailure.validation(
        field: 'unknown',
        message: '参数错误',
        details: error.message,
        code: 'argument_error',
      );
    }

    // 状态相关错误
    if (error is StateError) {
      return UIFailure.business(
        message: '状态错误',
        details: error.message,
        code: 'state_error',
      );
    }

    // 类型相关错误
    if (error is TypeError) {
      return UIFailure.format(
        message: '类型错误',
        details: error.toString(),
        code: 'type_error',
      );
    }

    // 未实现错误
    if (error is UnimplementedError) {
      return UIFailure.business(
        message: '功能暂未实现',
        details: error.message,
        code: 'unimplemented_error',
      );
    }

    // 超时错误
    if (error is TimeoutException) {
      return UIFailure.timeout(
        message: '操作超时',
        details: error.message,
        code: 'timeout_exception',
      );
    }

    // 其他异常
    if (error is Exception) {
      return UIFailure.unknown(
        message: '发生了未知错误',
        details: error.toString(),
        code: 'unknown_exception',
      );
    }

    // 错误对象
    if (error is Error) {
      return UIFailure.unknown(
        message: '系统错误',
        details: error.toString(),
        code: 'system_error',
      );
    }

    // 字符串错误
    if (error is String) {
      return UIFailure.unknown(
        message: error,
        code: 'string_error',
      );
    }

    // 完全未知的错误
    return UIFailure.unknown(
      message: '发生了未知错误',
      details: error?.toString(),
      code: 'completely_unknown',
    );
  }

  /// 处理HTTP状态码错误
  static UIFailure handleHttpError(int statusCode, [String? message]) {
    switch (statusCode) {
      case 400:
        return UIFailure.validation(
          field: 'request',
          message: message ?? '请求参数有误',
          code: 'bad_request',
        );
      case 401:
        return UIFailure.authentication(
          message: message ?? '身份验证失败',
          code: 'unauthorized',
        );
      case 403:
        return UIFailure.permission(
          action: 'access',
          message: message ?? '没有权限访问',
          code: 'forbidden',
        );
      case 404:
        return UIFailure.business(
          message: message ?? '请求的资源不存在',
          code: 'not_found',
        );
      case 408:
        return UIFailure.timeout(
          message: message ?? '请求超时',
          code: 'request_timeout',
        );
      case 422:
        return UIFailure.validation(
          field: 'data',
          message: message ?? '数据验证失败',
          code: 'unprocessable_entity',
        );
      case 429:
        return UIFailure.business(
          message: message ?? '请求过于频繁，请稍后重试',
          code: 'too_many_requests',
        );
      case 500:
        return UIFailure.server(
          message: message ?? '服务器内部错误',
          statusCode: statusCode,
          code: 'internal_server_error',
        );
      case 502:
        return UIFailure.server(
          message: message ?? '网关错误',
          statusCode: statusCode,
          code: 'bad_gateway',
        );
      case 503:
        return UIFailure.server(
          message: message ?? '服务暂时不可用',
          statusCode: statusCode,
          code: 'service_unavailable',
        );
      case 504:
        return UIFailure.timeout(
          message: message ?? '网关超时',
          code: 'gateway_timeout',
        );
      default:
        if (statusCode >= 400 && statusCode < 500) {
          return UIFailure.business(
            message: message ?? '客户端错误',
            code: 'client_error',
          );
        } else if (statusCode >= 500) {
          return UIFailure.server(
            message: message ?? '服务器错误',
            statusCode: statusCode,
            code: 'server_error',
          );
        } else {
          return UIFailure.unknown(
            message: message ?? '未知的HTTP状态码: $statusCode',
            code: 'unknown_http_status',
          );
        }
    }
  }

  /// 记录错误日志
  static void logError(
    dynamic error, [
    StackTrace? stackTrace,
    String? context,
  ]) {
    if (kDebugMode) {
      print('=== ERROR LOG ===');
      if (context != null) {
        print('Context: $context');
      }
      print('Error: $error');
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
      print('=================');
    }
    
    // 在生产环境中，这里可以集成崩溃报告服务
    // 如 Firebase Crashlytics, Sentry 等
  }

  /// 处理并记录错误
  static UIFailure handleAndLogError(
    dynamic error, [
    StackTrace? stackTrace,
    String? context,
  ]) {
    logError(error, stackTrace, context);
    return handleError(error, stackTrace);
  }

  /// UI层错误处理方法
  /// 用于在UI中显示错误信息
  static void handle(
    BuildContext context,
    UIFailure failure, {
    String? customMessage,
    bool showSnackBar = true,
    Duration duration = const Duration(seconds: 4),
  }) {
    // 记录错误
    logError(failure, null, 'UI Error Handler');

    // 确定要显示的消息
    final displayMessage = customMessage ?? failure.userFriendlyMessage;

    if (showSnackBar) {
      // 显示SnackBar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                _getErrorIcon(failure),
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  displayMessage,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: _getErrorColor(failure),
          duration: duration,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  /// 获取错误对应的图标
  static IconData _getErrorIcon(UIFailure failure) {
    return failure.when(
      network: (message, details, statusCode, code) => Icons.wifi_off,
      validation: (field, message, details, code) => Icons.error_outline,
      permission: (action, message, details, code) => Icons.lock_outline,
      storage: (operation, message, details, code) => Icons.storage,
      authentication: (message, details, code) => Icons.person_off,
      business: (message, details, code) => Icons.business,
      server: (message, details, statusCode, code) => Icons.dns,
      unknown: (message, details, code) => Icons.help_outline,
      timeout: (message, details, timeoutSeconds, code) => Icons.access_time,
      format: (message, details, expectedFormat, code) => Icons.format_align_left,
    );
  }

  /// 获取错误对应的颜色
  static Color _getErrorColor(UIFailure failure) {
    return failure.when(
      network: (message, details, statusCode, code) => Colors.orange,
      validation: (field, message, details, code) => Colors.amber,
      permission: (action, message, details, code) => Colors.red,
      storage: (operation, message, details, code) => Colors.purple,
      authentication: (message, details, code) => Colors.red,
      business: (message, details, code) => Colors.blue,
      server: (message, details, statusCode, code) => Colors.red,
      unknown: (message, details, code) => Colors.grey,
      timeout: (message, details, timeoutSeconds, code) => Colors.orange,
      format: (message, details, expectedFormat, code) => Colors.amber,
    );
  }
}

/// 超时异常
class TimeoutException implements Exception {
  const TimeoutException(this.message, [this.duration]);

  final String message;
  final Duration? duration;

  @override
  String toString() {
    if (duration != null) {
      return 'TimeoutException: $message (${duration!.inSeconds}s)';
    }
    return 'TimeoutException: $message';
  }
}