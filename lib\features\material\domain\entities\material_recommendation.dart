import 'package:freezed_annotation/freezed_annotation.dart';
import 'material.dart';

part 'material_recommendation.freezed.dart';
part 'material_recommendation.g.dart';

/// 材料推荐类型
enum RecommendationType {
  projectBased,     // 基于项目推荐
  systemBased,      // 基于系统推荐
  similarMaterial,  // 相似材料
  complementary,    // 配套材料
  popular,          // 热门材料
  valueForMoney,    // 性价比高
  recentlyUsed,     // 最近使用
  frequentlyUsed,   // 常用材料
}

extension RecommendationTypeX on RecommendationType {
  String get displayName {
    switch (this) {
      case RecommendationType.projectBased:
        return '项目推荐';
      case RecommendationType.systemBased:
        return '系统推荐';
      case RecommendationType.similarMaterial:
        return '相似材料';
      case RecommendationType.complementary:
        return '配套材料';
      case RecommendationType.popular:
        return '热门材料';
      case RecommendationType.valueForMoney:
        return '高性价比';
      case RecommendationType.recentlyUsed:
        return '最近使用';
      case RecommendationType.frequentlyUsed:
        return '常用材料';
    }
  }
}

/// 材料推荐结果
@freezed
class MaterialRecommendation with _$MaterialRecommendation {
  const factory MaterialRecommendation({
    /// 推荐的材料
    required Material material,
    
    /// 相关度评分（0-100）
    required double relevanceScore,
    
    /// 推荐原因
    required String reason,
    
    /// 推荐类型
    required RecommendationType type,
    
    /// 使用次数
    @Default(0) int usageCount,
    
    /// 平均价格
    double? averagePrice,
    
    /// 相似项目列表
    List<String>? similarProjects,
    
    /// 常用搭配材料ID列表
    List<String>? complementaryMaterialIds,
    
    /// 推荐权重因素
    Map<String, double>? weightFactors,
  }) = _MaterialRecommendation;

  factory MaterialRecommendation.fromJson(Map<String, dynamic> json) => _$MaterialRecommendationFromJson(json);
}

extension MaterialRecommendationX on MaterialRecommendation {
  /// 获取推荐标签颜色
  String get tagColor {
    switch (type) {
      case RecommendationType.projectBased:
        return '#4CAF50'; // 绿色
      case RecommendationType.systemBased:
        return '#2196F3'; // 蓝色
      case RecommendationType.similarMaterial:
        return '#9C27B0'; // 紫色
      case RecommendationType.complementary:
        return '#FF9800'; // 橙色
      case RecommendationType.popular:
        return '#F44336'; // 红色
      case RecommendationType.valueForMoney:
        return '#009688'; // 青色
      case RecommendationType.recentlyUsed:
        return '#607D8B'; // 蓝灰色
      case RecommendationType.frequentlyUsed:
        return '#795548'; // 棕色
    }
  }
  
  /// 获取推荐强度描述
  String get strengthDescription {
    if (relevanceScore >= 90) return '强烈推荐';
    if (relevanceScore >= 70) return '推荐';
    if (relevanceScore >= 50) return '可能相关';
    return '参考选择';
  }
}