/// VanHub品牌颜色系统
/// 
/// 情感化颜色设计，从功能性到情感化的升级
/// 支持动态主题和情感状态映射

import 'package:flutter/material.dart';

/// 情感状态枚举
enum EmotionalState {
  excited,    // 兴奋 - 橙红渐变
  confident,  // 自信 - 蓝色渐变  
  peaceful,   // 平静 - 绿色渐变
  focused,    // 专注 - 紫色渐变
  energetic,  // 活力 - 黄色渐变
  calm,       // 冷静 - 青色渐变
}

/// VanHub品牌颜色系统
class VanHubBrandColors {
  VanHubBrandColors._();

  // ============ 核心品牌色彩 ============
  
  /// 主品牌色 - 专业蓝
  static const Color primary = Color(0xFF2563EB);
  static const Color primaryLight = Color(0xFF3B82F6);
  static const Color primaryDark = Color(0xFF1D4ED8);
  static const Color primaryContainer = Color(0xFFEBF4FF);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onPrimaryContainer = Color(0xFF1E3A8A);

  /// 次要品牌色 - 活力红
  static const Color secondary = Color(0xFFEF4444);
  static const Color secondaryLight = Color(0xFFF87171);
  static const Color secondaryDark = Color(0xFFDC2626);
  static const Color secondaryContainer = Color(0xFFFEF2F2);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSecondaryContainer = Color(0xFF991B1B);

  /// 强调色 - 成功绿
  static const Color accent = Color(0xFF10B981);
  static const Color accentLight = Color(0xFF34D399);
  static const Color accentDark = Color(0xFF059669);
  static const Color accentContainer = Color(0xFFECFDF5);
  static const Color onAccent = Color(0xFFFFFFFF);
  static const Color onAccentContainer = Color(0xFF064E3B);

  // ============ 情感化颜色映射 ============
  
  /// 获取情感化颜色
  static Color getEmotionalColor(EmotionalState state) {
    switch (state) {
      case EmotionalState.excited:
        return const Color(0xFFFF6B35); // 兴奋橙
      case EmotionalState.confident:
        return primary; // 自信蓝
      case EmotionalState.peaceful:
        return accent; // 平静绿
      case EmotionalState.focused:
        return const Color(0xFF8B5CF6); // 专注紫
      case EmotionalState.energetic:
        return const Color(0xFFF59E0B); // 活力黄
      case EmotionalState.calm:
        return const Color(0xFF06B6D4); // 冷静青
    }
  }

  /// 获取情感化渐变
  static LinearGradient getEmotionalGradient(EmotionalState state) {
    switch (state) {
      case EmotionalState.excited:
        return const LinearGradient(
          colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case EmotionalState.confident:
        return const LinearGradient(
          colors: [Color(0xFF2563EB), Color(0xFF3B82F6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case EmotionalState.peaceful:
        return const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF34D399)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case EmotionalState.focused:
        return const LinearGradient(
          colors: [Color(0xFF8B5CF6), Color(0xFFA78BFA)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case EmotionalState.energetic:
        return const LinearGradient(
          colors: [Color(0xFFF59E0B), Color(0xFFFBBF24)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case EmotionalState.calm:
        return const LinearGradient(
          colors: [Color(0xFF06B6D4), Color(0xFF22D3EE)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  // ============ 品牌渐变系统 ============
  
  /// 主品牌渐变
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2563EB), Color(0xFF3B82F6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 次要品牌渐变
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFFEF4444), Color(0xFFF87171)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 强调色渐变
  static const LinearGradient accentGradient = LinearGradient(
    colors: [Color(0xFF10B981), Color(0xFF34D399)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 高端金属渐变
  static const LinearGradient metalGradient = LinearGradient(
    colors: [
      Color(0xFFE5E7EB),
      Color(0xFFF9FAFB),
      Color(0xFFE5E7EB),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    stops: [0.0, 0.5, 1.0],
  );

  /// 玻璃态渐变
  static const LinearGradient glassGradient = LinearGradient(
    colors: [
      Color(0x40FFFFFF),
      Color(0x20FFFFFF),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ============ 动态主题颜色 ============
  
  /// 获取浅色主题颜色方案
  static ColorScheme getLightColorScheme() {
    return ColorScheme.light(
      primary: primary,
      onPrimary: onPrimary,
      primaryContainer: primaryContainer,
      onPrimaryContainer: onPrimaryContainer,
      secondary: secondary,
      onSecondary: onSecondary,
      secondaryContainer: secondaryContainer,
      onSecondaryContainer: onSecondaryContainer,
      tertiary: accent,
      onTertiary: onAccent,
      tertiaryContainer: accentContainer,
      onTertiaryContainer: onAccentContainer,
      surface: const Color(0xFFFFFFFF),
      onSurface: const Color(0xFF1F2937),
      surfaceContainerHighest: const Color(0xFFF9FAFB),
      outline: const Color(0xFFE5E7EB),
    );
  }

  /// 获取深色主题颜色方案
  static ColorScheme getDarkColorScheme() {
    return ColorScheme.dark(
      primary: primaryLight,
      onPrimary: const Color(0xFF1E3A8A),
      primaryContainer: primaryDark,
      onPrimaryContainer: const Color(0xFFEBF4FF),
      secondary: secondaryLight,
      onSecondary: const Color(0xFF991B1B),
      secondaryContainer: secondaryDark,
      onSecondaryContainer: const Color(0xFFFEF2F2),
      tertiary: accentLight,
      onTertiary: const Color(0xFF064E3B),
      tertiaryContainer: accentDark,
      onTertiaryContainer: const Color(0xFFECFDF5),
      surface: const Color(0xFF111827),
      onSurface: const Color(0xFFF9FAFB),
      surfaceContainerHighest: const Color(0xFF1F2937),
      outline: const Color(0xFF374151),
    );
  }

  // ============ 工具方法 ============
  
  /// 创建自定义渐变
  static LinearGradient createCustomGradient({
    required Color startColor,
    required Color endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      colors: [startColor, endColor],
      begin: begin,
      end: end,
      stops: stops,
    );
  }

  /// 创建多色渐变
  static LinearGradient createMultiColorGradient({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      colors: colors,
      begin: begin,
      end: end,
      stops: stops,
    );
  }

  /// 获取颜色的透明度变体
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// 获取颜色的亮度变体
  static Color lighten(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 获取颜色的暗度变体
  static Color darken(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
}
