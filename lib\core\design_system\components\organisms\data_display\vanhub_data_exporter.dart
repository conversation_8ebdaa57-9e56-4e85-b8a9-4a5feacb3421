import 'package:flutter/foundation.dart';
import 'package:excel/excel.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'vanhub_data_models.dart';

/// 数据导出器
class VanHubDataExporter {
  final List<VanHubDataColumn> columns;
  final List<VanHubDataRow> rows;
  final VanHubExportFormat format;
  final String fileName;

  VanHubDataExporter({
    required this.columns,
    required this.rows,
    required this.format,
    required this.fileName,
  });

  /// 导出数据
  Future<void> export() async {
    switch (format) {
      case VanHubExportFormat.csv:
        await _exportToCsv();
        break;
      case VanHubExportFormat.excel:
        await _exportToExcel();
        break;
      case VanHubExportFormat.pdf:
        await _exportToPdf();
        break;
    }
  }

  /// 导出为CSV
  Future<void> _exportToCsv() async {
    final csv = StringBuffer();
    
    // 添加表头
    csv.writeln(columns.map((col) => '"${col.label}"').join(','));
    
    // 添加数据行
    for (final row in rows) {
      final rowData = columns.map((col) {
        final cell = row.cells[col.id];
        final value = cell?.value?.toString() ?? '';
        return '"$value"';
      }).join(',');
      
      csv.writeln(rowData);
    }
    
    // 在实际应用中，这里应该使用平台特定的文件保存方法
    debugPrint('CSV生成成功，大小: ${csv.toString().length} 字符');

    // TODO: 实现平台特定的文件下载逻辑
  }

  /// 导出为Excel
  Future<void> _exportToExcel() async {
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];
    
    // 添加表头
    for (var i = 0; i < columns.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
        ..value = TextCellValue(columns[i].label)
        ..cellStyle = CellStyle(
          bold: true,
          horizontalAlign: HorizontalAlign.Center,
        );
    }
    
    // 添加数据行
    for (var i = 0; i < rows.length; i++) {
      final row = rows[i];
      
      for (var j = 0; j < columns.length; j++) {
        final column = columns[j];
        final cell = row.cells[column.id];
        final value = cell?.value;
        
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1))
          ..value = TextCellValue(value?.toString() ?? '');
      }
    }
    
    // 在实际应用中，这里应该使用平台特定的文件保存方法
    final bytes = excel.encode();
    if (bytes != null) {
      debugPrint('Excel生成成功，大小: ${bytes.length} bytes');

      // TODO: 实现平台特定的文件下载逻辑
    }
  }

  /// 导出为PDF
  Future<void> _exportToPdf() async {
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.Page(
        build: (context) {
          final tableHeaders = columns.map((col) => pw.Text(col.label)).toList();
          
          final tableData = rows.map((row) {
            return columns.map((col) {
              final cell = row.cells[col.id];
              return pw.Text(cell?.value?.toString() ?? '');
            }).toList();
          }).toList();
          
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Header(
                level: 0,
                child: pw.Text('数据导出'),
              ),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: tableHeaders,
                data: tableData,
                border: pw.TableBorder.all(),
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                headerDecoration: const pw.BoxDecoration(
                  color: PdfColors.grey300,
                ),
                cellHeight: 30,
                cellAlignments: Map.fromEntries(
                  columns.asMap().entries.map((entry) {
                    return MapEntry(
                      entry.key,
                      entry.value.numeric
                          ? pw.Alignment.centerRight
                          : pw.Alignment.centerLeft,
                    );
                  }),
                ),
              ),
            ],
          );
        },
      ),
    );
    
    final bytes = await pdf.save();

    // 在实际应用中，这里应该使用平台特定的文件保存方法
    // 例如：path_provider + File.writeAsBytes (移动端)
    // 或者：dart:html (Web端)
    debugPrint('PDF生成成功，大小: ${bytes.length} bytes');

    // TODO: 实现平台特定的文件下载逻辑
  }
}