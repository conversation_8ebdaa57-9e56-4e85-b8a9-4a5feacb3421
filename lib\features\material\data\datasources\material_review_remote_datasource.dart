import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/material_review.dart';
import '../models/material_review_model.dart';

/// 材料评价远程数据源抽象接口
abstract class MaterialReviewRemoteDataSource {
  /// 创建材料评价
  Future<MaterialReviewModel> createReview(MaterialReviewModel review);

  /// 更新材料评价
  Future<MaterialReviewModel> updateReview(MaterialReviewModel review);

  /// 删除材料评价
  Future<void> deleteReview(String reviewId);

  /// 获取单个材料评价
  Future<MaterialReviewModel> getReview(String reviewId);

  /// 获取材料的所有评价
  Future<List<MaterialReviewModel>> getMaterialReviews(
    String materialId, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  });

  /// 获取用户的所有评价
  Future<List<MaterialReviewModel>> getUserReviews(
    String userId, {
    int? limit,
    int? offset,
  });

  /// 获取材料评价摘要
  Future<MaterialReviewSummaryModel> getMaterialReviewSummary(String materialId);

  /// 标记评价为有用
  Future<void> markReviewAsHelpful(String reviewId, String userId);

  /// 取消标记评价为有用
  Future<void> unmarkReviewAsHelpful(String reviewId, String userId);

  /// 点赞评价
  Future<void> likeReview(String reviewId, String userId);

  /// 取消点赞评价
  Future<void> unlikeReview(String reviewId, String userId);

  /// 检查用户是否已评价某材料
  Future<bool> hasUserReviewedMaterial(String materialId, String userId);

  /// 获取热门评价
  Future<List<MaterialReviewModel>> getPopularReviews({
    int limit = 10,
    int timeRange = 30,
  });

  /// 搜索评价
  Future<List<MaterialReviewModel>> searchReviews(
    String query, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  });

  /// 获取评价统计信息
  Future<Map<String, MaterialReviewSummaryModel>> getBatchReviewSummaries(
    List<String> materialIds,
  );

  /// 举报评价
  Future<void> reportReview(String reviewId, String reporterId, String reason);
}

/// 材料评价远程数据源实现
/// 使用Supabase作为后端服务
class MaterialReviewRemoteDataSourceImpl implements MaterialReviewRemoteDataSource {
  final SupabaseClient supabaseClient;
  static const String _tableName = 'material_reviews';

  const MaterialReviewRemoteDataSourceImpl({
    required this.supabaseClient,
  });

  @override
  Future<MaterialReviewModel> createReview(MaterialReviewModel review) async {
    try {
      final response = await supabaseClient
          .from(_tableName)
          .insert(review.toInsertMap())
          .select()
          .single();

      return MaterialReviewModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '创建评价失败: $e');
    }
  }

  @override
  Future<MaterialReviewModel> updateReview(MaterialReviewModel review) async {
    try {
      final response = await supabaseClient
          .from(_tableName)
          .update(review.toUpdateMap())
          .eq('id', review.id)
          .select()
          .single();

      return MaterialReviewModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '更新评价失败: $e');
    }
  }

  @override
  Future<void> deleteReview(String reviewId) async {
    try {
      await supabaseClient
          .from(_tableName)
          .delete()
          .eq('id', reviewId);
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '删除评价失败: $e');
    }
  }

  @override
  Future<MaterialReviewModel> getReview(String reviewId) async {
    try {
      final response = await supabaseClient
          .from(_tableName)
          .select()
          .eq('id', reviewId)
          .single();

      return MaterialReviewModel.fromJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw NotFoundException(message: '评价不存在');
      }
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '获取评价失败: $e');
    }
  }

  @override
  Future<List<MaterialReviewModel>> getMaterialReviews(
    String materialId, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) async {
    try {
      dynamic query = supabaseClient
          .from(_tableName)
          .select()
          .eq('material_id', materialId);

      // 应用过滤条件
      if (filterCriteria != null) {
        query = _applyFilterCriteria(query, filterCriteria);
      }

      // 应用排序
      query = _applySorting(query, filterCriteria?.sortBy ?? ReviewSortType.newest);

      // 应用分页
      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query;
      return (response as List)
          .map((json) => MaterialReviewModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '获取材料评价失败: $e');
    }
  }

  @override
  Future<List<MaterialReviewModel>> getUserReviews(
    String userId, {
    int? limit,
    int? offset,
  }) async {
    try {
      var query = supabaseClient
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query;
      return (response as List)
          .map((json) => MaterialReviewModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '获取用户评价失败: $e');
    }
  }

  @override
  Future<MaterialReviewSummaryModel> getMaterialReviewSummary(String materialId) async {
    try {
      // 使用数据库函数或视图获取统计信息
      // 这里简化实现，实际应该使用数据库聚合查询
      final reviews = await getMaterialReviews(materialId);
      
      if (reviews.isEmpty) {
        return MaterialReviewSummaryModel(
          materialId: materialId,
          totalReviews: 0,
          averageRating: 0.0,
          qualityAverage: 0.0,
          valueAverage: 0.0,
          durabilityAverage: 0.0,
          installationAverage: 0.0,
          ratingDistribution: {},
          verifiedPurchaseCount: 0,
          recommendationScore: 0.0,
        );
      }

      // 计算统计信息
      final totalReviews = reviews.length;
      final averageRating = reviews.map((r) => r.rating).reduce((a, b) => a + b) / totalReviews;
      final qualityAverage = reviews.map((r) => r.qualityRating).reduce((a, b) => a + b) / totalReviews;
      final valueAverage = reviews.map((r) => r.valueRating).reduce((a, b) => a + b) / totalReviews;
      final durabilityAverage = reviews.map((r) => r.durabilityRating).reduce((a, b) => a + b) / totalReviews;
      final installationAverage = reviews.map((r) => r.installationRating).reduce((a, b) => a + b) / totalReviews;
      
      // 计算评分分布
      final Map<String, int> ratingDistribution = {};
      for (int i = 1; i <= 5; i++) {
        ratingDistribution[i.toString()] = reviews.where((r) => r.rating.round() == i).length;
      }

      final verifiedPurchaseCount = reviews.where((r) => r.isVerifiedPurchase).length;
      final latestReviewDate = reviews.map((r) => r.createdAt).reduce((a, b) => a.isAfter(b) ? a : b);

      return MaterialReviewSummaryModel(
        materialId: materialId,
        totalReviews: totalReviews,
        averageRating: averageRating,
        qualityAverage: qualityAverage,
        valueAverage: valueAverage,
        durabilityAverage: durabilityAverage,
        installationAverage: installationAverage,
        ratingDistribution: ratingDistribution,
        verifiedPurchaseCount: verifiedPurchaseCount,
        latestReviewDate: latestReviewDate,
        recommendationScore: _calculateRecommendationScore(averageRating, totalReviews, verifiedPurchaseCount),
      );
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '获取评价摘要失败: $e');
    }
  }

  @override
  Future<void> markReviewAsHelpful(String reviewId, String userId) async {
    try {
      // 使用数组操作添加用户ID到helpful_user_ids
      await supabaseClient.rpc('add_helpful_user', params: {
        'review_id': reviewId,
        'user_id': userId,
      });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '标记有用失败: $e');
    }
  }

  @override
  Future<void> unmarkReviewAsHelpful(String reviewId, String userId) async {
    try {
      // 使用数组操作从helpful_user_ids移除用户ID
      await supabaseClient.rpc('remove_helpful_user', params: {
        'review_id': reviewId,
        'user_id': userId,
      });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '取消标记有用失败: $e');
    }
  }

  @override
  Future<void> likeReview(String reviewId, String userId) async {
    try {
      await supabaseClient.rpc('add_like_user', params: {
        'review_id': reviewId,
        'user_id': userId,
      });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '点赞失败: $e');
    }
  }

  @override
  Future<void> unlikeReview(String reviewId, String userId) async {
    try {
      await supabaseClient.rpc('remove_like_user', params: {
        'review_id': reviewId,
        'user_id': userId,
      });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '取消点赞失败: $e');
    }
  }

  @override
  Future<bool> hasUserReviewedMaterial(String materialId, String userId) async {
    try {
      final response = await supabaseClient
          .from(_tableName)
          .select('id')
          .eq('material_id', materialId)
          .eq('user_id', userId)
          .limit(1);

      return (response as List).isNotEmpty;
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '检查评价状态失败: $e');
    }
  }

  @override
  Future<List<MaterialReviewModel>> getPopularReviews({
    int limit = 10,
    int timeRange = 30,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: timeRange));
      
      final response = await supabaseClient
          .from(_tableName)
          .select()
          .gte('created_at', cutoffDate.toIso8601String())
          .order('helpful_count', ascending: false)
          .limit(limit);

      return (response as List)
          .map((json) => MaterialReviewModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '获取热门评价失败: $e');
    }
  }

  @override
  Future<List<MaterialReviewModel>> searchReviews(
    String query, {
    ReviewFilterCriteria? filterCriteria,
    int? limit,
    int? offset,
  }) async {
    try {
      dynamic supabaseQuery = supabaseClient
          .from(_tableName)
          .select()
          .textSearch('content', query);

      if (filterCriteria != null) {
        supabaseQuery = _applyFilterCriteria(supabaseQuery, filterCriteria);
      }

      supabaseQuery = _applySorting(supabaseQuery, filterCriteria?.sortBy ?? ReviewSortType.newest);

      if (limit != null) {
        supabaseQuery = supabaseQuery.limit(limit);
      }
      if (offset != null) {
        supabaseQuery = supabaseQuery.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await supabaseQuery;
      return (response as List)
          .map((json) => MaterialReviewModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '搜索评价失败: $e');
    }
  }

  @override
  Future<Map<String, MaterialReviewSummaryModel>> getBatchReviewSummaries(
    List<String> materialIds,
  ) async {
    try {
      final Map<String, MaterialReviewSummaryModel> summaries = {};
      
      // 批量获取评价摘要（简化实现，实际应该使用批量查询）
      for (final materialId in materialIds) {
        try {
          final summary = await getMaterialReviewSummary(materialId);
          summaries[materialId] = summary;
        } catch (e) {
          // 如果某个材料的摘要获取失败，跳过但不影响其他材料
          continue;
        }
      }
      
      return summaries;
    } catch (e) {
      throw ServerException(message: '批量获取评价摘要失败: $e');
    }
  }

  @override
  Future<void> reportReview(String reviewId, String reporterId, String reason) async {
    try {
      await supabaseClient.from('review_reports').insert({
        'review_id': reviewId,
        'reporter_id': reporterId,
        'reason': reason,
        'created_at': DateTime.now().toIso8601String(),
      });
    } on PostgrestException catch (e) {
      throw ServerException(message: e.message);
    } catch (e) {
      throw ServerException(message: '举报评价失败: $e');
    }
  }

  /// 应用过滤条件
  dynamic _applyFilterCriteria(
    dynamic query,
    ReviewFilterCriteria criteria,
  ) {
    if (criteria.minRating != null) {
      query = query.gte('rating', criteria.minRating!);
    }
    if (criteria.maxRating != null) {
      query = query.lte('rating', criteria.maxRating!);
    }
    if (criteria.verifiedPurchaseOnly) {
      query = query.eq('is_verified_purchase', true);
    }
    if (criteria.vehicleType != null) {
      query = query.eq('vehicle_type', criteria.vehicleType!);
    }
    if (criteria.systemType != null) {
      query = query.eq('system_type', criteria.systemType!);
    }
    if (criteria.withImagesOnly) {
      query = query.not('image_urls', 'is', null);
    }
    if (criteria.fromDate != null) {
      query = query.gte('created_at', criteria.fromDate!.toIso8601String());
    }
    if (criteria.toDate != null) {
      query = query.lte('created_at', criteria.toDate!.toIso8601String());
    }
    return query;
  }

  /// 应用排序
  dynamic _applySorting(
    dynamic query,
    ReviewSortType sortType,
  ) {
    switch (sortType) {
      case ReviewSortType.newest:
        return query.order('created_at', ascending: false);
      case ReviewSortType.oldest:
        return query.order('created_at', ascending: true);
      case ReviewSortType.ratingHighToLow:
        return query.order('rating', ascending: false);
      case ReviewSortType.ratingLowToHigh:
        return query.order('rating', ascending: true);
      case ReviewSortType.mostHelpful:
        return query.order('helpful_count', ascending: false);
      case ReviewSortType.verifiedFirst:
        return query.order('is_verified_purchase', ascending: false)
                   .order('created_at', ascending: false);
    }
  }

  /// 计算推荐度评分
  double _calculateRecommendationScore(double averageRating, int totalReviews, int verifiedCount) {
    // 基础评分权重
    double score = averageRating * 0.6;
    
    // 评价数量权重（对数增长）
    double reviewCountWeight = (totalReviews > 0) ? (1 + (totalReviews.clamp(1, 100) / 100)) * 0.2 : 0;
    
    // 验证购买权重
    double verifiedWeight = (totalReviews > 0) ? (verifiedCount / totalReviews) * 0.2 : 0;
    
    return (score + reviewCountWeight + verifiedWeight).clamp(0.0, 5.0);
  }
}
