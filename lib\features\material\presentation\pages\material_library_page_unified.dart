import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../widgets/material_search_bar_widget.dart';
import '../widgets/material_filter_widget.dart';
import '../widgets/material_card_unified_widget.dart';
import '../widgets/material_empty_state_widget.dart';
import '../widgets/create_material_dialog_widget.dart';
import '../widgets/smart_filter_suggestions_widget.dart';
import '../widgets/enhanced_material_detail_dialog.dart';

/// 统一的材料库页面
/// 
/// 特性：
/// - 游客和登录用户体验完全一致
/// - 使用VanHub设计系统V2
/// - 响应式布局支持
/// - 智能搜索和筛选
/// - 性能优化和无障碍支持
class MaterialLibraryPageUnified extends ConsumerStatefulWidget {
  final String? userId;
  final bool isGuestMode;

  const MaterialLibraryPageUnified({
    super.key,
    this.userId,
    this.isGuestMode = false,
  });

  @override
  ConsumerState<MaterialLibraryPageUnified> createState() => _MaterialLibraryPageUnifiedState();
}

class _MaterialLibraryPageUnifiedState extends ConsumerState<MaterialLibraryPageUnified>
    with TickerProviderStateMixin {
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // 搜索和筛选状态
  String _searchQuery = '';
  String _selectedCategory = '全部';
  List<String> _selectedBrands = [];
  List<String> _selectedStatuses = [];
  double? _minPrice;
  double? _maxPrice;
  MaterialViewType _viewType = MaterialViewType.list;
  MaterialSortType _sortType = MaterialSortType.newest;

  // 布局状态
  bool _isFilterExpanded = false;
  bool _showSmartSuggestions = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // 启动入场动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final effectiveUserId = widget.userId ?? currentUserId;
    final isGuest = effectiveUserId == null || widget.isGuestMode;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: CustomScrollView(
            slivers: [
              _buildModernAppBar(isGuest),
              _buildSearchSection(),
              _buildFilterSection(),
              _buildMaterialGrid(effectiveUserId, isGuest),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(isGuest),
    );
  }
  
  /// 构建现代化应用栏
  Widget _buildModernAppBar(bool isGuest) {
    return SliverAppBar(
      expandedHeight: context.responsiveValue(
        xs: 100,
        sm: 110,
        md: 120,
        lg: 140,
        xl: 160,
        defaultValue: 120,
      ),
      floating: true,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '材料库',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: context.responsiveValue(
                  xs: 16,
                  sm: 18,
                  md: 20,
                  lg: 22,
                  xl: 24,
                  defaultValue: 20,
                ),
              ),
            ),
            if (!context.isMobile) // 在移动端隐藏副标题以节省空间
              Text(
                isGuest ? '发现优质改装材料' : '管理您的材料收藏',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: context.responsiveValue(
                    xs: 11,
                    sm: 12,
                    md: 13,
                    lg: 14,
                    xl: 15,
                    defaultValue: 13,
                  ),
                ),
              ),
          ],
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      actions: _buildAppBarActions(isGuest),
    );
  }

  /// 构建应用栏操作按钮
  List<Widget> _buildAppBarActions(bool isGuest) {
    final actions = <Widget>[];

    // 智能建议按钮 - 在所有设备上显示
    actions.add(
      IconButton(
        icon: Icon(_showSmartSuggestions
            ? Icons.auto_awesome
            : Icons.auto_awesome_outlined),
        onPressed: () {
          setState(() {
            _showSmartSuggestions = !_showSmartSuggestions;
          });
        },
        tooltip: _showSmartSuggestions ? '隐藏智能建议' : '显示智能建议',
      ),
    );

    // 视图切换按钮 - 在平板和桌面上显示
    if (!context.isMobile) {
      actions.add(
        IconButton(
          icon: Icon(_viewType == MaterialViewType.grid
              ? Icons.view_list_outlined
              : Icons.grid_view_outlined),
          onPressed: _toggleViewType,
          tooltip: _viewType == MaterialViewType.grid ? '列表视图' : '网格视图',
        ),
      );
    }

    // 排序按钮 - 在所有设备上显示
    actions.add(
      IconButton(
        icon: const Icon(Icons.sort_outlined),
        onPressed: _showSortOptions,
        tooltip: '排序选项',
      ),
    );

    // 添加材料按钮 - 仅登录用户显示，在桌面端显示
    if (!isGuest && context.isDesktop) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: _showCreateMaterialDialog,
          tooltip: '添加材料',
        ),
      );
    }

    actions.add(SizedBox(width: context.responsiveValue(
      xs: 4,
      sm: 6,
      md: 8,
      lg: 10,
      xl: 12,
      defaultValue: 8,
    )));

    return actions;
  }

  /// 构建搜索区域
  Widget _buildSearchSection() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(context.responsiveValue(
              xs: 12,
              sm: 14,
              md: 16,
              lg: 18,
              xl: 20,
              defaultValue: 16,
            )),
            child: MaterialSearchBarWidget(
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
              onFilterTap: () {
                setState(() {
                  _isFilterExpanded = !_isFilterExpanded;
                });
              },
            ),
          ),
          // 智能筛选建议
          if (_showSmartSuggestions) _buildSmartSuggestions(),
        ],
      ),
    );
  }

  /// 构建智能筛选建议
  Widget _buildSmartSuggestions() {
    return SmartFilterSuggestionsWidget(
      onApplyFilter: (filters) {
        setState(() {
          // 应用筛选条件
          if (filters.containsKey('category')) {
            _selectedCategory = filters['category'];
          }
          if (filters.containsKey('brands')) {
            _selectedBrands = List<String>.from(filters['brands']);
          }
          if (filters.containsKey('statuses')) {
            _selectedStatuses = List<String>.from(filters['statuses']);
          }
          if (filters.containsKey('priceRange')) {
            final range = filters['priceRange'] as List<int>;
            _minPrice = range[0].toDouble();
            _maxPrice = range[1].toDouble();
          }
          // 自动展开筛选面板以显示应用的筛选条件
          _isFilterExpanded = true;
          // 隐藏智能建议
          _showSmartSuggestions = false;
        });
      },
      currentFilters: {
        'category': _selectedCategory,
        'brands': _selectedBrands,
        'statuses': _selectedStatuses,
        'priceRange': _minPrice != null && _maxPrice != null
            ? [_minPrice!.toInt(), _maxPrice!.toInt()]
            : null,
      },
    );
  }

  /// 构建筛选区域
  Widget _buildFilterSection() {
    return SliverToBoxAdapter(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: _isFilterExpanded ? null : 0,
        child: _isFilterExpanded
            ? MaterialFilterWidget(
                selectedCategory: _selectedCategory,
                onCategoryChanged: (category) {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                selectedBrands: _selectedBrands,
                onBrandsChanged: (brands) {
                  setState(() {
                    _selectedBrands = brands;
                  });
                },
                selectedStatuses: _selectedStatuses,
                onStatusesChanged: (statuses) {
                  setState(() {
                    _selectedStatuses = statuses;
                  });
                },
                minPrice: _minPrice,
                maxPrice: _maxPrice,
                onPriceRangeChanged: (min, max) {
                  setState(() {
                    _minPrice = min;
                    _maxPrice = max;
                  });
                },
                totalCount: 100, // TODO: 从实际数据获取
                filteredCount: 50, // TODO: 从筛选结果获取
              )
            : null,
      ),
    );
  }

  /// 构建材料网格
  Widget _buildMaterialGrid(String? userId, bool isGuest) {
    final materialsAsync = userId != null
        ? ref.watch(userMaterialsProvider(userId))
        : ref.watch(materialsNotifierProvider);

    return materialsAsync.when(
      data: (materials) {
        final filteredMaterials = _filterMaterials(materials);

        if (filteredMaterials.isEmpty) {
          return SliverToBoxAdapter(
            child: MaterialEmptyStateWidget(
              isGuestMode: isGuest,
              isSearchResult: _searchQuery.isNotEmpty || _selectedCategory != '全部',
              searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
              onAddMaterial: _showCreateMaterialDialog,
              onLogin: _showLoginDialog,
              onClearSearch: _clearSearch,
            ),
          );
        }

        return SliverPadding(
          padding: EdgeInsets.all(context.responsiveValue(
            xs: 12,
            sm: 14,
            md: 16,
            lg: 18,
            xl: 20,
            defaultValue: 16,
          )),
          sliver: _viewType == MaterialViewType.grid
              ? _buildGridLayout(filteredMaterials, isGuest)
              : _buildListLayout(filteredMaterials, isGuest),
        );
      },
      loading: () => SliverToBoxAdapter(
        child: _buildLoadingState(),
      ),
      error: (error, stack) => SliverToBoxAdapter(
        child: _buildErrorState(error),
      ),
    );
  }

  /// 构建网格布局
  Widget _buildGridLayout(List<domain.Material> materials, bool isGuest) {
    return SliverGrid(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getResponsiveGridCrossAxisCount(),
        childAspectRatio: context.responsiveValue(
          xs: 0.8,
          sm: 0.75,
          md: 0.7,
          lg: 0.75,
          xl: 0.8,
          defaultValue: 0.75,
        ),
        crossAxisSpacing: context.responsiveValue(
          xs: 8,
          sm: 12,
          md: 16,
          lg: 20,
          xl: 24,
          defaultValue: 16,
        ),
        mainAxisSpacing: context.responsiveValue(
          xs: 8,
          sm: 12,
          md: 16,
          lg: 20,
          xl: 24,
          defaultValue: 16,
        ),
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final material = materials[index];
          return MaterialCardUnifiedWidget(
            material: material,
            isGuestMode: isGuest,
            onTap: () => _showMaterialDetails(material),
            onEdit: () => _showEditMaterialDialog(material),
            onDelete: () => _deleteMaterial(material.id),
            onAddToBom: () => _showAddToBomDialog(material),
            onFavorite: () => _toggleFavorite(material.id),
            onShare: () => _shareMaterial(material),
          );
        },
        childCount: materials.length,
      ),
    );
  }

  /// 构建列表布局
  Widget _buildListLayout(List<domain.Material> materials, bool isGuest) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final material = materials[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: MaterialCardUnifiedWidget(
              material: material,
              isListView: true,
              isGuestMode: isGuest,
              onTap: () => _showMaterialDetails(material),
              onEdit: () => _showEditMaterialDialog(material),
              onDelete: () => _deleteMaterial(material.id),
              onAddToBom: () => _showAddToBomDialog(material),
              onFavorite: () => _toggleFavorite(material.id),
              onShare: () => _shareMaterial(material),
            ),
          );
        },
        childCount: materials.length,
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget? _buildFloatingActionButton(bool isGuest) {
    // 在桌面端不显示浮动按钮，因为已经在应用栏中有添加按钮
    if (context.isDesktop) return null;

    // 游客模式显示登录提示按钮，登录用户显示添加材料按钮
    if (isGuest) {
      return FloatingActionButton.extended(
        onPressed: _showLoginDialog,
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.login),
        label: Text(context.isMobile ? '登录' : '登录以添加材料'),
      );
    }

    return FloatingActionButton.extended(
      onPressed: _showCreateMaterialDialog,
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: Text(context.isMobile ? '添加' : '添加材料'),
    );
  }

  // 辅助方法
  List<domain.Material> _filterMaterials(List<domain.Material> materials) {
    var filtered = materials;

    // 搜索过滤
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((material) {
        final query = _searchQuery.toLowerCase();
        return material.name.toLowerCase().contains(query) ||
               (material.brand?.toLowerCase().contains(query) ?? false) ||
               (material.model?.toLowerCase().contains(query) ?? false) ||
               material.category.toLowerCase().contains(query);
      }).toList();
    }

    // 分类过滤
    if (_selectedCategory != '全部') {
      filtered = filtered.where((material) =>
          material.category == _selectedCategory).toList();
    }

    // 排序
    switch (_sortType) {
      case MaterialSortType.newest:
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case MaterialSortType.oldest:
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case MaterialSortType.priceHigh:
        filtered.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
        break;
      case MaterialSortType.priceLow:
        filtered.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
        break;
      case MaterialSortType.nameAZ:
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case MaterialSortType.nameZA:
        filtered.sort((a, b) => b.name.compareTo(a.name));
        break;
    }

    return filtered;
  }

  /// 获取响应式网格列数
  int _getResponsiveGridCrossAxisCount() {
    return context.responsiveValue(
      xs: 1,  // 小手机：1列
      sm: 2,  // 大手机：2列
      md: 2,  // 平板：2列
      lg: 3,  // 笔记本：3列
      xl: 4,  // 桌面：4列
      defaultValue: 2,
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 400,
      margin: const EdgeInsets.all(16),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Container(
      height: 400,
      margin: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 重新加载
                ref.invalidate(materialsNotifierProvider);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  // 事件处理方法
  void _toggleViewType() {
    setState(() {
      _viewType = _viewType == MaterialViewType.grid
          ? MaterialViewType.list
          : MaterialViewType.grid;
    });
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildSortBottomSheet(),
    );
  }

  Widget _buildSortBottomSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '排序方式',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ...MaterialSortType.values.map((sortType) {
            return ListTile(
              title: Text(_getSortTypeName(sortType)),
              leading: Radio<MaterialSortType>(
                value: sortType,
                groupValue: _sortType,
                onChanged: (value) {
                  setState(() {
                    _sortType = value!;
                  });
                  Navigator.pop(context);
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  String _getSortTypeName(MaterialSortType sortType) {
    switch (sortType) {
      case MaterialSortType.newest:
        return '最新添加';
      case MaterialSortType.oldest:
        return '最早添加';
      case MaterialSortType.priceHigh:
        return '价格从高到低';
      case MaterialSortType.priceLow:
        return '价格从低到高';
      case MaterialSortType.nameAZ:
        return '名称A-Z';
      case MaterialSortType.nameZA:
        return '名称Z-A';
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _selectedCategory = '全部';
    });
  }

  void _showCreateMaterialDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateMaterialDialogWidget(),
    );
  }

  void _showLoginDialog() {
    // TODO: 实现登录对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('登录功能待实现')),
    );
  }

  void _showMaterialDetails(domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => EnhancedMaterialDetailDialog(
        material: material,
        projectId: null, // MaterialLibraryPageUnified没有projectId
      ),
    );
  }

  void _showEditMaterialDialog(domain.Material material) {
    // TODO: 实现编辑材料对话框
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('编辑材料: ${material.name}')),
    );
  }

  void _deleteMaterial(String materialId) {
    // TODO: 实现删除材料功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('删除材料功能待实现')),
    );
  }

  void _showAddToBomDialog(domain.Material material) {
    // TODO: 实现添加到BOM功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('添加到BOM: ${material.name}')),
    );
  }

  void _toggleFavorite(String materialId) {
    // TODO: 实现收藏功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('收藏功能待实现')),
    );
  }

  void _shareMaterial(domain.Material material) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('分享材料: ${material.name}')),
    );
  }
}

// 枚举定义
enum MaterialViewType { grid, list }
enum MaterialSortType { newest, oldest, priceHigh, priceLow, nameAZ, nameZA }
