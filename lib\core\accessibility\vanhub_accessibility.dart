import 'package:flutter/material.dart';

/// VanHub无障碍支持系统
/// 提供WCAG 2.1 AA级别的无障碍支持
class VanHubAccessibility {
  /// 获取语义化标签
  static String getSemanticsLabel(String text, {String? context}) {
    if (context != null) {
      return '$text, $context';
    }
    return text;
  }

  /// 获取语义化提示
  static String getSemanticsHint(String hint) {
    return hint;
  }

  /// 创建无障碍焦点节点
  static FocusNode createAccessibleFocusNode({String? debugLabel}) {
    return FocusNode(debugLabel: debugLabel);
  }

  /// 设置无障碍焦点顺序
  static Widget setFocusOrder({
    required Widget child,
    required int order,
  }) {
    return Focus(
      canRequestFocus: false,
      skipTraversal: false,
      child: child,
    );
  }

  /// 创建无障碍分组
  static Widget createAccessibleGroup({
    required Widget child,
    required String label,
    String? hint,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      container: true,
      explicitChildNodes: true,
      child: child,
    );
  }

  /// 添加无障碍标签
  static Widget addAccessibilityLabel({
    required Widget child,
    required String label,
    String? hint,
    bool excludeSemantics = false,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      excludeSemantics: excludeSemantics,
      child: child,
    );
  }

  /// 创建无障碍按钮
  static Widget createAccessibleButton({
    required Widget child,
    required VoidCallback onPressed,
    required String label,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: true,
      enabled: isEnabled,
      onTap: isEnabled ? onPressed : null,
      child: child,
    );
  }

  /// 创建无障碍图像
  static Widget createAccessibleImage({
    required Widget image,
    required String label,
    String? hint,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      image: true,
      child: image,
    );
  }

  /// 创建无障碍文本字段
  static Widget createAccessibleTextField({
    required Widget textField,
    required String label,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      textField: true,
      enabled: isEnabled,
      child: textField,
    );
  }

  /// 创建无障碍复选框
  static Widget createAccessibleCheckbox({
    required Widget checkbox,
    required String label,
    required bool checked,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      checked: checked,
      enabled: isEnabled,
      child: checkbox,
    );
  }

  /// 创建无障碍单选按钮
  static Widget createAccessibleRadioButton({
    required Widget radio,
    required String label,
    required bool selected,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      selected: selected,
      enabled: isEnabled,
      inMutuallyExclusiveGroup: true,
      child: radio,
    );
  }

  /// 创建无障碍开关
  static Widget createAccessibleSwitch({
    required Widget switchWidget,
    required String label,
    required bool toggled,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      toggled: toggled,
      enabled: isEnabled,
      child: switchWidget,
    );
  }

  /// 创建无障碍滑块
  static Widget createAccessibleSlider({
    required Widget slider,
    required String label,
    required double value,
    required double min,
    required double max,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      value: '${value.toStringAsFixed(0)}，范围从${min.toStringAsFixed(0)}到${max.toStringAsFixed(0)}',
      enabled: isEnabled,
      slider: true,
      child: slider,
    );
  }

  /// 创建无障碍列表项
  static Widget createAccessibleListItem({
    required Widget listItem,
    required String label,
    String? hint,
    bool isSelected = false,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      selected: isSelected,
      enabled: isEnabled,
      child: listItem,
    );
  }

  /// 创建无障碍标题
  static Widget createAccessibleHeader({
    required Widget header,
    required String label,
    String? hint,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      header: true,
      child: header,
    );
  }

  /// 创建无障碍链接
  static Widget createAccessibleLink({
    required Widget link,
    required String label,
    String? hint,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      link: true,
      enabled: isEnabled,
      child: link,
    );
  }

  /// 创建无障碍进度指示器
  static Widget createAccessibleProgressIndicator({
    required Widget progressIndicator,
    required String label,
    String? hint,
    double? value,
    bool isEnabled = true,
  }) {
    String valueText = '';
    if (value != null) {
      valueText = '${(value * 100).toStringAsFixed(0)}%';
    }
    
    return Semantics(
      label: label,
      hint: hint,
      value: valueText,
      enabled: isEnabled,
      child: progressIndicator,
    );
  }
}

/// 无障碍按钮组件
class VanHubAccessibleButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final String semanticLabel;
  final String? semanticHint;

  const VanHubAccessibleButton({
    super.key,
    required this.onPressed,
    required this.child,
    required this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: onPressed != null,
      onTap: onPressed,
      child: ElevatedButton(
        onPressed: onPressed,
        child: child,
      ),
    );
  }
}

/// 无障碍文本字段组件
class VanHubAccessibleTextField extends StatelessWidget {
  final String labelText;
  final String semanticLabel;
  final String? semanticHint;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final bool enabled;

  const VanHubAccessibleTextField({
    super.key,
    required this.labelText,
    required this.semanticLabel,
    this.semanticHint,
    this.controller,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      textField: true,
      enabled: enabled,
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
        ),
        obscureText: obscureText,
        keyboardType: keyboardType,
        validator: validator,
        onChanged: onChanged,
        enabled: enabled,
      ),
    );
  }
}

/// 无障碍列表项组件
class VanHubAccessibleListTile extends StatelessWidget {
  final Widget title;
  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final String semanticLabel;
  final String? semanticHint;
  final bool isSelected;
  final bool enabled;

  const VanHubAccessibleListTile({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    required this.semanticLabel,
    this.semanticHint,
    this.isSelected = false,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      selected: isSelected,
      enabled: enabled,
      child: ListTile(
        title: title,
        subtitle: subtitle,
        leading: leading,
        trailing: trailing,
        onTap: enabled ? onTap : null,
        enabled: enabled,
        selected: isSelected,
      ),
    );
  }
}

/// 无障碍图像组件
class VanHubAccessibleImage extends StatelessWidget {
  final ImageProvider image;
  final String semanticLabel;
  final String? semanticHint;
  final double? width;
  final double? height;
  final BoxFit? fit;

  const VanHubAccessibleImage({
    super.key,
    required this.image,
    required this.semanticLabel,
    this.semanticHint,
    this.width,
    this.height,
    this.fit,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      image: true,
      child: Image(
        image: image,
        width: width,
        height: height,
        fit: fit,
      ),
    );
  }
}

/// 无障碍开关组件
class VanHubAccessibleSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final String semanticLabel;
  final String? semanticHint;
  final bool enabled;

  const VanHubAccessibleSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    required this.semanticLabel,
    this.semanticHint,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      toggled: value,
      enabled: enabled,
      child: Switch(
        value: value,
        onChanged: enabled ? onChanged : null,
      ),
    );
  }
}

/// 无障碍复选框组件
class VanHubAccessibleCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final String semanticLabel;
  final String? semanticHint;
  final bool enabled;

  const VanHubAccessibleCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    required this.semanticLabel,
    this.semanticHint,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      checked: value,
      enabled: enabled,
      child: Checkbox(
        value: value,
        onChanged: enabled ? onChanged : null,
      ),
    );
  }
}
