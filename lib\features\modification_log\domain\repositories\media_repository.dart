import 'dart:io';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/log_media.dart';
import '../entities/enums.dart';

/// 媒体元数据
class MediaMetadata {
  final String? caption;
  final int? sortOrder;
  final Map<String, dynamic>? additionalMetadata;
  
  const MediaMetadata({
    this.caption,
    this.sortOrder,
    this.additionalMetadata,
  });
  
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {};
    
    if (caption != null) result['caption'] = caption;
    if (sortOrder != null) result['sort_order'] = sortOrder;
    if (additionalMetadata != null) result.addAll(additionalMetadata!);
    
    return result;
  }
}

/// 媒体仓库接口
abstract class MediaRepository {
  /// 上传媒体文件
  Future<Either<Failure, LogMedia>> uploadMedia({
    required File file,
    required String logId,
    required MediaType type,
    required String uploadedBy,
    MediaMetadata? metadata,
  });
  
  /// 获取日志关联的所有媒体
  Future<Either<Failure, List<LogMedia>>> getLogMedia(String logId);
  
  /// 获取单个媒体详情
  Future<Either<Failure, LogMedia>> getMedia(String mediaId);
  
  /// 删除媒体
  Future<Either<Failure, void>> deleteMedia(String mediaId);
  
  /// 更新媒体元数据
  Future<Either<Failure, LogMedia>> updateMediaMetadata(String mediaId, MediaMetadata metadata);
  
  /// 批量上传媒体
  Future<Either<Failure, List<LogMedia>>> uploadMultipleMedia({
    required List<File> files,
    required String logId,
    required MediaType type,
    required String uploadedBy,
  });
  
  /// 更新媒体排序
  Future<Either<Failure, List<LogMedia>>> updateMediaOrder(String logId, List<String> orderedMediaIds);
  
  /// 获取用户上传的所有媒体
  Future<Either<Failure, List<LogMedia>>> getUserMedia(String userId, {int? limit, int? offset});
  
  /// 获取项目的所有媒体
  Future<Either<Failure, List<LogMedia>>> getProjectMedia(String projectId, {int? limit, int? offset});
}