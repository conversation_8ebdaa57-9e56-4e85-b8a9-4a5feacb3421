import 'package:flutter/foundation.dart';
import 'dart:async';

/// 性能监控工具
class PerformanceMonitor {
  static final Map<String, Stopwatch> _stopwatches = {};
  static final Map<String, List<int>> _metrics = {};
  static final Map<String, DateTime> _timestamps = {};

  /// 开始性能监控
  static void start(String key) {
    if (!kDebugMode) return;
    
    _stopwatches[key] = Stopwatch()..start();
    _timestamps[key] = DateTime.now();
    
    if (kDebugMode) {
      print('🚀 性能监控开始: $key');
    }
  }

  /// 结束性能监控并记录结果
  static int end(String key) {
    if (!kDebugMode) return 0;
    
    final stopwatch = _stopwatches[key];
    if (stopwatch == null) {
      if (kDebugMode) {
        print('⚠️ 性能监控警告: 未找到监控项 $key');
      }
      return 0;
    }

    stopwatch.stop();
    final duration = stopwatch.elapsedMilliseconds;
    
    // 记录到历史数据
    _metrics.putIfAbsent(key, () => []).add(duration);
    
    // 保持最近100次记录
    if (_metrics[key]!.length > 100) {
      _metrics[key]!.removeAt(0);
    }

    if (kDebugMode) {
      print('⏱️ 性能监控结束: $key - ${duration}ms');
    }

    _stopwatches.remove(key);
    return duration;
  }

  /// 记录单次性能数据
  static void record(String key, int durationMs) {
    if (!kDebugMode) return;
    
    _metrics.putIfAbsent(key, () => []).add(durationMs);
    _timestamps[key] = DateTime.now();
    
    // 保持最近100次记录
    if (_metrics[key]!.length > 100) {
      _metrics[key]!.removeAt(0);
    }

    if (kDebugMode) {
      print('📊 性能记录: $key - ${durationMs}ms');
    }
  }

  /// 获取性能统计信息
  static Map<String, dynamic> getStats(String key) {
    final metrics = _metrics[key];
    if (metrics == null || metrics.isEmpty) {
      return {
        'key': key,
        'count': 0,
        'average': 0,
        'min': 0,
        'max': 0,
        'latest': 0,
        'timestamp': null,
      };
    }

    final sum = metrics.reduce((a, b) => a + b);
    final average = sum / metrics.length;
    final min = metrics.reduce((a, b) => a < b ? a : b);
    final max = metrics.reduce((a, b) => a > b ? a : b);
    final latest = metrics.last;

    return {
      'key': key,
      'count': metrics.length,
      'average': average.round(),
      'min': min,
      'max': max,
      'latest': latest,
      'timestamp': _timestamps[key]?.toIso8601String(),
    };
  }

  /// 获取所有性能统计
  static Map<String, Map<String, dynamic>> getAllStats() {
    final result = <String, Map<String, dynamic>>{};
    
    for (final key in _metrics.keys) {
      result[key] = getStats(key);
    }
    
    return result;
  }

  /// 打印性能报告
  static void printReport() {
    if (!kDebugMode) return;
    
    debugPrint('\n📈 性能监控报告');
    debugPrint('=' * 50);

    final stats = getAllStats();
    if (stats.isEmpty) {
      debugPrint('暂无性能数据');
      return;
    }

    for (final entry in stats.entries) {
      final key = entry.key;
      final data = entry.value;
      
      debugPrint('🔍 $key:');
      debugPrint('  次数: ${data['count']}');
      debugPrint('  平均: ${data['average']}ms');
      debugPrint('  最小: ${data['min']}ms');
      debugPrint('  最大: ${data['max']}ms');
      debugPrint('  最新: ${data['latest']}ms');
      debugPrint('  时间: ${data['timestamp']}');
      debugPrint('');
    }

    debugPrint('=' * 50);
  }

  /// 清除所有性能数据
  static void clear() {
    _stopwatches.clear();
    _metrics.clear();
    _timestamps.clear();
    
    if (kDebugMode) {
      print('🧹 性能监控数据已清除');
    }
  }

  /// 清除特定key的性能数据
  static void clearKey(String key) {
    _stopwatches.remove(key);
    _metrics.remove(key);
    _timestamps.remove(key);
    
    if (kDebugMode) {
      print('🧹 性能监控数据已清除: $key');
    }
  }

  /// 监控异步操作的性能
  static Future<T> monitor<T>(String key, Future<T> Function() operation) async {
    start(key);
    try {
      final result = await operation();
      end(key);
      return result;
    } catch (e) {
      end(key);
      rethrow;
    }
  }

  /// 监控同步操作的性能
  static T monitorSync<T>(String key, T Function() operation) {
    start(key);
    try {
      final result = operation();
      end(key);
      return result;
    } catch (e) {
      end(key);
      rethrow;
    }
  }

  /// 获取性能等级（基于平均响应时间）
  static String getPerformanceGrade(String key) {
    final stats = getStats(key);
    final average = stats['average'] as int;
    
    if (average == 0) return 'N/A';
    if (average < 100) return 'A+ (优秀)';
    if (average < 300) return 'A (良好)';
    if (average < 500) return 'B (一般)';
    if (average < 1000) return 'C (较慢)';
    return 'D (需要优化)';
  }

  /// 检查是否有性能问题
  static List<String> getPerformanceIssues() {
    final issues = <String>[];
    final stats = getAllStats();
    
    for (final entry in stats.entries) {
      final key = entry.key;
      final data = entry.value;
      final average = data['average'] as int;
      final max = data['max'] as int;
      
      if (average > 1000) {
        issues.add('$key 平均响应时间过长: ${average}ms');
      }
      
      if (max > 3000) {
        issues.add('$key 最大响应时间过长: ${max}ms');
      }
    }
    
    return issues;
  }

  /// 定期打印性能报告
  static Timer? _reportTimer;
  
  static void startPeriodicReport({Duration interval = const Duration(minutes: 5)}) {
    if (!kDebugMode) return;
    
    _reportTimer?.cancel();
    _reportTimer = Timer.periodic(interval, (timer) {
      printReport();
      
      final issues = getPerformanceIssues();
      if (issues.isNotEmpty) {
        debugPrint('⚠️ 性能问题:');
        for (final issue in issues) {
          debugPrint('  - $issue');
        }
      }
    });

    debugPrint('📊 性能监控定期报告已启动 (间隔: ${interval.inMinutes}分钟)');
  }
  
  static void stopPeriodicReport() {
    _reportTimer?.cancel();
    _reportTimer = null;
    
    if (kDebugMode) {
      print('📊 性能监控定期报告已停止');
    }
  }
}
